import React, { useState, useEffect } from 'react';
import PersonaCard from './components/PersonaCard';
import { Search, Filter, Users, TrendingUp } from 'lucide-react';

const samplePersonas = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+60 12-345-6789',
    address: 'Kuala Lumpur, Malaysia',
    utmSource: 'Facebook',
    tags: ['Tech Enthusiast'],
    category: 'Advocator' as const,
    ltvSegment: 'Platinum' as const,
    engagement: 'Hot' as const,
    priority: 'High' as const,
    notes: 'Highly engaged customer, prefers premium services',
    nextAction: 'Schedule premium consultation',
    lifecycleProgress: 85,
    overallScore: 92,
    emailVerified: true,
    phoneVerified: true,
    qualityScore: 'excellent',
    customFields: {
      'Industry': 'Technology',
      'Company Size': '50-100 employees'
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+60 11-234-5678',
    address: 'Penang, Malaysia',
    utmSource: 'Instagram',
    tags: ['Growth'],
    category: 'Loyal' as const,
    ltvSegment: 'Gold+' as const,
    engagement: 'Warm' as const,
    priority: 'Medium' as const,
    notes: 'Small business owner, interested in scaling solutions',
    nextAction: 'Send growth package proposal',
    lifecycleProgress: 68,
    overallScore: 76,
    emailVerified: true,
    phoneVerified: false,
    qualityScore: 'good',
    customFields: {
      'Industry': 'Retail',
      'Years in Business': '5+'
    }
  },
  {
    id: '3',
    name: 'Li Wei Ming',
    email: '<EMAIL>',
    phone: '+60 16-789-0123',
    address: 'Johor Bahru, Malaysia',
    utmSource: 'TikTok',
    tags: ['Young Professional'],
    category: 'Retainer' as const,
    ltvSegment: 'Gold' as const,
    engagement: 'Cold' as const,
    priority: 'Low' as const,
    notes: 'Startup founder, budget-conscious but growth-oriented',
    nextAction: 'Re-engagement campaign',
    lifecycleProgress: 34,
    overallScore: 58,
    emailVerified: false,
    phoneVerified: true,
    qualityScore: 'fair',
    customFields: {
      'Funding Stage': 'Seed',
      'Team Size': '5-10'
    }
  },
  {
    id: '4',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+60 17-456-7890',
    address: 'Cyberjaya, Malaysia',
    utmSource: 'LinkedIn',
    tags: ['Decision Maker'],
    category: 'First Timer' as const,
    ltvSegment: 'Silver' as const,
    engagement: 'Warm' as const,
    priority: 'High' as const,
    notes: 'New enterprise prospect, high potential value',
    nextAction: 'Enterprise demo presentation',
    lifecycleProgress: 22,
    overallScore: 71,
    emailVerified: true,
    phoneVerified: true,
    qualityScore: 'poor',
    customFields: {
      'Role': 'Chief Technology Officer',
      'Company': 'Tech Corp Malaysia'
    }
  }
];

function App() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [filteredPersonas, setFilteredPersonas] = useState(samplePersonas);

  useEffect(() => {
    let filtered = samplePersonas;

    if (searchTerm) {
      filtered = filtered.filter(persona =>
        persona.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        persona.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        persona.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (filterBy !== 'all') {
      filtered = filtered.filter(persona => {
        switch (filterBy) {
          case 'hot':
            return persona.engagement === 'Hot';
          case 'high-ltv':
            return persona.ltvSegment === 'Platinum' || persona.ltvSegment === 'Gold+';
          case 'advocates':
            return persona.category === 'Advocator';
          case 'high-priority':
            return persona.priority === 'High';
          default:
            return true;
        }
      });
    }

    setFilteredPersonas(filtered);
  }, [searchTerm, filterBy]);

  const getOverallStats = () => {
    const totalPersonas = samplePersonas.length;
    const avgScore = Math.round(samplePersonas.reduce((sum, p) => sum + p.overallScore, 0) / totalPersonas);
    const hotLeads = samplePersonas.filter(p => p.engagement === 'Hot').length;
    const advocators = samplePersonas.filter(p => p.category === 'Advocator').length;

    return { totalPersonas, avgScore, hotLeads, advocators };
  };

  const stats = getOverallStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">CRM Persona Dashboard</h1>
              <p className="text-gray-600 mt-1">Visualize and manage your customer personas</p>
            </div>
            
            {/* Stats */}
            <div className="flex gap-6">
              <div className="text-center">
                <div className="flex items-center gap-2 text-blue-600">
                  <Users size={20} />
                  <span className="text-2xl font-bold">{stats.totalPersonas}</span>
                </div>
                <div className="text-xs text-gray-500">Total Personas</div>
              </div>
              <div className="text-center">
                <div className="flex items-center gap-2 text-green-600">
                  <TrendingUp size={20} />
                  <span className="text-2xl font-bold">{stats.avgScore}%</span>
                </div>
                <div className="text-xs text-gray-500">Avg Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.hotLeads}</div>
                <div className="text-xs text-gray-500">Hot Leads</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.advocators}</div>
                <div className="text-xs text-gray-500">Advocators</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="flex gap-4 mb-8">
          {/* Search */}
          <div className="relative flex-1">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search personas by name, email, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
            />
          </div>

          {/* Filter */}
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="pl-10 pr-8 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm appearance-none"
              className="pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm appearance-none"
            >
              <option value="all">All Personas</option>
              <option value="hot">Hot Leads</option>
              <option value="high-ltv">High LTV</option>
              <option value="advocates">Advocators</option>
              <option value="high-priority">High Priority</option>
            </select>
          </div>
        </div>

        {/* Personas Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {filteredPersonas.map((persona) => (
            <PersonaCard key={persona.id} persona={persona} />
          ))}
        </div>

        {filteredPersonas.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg">No personas match your search criteria</div>
            <button
              onClick={() => {
                setSearchTerm('');
                setFilterBy('all');
              }}
              className="mt-4 text-blue-600 hover:text-blue-700 font-medium"
            >
              Clear filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;