import React from 'react';
import { useState, useEffect } from 'react';
import { User, Mail, Phone, MapPin, Target, TrendingUp, Clock, Star, Eye, CheckCircle } from 'lucide-react';
import PersonaDetailsModal from './PersonaDetailsModal';

interface PersonaData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  utmSource: string;
  tags: string[];
  category: 'First Timer' | 'Retainer' | 'Loyal' | 'Advocator';
  ltvSegment: 'Silver' | 'Gold' | 'Gold+' | 'Platinum';
  engagement: 'Hot' | 'Warm' | 'Cold' | 'Frozen';
  priority: 'High' | 'Medium' | 'Low';
  notes: string;
  nextAction: string;
  lifecycleProgress: number;
  overallScore: number;
  emailVerified: boolean;
  phoneVerified: boolean;
  qualityScore: 'excellent' | 'good' | 'fair' | 'poor';
  customFields?: Record<string, string>;
}

interface PersonaCardProps {
  persona: PersonaData;
}

const PersonaCard: React.FC<PersonaCardProps> = ({ persona }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [animatedScore, setAnimatedScore] = useState(0);
  const [animatedLifecycle, setAnimatedLifecycle] = useState(0);

  useEffect(() => {
    // Animate overall score
    const scoreTimer = setTimeout(() => {
      const increment = persona.overallScore / 50; // 50 steps
      let current = 0;
      const scoreInterval = setInterval(() => {
        current += increment;
        if (current >= persona.overallScore) {
          setAnimatedScore(persona.overallScore);
          clearInterval(scoreInterval);
        } else {
          setAnimatedScore(Math.floor(current));
        }
      }, 30);
    }, 200);

    // Animate lifecycle progress
    const lifecycleTimer = setTimeout(() => {
      const increment = persona.lifecycleProgress / 50; // 50 steps
      let current = 0;
      const lifecycleInterval = setInterval(() => {
        current += increment;
        if (current >= persona.lifecycleProgress) {
          setAnimatedLifecycle(persona.lifecycleProgress);
          clearInterval(lifecycleInterval);
        } else {
          setAnimatedLifecycle(Math.floor(current));
        }
      }, 30);
    }, 400);

    return () => {
      clearTimeout(scoreTimer);
      clearTimeout(lifecycleTimer);
    };
  }, [persona.overallScore, persona.lifecycleProgress]);

  const getEngagementColor = (engagement: string) => {
    switch (engagement) {
      case 'Hot': return 'text-red-600 bg-red-100';
      case 'Warm': return 'text-orange-600 bg-orange-100';
      case 'Cold': return 'text-blue-600 bg-blue-100';
      case 'Frozen': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getLTVColor = (segment: string) => {
    switch (segment) {
      case 'Silver': return 'text-gray-700 bg-gray-200';
      case 'Gold': return 'text-yellow-700 bg-yellow-200';
      case 'Gold+': return 'text-yellow-800 bg-yellow-300';
      case 'Platinum': return 'text-purple-700 bg-purple-200';
      default: return 'text-gray-700 bg-gray-200';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'First Timer': return 'text-green-700 bg-green-100';
      case 'Retainer': return 'text-blue-700 bg-blue-100';
      case 'Loyal': return 'text-purple-700 bg-purple-100';
      case 'Advocator': return 'text-yellow-700 bg-yellow-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'text-red-700 bg-red-100';
      case 'Medium': return 'text-yellow-700 bg-yellow-100';
      case 'Low': return 'text-green-700 bg-green-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getUtmSourceColor = (source: string) => {
    switch (source.toLowerCase()) {
      case 'facebook': return 'text-white bg-blue-600'; // Facebook blue
      case 'instagram': return 'text-white bg-gradient-to-r from-purple-500 to-pink-500'; // Instagram gradient
      case 'tiktok': return 'text-white bg-black'; // TikTok black
      case 'linkedin': return 'text-white bg-blue-700'; // LinkedIn blue
      case 'twitter': return 'text-white bg-blue-400'; // Twitter blue
      case 'youtube': return 'text-white bg-red-600'; // YouTube red
      case 'whatsapp': return 'text-white bg-green-500'; // WhatsApp green
      case 'telegram': return 'text-white bg-blue-500'; // Telegram blue
      default: return 'text-gray-700 bg-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getProgressRingColor = (score: number) => {
    if (score >= 80) return '#16a34a'; // green-600
    if (score >= 60) return '#ca8a04'; // yellow-600
    if (score >= 40) return '#ea580c'; // orange-600
    return '#dc2626'; // red-600
  };

  const getLifecycleProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    if (progress >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getQualityScoreColor = (score: string) => {
    switch (score) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-orange-600 bg-orange-100';
      case 'fair': return 'text-blue-600 bg-blue-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get current color based on animated progress
  const getCurrentRingColor = () => {
    if (animatedScore >= 80) return '#16a34a';
    if (animatedScore >= 60) return '#ca8a04';
    if (animatedScore >= 40) return '#ea580c';
    return '#dc2626';
  };

  const getCurrentLifecycleColor = () => {
    if (animatedLifecycle >= 80) return 'bg-green-500';
    if (animatedLifecycle >= 60) return 'bg-yellow-500';
    if (animatedLifecycle >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 cursor-pointer" onClick={() => setShowDetails(true)}>
      {/* Header with Overall Score and UTM Source */}
      <div className="flex justify-between items-center mb-4">
        {/* Overall Score - Top Left */}
        <div className={`text-2xl font-bold ${getScoreColor(animatedScore)} transition-all duration-100`}>
          {animatedScore}%
        </div>
        
        {/* UTM Source - Top Right */}
        <div className={`px-3 py-1 rounded text-xs font-medium ${getUtmSourceColor(persona.utmSource)}`}>
          {persona.utmSource}
        </div>
      </div>

      {/* Central figure container */}
      <div className="flex flex-col items-center mb-4">
        {/* Progress ring and human figure */}
        <div className="relative">
          {/* Progress ring */}
          <div className="w-32 h-32 relative">
            <svg className="w-32 h-32 transform -rotate-90 transition-all duration-1000 ease-out" viewBox="0 0 120 120">
              {/* Background circle */}
              <circle
                cx="60"
                cy="60"
                r="52"
                stroke="#e5e7eb"
                strokeWidth="8"
                fill="none"
              />
              {/* Progress circle */}
              <circle
                cx="60"
                cy="60"
                r="52"
                stroke={getCurrentRingColor()}
                strokeWidth="8"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={`${(animatedScore / 100) * 326.73} 326.73`}
                className="transition-all duration-100 ease-out"
              />
            </svg>
            
            {/* Human figure in center */}
            <div className="absolute inset-4 bg-blue-100 rounded-full flex items-center justify-center">
              <User size={48} className="text-gray-600" />
            </div>
          </div>
        </div>

        {/* Name and basic info */}
        <div className="text-center mt-2 space-y-2">
          <h3 className="text-xl font-bold text-gray-800">{persona.name}</h3>
          <div className={`px-2 py-1 rounded text-xs font-medium ${getQualityScoreColor(persona.qualityScore)}`}>
            Data Quality: {persona.qualityScore.charAt(0).toUpperCase() + persona.qualityScore.slice(1)}
          </div>
          <div className="flex flex-col items-center gap-1 text-sm text-gray-600">
            <div className="flex items-center gap-1 max-w-full">
              <Mail size={14} />
              <span className="truncate">{persona.email}</span>
              {persona.emailVerified && (
                <div className="flex-shrink-0 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 1L3.5 6.5L1 4" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              )}
            </div>
            <div className="flex items-center gap-1 max-w-full">
              <Phone size={14} />
              <span>{persona.phone}</span>
              {persona.phoneVerified && (
                <div className="flex-shrink-0 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 1L3.5 6.5L1 4" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Parameters Grid */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className={`px-2 py-1 rounded text-xs font-medium text-center ${getEngagementColor(persona.engagement)}`}>
          {persona.engagement}
        </div>
        <div className={`px-2 py-1 rounded text-xs font-medium text-center ${getCategoryColor(persona.category)}`}>
          {persona.category}
        </div>
        <div className={`px-2 py-1 rounded text-xs font-medium text-center ${getLTVColor(persona.ltvSegment)}`}>
          {persona.ltvSegment}
        </div>
        <div className={`px-2 py-1 rounded text-xs font-medium text-center ${getPriorityColor(persona.priority)}`}>
          {persona.priority}
        </div>
      </div>

      {/* Lifecycle progress bar */}
      <div className="mb-4">
        <div className="flex justify-between text-xs text-gray-500 mb-2">
          <span>Lifecycle Progress</span>
          <span>{animatedLifecycle}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded h-2">
          <div 
            className={`${getCurrentLifecycleColor()} h-2 rounded transition-all duration-100 ease-out`}
            style={{ width: `${animatedLifecycle}%` }}
          ></div>
        </div>
      </div>

      {/* Next action */}
      <div className="text-center">
        <div className="text-xs text-gray-500 mb-1">Next Action</div>
        <div className="text-sm font-medium text-gray-700 bg-gray-50 rounded px-3 py-2">
          {persona.nextAction}
        </div>
        
        {/* View Details Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowDetails(true);
          }}
          className="mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
        >
          <Eye size={16} />
          View Details
        </button>
      </div>
    </div>
    
    <PersonaDetailsModal
      persona={persona}
      isOpen={showDetails}
      onClose={() => setShowDetails(false)}
    />
    </>
  );
};

export default PersonaCard;