import React, { useState } from 'react';
import { X, MapPin, FileText, CreditCard, Activity, StickyNote, Calendar, DollarSign, Eye, Download, Plus } from 'lucide-react';

interface PersonaData {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  utmSource: string;
  tags: string[];
  category: 'First Timer' | 'Retainer' | 'Loyal' | 'Advocator';
  ltvSegment: 'Silver' | 'Gold' | 'Gold+' | 'Platinum';
  engagement: 'Hot' | 'Warm' | 'Cold' | 'Frozen';
  priority: 'High' | 'Medium' | 'Low';
  notes: string;
  nextAction: string;
  lifecycleProgress: number;
  overallScore: number;
  emailVerified: boolean;
  phoneVerified: boolean;
  qualityScore: 'excellent' | 'good' | 'fair' | 'poor';
  customFields?: Record<string, string>;
}

interface PersonaDetailsModalProps {
  persona: PersonaData;
  isOpen: boolean;
  onClose: () => void;
}

const PersonaDetailsModal: React.FC<PersonaDetailsModalProps> = ({ persona, isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [newNote, setNewNote] = useState('');

  if (!isOpen) return null;

  // Sample data for the detailed view
  const quotations = [
    { id: 'Q001', date: '2024-01-15', amount: 15000, status: 'Pending', description: 'Premium Service Package' },
    { id: 'Q002', date: '2024-01-10', amount: 8500, status: 'Accepted', description: 'Growth Consultation' },
    { id: 'Q003', date: '2023-12-20', amount: 12000, status: 'Rejected', description: 'Enterprise Solution' }
  ];

  const invoices = [
    { id: 'INV001', date: '2024-01-12', amount: 8500, status: 'Paid', dueDate: '2024-01-27' },
    { id: 'INV002', date: '2023-11-15', amount: 5200, status: 'Paid', dueDate: '2023-11-30' },
    { id: 'INV003', date: '2023-09-08', amount: 3800, status: 'Overdue', dueDate: '2023-09-23' }
  ];

  const transactions = [
    { id: 'TXN001', date: '2024-01-12', amount: 8500, type: 'Payment', method: 'Bank Transfer', status: 'Completed' },
    { id: 'TXN002', date: '2023-11-15', amount: 5200, type: 'Payment', method: 'Credit Card', status: 'Completed' },
    { id: 'TXN003', date: '2023-09-08', amount: 3800, type: 'Refund', method: 'Bank Transfer', status: 'Pending' }
  ];

  const activityLogs = [
    { id: 1, date: '2024-01-15 10:30', action: 'Email sent', description: 'Premium package proposal sent', user: 'Sarah Admin' },
    { id: 2, date: '2024-01-14 15:45', action: 'Call completed', description: '30-min consultation call', user: 'John Sales' },
    { id: 3, date: '2024-01-12 09:15', action: 'Payment received', description: 'Invoice INV001 paid', user: 'System' },
    { id: 4, date: '2024-01-10 14:20', action: 'Quotation accepted', description: 'Q002 accepted by client', user: 'System' },
    { id: 5, date: '2024-01-08 11:00', action: 'Meeting scheduled', description: 'Follow-up meeting set for Jan 20', user: 'Sarah Admin' }
  ];

  const customNotes = [
    { id: 1, date: '2024-01-15', note: 'Client is very interested in premium features. Mentioned budget flexibility.', author: 'Sarah Admin' },
    { id: 2, date: '2024-01-10', note: 'Prefers email communication over phone calls. Responds quickly to emails.', author: 'John Sales' },
    { id: 3, date: '2024-01-05', note: 'Company is expanding rapidly. Good potential for upselling.', author: 'Sarah Admin' }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'completed':
      case 'accepted':
        return 'text-green-700 bg-green-100';
      case 'pending':
        return 'text-yellow-700 bg-yellow-100';
      case 'overdue':
      case 'rejected':
        return 'text-red-700 bg-red-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Eye },
    { id: 'quotations', label: 'Quotations', icon: FileText },
    { id: 'invoices', label: 'Invoices', icon: FileText },
    { id: 'transactions', label: 'Transactions', icon: CreditCard },
    { id: 'activity', label: 'Activity', icon: Activity },
    { id: 'notes', label: 'Notes', icon: StickyNote }
  ];

  const handleAddNote = () => {
    if (newNote.trim()) {
      // In a real app, this would make an API call
      console.log('Adding note:', newNote);
      setNewNote('');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold">{persona.name}</h2>
              <p className="text-blue-100 mt-1">{persona.email}</p>
              <div className="flex items-center gap-4 mt-2 text-sm">
                <span>{persona.phone}</span>
                <span>•</span>
                <span>{persona.category}</span>
                <span>•</span>
                <span>{persona.ltvSegment}</span>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-4 text-sm font-medium whitespace-nowrap transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <MapPin size={20} />
                  Personal Information
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Address</label>
                    <p className="text-gray-900">{persona.address}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">UTM Source</label>
                    <p className="text-gray-900">{persona.utmSource}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Tags</label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {persona.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Overall Score</span>
                      <span className="font-medium">{persona.overallScore}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded transition-all duration-300"
                        style={{ width: `${persona.overallScore}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Lifecycle Progress</span>
                      <span className="font-medium">{persona.lifecycleProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded h-2">
                      <div 
                        className="bg-green-600 h-2 rounded transition-all duration-300"
                        style={{ width: `${persona.lifecycleProgress}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        RM {(quotations.reduce((sum, q) => sum + q.amount, 0) / 1000).toFixed(0)}K
                      </div>
                      <div className="text-xs text-gray-500">Total Quoted</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        RM {(transactions.filter(t => t.status === 'Completed').reduce((sum, t) => sum + t.amount, 0) / 1000).toFixed(0)}K
                      </div>
                      <div className="text-xs text-gray-500">Total Paid</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Custom Fields */}
              {persona.customFields && (
                <div className="bg-gray-50 rounded-lg p-4 md:col-span-2">
                  <h3 className="text-lg font-semibold mb-4">Additional Information</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {Object.entries(persona.customFields).map(([key, value]) => (
                      <div key={key}>
                        <label className="text-sm font-medium text-gray-500">{key}</label>
                        <p className="text-gray-900">{value}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'quotations' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Quotations</h3>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                  <Plus size={16} />
                  New Quotation
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">ID</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Description</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {quotations.map((quote) => (
                      <tr key={quote.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 font-medium">{quote.id}</td>
                        <td className="py-3 px-4">{quote.date}</td>
                        <td className="py-3 px-4">{quote.description}</td>
                        <td className="py-3 px-4 font-medium">RM {quote.amount.toLocaleString()}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(quote.status)}`}>
                            {quote.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <button className="text-blue-600 hover:text-blue-700 mr-2">
                            <Eye size={16} />
                          </button>
                          <button className="text-gray-600 hover:text-gray-700">
                            <Download size={16} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'invoices' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Invoices</h3>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                  <Plus size={16} />
                  New Invoice
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">ID</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Due Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoices.map((invoice) => (
                      <tr key={invoice.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 font-medium">{invoice.id}</td>
                        <td className="py-3 px-4">{invoice.date}</td>
                        <td className="py-3 px-4 font-medium">RM {invoice.amount.toLocaleString()}</td>
                        <td className="py-3 px-4">{invoice.dueDate}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(invoice.status)}`}>
                            {invoice.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <button className="text-blue-600 hover:text-blue-700 mr-2">
                            <Eye size={16} />
                          </button>
                          <button className="text-gray-600 hover:text-gray-700">
                            <Download size={16} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'transactions' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Transaction History</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">ID</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Type</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Method</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map((transaction) => (
                      <tr key={transaction.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 font-medium">{transaction.id}</td>
                        <td className="py-3 px-4">{transaction.date}</td>
                        <td className="py-3 px-4">
                          <span className={`flex items-center gap-2 ${transaction.type === 'Payment' ? 'text-green-600' : 'text-orange-600'}`}>
                            <DollarSign size={16} />
                            {transaction.type}
                          </span>
                        </td>
                        <td className="py-3 px-4 font-medium">RM {transaction.amount.toLocaleString()}</td>
                        <td className="py-3 px-4">{transaction.method}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(transaction.status)}`}>
                            {transaction.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Activity Log</h3>
              <div className="space-y-4">
                {activityLogs.map((log) => (
                  <div key={log.id} className="flex gap-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Activity size={16} className="text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-900">{log.action}</span>
                        <span className="text-sm text-gray-500">by {log.user}</span>
                      </div>
                      <p className="text-gray-700 text-sm mb-1">{log.description}</p>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Calendar size={12} />
                        {log.date}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'notes' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Custom Notes</h3>
              </div>
              
              {/* Add new note */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium mb-2">Add New Note</h4>
                <textarea
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  placeholder="Enter your note here..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={3}
                />
                <div className="flex justify-end mt-2">
                  <button
                    onClick={handleAddNote}
                    disabled={!newNote.trim()}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Note
                  </button>
                </div>
              </div>

              {/* Existing notes */}
              <div className="space-y-4">
                {customNotes.map((note) => (
                  <div key={note.id} className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-medium text-gray-900">{note.author}</span>
                      <span className="text-sm text-gray-500">{note.date}</span>
                    </div>
                    <p className="text-gray-700">{note.note}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PersonaDetailsModal;