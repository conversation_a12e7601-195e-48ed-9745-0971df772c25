# KDT Development Environment Makefile

.PHONY: help start stop restart rebuild watch status logs clean install

# Default target
help: ## Show this help message
	@echo "KDT Development Environment"
	@echo ""
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install all dependencies
	@echo "Installing development dependencies..."
	@chmod +x scripts/dev.sh
	@chmod +x scripts/watch-and-rebuild.sh
	@chmod +x scripts/watch-and-rebuild.js
	@cd scripts && npm install
	@echo "Dependencies installed successfully"

start: ## Start development environment
	@scripts/dev.sh start

stop: ## Stop development environment
	@scripts/dev.sh stop

restart: ## Restart development environment
	@scripts/dev.sh restart

rebuild: ## Rebuild development environment
	@scripts/dev.sh rebuild

watch: ## Start development environment with auto-rebuild watcher
	@scripts/dev.sh watch

status: ## Show development environment status
	@scripts/dev.sh status

logs: ## Show all logs
	@scripts/dev.sh logs

logs-backend: ## Show backend logs
	@scripts/dev.sh logs kdt-backend

logs-frontend: ## Show frontend logs
	@scripts/dev.sh logs kdt-frontend

logs-db: ## Show database logs
	@scripts/dev.sh logs kdt-postgres

# Backend commands
backend-shell: ## Open shell in backend container
	@docker-compose -f docker-compose.dev.yml exec kdt-backend bash

backend-migrate: ## Run database migrations
	@docker-compose -f docker-compose.dev.yml exec kdt-backend php artisan migrate

backend-migrate-fresh: ## Fresh database migration with seeding
	@docker-compose -f docker-compose.dev.yml exec kdt-backend php artisan migrate:fresh --seed

backend-tinker: ## Open Laravel Tinker
	@docker-compose -f docker-compose.dev.yml exec kdt-backend php artisan tinker

backend-test: ## Run backend tests
	@docker-compose -f docker-compose.dev.yml exec kdt-backend php artisan test

# Frontend commands
frontend-shell: ## Open shell in frontend container
	@docker-compose -f docker-compose.dev.yml exec kdt-frontend sh

frontend-install: ## Install frontend dependencies
	@docker-compose -f docker-compose.dev.yml exec kdt-frontend npm install

frontend-build: ## Build frontend for production
	@docker-compose -f docker-compose.dev.yml exec kdt-frontend npm run build

frontend-test: ## Run frontend tests
	@docker-compose -f docker-compose.dev.yml exec kdt-frontend npm test

# Database commands
db-shell: ## Open database shell
	@docker-compose -f docker-compose.dev.yml exec kdt-postgres psql -U kdt -d kdt

db-backup: ## Create database backup
	@docker-compose -f docker-compose.dev.yml exec kdt-postgres pg_dump -U kdt kdt > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Database backup created: backup_$(shell date +%Y%m%d_%H%M%S).sql"

# Cleanup commands
clean: ## Clean up containers and volumes
	@echo "Cleaning up development environment..."
	@docker-compose -f docker-compose.dev.yml down -v
	@docker system prune -f
	@echo "Cleanup completed"

clean-all: ## Clean up everything including images
	@echo "Cleaning up everything..."
	@docker-compose -f docker-compose.dev.yml down -v --rmi all
	@docker system prune -af
	@echo "Full cleanup completed"

# Production commands
prod-start: ## Start production environment
	@docker-compose up -d

prod-stop: ## Stop production environment
	@docker-compose down

prod-rebuild: ## Rebuild production environment
	@docker-compose down
	@docker-compose build --no-cache
	@docker-compose up -d

# Development workflow shortcuts
dev: install start ## Install dependencies and start development environment

dev-watch: install watch ## Install dependencies and start with auto-rebuild watcher

quick-start: start ## Quick start without dependency check

# Health checks
health: ## Check service health
	@echo "Checking service health..."
	@curl -s http://localhost:8000/api/v1/health || echo "Backend not responding"
	@curl -s http://localhost:3000 > /dev/null && echo "Frontend: OK" || echo "Frontend not responding"

# Documentation
docs: ## Open documentation
	@echo "Opening documentation..."
	@echo "Frontend: http://localhost:3000"
	@echo "Backend API: http://localhost:8000"
	@echo "Adminer: http://localhost:8001"
