# KDT Project .gitignore

# Environment files (contains sensitive data)
.env
.env.local
.env.production
.env.staging
backend/.env
backend/.env.production
backend/.env.staging

# Dependencies
node_modules/
backend/vendor/

# Build outputs
dist/
build/
backend/public/build/

# Logs
*.log
logs/
deploy.log
monitor.log
backup.log
console-logs.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Laravel specific
backend/storage/app/*
!backend/storage/app/.gitkeep
backend/storage/framework/cache/*
!backend/storage/framework/cache/.gitkeep
backend/storage/framework/sessions/*
!backend/storage/framework/sessions/.gitkeep
backend/storage/framework/testing/*
!backend/storage/framework/testing/.gitkeep
backend/storage/framework/views/*
!backend/storage/framework/views/.gitkeep
backend/storage/logs/*
!backend/storage/logs/.gitkeep
backend/bootstrap/cache/*
!backend/bootstrap/cache/.gitkeep

# Docker
.docker/

# Backups (contains sensitive data)
backups/
*.sql
*.sql.gz
*.tar.gz
*settings-backup*
*kdt-backup*
.kdt-credentials

# Production files that may contain sensitive data
deploy/ssl/
*.pem
*.key
*.crt

# IDE files
*.sublime-project
*.sublime-workspace

# Test files
test_*.csv
missing_records*.csv
master_contacts_*.csv
test_email_settings.php
**/test_email_settings.php

# Temporary files
*.tmp
*.temp

# Database files
*.sqlite
*.db

# Compiled files
*.com
*.class
*.dll
*.exe
*.o
*.so

# Archives
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# System files
.fuse_hidden*
.directory
.Trash-*
.nfs*
