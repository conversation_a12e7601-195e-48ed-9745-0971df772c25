#!/bin/bash

# Simple frontend deployment script for ts.crtvmkmn.space
# This script only updates the frontend files without full deployment

set -euo pipefail

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PI_HOST="<EMAIL>"
PI_PROJECT_PATH="/home/<USER>/Apps/KDT"
DIST_PATH="$PROJECT_ROOT/dist"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${BLUE}[INFO]${NC} $message" ;;
        SUCCESS) echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        WARNING) echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" ;;
    esac
}

# Check if dist directory exists
if [[ ! -d "$DIST_PATH" ]]; then
    log ERROR "Build directory not found: $DIST_PATH"
    log INFO "Please run 'npm run build' first"
    exit 1
fi

log INFO "🚀 Starting frontend-only deployment to $PI_HOST"

# Test connection
log INFO "Testing connection to $PI_HOST..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$PI_HOST" "echo 'Connection test successful'" 2>/dev/null; then
    log ERROR "Cannot connect to $PI_HOST"
    log INFO "Please ensure:"
    log INFO "  1. SSH key is configured"
    log INFO "  2. Server is accessible"
    log INFO "  3. User has proper permissions"
    exit 1
fi

log SUCCESS "Connection to $PI_HOST successful"

# Copy frontend files
log INFO "📦 Copying frontend files..."
if rsync -avz --delete "$DIST_PATH/" "$PI_HOST:$PI_PROJECT_PATH/frontend/dist/"; then
    log SUCCESS "Frontend files copied successfully"
else
    log ERROR "Failed to copy frontend files"
    exit 1
fi

# Set proper permissions on the server
log INFO "🔧 Setting proper permissions..."
if ssh "$PI_HOST" "sudo chown -R pi:www-data $PI_PROJECT_PATH/frontend/dist && sudo chmod -R 755 $PI_PROJECT_PATH/frontend/dist"; then
    log SUCCESS "Permissions set successfully"
else
    log WARNING "Failed to set permissions (this might be okay)"
fi

# Test nginx configuration and reload
log INFO "🔄 Reloading nginx..."
if ssh "$PI_HOST" "sudo nginx -t && sudo systemctl reload nginx"; then
    log SUCCESS "Nginx reloaded successfully"
else
    log ERROR "Failed to reload nginx"
    exit 1
fi

# Verify deployment
log INFO "✅ Verifying deployment..."
if ssh "$PI_HOST" "test -f $PI_PROJECT_PATH/frontend/dist/index.html"; then
    log SUCCESS "Deployment verification successful"
    log INFO "Frontend deployment completed!"
    log INFO "Visit https://ts.crtvmkmn.space to see the changes"
else
    log ERROR "Deployment verification failed"
    exit 1
fi
