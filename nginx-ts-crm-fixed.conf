server {
    listen 3000;
    server_name localhost;

    # Increase client body size for file uploads
    client_max_body_size 50M;

    # Increase timeouts for long-running operations
    proxy_connect_timeout 1800s;
    proxy_send_timeout 1800s;
    proxy_read_timeout 1800s;
    fastcgi_connect_timeout 1800s;
    fastcgi_send_timeout 1800s;
    fastcgi_read_timeout 1800s;

    # Serve static assets directly (JS, CSS, images, etc.)
    location /app/assets/ {
        root /var/www/ts-crm/public;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # Ensure correct MIME types
        location ~* \.js$ {
            add_header Content-Type application/javascript;
        }
        location ~* \.css$ {
            add_header Content-Type text/css;
        }
    }

    # Serve other static files (favicon, etc.)
    location ~* \.(ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
        root /var/www/ts-crm/public/app;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API routes go to Laravel - FIXED TO HANDLE ALL HTTP METHODS
    location /api {
        root /var/www/ts-crm/public;
        
        # Allow all HTTP methods for API endpoints
        if ($request_method !~ ^(GET|POST|PUT|PATCH|DELETE|OPTIONS)$ ) {
            return 405;
        }
        
        # Handle preflight OPTIONS requests for CORS
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, PATCH, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        try_files $uri $uri/ /index.php?$query_string;

        location ~ \.php$ {
            root /var/www/ts-crm/public;
            fastcgi_pass unix:/var/run/php/php8.2-fpm-kdt.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            
            # Pass the HTTP method to PHP
            fastcgi_param REQUEST_METHOD $request_method;
            
            # Increase timeouts for long operations
            fastcgi_connect_timeout 1800s;
            fastcgi_send_timeout 1800s;
            fastcgi_read_timeout 1800s;
        }
    }

    # Serve React app for all other routes
    location / {
        root /var/www/ts-crm/public/app;
        try_files $uri $uri/ /index.html;
        index index.html;
    }
}
