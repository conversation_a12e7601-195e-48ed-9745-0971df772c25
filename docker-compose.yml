services:
  # PostgreSQL Database
  kdt-postgres:
    image: postgres:15-alpine
    container_name: kdt-postgres
    environment:
      POSTGRES_DB: kdt
      POSTGRES_USER: kdt
      POSTGRES_PASSWORD: kdt_password
    volumes:
      - kdt_postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kdt"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  kdt-redis:
    image: redis:7-alpine
    container_name: kdt-redis
    ports:
      - "6379:6379"
    volumes:
      - kdt_redis_data:/data
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Laravel Backend API
  kdt-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kdt-backend
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=kdt-postgres
      - DB_DATABASE=kdt
      - DB_USERNAME=kdt
      - DB_PASSWORD=kdt_password
      - REDIS_HOST=kdt-redis
    volumes:
      - kdt_backend_storage:/var/www/html/storage
    depends_on:
      kdt-postgres:
        condition: service_healthy
      kdt-redis:
        condition: service_healthy
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Frontend
  kdt-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_URL=http://localhost:8000/api/v1
        - VITE_APP_NAME=KDT
        - VITE_APP_ENV=production
    container_name: kdt-frontend
    ports:
      - "3000:3000"
    depends_on:
      - kdt-backend
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Adminer for Database Management
  kdt-adminer:
    image: adminer:latest
    container_name: kdt-adminer
    ports:
      - "8001:8080"
    environment:
      ADMINER_DEFAULT_SERVER: kdt-postgres
    depends_on:
      - kdt-postgres
    networks:
      - kdt-network

volumes:
  kdt_postgres_data:
    driver: local
  kdt_redis_data:
    driver: local
  kdt_backend_storage:
    driver: local

networks:
  kdt-network:
    driver: bridge
