# KDT Production Deployment Guide

## 📋 Complete Production Deployment with Settings Preservation

This guide provides step-by-step instructions for deploying your KDT CRM system to production on Raspberry Pi while preserving all your configured email and 2FA settings.

## 🔍 Settings Persistence Analysis Summary

### ✅ What's Preserved Automatically
- **Email Configuration**: Zoho SMTP settings (host, port, username, encrypted password)
- **2FA Settings**: System-wide and user-level 2FA preferences
- **System Settings**: All configuration stored in `system_settings` table
- **User Data**: User accounts, roles, and 2FA preferences
- **Encryption**: All sensitive data (passwords) properly encrypted with <PERSON><PERSON>'s encryption

### ⚠️ What Requires Manual Configuration
- **Environment Variables**: APP_KEY, database passwords, Redis passwords
- **SSL Certificates**: Cloudflare tunnel configuration
- **File Permissions**: Storage and cache directories
- **Service Configuration**: Nginx, PHP-FPM, PostgreSQL, Redis

## 🚀 Step-by-Step Deployment Process

### Phase 1: Pre-Deployment Preparation (Development Environment)

#### 1.1 Verify Current Settings
```bash
# Check that email and 2FA are properly configured
docker exec kdt-backend php artisan tinker --execute="
echo 'Email Host: ' . \App\Models\SystemSetting::get('zoho_smtp_host', 'not set') . PHP_EOL;
echo 'Email User: ' . \App\Models\SystemSetting::get('zoho_smtp_username', 'not set') . PHP_EOL;
echo '2FA Enabled: ' . (\App\Models\SystemSetting::get('two_factor_auth_enabled', false) ? 'Yes' : 'No') . PHP_EOL;
echo 'Settings Count: ' . \App\Models\SystemSetting::count() . PHP_EOL;
"
```

#### 1.2 Test Email Functionality
```bash
# Test email sending in development
curl -X POST http://localhost:8000/api/v1/two-factor/test-email \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"email": "<EMAIL>"}'
```

#### 1.3 Create Settings Backup
```bash
# Backup all settings from development
./deploy/pi-native/scripts/backup-settings.sh
```

#### 1.4 Commit and Push Changes
```bash
# Ensure all changes are committed
git add .
git commit -m "feat: finalize email and 2FA configuration for production deployment"
git push origin pi-native-deployment
```

### Phase 2: Production Deployment

#### 2.1 Connect to Raspberry Pi
```bash
# SSH to your Raspberry Pi
ssh zulhelminasir@your-pi-ip
cd /home/<USER>/Apps/ts-crm
```

#### 2.2 Transfer Settings Backup
```bash
# Copy settings backup from development to production
scp -r /tmp/kdt-settings-backup-* zulhelminasir@your-pi-ip:/home/<USER>/Apps/ts-crm/
```

#### 2.3 Execute Production Deployment
```bash
# Run deployment with settings migration
./deploy/pi-native/scripts/deploy-with-settings.sh --settings-backup /path/to/backup

# Or use automatic backup detection
./deploy/pi-native/scripts/deploy-with-settings.sh
```

#### 2.4 Verify Deployment
```bash
# Run verification script
./deploy/pi-native/scripts/verify-deployment.sh
```

### Phase 3: Post-Deployment Configuration

#### 3.1 Access Production Application
- **Frontend**: https://ts.crtvmkmn.space
- **Admin Panel**: https://ts.crtvmkmn.space/settings

#### 3.2 Verify Settings Migration
1. Log into the admin panel
2. Go to **Settings > Email**
3. Verify Zoho SMTP settings are present
4. Test email functionality
5. Go to **Settings > Security**
6. Verify 2FA settings if enabled

#### 3.3 Security Hardening Checklist
- [ ] **APP_DEBUG=false** in production environment
- [ ] **Strong database passwords** configured
- [ ] **Email credentials encrypted** and working
- [ ] **2FA enabled** (if desired)
- [ ] **HTTPS working** via Cloudflare tunnel
- [ ] **File permissions** properly set
- [ ] **Services running** (nginx, php-fpm, postgresql, redis)

## 🔒 Security Configuration Details

### Database Security
```bash
# Verify database security
sudo -u postgres psql -c "\du"  # Check user permissions
sudo -u postgres psql -c "\l"   # Check database list
```

### Laravel Security Settings
```bash
# Verify Laravel security configuration
cd /home/<USER>/Apps/ts-crm/backend
php artisan config:show app.debug    # Should be false
php artisan config:show app.env      # Should be production
php artisan config:show app.key      # Should be set
```

### Email Encryption Verification
```bash
# Verify email password is encrypted
php artisan tinker --execute="
\$password = \App\Models\SystemSetting::get('zoho_smtp_password');
echo 'Password length: ' . strlen(\$password) . ' chars' . PHP_EOL;
echo 'Appears encrypted: ' . (strlen(\$password) > 50 ? 'Yes' : 'No') . PHP_EOL;
"
```

## 🔄 Rollback Procedures

### If Deployment Fails
```bash
# Automatic rollback
./deploy/pi-native/scripts/deploy.sh --rollback

# Manual rollback
# 1. Restore previous code version
git checkout previous-commit-hash

# 2. Restore database backup
sudo -u postgres psql kdt_production < /tmp/kdt-production-backup-*.sql

# 3. Restart services
sudo systemctl restart nginx php8.2-fpm
```

### If Settings Migration Fails
```bash
# Re-run settings migration only
cd /home/<USER>/Apps/ts-crm/backend
cp /path/to/backup/restore_settings.php .
cp /path/to/backup/system_settings.json .
php restore_settings.php
```

## 🧪 Testing Procedures

### 1. Basic Functionality Test
- [ ] Application loads at https://ts.crtvmkmn.space
- [ ] Login page accessible
- [ ] API health endpoint responds: https://ts.crtvmkmn.space/api/v1/health

### 2. Authentication Test
- [ ] Login with existing credentials works
- [ ] 2FA flow works (if enabled)
- [ ] User sessions persist correctly

### 3. Email Functionality Test
- [ ] Password reset emails send successfully
- [ ] 2FA verification codes send successfully
- [ ] Email settings show correct configuration

### 4. Database Test
- [ ] All data migrated correctly
- [ ] Settings preserved
- [ ] User accounts intact

## 📞 Troubleshooting

### Common Issues and Solutions

#### Email Not Working
```bash
# Check email settings
php artisan tinker --execute="
echo 'SMTP Host: ' . \App\Models\SystemSetting::get('zoho_smtp_host') . PHP_EOL;
echo 'SMTP Port: ' . \App\Models\SystemSetting::get('zoho_smtp_port') . PHP_EOL;
echo 'SMTP User: ' . \App\Models\SystemSetting::get('zoho_smtp_username') . PHP_EOL;
"

# Test email manually
php artisan tinker --execute="
Mail::raw('Test email from production', function(\$message) {
    \$message->to('<EMAIL>')->subject('Production Test');
});
echo 'Email sent';
"
```

#### 2FA Not Working
```bash
# Check 2FA status
php artisan tinker --execute="
echo 'System 2FA: ' . (\App\Models\SystemSetting::get('two_factor_auth_enabled') ? 'Enabled' : 'Disabled') . PHP_EOL;
echo 'User count: ' . \App\Models\User::count() . PHP_EOL;
"

# Temporarily disable 2FA
php artisan tinker --execute="
\App\Models\SystemSetting::set('two_factor_auth_enabled', false, 'boolean');
echo '2FA temporarily disabled';
"
```

#### Database Connection Issues
```bash
# Check database connectivity
php artisan tinker --execute="
try {
    DB::connection()->getPdo();
    echo 'Database connected successfully';
} catch (Exception \$e) {
    echo 'Database error: ' . \$e->getMessage();
}
"
```

## 📊 Monitoring and Maintenance

### Log Files to Monitor
- **Application Logs**: `/home/<USER>/Apps/ts-crm/backend/storage/logs/laravel.log`
- **Nginx Logs**: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- **PHP-FPM Logs**: `/var/log/php8.2-fpm.log`
- **PostgreSQL Logs**: `/var/log/postgresql/postgresql-*.log`

### Regular Maintenance Tasks
- **Weekly**: Check log files for errors
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Rotate encryption keys and passwords
- **Backup**: Daily automated database backups

## 🎉 Success Criteria

Deployment is considered successful when:
- [ ] Application accessible via HTTPS
- [ ] All authentication flows working
- [ ] Email functionality operational
- [ ] 2FA working (if enabled)
- [ ] All services running and healthy
- [ ] Security score > 80/100 in admin panel
- [ ] No critical errors in logs

---

**Need Help?** Check the troubleshooting section or review the deployment logs for specific error messages.
