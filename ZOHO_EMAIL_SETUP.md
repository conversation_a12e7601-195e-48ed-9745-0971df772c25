# Zoho Email Configuration Guide

## Overview
This guide will help you configure Zoho email service for your CRM system with secure credential handling.

## Prerequisites
- Active Zoho Mail account
- Domain configured with Zoho Mail
- Admin access to your CRM system

## Step 1: Generate Zoho App Password (Recommended)

For enhanced security, create an app-specific password instead of using your main Zoho password:

1. **Login to Zoho Mail**
   - Go to https://mail.zoho.com
   - Login with your Zoho account

2. **Access Security Settings**
   - Click on your profile picture (top right)
   - Select "My Account" or "Account Settings"
   - Navigate to "Security" section

3. **Generate App Password**
   - Look for "App Passwords" or "Application-Specific Passwords"
   - Click "Generate New Password"
   - Enter a name like "CRM System" or "SMTP Access"
   - Copy the generated password (you won't see it again)

## Step 2: Configure CRM Email Settings

1. **Access Settings**
   - Login to your CRM system
   - Navigate to Settings → Email tab

2. **Enter Zoho Configuration**
   ```
   Password: [Your app-specific password or Zoho password]
   SMTP Host: smtp.zoho.com
   SMTP Port: 587
   SMTP Username: [Your full Zoho email address]
   Encryption: TLS (Recommended)
   From Address: [Your verified domain email]
   From Name: [Your organization name]
   Reply-To Address: [Your support email]
   ```

## Step 3: Verify Configuration

1. **Save Settings**
   - Click "Save Email Settings"
   - Wait for confirmation message

2. **Test Email**
   - Click "Send Test Email"
   - Check if test email is received
   - For development: Check Mailpit at http://localhost:8025

## Security Best Practices

### ✅ Recommended
- Use app-specific passwords instead of main account password
- Use TLS encryption (port 587)
- Verify sender domain with Zoho
- Regularly rotate app passwords
- Monitor email sending logs

### ❌ Avoid
- Using main account password for SMTP
- Storing passwords in plain text
- Using unencrypted connections
- Sharing SMTP credentials

## Troubleshooting

### Common Issues

**Authentication Failed**
- Verify username is your full email address
- Check if password is correct (try app-specific password)
- Ensure 2FA is properly configured

**Connection Timeout**
- Check SMTP host: smtp.zoho.com
- Verify port: 587 for TLS, 465 for SSL
- Check firewall settings

**Sender Verification**
- Ensure "From Address" is verified in Zoho
- Domain must be configured with Zoho Mail
- Check SPF/DKIM records

### Testing in Development

For development environment:
- Test emails are captured by Mailpit
- Access Mailpit at: http://localhost:8025
- No actual emails are sent externally

## Configuration Values

### Required Fields
- **Password**: Your Zoho app password or account password
- **SMTP Username**: Your full Zoho email address (e.g., <EMAIL>)
- **From Address**: Verified email address from your domain

### Optional Fields
- **From Name**: Display name for sent emails
- **Reply-To Address**: Email for replies (can be different from From Address)

## Support

If you encounter issues:
1. Check Zoho Mail documentation
2. Verify domain configuration
3. Test with a simple email client first
4. Check CRM system logs for detailed error messages

---

**Note**: This configuration enables secure email delivery for 2FA codes, notifications, and system emails through your Zoho Mail service.
