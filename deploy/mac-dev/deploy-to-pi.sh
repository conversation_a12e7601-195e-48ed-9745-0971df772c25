#!/bin/bash

# KDT CRM - Mac Development to Pi Deployment Script
# Usage: ./deploy-to-pi.sh [--force] [--dry-run] [--rollback]
# Author: KDT Development Team
# Version: 1.0.0

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PI_HOST="<EMAIL>"
PI_PROJECT_PATH="/home/<USER>/Apps/ts-crm"
BRANCH="pi-native-deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
DRY_RUN=false
FORCE=false
ROLLBACK=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --help|-h)
            echo "KDT CRM Mac to Pi Deployment Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --dry-run     Show what would be done without executing"
            echo "  --force       Force deployment even if validation fails"
            echo "  --rollback    Rollback Pi to previous deployment"
            echo "  --verbose     Enable verbose output"
            echo "  --help        Show this help message"
            echo ""
            echo "Workflow:"
            echo "  1. Validates local changes and git status"
            echo "  2. Pushes code to pi-native-deployment branch"
            echo "  3. Triggers deployment on Raspberry Pi"
            echo "  4. Monitors deployment progress and results"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        DEBUG)
            if [[ "$VERBOSE" == "true" ]]; then
                echo -e "${CYAN}[DEBUG]${NC} $message"
            fi
            ;;
    esac
}

# Validation functions
validate_local_environment() {
    log INFO "🔍 Validating local development environment"
    
    local errors=0
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_ROOT/package.json" ]]; then
        log ERROR "Not in KDT project root directory"
        ((errors++))
    fi
    
    # Check git repository
    if [[ ! -d "$PROJECT_ROOT/.git" ]]; then
        log ERROR "Not a git repository"
        ((errors++))
    fi
    
    # Check current branch
    cd "$PROJECT_ROOT"
    local current_branch=$(git branch --show-current)
    if [[ "$current_branch" != "$BRANCH" ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "Not on $BRANCH branch (currently on: $current_branch)"
        log INFO "Switch to $BRANCH branch or use --force"
        ((errors++))
    fi
    
    # Check for uncommitted changes
    if [[ $(git status --porcelain | wc -l) -gt 0 ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "Uncommitted changes detected:"
        git status --porcelain | head -10 | while read line; do
            log ERROR "  $line"
        done
        log INFO "Commit changes or use --force to stash them"
        ((errors++))
    fi
    
    # Check remote connectivity
    if ! git ls-remote origin &>/dev/null; then
        log ERROR "Cannot connect to git remote"
        ((errors++))
    fi
    
    # Check Pi connectivity
    if ! ssh -o ConnectTimeout=5 "$PI_HOST" "echo 'Pi connection test'" &>/dev/null; then
        log ERROR "Cannot connect to Raspberry Pi at $PI_HOST"
        log INFO "Ensure Pi is accessible and SSH keys are configured"
        ((errors++))
    fi
    
    if [[ $errors -gt 0 ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "Local environment validation failed with $errors errors"
        log INFO "Use --force to override validation errors"
        exit 1
    fi
    
    log SUCCESS "Local environment validation passed"
}

# Git operations
prepare_and_push_code() {
    log INFO "📤 Preparing and pushing code to Pi"
    
    cd "$PROJECT_ROOT"
    
    # Stash uncommitted changes if any
    if [[ $(git status --porcelain | wc -l) -gt 0 ]]; then
        log WARNING "Stashing uncommitted changes"
        git stash push -m "Auto-stash before Pi deployment $(date)"
    fi
    
    # Get current commit info
    local current_commit=$(git rev-parse HEAD)
    local commit_message=$(git log -1 --pretty=format:"%s")
    
    log INFO "Current commit: $current_commit"
    log INFO "Commit message: $commit_message"
    
    # Push to remote
    log INFO "Pushing to origin/$BRANCH..."
    if git push origin "$BRANCH"; then
        log SUCCESS "Code pushed successfully"
    else
        log ERROR "Failed to push code"
        return 1
    fi
    
    # Verify remote has our changes
    local remote_commit=$(git ls-remote origin "$BRANCH" | cut -f1)
    if [[ "$current_commit" == "$remote_commit" ]]; then
        log SUCCESS "Remote repository updated"
    else
        log ERROR "Remote repository not updated correctly"
        return 1
    fi
}

# Pi deployment operations
trigger_pi_deployment() {
    log INFO "🚀 Triggering deployment on Raspberry Pi"
    
    local deploy_flags=""
    [[ "$VERBOSE" == "true" ]] && deploy_flags="$deploy_flags --verbose"
    [[ "$FORCE" == "true" ]] && deploy_flags="$deploy_flags --force"
    [[ "$DRY_RUN" == "true" ]] && deploy_flags="$deploy_flags --dry-run"
    [[ "$ROLLBACK" == "true" ]] && deploy_flags="$deploy_flags --rollback"
    
    log INFO "Executing deployment on Pi..."
    log DEBUG "Command: ssh $PI_HOST 'cd $PI_PROJECT_PATH && ./deploy/pi-native/scripts/deploy.sh $deploy_flags'"
    
    # Execute deployment on Pi with real-time output
    if ssh -t "$PI_HOST" "cd $PI_PROJECT_PATH && ./deploy/pi-native/scripts/deploy.sh $deploy_flags"; then
        log SUCCESS "Pi deployment completed successfully"
        return 0
    else
        log ERROR "Pi deployment failed"
        return 1
    fi
}

# Monitoring and verification
verify_deployment_success() {
    log INFO "✅ Verifying deployment success"
    
    # Test frontend accessibility
    log INFO "Testing frontend accessibility..."
    local frontend_test=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" || echo "000")
    
    if [[ "$frontend_test" == "200" ]]; then
        log SUCCESS "Frontend accessible (HTTP 200)"
    else
        log ERROR "Frontend not accessible (HTTP $frontend_test)"
        return 1
    fi
    
    # Test API accessibility
    log INFO "Testing API accessibility..."
    local api_test=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" || echo "000")
    
    if [[ "$api_test" == "200" ]]; then
        log SUCCESS "API accessible (HTTP 200)"
    else
        log ERROR "API not accessible (HTTP $api_test)"
        return 1
    fi
    
    # Get deployment state from Pi
    log INFO "Fetching deployment state from Pi..."
    if ssh "$PI_HOST" "cd $PI_PROJECT_PATH && git rev-parse HEAD" &>/dev/null; then
        local pi_commit=$(ssh "$PI_HOST" "cd $PI_PROJECT_PATH && git rev-parse HEAD")
        local local_commit=$(cd "$PROJECT_ROOT" && git rev-parse HEAD)
        
        if [[ "$pi_commit" == "$local_commit" ]]; then
            log SUCCESS "Pi is synchronized with local repository"
        else
            log WARNING "Pi commit ($pi_commit) differs from local ($local_commit)"
        fi
    fi
    
    log SUCCESS "Deployment verification completed"
}

# Dry run function
dry_run() {
    log INFO "🔍 Dry run - showing what would be done"
    echo "========================================"
    echo "  KDT CRM - Mac to Pi Deployment Dry Run"
    echo "========================================"
    
    cd "$PROJECT_ROOT"
    local current_commit=$(git rev-parse HEAD)
    local current_branch=$(git branch --show-current)
    local uncommitted=$(git status --porcelain | wc -l)
    
    echo "Local state:"
    echo "  Branch: $current_branch"
    echo "  Commit: $current_commit"
    echo "  Uncommitted changes: $uncommitted"
    echo ""
    
    if [[ $uncommitted -gt 0 ]]; then
        echo "Would stash uncommitted changes:"
        git status --porcelain | head -5 | while read line; do
            echo "  $line"
        done
        echo ""
    fi
    
    echo "Would execute:"
    echo "  1. 📤 Push code to origin/$BRANCH"
    echo "  2. 🚀 Trigger Pi deployment via SSH"
    echo "  3. ✅ Verify deployment success"
    echo ""
    echo "Pi deployment would:"
    echo "  - Pull latest code"
    echo "  - Rebuild frontend with production config"
    echo "  - Deploy to nginx"
    echo "  - Restart services"
    echo "  - Verify deployment"
    echo ""
    echo "Use without --dry-run to execute deployment"
}

# Main execution
main() {
    echo "========================================"
    echo "  KDT CRM - Mac to Pi Deployment"
    echo "  $(date)"
    echo "========================================"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        dry_run
        exit 0
    fi
    
    if [[ "$ROLLBACK" == "true" ]]; then
        log INFO "🔄 Triggering rollback on Pi"
        trigger_pi_deployment
        exit $?
    fi
    
    # Execute deployment workflow
    if validate_local_environment && \
       prepare_and_push_code && \
       trigger_pi_deployment && \
       verify_deployment_success; then
        
        echo ""
        echo -e "${GREEN}🎉 Deployment pipeline completed successfully!${NC}"
        echo "   Frontend: https://ts.crtvmkmn.space"
        echo "   API: https://ts.crtvmkmn.space/api/v1"
        echo ""
        echo "Next steps:"
        echo "   - Test the application in your browser"
        echo "   - Check mobile responsiveness"
        echo "   - Verify all features are working"
        exit 0
    else
        echo ""
        echo -e "${RED}💥 Deployment pipeline failed!${NC}"
        echo "   Use --rollback to restore previous version"
        echo "   Use --verbose for detailed output"
        exit 1
    fi
}

# Execute main function
main "$@"
