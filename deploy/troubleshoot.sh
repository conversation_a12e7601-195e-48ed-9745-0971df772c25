#!/bin/bash

# KDT CRM - Deployment Troubleshooting Script
# Diagnoses common deployment issues and provides solutions
# Usage: ./troubleshoot.sh [--fix] [--logs] [--state]
# Author: KDT Development Team

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PI_HOST="<EMAIL>"
FRONTEND_URL="https://ts.crtvmkmn.space"
API_URL="https://ts.crtvmkmn.space/api/v1"

# Flags
AUTO_FIX=false
SHOW_LOGS=false
SHOW_STATE=false

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --fix)
            AUTO_FIX=true
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --state)
            SHOW_STATE=true
            shift
            ;;
        --help|-h)
            echo "KDT CRM Deployment Troubleshooting Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --fix      Attempt automatic fixes"
            echo "  --logs     Show recent logs"
            echo "  --state    Show deployment state"
            echo "  --help     Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[✓]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[!]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[✗]${NC} $message"
            ;;
        FIX)
            echo -e "${PURPLE}[FIX]${NC} $message"
            ;;
    esac
}

check_connectivity() {
    log INFO "Checking connectivity..."
    
    # Test Pi connectivity
    if ssh -o ConnectTimeout=5 "$PI_HOST" "echo 'Pi connection test'" &>/dev/null; then
        log SUCCESS "Pi SSH connection working"
    else
        log ERROR "Cannot connect to Pi via SSH"
        echo "  Solutions:"
        echo "  - Check Pi is powered on and connected to network"
        echo "  - Verify SSH keys: ssh-copy-id $PI_HOST"
        echo "  - Test manual connection: ssh $PI_HOST"
        return 1
    fi
    
    # Test frontend
    local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$FRONTEND_URL" || echo "000")
    if [[ "$frontend_status" == "200" ]]; then
        log SUCCESS "Frontend accessible"
    else
        log ERROR "Frontend not accessible (HTTP $frontend_status)"
        echo "  Solutions:"
        echo "  - Check Cloudflare tunnel is running"
        echo "  - Verify nginx is running: systemctl status nginx"
        echo "  - Check nginx configuration: nginx -t"
        return 1
    fi
    
    # Test API
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$API_URL/health" || echo "000")
    if [[ "$api_status" == "200" ]]; then
        log SUCCESS "API accessible"
    else
        log ERROR "API not accessible (HTTP $api_status)"
        echo "  Solutions:"
        echo "  - Check PHP-FPM is running: systemctl status php8.2-fpm"
        echo "  - Check Laravel logs: tail -f storage/logs/laravel.log"
        echo "  - Verify database connection"
        return 1
    fi
    
    return 0
}

check_pi_services() {
    log INFO "Checking Pi services..."
    
    # Check services via SSH
    local services_output=$(ssh "$PI_HOST" "
        echo 'Nginx:' \$(systemctl is-active nginx 2>/dev/null || echo 'inactive')
        echo 'PHP-FPM:' \$(systemctl is-active php8.2-fpm 2>/dev/null || echo 'inactive')
        echo 'Nginx config:' \$(sudo nginx -t 2>&1 | grep -o 'successful\|failed' || echo 'unknown')
    " 2>/dev/null || echo "SSH connection failed")
    
    echo "$services_output"
    
    if echo "$services_output" | grep -q "inactive\|failed"; then
        log ERROR "Some services are not running properly"
        
        if [[ "$AUTO_FIX" == "true" ]]; then
            log FIX "Attempting to restart services..."
            ssh "$PI_HOST" "
                sudo systemctl restart nginx
                sudo systemctl restart php8.2-fpm
                sudo systemctl reload nginx
            " && log SUCCESS "Services restarted" || log ERROR "Failed to restart services"
        else
            echo "  Use --fix to attempt automatic restart"
        fi
        return 1
    else
        log SUCCESS "All services running"
        return 0
    fi
}

check_build_files() {
    log INFO "Checking build files..."
    
    # Check if build files exist on Pi
    local build_check=$(ssh "$PI_HOST" "
        if [[ -f /home/<USER>/Apps/ts-crm/frontend/dist/index.html ]]; then
            echo 'index.html: exists'
        else
            echo 'index.html: missing'
        fi
        
        css_count=\$(find /home/<USER>/Apps/ts-crm/frontend/dist/assets -name '*.css' 2>/dev/null | wc -l)
        js_count=\$(find /home/<USER>/Apps/ts-crm/frontend/dist/assets -name '*.js' 2>/dev/null | wc -l)
        
        echo \"CSS files: \$css_count\"
        echo \"JS files: \$js_count\"
        
        if [[ \$css_count -gt 0 ]] && [[ \$js_count -gt 0 ]]; then
            echo 'Build files: complete'
        else
            echo 'Build files: incomplete'
        fi
    " 2>/dev/null || echo "SSH connection failed")
    
    echo "$build_check"
    
    if echo "$build_check" | grep -q "missing\|incomplete"; then
        log ERROR "Build files are missing or incomplete"
        
        if [[ "$AUTO_FIX" == "true" ]]; then
            log FIX "Attempting to rebuild and redeploy..."
            ssh "$PI_HOST" "cd /home/<USER>/Apps/ts-crm && ./deploy/pi-native/scripts/deploy.sh" && \
                log SUCCESS "Rebuild completed" || log ERROR "Rebuild failed"
        else
            echo "  Use --fix to attempt automatic rebuild"
            echo "  Or run: ./deploy/mac-dev/deploy-to-pi.sh"
        fi
        return 1
    else
        log SUCCESS "Build files are complete"
        return 0
    fi
}

check_git_sync() {
    log INFO "Checking git synchronization..."
    
    # Get local commit
    local local_commit=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    
    # Get Pi commit
    local pi_commit=$(ssh "$PI_HOST" "cd /home/<USER>/Apps/ts-crm && git rev-parse HEAD" 2>/dev/null || echo "unknown")
    
    echo "Local commit: $local_commit"
    echo "Pi commit:    $pi_commit"
    
    if [[ "$local_commit" != "$pi_commit" ]]; then
        log WARNING "Git commits are out of sync"
        
        if [[ "$AUTO_FIX" == "true" ]]; then
            log FIX "Attempting to sync..."
            ./deploy/mac-dev/deploy-to-pi.sh && \
                log SUCCESS "Sync completed" || log ERROR "Sync failed"
        else
            echo "  Use --fix to attempt automatic sync"
            echo "  Or run: ./deploy/mac-dev/deploy-to-pi.sh"
        fi
        return 1
    else
        log SUCCESS "Git repositories are synchronized"
        return 0
    fi
}

show_logs() {
    log INFO "Showing recent logs..."
    
    echo ""
    echo "=== Nginx Error Log ==="
    ssh "$PI_HOST" "sudo tail -20 /var/log/nginx/error.log 2>/dev/null || echo 'No nginx error log found'"
    
    echo ""
    echo "=== PHP-FPM Log ==="
    ssh "$PI_HOST" "sudo journalctl -u php8.2-fpm --no-pager -n 10 2>/dev/null || echo 'No PHP-FPM logs found'"
    
    echo ""
    echo "=== Recent Deployment Logs ==="
    ssh "$PI_HOST" "ls -t /tmp/kdt-deploy-*.log 2>/dev/null | head -1 | xargs cat 2>/dev/null || echo 'No deployment logs found'"
}

show_state() {
    log INFO "Showing deployment state..."
    
    echo ""
    echo "=== Current State ==="
    ssh "$PI_HOST" "
        echo 'Git commit:' \$(cd /home/<USER>/Apps/ts-crm && git rev-parse HEAD 2>/dev/null || echo 'unknown')
        echo 'Git branch:' \$(cd /home/<USER>/Apps/ts-crm && git branch --show-current 2>/dev/null || echo 'unknown')
        echo 'Last deployment:' \$(ls -t /tmp/kdt-deploy-*.log 2>/dev/null | head -1 | xargs stat -c '%y' 2>/dev/null || echo 'unknown')
        
        if [[ -f /tmp/full_state_after.json ]]; then
            echo 'Build assets:'
            jq -r '.build.assets[]? | \"  - \\(.name) (\\(.size) bytes)\"' /tmp/full_state_after.json 2>/dev/null || echo '  No build state found'
        fi
    " 2>/dev/null || echo "Could not retrieve state information"
}

main() {
    echo "========================================"
    echo "  KDT CRM - Deployment Troubleshooting"
    echo "  $(date)"
    echo "========================================"
    
    if [[ "$SHOW_LOGS" == "true" ]]; then
        show_logs
        exit 0
    fi
    
    if [[ "$SHOW_STATE" == "true" ]]; then
        show_state
        exit 0
    fi
    
    local total_issues=0
    
    # Run diagnostic checks
    echo "Running diagnostic checks..."
    echo ""
    
    check_connectivity || ((total_issues++))
    echo ""
    
    check_pi_services || ((total_issues++))
    echo ""
    
    check_build_files || ((total_issues++))
    echo ""
    
    check_git_sync || ((total_issues++))
    echo ""
    
    # Summary
    echo "========================================"
    echo "  Troubleshooting Summary"
    echo "========================================"
    
    if [[ $total_issues -eq 0 ]]; then
        log SUCCESS "No issues detected - deployment appears healthy"
        echo ""
        echo "If you're still experiencing problems:"
        echo "  - Run: ./deploy/verify-deployment.sh --all"
        echo "  - Check browser console for JavaScript errors"
        echo "  - Clear browser cache and try again"
    else
        log ERROR "$total_issues issues detected"
        echo ""
        echo "Recommended actions:"
        echo "  1. Run with --fix to attempt automatic repairs"
        echo "  2. Check logs with --logs flag"
        echo "  3. Review deployment state with --state flag"
        echo "  4. Consider manual rollback: ./deploy/mac-dev/deploy-to-pi.sh --rollback"
    fi
    
    exit $total_issues
}

main "$@"
