; PHP Production Configuration for Raspberry Pi
; Optimized for limited resources

; Memory settings
memory_limit = 256M
max_execution_time = 300
max_input_time = 300

; File upload settings
upload_max_filesize = 50M
post_max_size = 50M
max_file_uploads = 20

; Error reporting (production)
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/www/html/storage/logs/php_errors.log

; Session settings
session.gc_maxlifetime = 7200
session.gc_probability = 1
session.gc_divisor = 100

; OPcache settings for performance
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 64
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1

; Security settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Date settings
date.timezone = UTC

; Realpath cache (performance)
realpath_cache_size = 4096K
realpath_cache_ttl = 600
