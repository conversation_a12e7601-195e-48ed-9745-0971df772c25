#!/bin/bash

# KDT CRM - Deployment Verification Script
# Comprehensive testing of deployment success
# Usage: ./verify-deployment.sh [--mobile] [--api] [--all]
# Author: KDT Development Team

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_URL="https://ts.crtvmkmn.space"
API_URL="https://ts.crtvmkmn.space/api/v1"
TIMEOUT=10

# Test flags
TEST_BASIC=true
TEST_API=false
TEST_MOBILE=false
TEST_ALL=false

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --api)
            TEST_API=true
            shift
            ;;
        --mobile)
            TEST_MOBILE=true
            shift
            ;;
        --all)
            TEST_ALL=true
            TEST_API=true
            TEST_MOBILE=true
            shift
            ;;
        --help|-h)
            echo "KDT CRM Deployment Verification Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --api      Test API endpoints"
            echo "  --mobile   Test mobile responsiveness"
            echo "  --all      Run all tests"
            echo "  --help     Show this help"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[✓]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[!]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[✗]${NC} $message"
            ;;
        TEST)
            echo -e "${PURPLE}[TEST]${NC} $message"
            ;;
    esac
}

# Test functions
test_basic_connectivity() {
    log TEST "Testing basic connectivity..."
    
    local errors=0
    
    # Test frontend
    log INFO "Testing frontend accessibility..."
    local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$FRONTEND_URL" || echo "000")
    
    if [[ "$frontend_status" == "200" ]]; then
        log SUCCESS "Frontend accessible (HTTP 200)"
    else
        log ERROR "Frontend not accessible (HTTP $frontend_status)"
        ((errors++))
    fi
    
    # Test API health
    log INFO "Testing API health endpoint..."
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$API_URL/health" || echo "000")
    
    if [[ "$api_status" == "200" ]]; then
        log SUCCESS "API health endpoint responding (HTTP 200)"
    else
        log ERROR "API health endpoint not responding (HTTP $api_status)"
        ((errors++))
    fi
    
    # Test HTTPS redirect
    log INFO "Testing HTTPS redirect..."
    local http_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "http://ts.crtvmkmn.space" || echo "000")
    
    if [[ "$http_status" =~ ^30[1-8]$ ]]; then
        log SUCCESS "HTTP to HTTPS redirect working (HTTP $http_status)"
    else
        log WARNING "HTTP redirect may not be configured (HTTP $http_status)"
    fi
    
    return $errors
}

test_api_endpoints() {
    log TEST "Testing API endpoints..."
    
    local errors=0
    
    # Test various API endpoints
    local endpoints=(
        "/health:Health check"
        "/clients:Clients endpoint"
        "/leads:Leads endpoint"
        "/deals:Deals endpoint"
        "/dashboard/stats:Dashboard stats"
    )
    
    for endpoint_info in "${endpoints[@]}"; do
        IFS=':' read -r endpoint description <<< "$endpoint_info"
        
        log INFO "Testing $description..."
        local status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$API_URL$endpoint" || echo "000")
        
        case $status in
            200)
                log SUCCESS "$description responding (HTTP 200)"
                ;;
            401|403)
                log SUCCESS "$description protected (HTTP $status) - expected for auth endpoints"
                ;;
            404)
                log ERROR "$description not found (HTTP 404)"
                ((errors++))
                ;;
            500|502|503)
                log ERROR "$description server error (HTTP $status)"
                ((errors++))
                ;;
            000)
                log ERROR "$description connection failed"
                ((errors++))
                ;;
            *)
                log WARNING "$description unexpected response (HTTP $status)"
                ;;
        esac
    done
    
    return $errors
}

test_mobile_responsiveness() {
    log TEST "Testing mobile responsiveness..."
    
    local errors=0
    
    # Download frontend HTML
    log INFO "Downloading frontend HTML..."
    local html_content=$(curl -s --max-time $TIMEOUT "$FRONTEND_URL" || echo "")
    
    if [[ -z "$html_content" ]]; then
        log ERROR "Could not download frontend HTML"
        return 1
    fi
    
    # Check for viewport meta tag
    if echo "$html_content" | grep -q 'name="viewport"'; then
        log SUCCESS "Viewport meta tag found"
    else
        log ERROR "Viewport meta tag missing"
        ((errors++))
    fi
    
    # Check for responsive CSS classes (in referenced CSS files)
    local css_files=$(echo "$html_content" | grep -o 'href="[^"]*\.css[^"]*"' | sed 's/href="//;s/"//' || echo "")
    
    if [[ -n "$css_files" ]]; then
        log INFO "Found CSS files, checking for responsive classes..."
        
        local responsive_found=false
        while IFS= read -r css_file; do
            if [[ "$css_file" =~ ^/ ]]; then
                css_file="$FRONTEND_URL$css_file"
            elif [[ ! "$css_file" =~ ^https?:// ]]; then
                css_file="$FRONTEND_URL/$css_file"
            fi
            
            local css_content=$(curl -s --max-time $TIMEOUT "$css_file" || echo "")
            
            # Check for mobile-responsive patterns
            if echo "$css_content" | grep -q -E "(sm:|md:|lg:|xl:|@media|flex-col|w-full)"; then
                log SUCCESS "Responsive CSS classes found in $(basename "$css_file")"
                responsive_found=true
                break
            fi
        done <<< "$css_files"
        
        if [[ "$responsive_found" == "false" ]]; then
            log WARNING "No responsive CSS classes detected"
        fi
    else
        log WARNING "No CSS files found in HTML"
    fi
    
    # Check for mobile-friendly features
    if echo "$html_content" | grep -q -i "touch\|mobile\|responsive"; then
        log SUCCESS "Mobile-friendly features detected"
    else
        log WARNING "No explicit mobile features detected"
    fi
    
    return $errors
}

test_performance() {
    log TEST "Testing performance metrics..."
    
    local errors=0
    
    # Test frontend load time
    log INFO "Testing frontend load time..."
    local start_time=$(date +%s%N)
    local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$FRONTEND_URL" || echo "000")
    local end_time=$(date +%s%N)
    local load_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    if [[ "$frontend_status" == "200" ]]; then
        if [[ $load_time -lt 3000 ]]; then
            log SUCCESS "Frontend load time: ${load_time}ms (good)"
        elif [[ $load_time -lt 5000 ]]; then
            log WARNING "Frontend load time: ${load_time}ms (acceptable)"
        else
            log ERROR "Frontend load time: ${load_time}ms (slow)"
            ((errors++))
        fi
    else
        log ERROR "Frontend not accessible for performance test"
        ((errors++))
    fi
    
    # Test API response time
    log INFO "Testing API response time..."
    start_time=$(date +%s%N)
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$API_URL/health" || echo "000")
    end_time=$(date +%s%N)
    local api_time=$(( (end_time - start_time) / 1000000 ))
    
    if [[ "$api_status" == "200" ]]; then
        if [[ $api_time -lt 1000 ]]; then
            log SUCCESS "API response time: ${api_time}ms (excellent)"
        elif [[ $api_time -lt 2000 ]]; then
            log SUCCESS "API response time: ${api_time}ms (good)"
        else
            log WARNING "API response time: ${api_time}ms (slow)"
        fi
    else
        log ERROR "API not accessible for performance test"
        ((errors++))
    fi
    
    return $errors
}

generate_report() {
    local total_errors=$1
    
    echo ""
    echo "========================================"
    echo "  Deployment Verification Report"
    echo "========================================"
    echo "Date: $(date)"
    echo "Frontend URL: $FRONTEND_URL"
    echo "API URL: $API_URL"
    echo ""
    
    if [[ $total_errors -eq 0 ]]; then
        echo -e "${GREEN}🎉 All tests passed successfully!${NC}"
        echo ""
        echo "✅ Deployment is working correctly"
        echo "✅ All endpoints are accessible"
        echo "✅ Mobile responsiveness is active"
        echo "✅ Performance is within acceptable limits"
    elif [[ $total_errors -le 2 ]]; then
        echo -e "${YELLOW}⚠️  Deployment mostly successful with minor issues${NC}"
        echo ""
        echo "✅ Core functionality is working"
        echo "⚠️  $total_errors minor issues detected"
        echo "💡 Review warnings above for optimization opportunities"
    else
        echo -e "${RED}❌ Deployment has significant issues${NC}"
        echo ""
        echo "❌ $total_errors errors detected"
        echo "🔧 Review errors above and consider rollback"
        echo "📋 Check deployment logs for more details"
    fi
    
    echo ""
    echo "Next steps:"
    echo "  1. Test the application manually in your browser"
    echo "  2. Verify login functionality"
    echo "  3. Check mobile responsiveness on actual devices"
    echo "  4. Monitor application logs for any issues"
}

main() {
    echo "========================================"
    echo "  KDT CRM - Deployment Verification"
    echo "  $(date)"
    echo "========================================"
    
    local total_errors=0
    
    # Run basic tests
    if [[ "$TEST_BASIC" == "true" ]]; then
        test_basic_connectivity
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    # Run API tests
    if [[ "$TEST_API" == "true" ]] || [[ "$TEST_ALL" == "true" ]]; then
        test_api_endpoints
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    # Run mobile tests
    if [[ "$TEST_MOBILE" == "true" ]] || [[ "$TEST_ALL" == "true" ]]; then
        test_mobile_responsiveness
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    # Run performance tests
    if [[ "$TEST_ALL" == "true" ]]; then
        test_performance
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    # Generate report
    generate_report $total_errors
    
    # Exit with appropriate code
    if [[ $total_errors -eq 0 ]]; then
        exit 0
    elif [[ $total_errors -le 2 ]]; then
        exit 1
    else
        exit 2
    fi
}

main "$@"
