# KDT Production Readiness - Quick Reference

## 🚀 Quick Commands

### From Mac (Remote Verification)
```bash
# Basic check
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@your-pi-ip

# Detailed assessment
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@your-pi-ip --detailed

# Skip resource checks (faster)
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@your-pi-ip --skip-resources
```

### On Pi (Direct Verification)
```bash
# Basic check
./deploy/pi-native/scripts/verify-production-readiness.sh

# Detailed assessment
./deploy/pi-native/scripts/verify-production-readiness.sh --detailed

# Skip backup checks
./deploy/pi-native/scripts/verify-production-readiness.sh --skip-backups
```

## 📋 What Gets Checked

| Category | Checks |
|----------|--------|
| **Environment** | Pi detection, project directory, permissions |
| **Resources** | Disk space, memory, CPU load, temperature |
| **Services** | nginx, php8.2-fpm, postgresql, redis-server |
| **Database** | PostgreSQL connectivity, production DB, Redis |
| **Application** | Frontend/backend files, HTTPS access, API |
| **Backups** | Recent backups, backup space availability |
| **Configuration** | nginx config, environment files, permissions |

## 🎯 Status Indicators

- 🟢 **[PASS]** - Ready for deployment
- 🟡 **[WARN]** - Review recommended
- 🔴 **[FAIL]** - Must fix before deployment
- 🔵 **[INFO]** - Informational

## 🔧 Common Fixes

### Service Not Running
```bash
sudo systemctl start nginx
sudo systemctl start php8.2-fpm
sudo systemctl start postgresql
sudo systemctl start redis-server
```

### High Disk Usage
```bash
sudo journalctl --vacuum-time=7d
sudo apt clean
sudo find /var/log -name "*.log" -mtime +7 -delete
```

### Database Issues
```bash
sudo systemctl restart postgresql
sudo -u postgres psql -c '\l'
```

## 📊 Thresholds

| Metric | Warning | Critical |
|--------|---------|----------|
| Disk Usage | 75% | 85% |
| Memory Usage | 80% | 90% |
| CPU Temperature | 70°C | 80°C |
| Load Average | 2.0 | - |
| Free Space | - | < 2GB |

## 🔄 Pre-Deployment Workflow

1. **Verify Production Readiness**
   ```bash
   ./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@your-pi --detailed
   ```

2. **Create Backups** (if ready)
   ```bash
   ./deploy/pi-native/scripts/backup-dev-data.sh
   ./deploy/pi-native/scripts/backup-settings.sh
   ```

3. **Test Deployment**
   ```bash
   ./deploy/pi-native/scripts/deploy-with-settings.sh --dry-run
   ```

4. **Execute Deployment**
   ```bash
   ./deploy/pi-native/scripts/deploy-with-settings.sh
   ```

## 📁 Output Files

- **Logs:** `/tmp/kdt-production-readiness-YYYYMMDD-HHMMSS.log`
- **Reports:** `/tmp/kdt-production-readiness-report-YYYYMMDD-HHMMSS.md`
- **Local Copies:** `/tmp/kdt-remote-verification/`

## 🆘 Emergency Commands

### Check System Status
```bash
# System overview
htop
df -h
free -h

# Service status
sudo systemctl status nginx php8.2-fpm postgresql redis-server

# Application status
curl -I https://ts.crtvmkmn.space/
curl -I https://ts.crtvmkmn.space/api/v1/health
```

### Restart All Services
```bash
sudo systemctl restart nginx php8.2-fpm postgresql redis-server
```

### Check Logs
```bash
sudo journalctl -u nginx -f
sudo journalctl -u php8.2-fpm -f
sudo tail -f /var/log/nginx/error.log
```

## 📞 Support

- **Full Documentation:** `deploy/pi-native/PRODUCTION_READINESS_GUIDE.md`
- **Log Location:** `/tmp/kdt-production-readiness-*.log`
- **Script Location:** `deploy/pi-native/scripts/verify-production-readiness.sh`
