#!/bin/bash

# KDT Data Synchronization Verification Script
# Verifies that data synchronization between development and production is working correctly
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/tmp/kdt-data-sync-verification.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
CHECK_PRODUCTION=false
DETAILED_REPORT=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --check-production)
            CHECK_PRODUCTION=true
            shift
            ;;
        --detailed)
            DETAILED_REPORT=true
            shift
            ;;
        --help|-h)
            echo "KDT Data Synchronization Verification Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --check-production     Also verify production environment"
            echo "  --detailed             Show detailed statistics"
            echo "  --help                 Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check development environment
check_development_environment() {
    log INFO "🔍 Checking development environment"
    
    # Check Docker containers
    if ! docker ps | grep -q kdt-postgres; then
        log ERROR "Development PostgreSQL container not running"
        return 1
    fi
    
    if ! docker ps | grep -q kdt-backend; then
        log ERROR "Development backend container not running"
        return 1
    fi
    
    # Check database connectivity
    if ! docker exec kdt-postgres psql -U kdt -d kdt -c '\l' &>/dev/null; then
        log ERROR "Cannot connect to development database"
        return 1
    fi
    
    log SUCCESS "Development environment is ready"
}

# Get table statistics
get_table_stats() {
    local db_name=$1
    local container_name=$2
    local user=$3
    
    local tables=(
        "leads"
        "deals"
        "clients"
        "products"
        "quotations"
        "invoices"
        "transactions"
        "campaigns"
        "activity_logs"
        "users"
        "system_settings"
    )
    
    echo "📊 Database Statistics for $db_name:"
    echo "----------------------------------------"
    
    for table in "${tables[@]}"; do
        local count
        if [[ "$container_name" == "production" ]]; then
            count=$(sudo -u postgres psql $db_name -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ' || echo "N/A")
        else
            count=$(docker exec $container_name psql -U $user -d $db_name -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ' || echo "N/A")
        fi
        printf "  %-20s: %s\n" "$table" "$count"
    done
    echo ""
}

# Verify development data
verify_development_data() {
    log INFO "📊 Verifying development data"
    
    if [[ "$DETAILED_REPORT" == "true" ]]; then
        get_table_stats "kdt" "kdt-postgres" "kdt"
    fi
    
    # Check for essential data
    local leads_count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM leads;" 2>/dev/null | tr -d ' ' || echo "0")
    local deals_count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM deals;" 2>/dev/null | tr -d ' ' || echo "0")
    local clients_count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM clients;" 2>/dev/null | tr -d ' ' || echo "0")
    local products_count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM products;" 2>/dev/null | tr -d ' ' || echo "0")
    
    log INFO "Development data summary:"
    log INFO "  Leads: $leads_count"
    log INFO "  Deals: $deals_count"
    log INFO "  Clients: $clients_count"
    log INFO "  Products: $products_count"
    
    if [[ "$leads_count" -gt 0 ]] || [[ "$deals_count" -gt 0 ]] || [[ "$clients_count" -gt 0 ]]; then
        log SUCCESS "Development environment contains business data"
    else
        log WARNING "Development environment has no business data to synchronize"
    fi
}

# Verify production environment (if requested)
verify_production_environment() {
    if [[ "$CHECK_PRODUCTION" != "true" ]]; then
        log INFO "⏭️  Skipping production verification (use --check-production to include)"
        return 0
    fi
    
    log INFO "🔍 Checking production environment"
    
    # Check if we can connect to production database
    if ! sudo -u postgres psql -l | grep -q kdt_production; then
        log WARNING "Production database 'kdt_production' not found"
        return 0
    fi
    
    if [[ "$DETAILED_REPORT" == "true" ]]; then
        get_table_stats "kdt_production" "production" "postgres"
    fi
    
    # Check for essential data
    local leads_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM leads;" 2>/dev/null | tr -d ' ' || echo "0")
    local deals_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM deals;" 2>/dev/null | tr -d ' ' || echo "0")
    local clients_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM clients;" 2>/dev/null | tr -d ' ' || echo "0")
    local products_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM products;" 2>/dev/null | tr -d ' ' || echo "0")
    
    log INFO "Production data summary:"
    log INFO "  Leads: $leads_count"
    log INFO "  Deals: $deals_count"
    log INFO "  Clients: $clients_count"
    log INFO "  Products: $products_count"
    
    log SUCCESS "Production environment verified"
}

# Check backup files
check_backup_files() {
    log INFO "📁 Checking backup files"
    
    # Check for development data backups
    local dev_backups=($(find /tmp -name "kdt-dev-data-backup-*" -type d 2>/dev/null | sort -r))
    if [[ ${#dev_backups[@]} -gt 0 ]]; then
        log SUCCESS "Found ${#dev_backups[@]} development data backup(s)"
        if [[ "$DETAILED_REPORT" == "true" ]]; then
            for backup in "${dev_backups[@]:0:3}"; do
                local backup_date=$(basename "$backup" | sed 's/kdt-dev-data-backup-//')
                log INFO "  $backup_date"
            done
        fi
    else
        log WARNING "No development data backups found"
    fi
    
    # Check for settings backups
    local settings_backups=($(find /tmp -name "kdt-settings-backup-*" -type d 2>/dev/null | sort -r))
    if [[ ${#settings_backups[@]} -gt 0 ]]; then
        log SUCCESS "Found ${#settings_backups[@]} settings backup(s)"
    else
        log WARNING "No settings backups found"
    fi
    
    # Check for production backups
    local prod_backups=($(find /tmp -name "kdt-production-backup-*" -type f 2>/dev/null | sort -r))
    if [[ ${#prod_backups[@]} -gt 0 ]]; then
        log SUCCESS "Found ${#prod_backups[@]} production backup(s)"
    else
        log WARNING "No production backups found"
    fi
}

# Verify deployment scripts
verify_deployment_scripts() {
    log INFO "🔧 Verifying deployment scripts"
    
    local scripts=(
        "backup-dev-data.sh"
        "backup-settings.sh"
        "deploy-with-settings.sh"
        "deploy.sh"
    )
    
    for script in "${scripts[@]}"; do
        local script_path="$SCRIPT_DIR/$script"
        if [[ -f "$script_path" ]] && [[ -x "$script_path" ]]; then
            log SUCCESS "  $script: Available and executable"
        elif [[ -f "$script_path" ]]; then
            log WARNING "  $script: Available but not executable"
        else
            log ERROR "  $script: Not found"
        fi
    done
}

# Generate recommendations
generate_recommendations() {
    log INFO "💡 Generating recommendations"
    
    echo ""
    echo -e "${CYAN}📋 Data Synchronization Readiness Report${NC}"
    echo "========================================"
    
    # Check if ready for data sync
    local dev_data_ready=false
    local backups_available=false
    local scripts_ready=false
    
    # Check development data
    local leads_count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM leads;" 2>/dev/null | tr -d ' ' || echo "0")
    if [[ "$leads_count" -gt 0 ]]; then
        dev_data_ready=true
    fi
    
    # Check backups
    if find /tmp -name "kdt-dev-data-backup-*" -type d 2>/dev/null | grep -q .; then
        backups_available=true
    fi
    
    # Check scripts
    if [[ -x "$SCRIPT_DIR/deploy-with-settings.sh" ]]; then
        scripts_ready=true
    fi
    
    echo -e "${BLUE}Status:${NC}"
    echo "  Development Data: $([ "$dev_data_ready" == "true" ] && echo -e "${GREEN}Ready${NC}" || echo -e "${YELLOW}No data${NC}")"
    echo "  Backup Files: $([ "$backups_available" == "true" ] && echo -e "${GREEN}Available${NC}" || echo -e "${YELLOW}None found${NC}")"
    echo "  Deployment Scripts: $([ "$scripts_ready" == "true" ] && echo -e "${GREEN}Ready${NC}" || echo -e "${RED}Not ready${NC}")"
    
    echo ""
    echo -e "${YELLOW}Recommendations:${NC}"
    
    if [[ "$dev_data_ready" != "true" ]]; then
        echo "  1. Add some test data to development environment"
    fi
    
    if [[ "$backups_available" != "true" ]]; then
        echo "  2. Create development data backup:"
        echo "     ./deploy/pi-native/scripts/backup-dev-data.sh"
    fi
    
    if [[ "$scripts_ready" == "true" ]] && [[ "$dev_data_ready" == "true" ]]; then
        echo "  ✅ Ready for data synchronization!"
        echo ""
        echo -e "${GREEN}Next Steps:${NC}"
        echo "  1. Create fresh backup: ./deploy/pi-native/scripts/backup-dev-data.sh"
        echo "  2. Test deployment: ./deploy/pi-native/scripts/deploy-with-settings.sh --dry-run"
        echo "  3. Deploy to production: ./deploy/pi-native/scripts/deploy-with-settings.sh"
    fi
}

# Main verification function
main() {
    echo "========================================"
    echo "  KDT Data Synchronization Verification"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Starting data synchronization verification"
    log INFO "Log file: $LOG_FILE"
    
    # Execute verification steps
    check_development_environment
    verify_development_data
    verify_production_environment
    check_backup_files
    verify_deployment_scripts
    generate_recommendations
    
    log SUCCESS "🎉 Verification completed!"
    echo ""
    echo -e "${PURPLE}📋 Log File:${NC} $LOG_FILE"
}

# Execute main function
main "$@"
