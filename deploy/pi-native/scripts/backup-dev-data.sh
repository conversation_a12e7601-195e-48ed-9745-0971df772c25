#!/bin/bash

# KDT Development Data Backup Script
# Creates comprehensive backup of development business data for production synchronization
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
BACKUP_DIR="/tmp/kdt-dev-data-backup-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="/tmp/kdt-dev-data-backup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
INCLUDE_SETTINGS=false
INCLUDE_USERS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --include-settings)
            INCLUDE_SETTINGS=true
            shift
            ;;
        --include-users)
            INCLUDE_USERS=true
            shift
            ;;
        --help|-h)
            echo "KDT Development Data Backup Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --include-settings     Include system settings in backup"
            echo "  --include-users        Include user accounts in backup"
            echo "  --help                 Show this help message"
            echo ""
            echo "By default, only business data is backed up (leads, deals, clients, etc.)"
            echo "Sensitive settings and user passwords are excluded for security."
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check if Docker is running
check_docker() {
    if ! docker ps &>/dev/null; then
        log ERROR "Docker is not running or accessible"
        exit 1
    fi
    
    if ! docker exec kdt-postgres psql -U kdt -d kdt -c '\l' &>/dev/null; then
        log ERROR "KDT database container is not running or accessible"
        exit 1
    fi
    
    log SUCCESS "Docker environment is ready"
}

# Create comprehensive business data backup
backup_business_data() {
    log INFO "📊 Creating comprehensive business data backup"

    mkdir -p "$BACKUP_DIR"
    chmod 700 "$BACKUP_DIR"
    
    # Define business data tables
    local business_tables=(
        "leads"
        "deals" 
        "clients"
        "products"
        "quotations"
        "invoices"
        "transactions"
        "campaigns"
        "activity_logs"
    )
    
    # Create main business data backup
    local backup_file="$BACKUP_DIR/business_data.sql"
    
    log INFO "Backing up business data tables..."
    
    # Build pg_dump command with table inclusions
    local dump_cmd="pg_dump -U kdt kdt --data-only --inserts"
    for table in "${business_tables[@]}"; do
        dump_cmd="$dump_cmd --table=$table"
    done
    
    if docker exec kdt-postgres $dump_cmd > "$backup_file" 2>/dev/null; then
        chmod 600 "$backup_file"
        local file_size=$(du -h "$backup_file" | cut -f1)
        log SUCCESS "Business data backed up to: $backup_file ($file_size)"
        
        # Show table statistics
        for table in "${business_tables[@]}"; do
            local count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null | tr -d ' ' || echo "0")
            log INFO "  $table: $count records"
        done
    else
        log ERROR "Failed to backup business data"
        return 1
    fi
}

# Backup system settings (optional)
backup_system_settings() {
    if [[ "$INCLUDE_SETTINGS" != "true" ]]; then
        log INFO "⏭️  Skipping system settings backup (use --include-settings to include)"
        return 0
    fi
    
    log INFO "🔧 Backing up system settings"
    
    local settings_file="$BACKUP_DIR/system_settings.sql"
    
    if docker exec kdt-postgres pg_dump -U kdt kdt --table=system_settings --data-only --inserts > "$settings_file" 2>/dev/null; then
        chmod 600 "$settings_file"
        local count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM system_settings;" 2>/dev/null | tr -d ' ' || echo "0")
        log SUCCESS "System settings backed up: $count settings"
    else
        log WARNING "Failed to backup system settings"
    fi
}

# Backup user accounts (optional)
backup_user_accounts() {
    if [[ "$INCLUDE_USERS" != "true" ]]; then
        log INFO "⏭️  Skipping user accounts backup (use --include-users to include)"
        return 0
    fi
    
    log INFO "👥 Backing up user accounts (excluding passwords)"
    
    local users_file="$BACKUP_DIR/users.sql"
    
    # Backup users table excluding sensitive fields
    if docker exec kdt-postgres psql -U kdt -d kdt -c "
        COPY (
            SELECT id, name, email, role, is_active, two_factor_enabled, 
                   email_verified_at, created_at, updated_at
            FROM users
        ) TO STDOUT WITH CSV HEADER;
    " > "$BACKUP_DIR/users.csv" 2>/dev/null; then
        chmod 600 "$BACKUP_DIR/users.csv"
        local count=$(docker exec kdt-postgres psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM users;" 2>/dev/null | tr -d ' ' || echo "0")
        log SUCCESS "User accounts backed up: $count users (passwords excluded)"
    else
        log WARNING "Failed to backup user accounts"
    fi
}

# Create deployment instructions
create_deployment_instructions() {
    log INFO "📋 Creating deployment instructions"
    
    cat > "$BACKUP_DIR/DEPLOYMENT_INSTRUCTIONS.md" << EOF
# Development Data Deployment Instructions

## Backup Information
- **Created**: $(date)
- **Source**: Development environment
- **Backup Directory**: $BACKUP_DIR

## Contents
- **business_data.sql**: Complete business data (leads, deals, clients, products, etc.)
$([ "$INCLUDE_SETTINGS" == "true" ] && echo "- **system_settings.sql**: System configuration settings")
$([ "$INCLUDE_USERS" == "true" ] && echo "- **users.csv**: User accounts (passwords excluded)")

## Deployment to Production

### Option 1: Use Enhanced Deployment Script (Recommended)
\`\`\`bash
# Copy this backup to production server
scp -r $BACKUP_DIR pi@your-production-server:/tmp/

# On production server, run enhanced deployment
cd /home/<USER>/Apps/ts-crm
./deploy/pi-native/scripts/deploy-with-settings.sh \\
    --dev-db-backup $BACKUP_DIR/business_data.sql
\`\`\`

### Option 2: Manual Data Import
\`\`\`bash
# On production server
sudo -u postgres psql kdt_production < $BACKUP_DIR/business_data.sql
\`\`\`

## Security Notes
- This backup excludes user passwords and sensitive credentials
- Production SMTP settings will be preserved during deployment
- System settings can be included with --include-settings flag
- User accounts can be included with --include-users flag

## Verification
After deployment, verify:
1. Business data is present and accurate
2. Production settings are preserved
3. Email functionality still works
4. User accounts can still log in
EOF

    log SUCCESS "Created deployment instructions"
}

# Main backup function
main() {
    echo "========================================"
    echo "  KDT Development Data Backup"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Starting development data backup process"
    log INFO "Backup directory: $BACKUP_DIR"
    log INFO "Log file: $LOG_FILE"
    
    # Execute backup steps
    check_docker
    backup_business_data
    backup_system_settings
    backup_user_accounts
    create_deployment_instructions
    
    # Create summary
    log SUCCESS "🎉 Development data backup completed successfully!"
    echo ""
    echo -e "${GREEN}📁 Backup Location:${NC} $BACKUP_DIR"
    echo -e "${BLUE}📋 Instructions:${NC} $BACKUP_DIR/DEPLOYMENT_INSTRUCTIONS.md"
    echo -e "${PURPLE}📋 Log File:${NC} $LOG_FILE"
    echo ""
    echo -e "${CYAN}📈 Backup Contents:${NC}"
    echo "  ✅ Business data (leads, deals, clients, products, etc.)"
    echo "  $([ "$INCLUDE_SETTINGS" == "true" ] && echo "✅" || echo "⏭️ ") System settings"
    echo "  $([ "$INCLUDE_USERS" == "true" ] && echo "✅" || echo "⏭️ ") User accounts"
    echo ""
    echo -e "${YELLOW}🚀 Next Steps:${NC}"
    echo "  1. Review backup contents and instructions"
    echo "  2. Transfer backup to production server"
    echo "  3. Run enhanced deployment script with --dev-db-backup flag"
    echo "  4. Verify data synchronization"
    
    # Save backup location for easy access
    echo "$BACKUP_DIR" > "/tmp/kdt-last-dev-data-backup"
    echo "$BACKUP_DIR/business_data.sql" > "/tmp/kdt-last-dev-db-backup"
}

# Execute main function
main "$@"
