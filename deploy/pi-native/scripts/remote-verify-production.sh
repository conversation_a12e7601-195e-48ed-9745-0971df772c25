#!/bin/bash

# KDT Remote Production Readiness Verification Script
# Executes production readiness check on Pi from Mac development environment
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PI_HOST="pi@your-pi-hostname"  # Update with your Pi hostname/IP
PI_PROJECT_PATH="/home/<USER>/Apps/ts-crm"
LOCAL_LOG_DIR="/tmp/kdt-remote-verification"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
DETAILED_REPORT=false
SKIP_BACKUPS=false
SKIP_RESOURCES=false
COPY_LOGS=true
PI_HOSTNAME=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --pi-host)
            PI_HOSTNAME="$2"
            shift 2
            ;;
        --detailed)
            DETAILED_REPORT=true
            shift
            ;;
        --skip-backups)
            SKIP_BACKUPS=true
            shift
            ;;
        --skip-resources)
            SKIP_RESOURCES=true
            shift
            ;;
        --no-copy-logs)
            COPY_LOGS=false
            shift
            ;;
        --help|-h)
            echo "KDT Remote Production Readiness Verification Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --pi-host HOST        Pi hostname or IP (e.g., pi@*************)"
            echo "  --detailed            Generate detailed assessment report"
            echo "  --skip-backups        Skip backup verification checks"
            echo "  --skip-resources      Skip system resource checks"
            echo "  --no-copy-logs        Don't copy logs back to Mac"
            echo "  --help                Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --pi-host pi@*************"
            echo "  $0 --pi-host <EMAIL> --detailed"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Use provided hostname or default
if [[ -n "$PI_HOSTNAME" ]]; then
    PI_HOST="$PI_HOSTNAME"
fi

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# Check SSH connectivity
check_ssh_connectivity() {
    log INFO "🔗 Testing SSH connectivity to $PI_HOST"
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$PI_HOST" "echo 'SSH connection successful'" &>/dev/null; then
        log SUCCESS "SSH connection to Pi established"
    else
        log ERROR "Cannot connect to Pi via SSH"
        log ERROR "Please ensure:"
        log ERROR "  1. Pi is powered on and connected to network"
        log ERROR "  2. SSH is enabled on Pi"
        log ERROR "  3. SSH keys are properly configured"
        log ERROR "  4. Hostname/IP is correct: $PI_HOST"
        exit 1
    fi
}

# Check if production readiness script exists on Pi
check_remote_script() {
    log INFO "📋 Checking if production readiness script exists on Pi"
    
    local script_path="$PI_PROJECT_PATH/deploy/pi-native/scripts/verify-production-readiness.sh"
    
    if ssh "$PI_HOST" "test -f '$script_path'"; then
        log SUCCESS "Production readiness script found on Pi"
        
        # Check if script is executable
        if ssh "$PI_HOST" "test -x '$script_path'"; then
            log SUCCESS "Script is executable"
        else
            log WARNING "Script exists but is not executable - fixing permissions"
            ssh "$PI_HOST" "chmod +x '$script_path'"
        fi
    else
        log ERROR "Production readiness script not found on Pi: $script_path"
        log ERROR "Please ensure the latest deployment scripts are on the Pi"
        exit 1
    fi
}

# Execute production readiness check on Pi
execute_remote_verification() {
    log INFO "🚀 Executing production readiness verification on Pi"
    
    # Build command with flags
    local remote_cmd="cd '$PI_PROJECT_PATH' && ./deploy/pi-native/scripts/verify-production-readiness.sh"
    
    if [[ "$DETAILED_REPORT" == "true" ]]; then
        remote_cmd="$remote_cmd --detailed"
    fi
    
    if [[ "$SKIP_BACKUPS" == "true" ]]; then
        remote_cmd="$remote_cmd --skip-backups"
    fi
    
    if [[ "$SKIP_RESOURCES" == "true" ]]; then
        remote_cmd="$remote_cmd --skip-resources"
    fi
    
    log INFO "Executing: $remote_cmd"
    echo ""
    
    # Execute the command and capture exit code
    local exit_code=0
    ssh "$PI_HOST" "$remote_cmd" || exit_code=$?
    
    echo ""
    
    if [[ $exit_code -eq 0 ]]; then
        log SUCCESS "Production readiness verification completed successfully"
    else
        log WARNING "Production readiness verification completed with issues (exit code: $exit_code)"
    fi
    
    return $exit_code
}

# Copy logs and reports back to Mac
copy_logs_to_mac() {
    if [[ "$COPY_LOGS" != "true" ]]; then
        log INFO "⏭️  Skipping log copy (--no-copy-logs flag)"
        return 0
    fi
    
    log INFO "📁 Copying logs and reports from Pi to Mac"
    
    # Create local log directory
    mkdir -p "$LOCAL_LOG_DIR"
    
    # Copy log files
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local remote_logs=$(ssh "$PI_HOST" "ls -t /tmp/kdt-production-readiness-*.log 2>/dev/null | head -1" || echo "")
    local remote_reports=$(ssh "$PI_HOST" "ls -t /tmp/kdt-production-readiness-report-*.md 2>/dev/null | head -1" || echo "")
    
    if [[ -n "$remote_logs" ]]; then
        local local_log="$LOCAL_LOG_DIR/production-readiness-$timestamp.log"
        scp "$PI_HOST:$remote_logs" "$local_log" &>/dev/null
        log SUCCESS "Log file copied: $local_log"
    else
        log WARNING "No log files found on Pi"
    fi
    
    if [[ -n "$remote_reports" ]] && [[ "$DETAILED_REPORT" == "true" ]]; then
        local local_report="$LOCAL_LOG_DIR/production-readiness-report-$timestamp.md"
        scp "$PI_HOST:$remote_reports" "$local_report" &>/dev/null
        log SUCCESS "Report file copied: $local_report"
        
        # Show report summary
        echo ""
        log INFO "📊 Report Summary:"
        echo "----------------------------------------"
        head -20 "$local_report" | tail -15
        echo "----------------------------------------"
        log INFO "Full report: $local_report"
    fi
}

# Show deployment recommendations
show_deployment_recommendations() {
    echo ""
    echo "========================================"
    echo "  DEPLOYMENT RECOMMENDATIONS"
    echo "========================================"
    
    log INFO "🎯 Based on production readiness assessment:"
    echo ""
    
    # Get the latest verification result from Pi
    local latest_result=$(ssh "$PI_HOST" "ls -t /tmp/kdt-production-readiness-*.log 2>/dev/null | head -1 | xargs tail -10 2>/dev/null | grep -E '(READY|NOT READY)' | tail -1" || echo "")
    
    if echo "$latest_result" | grep -q "READY FOR DEPLOYMENT"; then
        echo -e "${GREEN}✅ Production environment is ready${NC}"
        echo ""
        echo -e "${BLUE}Next steps:${NC}"
        echo "  1. Create development data backup:"
        echo "     ./deploy/pi-native/scripts/backup-dev-data.sh"
        echo ""
        echo "  2. Create settings backup:"
        echo "     ./deploy/pi-native/scripts/backup-settings.sh"
        echo ""
        echo "  3. Test deployment (dry run):"
        echo "     ./deploy/pi-native/scripts/deploy-with-settings.sh --dry-run"
        echo ""
        echo "  4. Execute deployment:"
        echo "     ./deploy/pi-native/scripts/deploy-with-settings.sh"
        
    elif echo "$latest_result" | grep -q "READY WITH WARNINGS"; then
        echo -e "${YELLOW}⚠️  Production environment has warnings${NC}"
        echo ""
        echo -e "${BLUE}Recommended actions:${NC}"
        echo "  1. Review warnings in the log file"
        echo "  2. Address non-critical issues if possible"
        echo "  3. Proceed with deployment if warnings are acceptable"
        echo ""
        echo -e "${BLUE}To proceed with deployment:${NC}"
        echo "     ./deploy/pi-native/scripts/deploy-with-settings.sh"
        
    elif echo "$latest_result" | grep -q "NOT READY"; then
        echo -e "${RED}❌ Production environment has critical issues${NC}"
        echo ""
        echo -e "${BLUE}Required actions:${NC}"
        echo "  1. Review failed checks in the log file"
        echo "  2. Fix all critical issues"
        echo "  3. Re-run production readiness check"
        echo "  4. Only proceed when all critical checks pass"
        echo ""
        echo -e "${BLUE}To fix issues and re-check:${NC}"
        echo "     # Fix issues on Pi, then:"
        echo "     $0 --pi-host $PI_HOST"
        
    else
        echo -e "${YELLOW}⚠️  Could not determine production readiness status${NC}"
        echo ""
        echo -e "${BLUE}Recommended actions:${NC}"
        echo "  1. Review the verification output above"
        echo "  2. Check log files for detailed information"
        echo "  3. Ensure all critical services are running"
    fi
}

# Main function
main() {
    echo "========================================"
    echo "  KDT Remote Production Verification"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Starting remote production readiness verification"
    log INFO "Target Pi: $PI_HOST"
    log INFO "Local log directory: $LOCAL_LOG_DIR"
    
    # Execute verification steps
    check_ssh_connectivity
    check_remote_script
    
    local verification_result=0
    execute_remote_verification || verification_result=$?
    
    copy_logs_to_mac
    show_deployment_recommendations
    
    echo ""
    if [[ $verification_result -eq 0 ]]; then
        log SUCCESS "🎉 Remote production verification completed successfully!"
    else
        log WARNING "⚠️  Remote production verification completed with issues"
    fi
    
    exit $verification_result
}

# Execute main function
main "$@"
