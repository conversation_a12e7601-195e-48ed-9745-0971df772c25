#!/bin/bash

# Security Reminder Script for Production Deployment
# This script displays important security reminders after deployment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Icons
SHIELD="🛡️"
WARNING="⚠️"
KEY="🔑"
LOCK="🔒"
CHECK="✅"
CROSS="❌"

echo ""
echo -e "${RED}${SHIELD}${SHIELD}${SHIELD} CRITICAL SECURITY REMINDER ${SHIELD}${SHIELD}${SHIELD}${NC}"
echo -e "${WHITE}================================================================${NC}"
echo ""
echo -e "${YELLOW}${WARNING} Your application has been deployed, but you MUST complete these security steps:${NC}"
echo ""

echo -e "${CYAN}${LOCK} 1. UPDATE PAYMENT GATEWAY CREDENTIALS${NC}"
echo -e "   ${WHITE}• Log into your application: https://ts.crtvmkmn.space${NC}"
echo -e "   ${WHITE}• Go to Settings > Security${NC}"
echo -e "   ${WHITE}• Generate NEW ToyyibPay API keys from: https://toyyibpay.com${NC}"
echo -e "   ${WHITE}• Replace the example credentials with your real API keys${NC}"
echo -e "   ${RED}• NEVER use the example keys in production!${NC}"
echo ""

echo -e "${CYAN}${KEY} 2. CONFIGURE EMAIL SETTINGS${NC}"
echo -e "   ${WHITE}• In Settings > Security, update SMTP configuration${NC}"
echo -e "   ${WHITE}• Use your actual email service credentials (Gmail, Zoho, etc.)${NC}"
echo -e "   ${WHITE}• Test the email connection to ensure it works${NC}"
echo -e "   ${WHITE}• Remove any default/example passwords${NC}"
echo ""

echo -e "${CYAN}${SHIELD} 3. SECURITY CONFIGURATION${NC}"
echo -e "   ${WHITE}• Ensure Debug Mode is OFF in production${NC}"
echo -e "   ${WHITE}• Enable Two-Factor Authentication system-wide${NC}"
echo -e "   ${WHITE}• Generate strong database passwords${NC}"
echo -e "   ${WHITE}• Review all security settings in the Security tab${NC}"
echo ""

echo -e "${CYAN}${CHECK} 4. SECURITY CHECKLIST${NC}"
echo -e "   ${WHITE}□ ToyyibPay API keys updated with real credentials${NC}"
echo -e "   ${WHITE}□ SMTP password updated with real email service password${NC}"
echo -e "   ${WHITE}□ Debug mode disabled (APP_DEBUG=false)${NC}"
echo -e "   ${WHITE}□ Two-Factor Authentication enabled${NC}"
echo -e "   ${WHITE}□ All connection tests passed${NC}"
echo -e "   ${WHITE}□ Security score above 80/100${NC}"
echo ""

echo -e "${RED}${WARNING} IMPORTANT SECURITY WARNINGS:${NC}"
echo -e "   ${RED}• Default credentials are a major security risk${NC}"
echo -e "   ${RED}• Debug mode exposes sensitive information${NC}"
echo -e "   ${RED}• Weak passwords can be easily compromised${NC}"
echo -e "   ${RED}• Always use HTTPS in production${NC}"
echo ""

echo -e "${GREEN}${SHIELD} QUICK ACCESS LINKS:${NC}"
echo -e "   ${WHITE}• Application: https://ts.crtvmkmn.space${NC}"
echo -e "   ${WHITE}• Security Settings: https://ts.crtvmkmn.space/settings?tab=security${NC}"
echo -e "   ${WHITE}• ToyyibPay Dashboard: https://toyyibpay.com${NC}"
echo ""

echo -e "${PURPLE}${LOCK} NEXT STEPS:${NC}"
echo -e "   ${WHITE}1. Complete the security checklist above${NC}"
echo -e "   ${WHITE}2. Test all functionality with real credentials${NC}"
echo -e "   ${WHITE}3. Monitor security score in Settings > Security${NC}"
echo -e "   ${WHITE}4. Set up regular security audits${NC}"
echo ""

echo -e "${WHITE}================================================================${NC}"
echo -e "${GREEN}${CHECK} Deployment completed successfully!${NC}"
echo -e "${YELLOW}${WARNING} Don't forget to complete the security configuration!${NC}"
echo -e "${WHITE}================================================================${NC}"
echo ""

# Check if we can determine current security status
echo -e "${BLUE}Checking current security status...${NC}"

# Try to check if default credentials are still in use
BACKEND_PATH="/home/<USER>/Apps/ts-crm/backend"
if [[ -f "$BACKEND_PATH/.env" ]]; then
    echo -e "${WHITE}Environment file found. Checking for security issues...${NC}"
    
    # Check debug mode
    if grep -q "APP_DEBUG=true" "$BACKEND_PATH/.env" 2>/dev/null; then
        echo -e "   ${RED}${CROSS} Debug mode is ENABLED - This is a security risk!${NC}"
    else
        echo -e "   ${GREEN}${CHECK} Debug mode appears to be disabled${NC}"
    fi
    
    # Check log level
    if grep -q "LOG_LEVEL=debug" "$BACKEND_PATH/.env" 2>/dev/null; then
        echo -e "   ${RED}${CROSS} Log level is set to DEBUG - Change to 'error' for production${NC}"
    else
        echo -e "   ${GREEN}${CHECK} Log level appears to be production-ready${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}${WARNING} Remember to update credentials through the web interface!${NC}"
else
    echo -e "   ${YELLOW}${WARNING} Could not check environment file${NC}"
fi

echo ""
echo -e "${WHITE}Press any key to continue...${NC}"
read -n 1 -s
echo ""
