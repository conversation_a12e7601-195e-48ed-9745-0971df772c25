#!/bin/bash

# KDT CRM - Pi Deployment Setup Script
# Ensures all dependencies are installed for the deployment pipeline
# Usage: ./setup-deployment.sh
# Author: KDT Development Team

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

check_and_install_package() {
    local package=$1
    local description=$2
    
    if command -v "$package" &> /dev/null; then
        log SUCCESS "$description already installed"
        return 0
    fi
    
    log INFO "Installing $description..."
    case $package in
        jq)
            sudo apt-get update && sudo apt-get install -y jq
            ;;
        curl)
            sudo apt-get update && sudo apt-get install -y curl
            ;;
        git)
            sudo apt-get update && sudo apt-get install -y git
            ;;
        nginx)
            sudo apt-get update && sudo apt-get install -y nginx
            ;;
        node)
            # Install Node.js 18+ if not present
            if ! node --version 2>/dev/null | grep -q "v1[89]\|v[2-9][0-9]"; then
                log INFO "Installing Node.js 18..."
                curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                sudo apt-get install -y nodejs
            fi
            ;;
        *)
            log ERROR "Unknown package: $package"
            return 1
            ;;
    esac
    
    if command -v "$package" &> /dev/null; then
        log SUCCESS "$description installed successfully"
    else
        log ERROR "Failed to install $description"
        return 1
    fi
}

main() {
    echo "========================================"
    echo "  KDT CRM - Pi Deployment Setup"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Setting up deployment dependencies..."
    
    # Check if running on Pi
    if [[ ! -f "/etc/rpi-issue" ]]; then
        log WARNING "This doesn't appear to be a Raspberry Pi"
        log INFO "Continuing anyway..."
    fi
    
    # Update package list
    log INFO "Updating package list..."
    sudo apt-get update
    
    # Install required packages
    check_and_install_package "jq" "JSON processor"
    check_and_install_package "curl" "HTTP client"
    check_and_install_package "git" "Git version control"
    check_and_install_package "nginx" "Nginx web server"
    check_and_install_package "node" "Node.js runtime"
    
    # Check npm
    if command -v npm &> /dev/null; then
        log SUCCESS "npm already available"
    else
        log ERROR "npm not found after Node.js installation"
        exit 1
    fi
    
    # Verify versions
    echo ""
    log INFO "Installed versions:"
    echo "  Node.js: $(node --version)"
    echo "  npm: $(npm --version)"
    echo "  Git: $(git --version)"
    echo "  jq: $(jq --version)"
    echo "  curl: $(curl --version | head -1)"
    echo "  nginx: $(nginx -v 2>&1)"
    
    # Check services
    echo ""
    log INFO "Service status:"
    echo "  Nginx: $(systemctl is-active nginx 2>/dev/null || echo 'inactive')"
    echo "  PHP-FPM: $(systemctl is-active php8.2-fpm 2>/dev/null || echo 'not installed')"
    
    # Create deployment directories if needed
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
    
    if [[ -d "$PROJECT_ROOT" ]]; then
        log SUCCESS "Project directory found: $PROJECT_ROOT"
        
        # Make deployment script executable
        chmod +x "$PROJECT_ROOT/deploy/pi-native/scripts/deploy.sh"
        log SUCCESS "Deployment script made executable"
        
        # Check if .env.production exists
        if [[ -f "$PROJECT_ROOT/.env.production" ]]; then
            log SUCCESS ".env.production file found"
        else
            log WARNING ".env.production file not found"
            log INFO "Creating default .env.production..."
            cat > "$PROJECT_ROOT/.env.production" << 'EOF'
VITE_API_URL=https://ts.crtvmkmn.space/api/v1
EOF
            log SUCCESS "Default .env.production created"
        fi
    else
        log ERROR "Project directory not found"
        exit 1
    fi
    
    echo ""
    log SUCCESS "🎉 Deployment setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "  1. Test deployment: ./deploy/pi-native/scripts/deploy.sh --dry-run"
    echo "  2. Run full deployment: ./deploy/pi-native/scripts/deploy.sh"
    echo "  3. From Mac: ./deploy/mac-dev/deploy-to-pi.sh"
}

main "$@"
