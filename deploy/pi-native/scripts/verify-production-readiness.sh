#!/bin/bash

# KDT Production Readiness Verification Script
# Comprehensive assessment of production environment before deployment
# Author: KDT Development Team
# Usage: Run directly on the Raspberry Pi production server

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/home/<USER>/Apps/ts-crm"
LOG_FILE="/tmp/kdt-production-readiness-$(date +%Y%m%d-%H%M%S).log"
REPORT_FILE="/tmp/kdt-production-readiness-report-$(date +%Y%m%d-%H%M%S).md"

# Thresholds
DISK_THRESHOLD=85          # Disk usage warning threshold (%)
MEMORY_THRESHOLD=90        # Memory usage warning threshold (%)
LOAD_THRESHOLD=2.0         # Load average warning threshold
MIN_FREE_SPACE_GB=2        # Minimum free space required (GB)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
DETAILED_REPORT=false
SKIP_BACKUPS=false
SKIP_RESOURCES=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --detailed)
            DETAILED_REPORT=true
            shift
            ;;
        --skip-backups)
            SKIP_BACKUPS=true
            shift
            ;;
        --skip-resources)
            SKIP_RESOURCES=true
            shift
            ;;
        --help|-h)
            echo "KDT Production Readiness Verification Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --detailed        Generate detailed assessment report"
            echo "  --skip-backups    Skip backup verification checks"
            echo "  --skip-resources  Skip system resource checks"
            echo "  --help            Show this help message"
            echo ""
            echo "This script must be run directly on the Raspberry Pi production server."
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Counters for summary
TOTAL_CHECKS=0
PASSED_CHECKS=0
WARNING_CHECKS=0
FAILED_CHECKS=0

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[PASS]${NC} $message" | tee -a "$LOG_FILE"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            ;;
        WARNING)
            echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            ;;
        ERROR)
            echo -e "${RED}[FAIL]${NC} $message" | tee -a "$LOG_FILE"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            ;;
    esac

    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check if running on Raspberry Pi
check_pi_environment() {
    log INFO "🔍 Verifying Raspberry Pi environment"
    
    if [[ -f "/etc/rpi-issue" ]]; then
        local pi_model=$(cat /proc/device-tree/model 2>/dev/null | tr -d '\0' || echo "Unknown")
        log SUCCESS "Running on Raspberry Pi: $pi_model"
    else
        log ERROR "Not running on a Raspberry Pi - this script should only run on production server"
        return 1
    fi
    
    # Check if we're in the correct directory
    if [[ -d "$PROJECT_ROOT" ]]; then
        log SUCCESS "Project directory found: $PROJECT_ROOT"
    else
        log ERROR "Project directory not found: $PROJECT_ROOT"
        return 1
    fi
}

# Check system resources
check_system_resources() {
    if [[ "$SKIP_RESOURCES" == "true" ]]; then
        log INFO "⏭️  Skipping system resource checks (--skip-resources flag)"
        return 0
    fi
    
    log INFO "💾 Checking system resources"
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
    local free_space_gb=$(df / | awk 'NR==2{printf "%.1f", $4/1024/1024}')
    
    if [[ $disk_usage -gt $DISK_THRESHOLD ]]; then
        log ERROR "Disk usage critical: ${disk_usage}% (threshold: ${DISK_THRESHOLD}%)"
    elif [[ $disk_usage -gt 75 ]]; then
        log WARNING "Disk usage high: ${disk_usage}% (free: ${free_space_gb}GB)"
    else
        log SUCCESS "Disk usage normal: ${disk_usage}% (free: ${free_space_gb}GB)"
    fi

    if (( $(echo "$free_space_gb < $MIN_FREE_SPACE_GB" | bc -l 2>/dev/null || echo 0) )); then
        log ERROR "Insufficient free space: ${free_space_gb}GB (minimum required: ${MIN_FREE_SPACE_GB}GB)"
    fi
    
    # Check memory usage
    local memory_info=$(free | awk 'NR==2{printf "%.1f %.1f %.1f", $3/1024/1024, $2/1024/1024, $3*100/$2}')
    local used_gb=$(echo $memory_info | cut -d' ' -f1)
    local total_gb=$(echo $memory_info | cut -d' ' -f2)
    local memory_percent=$(echo $memory_info | cut -d' ' -f3 | cut -d'.' -f1)
    
    if [[ $memory_percent -gt $MEMORY_THRESHOLD ]]; then
        log ERROR "Memory usage critical: ${memory_percent}% (${used_gb}GB/${total_gb}GB)"
    elif [[ $memory_percent -gt 80 ]]; then
        log WARNING "Memory usage high: ${memory_percent}% (${used_gb}GB/${total_gb}GB)"
    else
        log SUCCESS "Memory usage normal: ${memory_percent}% (${used_gb}GB/${total_gb}GB)"
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    if (( $(echo "$load_avg > $LOAD_THRESHOLD" | bc -l 2>/dev/null || echo 0) )); then
        log WARNING "System load high: $load_avg (threshold: $LOAD_THRESHOLD)"
    else
        log SUCCESS "System load normal: $load_avg"
    fi
    
    # Check CPU temperature (Pi-specific)
    if [[ -f "/sys/class/thermal/thermal_zone0/temp" ]]; then
        local temp_c=$(($(cat /sys/class/thermal/thermal_zone0/temp) / 1000))
        if [[ $temp_c -gt 80 ]]; then
            log ERROR "CPU temperature critical: ${temp_c}°C"
        elif [[ $temp_c -gt 70 ]]; then
            log WARNING "CPU temperature high: ${temp_c}°C"
        else
            log SUCCESS "CPU temperature normal: ${temp_c}°C"
        fi
    fi
}

# Check required services
check_system_services() {
    log INFO "🔧 Checking system services"
    
    local required_services=("nginx" "php8.2-fpm" "postgresql" "redis-server")
    local failed_services=()
    
    for service in "${required_services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            local status=$(systemctl show "$service" --property=ActiveState --value)
            local uptime=$(systemctl show "$service" --property=ActiveEnterTimestamp --value)
            log SUCCESS "$service is running ($status since $uptime)"
        else
            log ERROR "$service is not running"
            failed_services+=("$service")
        fi
    done
    
    # Check if services are enabled
    for service in "${required_services[@]}"; do
        if systemctl is-enabled --quiet "$service"; then
            log SUCCESS "$service is enabled (will start on boot)"
        else
            log WARNING "$service is not enabled for auto-start"
        fi
    done
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log ERROR "Failed services detected: ${failed_services[*]}"
        return 1
    fi
}

# Check database connectivity and health
check_database_health() {
    log INFO "🗄️  Checking database health"
    
    # Check PostgreSQL connection
    if sudo -u postgres psql -c '\l' &>/dev/null; then
        log SUCCESS "PostgreSQL server is accessible"
    else
        log ERROR "Cannot connect to PostgreSQL server"
        return 1
    fi
    
    # Check production database
    if sudo -u postgres psql -l | grep -q "kdt_production"; then
        log SUCCESS "Production database 'kdt_production' exists"
        
        # Check database size
        local db_size=$(sudo -u postgres psql -d kdt_production -t -c "SELECT pg_size_pretty(pg_database_size('kdt_production'));" | tr -d ' ')
        log SUCCESS "Database size: $db_size"
        
        # Check table counts
        local table_count=$(sudo -u postgres psql -d kdt_production -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')
        log SUCCESS "Database contains $table_count tables"
        
        # Check for recent activity
        local recent_activity=$(sudo -u postgres psql -d kdt_production -t -c "SELECT COUNT(*) FROM activity_logs WHERE created_at > NOW() - INTERVAL '24 hours';" 2>/dev/null | tr -d ' ' || echo "0")
        if [[ "$recent_activity" -gt 0 ]]; then
            log SUCCESS "Recent database activity: $recent_activity entries in last 24h"
        else
            log WARNING "No recent database activity detected"
        fi
    else
        log ERROR "Production database 'kdt_production' not found"
        return 1
    fi
    
    # Check Redis connectivity
    if redis-cli ping &>/dev/null; then
        log SUCCESS "Redis server is responding"
        
        # Check Redis memory usage
        local redis_memory=$(redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')
        log SUCCESS "Redis memory usage: $redis_memory"
    else
        log ERROR "Redis server is not responding"
        return 1
    fi
}

# Check application status
check_application_status() {
    log INFO "🌐 Checking application status"
    
    # Check if application files exist
    if [[ -f "$PROJECT_ROOT/frontend/dist/index.html" ]]; then
        log SUCCESS "Frontend build files exist"
    else
        log ERROR "Frontend build files not found"
    fi
    
    if [[ -f "$PROJECT_ROOT/backend/public/index.php" ]]; then
        log SUCCESS "Backend application files exist"
    else
        log ERROR "Backend application files not found"
    fi
    
    # Check application accessibility
    local app_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" 2>/dev/null || echo "000")
    if [[ "$app_status" == "200" ]]; then
        log SUCCESS "Application accessible (HTTPS): HTTP $app_status"
    else
        log ERROR "Application not accessible (HTTPS): HTTP $app_status"
    fi
    
    # Check API endpoint
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" 2>/dev/null || echo "000")
    if [[ "$api_status" == "200" ]]; then
        log SUCCESS "API endpoint responding: HTTP $api_status"
    else
        log ERROR "API endpoint not responding: HTTP $api_status"
    fi
    
    # Check SSL certificate
    local ssl_expiry=$(echo | openssl s_client -servername ts.crtvmkmn.space -connect ts.crtvmkmn.space:443 2>/dev/null | openssl x509 -noout -dates | grep "notAfter" | cut -d= -f2)
    if [[ -n "$ssl_expiry" ]]; then
        log SUCCESS "SSL certificate valid until: $ssl_expiry"
    else
        log WARNING "Could not verify SSL certificate"
    fi
}

# Check backup status
check_backup_status() {
    if [[ "$SKIP_BACKUPS" == "true" ]]; then
        log INFO "⏭️  Skipping backup verification (--skip-backups flag)"
        return 0
    fi
    
    log INFO "💾 Checking backup status"
    
    # Check for recent database backups
    local backup_files=($(find /tmp -name "kdt-production-backup-*" -type f -mtime -7 2>/dev/null | sort -r))
    if [[ ${#backup_files[@]} -gt 0 ]]; then
        local latest_backup="${backup_files[0]}"
        local backup_age=$(stat -c %Y "$latest_backup")
        local current_time=$(date +%s)
        local age_hours=$(( (current_time - backup_age) / 3600 ))
        
        if [[ $age_hours -lt 24 ]]; then
            log SUCCESS "Recent database backup found: $(basename "$latest_backup") (${age_hours}h old)"
        else
            log WARNING "Latest database backup is ${age_hours}h old: $(basename "$latest_backup")"
        fi
    else
        log WARNING "No recent database backups found"
    fi
    
    # Check backup directory space
    local backup_space=$(df /tmp | awk 'NR==2{printf "%.1f", $4/1024/1024}')
    if (( $(echo "$backup_space < 1.0" | bc -l 2>/dev/null || echo 0) )); then
        log WARNING "Low backup space available: ${backup_space}GB"
    else
        log SUCCESS "Adequate backup space available: ${backup_space}GB"
    fi
}

# Check configuration files
check_configuration() {
    log INFO "⚙️  Checking configuration files"
    
    # Check nginx configuration
    if sudo nginx -t &>/dev/null; then
        log SUCCESS "Nginx configuration is valid"
    else
        log ERROR "Nginx configuration has errors"
    fi
    
    # Check environment files
    if [[ -f "$PROJECT_ROOT/.env.production" ]]; then
        log SUCCESS "Production environment file exists"
    else
        log WARNING "Production environment file not found"
    fi
    
    # Check file permissions
    local nginx_perms=$(stat -c "%a" "$PROJECT_ROOT/frontend/dist" 2>/dev/null || echo "000")
    if [[ "$nginx_perms" == "755" ]] || [[ "$nginx_perms" == "775" ]]; then
        log SUCCESS "Frontend directory permissions correct: $nginx_perms"
    else
        log WARNING "Frontend directory permissions may be incorrect: $nginx_perms"
    fi
    
    # Check log file permissions and space
    local log_dir="/var/log/nginx"
    if [[ -d "$log_dir" ]] && [[ -w "$log_dir" ]]; then
        log SUCCESS "Log directory is writable"
    else
        log WARNING "Log directory may not be writable"
    fi
}

# Generate detailed report
generate_detailed_report() {
    if [[ "$DETAILED_REPORT" != "true" ]]; then
        return 0
    fi
    
    log INFO "📋 Generating detailed assessment report"
    
    cat > "$REPORT_FILE" << EOF
# KDT Production Readiness Assessment Report

**Generated:** $(date)  
**Server:** $(hostname)  
**Pi Model:** $(cat /proc/device-tree/model 2>/dev/null | tr -d '\0' || echo "Unknown")

## Executive Summary

- **Total Checks:** $TOTAL_CHECKS
- **Passed:** $PASSED_CHECKS
- **Warnings:** $WARNING_CHECKS  
- **Failed:** $FAILED_CHECKS
- **Overall Status:** $([ $FAILED_CHECKS -eq 0 ] && echo "✅ READY" || echo "❌ NOT READY")

## System Information

### Hardware
- **CPU Temperature:** $(cat /sys/class/thermal/thermal_zone0/temp 2>/dev/null | awk '{print $1/1000"°C"}' || echo "N/A")
- **Load Average:** $(uptime | awk -F'load average:' '{print $2}')
- **Uptime:** $(uptime -p)

### Storage
- **Root Filesystem:** $(df -h / | awk 'NR==2{print $3"/"$2" ("$5" used)"}')
- **Available Space:** $(df -h / | awk 'NR==2{print $4}')

### Memory
$(free -h)

### Services Status
$(systemctl status nginx php8.2-fpm postgresql redis-server --no-pager -l 2>/dev/null | head -20)

## Database Information
- **Database Size:** $(sudo -u postgres psql -d kdt_production -t -c "SELECT pg_size_pretty(pg_database_size('kdt_production'));" 2>/dev/null | tr -d ' ' || echo "N/A")
- **Table Count:** $(sudo -u postgres psql -d kdt_production -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ' || echo "N/A")

## Network Connectivity
- **Application Status:** $(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" 2>/dev/null || echo "Failed")
- **API Status:** $(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" 2>/dev/null || echo "Failed")

## Recommendations

$([ $FAILED_CHECKS -gt 0 ] && echo "⚠️ **Critical Issues Found** - Address failed checks before deployment" || echo "✅ **System Ready** - All critical checks passed")

$([ $WARNING_CHECKS -gt 0 ] && echo "⚠️ **Warnings Present** - Review warning items for optimal performance" || echo "")

---
*Report generated by KDT Production Readiness Verification Script*
EOF

    log SUCCESS "Detailed report saved: $REPORT_FILE"
}

# Main verification function
main() {
    echo "========================================"
    echo "  KDT Production Readiness Assessment"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Starting production readiness verification"
    log INFO "Log file: $LOG_FILE"
    
    # Execute all checks
    check_pi_environment
    check_system_resources
    check_system_services
    check_database_health
    check_application_status
    check_backup_status
    check_configuration
    
    # Generate detailed report if requested
    generate_detailed_report
    
    # Summary
    echo ""
    echo "========================================"
    echo "  PRODUCTION READINESS SUMMARY"
    echo "========================================"
    
    local status_color=$GREEN
    local status_text="READY FOR DEPLOYMENT"
    
    if [[ $FAILED_CHECKS -gt 0 ]]; then
        status_color=$RED
        status_text="NOT READY - CRITICAL ISSUES"
    elif [[ $WARNING_CHECKS -gt 0 ]]; then
        status_color=$YELLOW
        status_text="READY WITH WARNINGS"
    fi
    
    echo -e "${status_color}Status: $status_text${NC}"
    echo -e "${BLUE}Checks: $PASSED_CHECKS passed, $WARNING_CHECKS warnings, $FAILED_CHECKS failed${NC}"
    echo ""
    
    if [[ $FAILED_CHECKS -gt 0 ]]; then
        echo -e "${RED}❌ Critical issues must be resolved before deployment${NC}"
        echo -e "${BLUE}📋 Review log file: $LOG_FILE${NC}"
    elif [[ $WARNING_CHECKS -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Warnings detected - review before deployment${NC}"
        echo -e "${BLUE}📋 Review log file: $LOG_FILE${NC}"
    else
        echo -e "${GREEN}✅ Production environment is ready for deployment${NC}"
        echo -e "${BLUE}🚀 You can proceed with: ./deploy/pi-native/scripts/deploy-with-settings.sh${NC}"
    fi
    
    if [[ "$DETAILED_REPORT" == "true" ]]; then
        echo -e "${PURPLE}📊 Detailed report: $REPORT_FILE${NC}"
    fi
    
    # Exit with appropriate code
    exit $FAILED_CHECKS
}

# Execute main function
main "$@"
