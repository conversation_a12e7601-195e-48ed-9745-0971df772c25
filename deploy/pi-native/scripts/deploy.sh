#!/bin/bash

# KDT CRM - Raspberry Pi Deployment Script with State Validation
# Usage: ./deploy.sh [--rollback] [--force] [--dry-run]
# Author: KDT Development Team
# Version: 1.0.0

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
FRONTEND_DIST="$PROJECT_ROOT/frontend/dist"
NGINX_DIST="/home/<USER>/Apps/ts-crm/frontend/dist"
LOG_FILE="/tmp/kdt-deploy-$(date +%Y%m%d-%H%M%S).log"
STATE_FILE="/tmp/kdt-deploy-state.json"
BACKUP_DIR="/tmp/kdt-backup-$(date +%Y%m%d-%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
DRY_RUN=false
FORCE=false
ROLLBACK=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --help|-h)
            echo "KDT CRM Deployment Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --dry-run     Show what would be done without executing"
            echo "  --force       Force deployment even if validation fails"
            echo "  --rollback    Rollback to previous deployment"
            echo "  --verbose     Enable verbose output"
            echo "  --help        Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        DEBUG)
            if [[ "$VERBOSE" == "true" ]]; then
                echo -e "${PURPLE}[DEBUG]${NC} $message" | tee -a "$LOG_FILE"
            fi
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Progress indicator
show_progress() {
    local current=$1
    local total=$2
    local description=$3
    local percentage=$((current * 100 / total))
    local bar_length=50
    local filled_length=$((percentage * bar_length / 100))
    
    printf "\r${CYAN}[%3d%%]${NC} [" "$percentage"
    printf "%*s" "$filled_length" | tr ' ' '='
    printf "%*s" $((bar_length - filled_length)) | tr ' ' '-'
    printf "] %s" "$description"
    
    if [[ $current -eq $total ]]; then
        echo ""
    fi
}

# State capture functions
capture_git_state() {
    local state_key=$1
    log DEBUG "Capturing git state: $state_key"
    
    cd "$PROJECT_ROOT"
    local commit_hash=$(git rev-parse HEAD)
    local branch=$(git branch --show-current)
    local remote_url=$(git remote get-url origin)
    local uncommitted_changes=$(git status --porcelain | wc -l)
    
    jq -n \
        --arg commit "$commit_hash" \
        --arg branch "$branch" \
        --arg remote "$remote_url" \
        --arg uncommitted "$uncommitted_changes" \
        '{
            commit_hash: $commit,
            branch: $branch,
            remote_url: $remote,
            uncommitted_changes: ($uncommitted | tonumber),
            timestamp: now
        }' > "/tmp/git_state_${state_key}.json"
}

capture_build_state() {
    local state_key=$1
    log DEBUG "Capturing build state: $state_key"
    
    local build_state="{}"
    
    # Capture dist directory state
    if [[ -d "$PROJECT_ROOT/dist" ]]; then
        cd "$PROJECT_ROOT/dist"
        build_state=$(jq -n \
            --argjson assets "$(find assets -type f -exec stat -c '{"name":"%n","size":%s,"modified":"%Y"}' {} \; 2>/dev/null | jq -s '.' || echo '[]')" \
            --arg index_exists "$(test -f index.html && echo 'true' || echo 'false')" \
            '{
                dist_exists: true,
                assets: $assets,
                index_exists: ($index_exists | test("true")),
                timestamp: now
            }')
    else
        build_state='{"dist_exists": false, "timestamp": null}'
    fi
    
    echo "$build_state" > "/tmp/build_state_${state_key}.json"
}

capture_service_state() {
    local state_key=$1
    log DEBUG "Capturing service state: $state_key"
    
    local nginx_status=$(systemctl is-active nginx 2>/dev/null || echo "inactive")
    local php_fpm_status=$(systemctl is-active php8.2-fpm 2>/dev/null || echo "inactive")
    local nginx_config_test=$(sudo nginx -t 2>&1 && echo "valid" || echo "invalid")
    
    jq -n \
        --arg nginx "$nginx_status" \
        --arg php_fpm "$php_fpm_status" \
        --arg nginx_config "$nginx_config_test" \
        '{
            nginx_status: $nginx,
            php_fpm_status: $php_fpm,
            nginx_config_valid: ($nginx_config | test("valid")),
            timestamp: now
        }' > "/tmp/service_state_${state_key}.json"
}

capture_nginx_files_state() {
    local state_key=$1
    log DEBUG "Capturing nginx files state: $state_key"
    
    local nginx_state="{}"
    
    if [[ -d "$NGINX_DIST" ]]; then
        cd "$NGINX_DIST"
        nginx_state=$(jq -n \
            --argjson assets "$(find . -name '*.js' -o -name '*.css' -exec stat -c '{"name":"%n","size":%s,"modified":"%Y"}' {} \; 2>/dev/null | jq -s '.' || echo '[]')" \
            --arg index_content "$(cat index.html 2>/dev/null | grep -o 'assets/[^"]*' | head -5 | tr '\n' ',' || echo '')" \
            '{
                nginx_dist_exists: true,
                served_assets: $assets,
                index_references: $index_content,
                timestamp: now
            }')
    else
        nginx_state='{"nginx_dist_exists": false, "timestamp": null}'
    fi
    
    echo "$nginx_state" > "/tmp/nginx_files_state_${state_key}.json"
}

# Comprehensive state capture
capture_full_state() {
    local state_key=$1
    log INFO "📊 Capturing full system state: $state_key"
    
    show_progress 1 4 "Capturing git state..."
    capture_git_state "$state_key"
    
    show_progress 2 4 "Capturing build state..."
    capture_build_state "$state_key"
    
    show_progress 3 4 "Capturing service state..."
    capture_service_state "$state_key"
    
    show_progress 4 4 "Capturing nginx files state..."
    capture_nginx_files_state "$state_key"
    
    # Combine all states
    jq -s '
        {
            git: .[0],
            build: .[1],
            services: .[2],
            nginx_files: .[3],
            capture_time: now
        }
    ' "/tmp/git_state_${state_key}.json" \
      "/tmp/build_state_${state_key}.json" \
      "/tmp/service_state_${state_key}.json" \
      "/tmp/nginx_files_state_${state_key}.json" > "/tmp/full_state_${state_key}.json"
    
    log SUCCESS "State captured successfully"
}

# State comparison
compare_states() {
    local before_state="/tmp/full_state_before.json"
    local after_state="/tmp/full_state_after.json"
    
    if [[ ! -f "$before_state" ]] || [[ ! -f "$after_state" ]]; then
        log WARNING "Cannot compare states - missing state files"
        return 1
    fi
    
    log INFO "📈 Deployment State Comparison"
    echo "=================================="
    
    # Git changes
    local before_commit=$(jq -r '.git.commit_hash' "$before_state")
    local after_commit=$(jq -r '.git.commit_hash' "$after_state")
    
    if [[ "$before_commit" != "$after_commit" ]]; then
        echo -e "${GREEN}✅ Git Commit Updated:${NC}"
        echo "   Before: $before_commit"
        echo "   After:  $after_commit"
    else
        echo -e "${YELLOW}⚠️  Git Commit Unchanged:${NC} $before_commit"
    fi
    
    # Build file changes
    local before_assets=$(jq -r '.build.assets[]? | "\(.name):\(.size)"' "$before_state" 2>/dev/null | sort)
    local after_assets=$(jq -r '.build.assets[]? | "\(.name):\(.size)"' "$after_state" 2>/dev/null | sort)
    
    if [[ "$before_assets" != "$after_assets" ]]; then
        echo -e "${GREEN}✅ Build Assets Updated:${NC}"
        echo "   New assets:"
        jq -r '.build.assets[]? | "   - \(.name) (\(.size) bytes)"' "$after_state" 2>/dev/null || echo "   None"
    else
        echo -e "${BLUE}ℹ️  Build Assets Unchanged${NC}"
    fi
    
    # Service status
    local nginx_before=$(jq -r '.services.nginx_status' "$before_state")
    local nginx_after=$(jq -r '.services.nginx_status' "$after_state")
    
    echo -e "${BLUE}🔧 Service Status:${NC}"
    echo "   Nginx: $nginx_before → $nginx_after"
    
    # Nginx served files
    local nginx_assets_before=$(jq -r '.nginx_files.served_assets[]? | "\(.name):\(.size)"' "$before_state" 2>/dev/null | sort)
    local nginx_assets_after=$(jq -r '.nginx_files.served_assets[]? | "\(.name):\(.size)"' "$after_state" 2>/dev/null | sort)
    
    if [[ "$nginx_assets_before" != "$nginx_assets_after" ]]; then
        echo -e "${GREEN}✅ Nginx Served Files Updated:${NC}"
        echo "   Currently serving:"
        jq -r '.nginx_files.served_assets[]? | "   - \(.name) (\(.size) bytes)"' "$after_state" 2>/dev/null || echo "   None"
    else
        echo -e "${BLUE}ℹ️  Nginx Served Files Unchanged${NC}"
    fi
    
    echo "=================================="
}

# Validation functions
validate_environment() {
    log INFO "🔍 Validating deployment environment"

    local errors=0

    # Check if we're on the Pi
    if [[ ! -f "/etc/rpi-issue" ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "This script should only be run on a Raspberry Pi"
        ((errors++))
    fi

    # Check required directories
    if [[ ! -d "$PROJECT_ROOT" ]]; then
        log ERROR "Project root not found: $PROJECT_ROOT"
        ((errors++))
    fi

    # Check git repository
    if [[ ! -d "$PROJECT_ROOT/.git" ]]; then
        log ERROR "Not a git repository: $PROJECT_ROOT"
        ((errors++))
    fi

    # Check required commands
    local required_commands=("git" "npm" "jq" "nginx" "systemctl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log ERROR "Required command not found: $cmd"
            ((errors++))
        fi
    done

    # Check nginx configuration
    if ! sudo nginx -t &>/dev/null; then
        log ERROR "Nginx configuration is invalid"
        ((errors++))
    fi

    # Check environment file
    if [[ ! -f "$PROJECT_ROOT/.env.production" ]]; then
        log WARNING ".env.production file not found - will use defaults"
    fi

    if [[ $errors -gt 0 ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "Environment validation failed with $errors errors"
        log INFO "Use --force to override validation errors"
        exit 1
    fi

    log SUCCESS "Environment validation passed"
}

# Backup functions
create_backup() {
    log INFO "💾 Creating deployment backup"

    mkdir -p "$BACKUP_DIR"

    # Backup current nginx files
    if [[ -d "$NGINX_DIST" ]]; then
        cp -r "$NGINX_DIST" "$BACKUP_DIR/nginx_dist"
        log DEBUG "Backed up nginx files to $BACKUP_DIR/nginx_dist"
    fi

    # Backup current build files
    if [[ -d "$PROJECT_ROOT/dist" ]]; then
        cp -r "$PROJECT_ROOT/dist" "$BACKUP_DIR/build_dist"
        log DEBUG "Backed up build files to $BACKUP_DIR/build_dist"
    fi

    # Backup git state
    cd "$PROJECT_ROOT"
    git rev-parse HEAD > "$BACKUP_DIR/commit_hash"
    git branch --show-current > "$BACKUP_DIR/branch"

    # Backup environment
    if [[ -f "$PROJECT_ROOT/.env.production" ]]; then
        cp "$PROJECT_ROOT/.env.production" "$BACKUP_DIR/"
    fi

    log SUCCESS "Backup created at $BACKUP_DIR"
    echo "$BACKUP_DIR" > "/tmp/kdt-last-backup"
}

restore_backup() {
    local backup_path=${1:-$(cat "/tmp/kdt-last-backup" 2>/dev/null)}

    if [[ -z "$backup_path" ]] || [[ ! -d "$backup_path" ]]; then
        log ERROR "No valid backup found for restoration"
        return 1
    fi

    log INFO "🔄 Restoring from backup: $backup_path"

    # Restore nginx files
    if [[ -d "$backup_path/nginx_dist" ]]; then
        rm -rf "$NGINX_DIST"
        cp -r "$backup_path/nginx_dist" "$NGINX_DIST"
        log DEBUG "Restored nginx files"
    fi

    # Restore build files
    if [[ -d "$backup_path/build_dist" ]]; then
        rm -rf "$PROJECT_ROOT/dist"
        cp -r "$backup_path/build_dist" "$PROJECT_ROOT/dist"
        log DEBUG "Restored build files"
    fi

    # Restore git state
    if [[ -f "$backup_path/commit_hash" ]]; then
        cd "$PROJECT_ROOT"
        local backup_commit=$(cat "$backup_path/commit_hash")
        git checkout "$backup_commit" 2>/dev/null || log WARNING "Could not restore git commit"
    fi

    # Restart services
    sudo systemctl reload nginx

    log SUCCESS "Backup restoration completed"
}

# Git operations
pull_latest_code() {
    log INFO "📥 Pulling latest code from repository"

    cd "$PROJECT_ROOT"

    # Stash any local changes
    if [[ $(git status --porcelain | wc -l) -gt 0 ]]; then
        log WARNING "Stashing local changes"
        git stash push -m "Auto-stash before deployment $(date)"
    fi

    # Fetch latest changes
    git fetch origin

    # Get current and remote commit hashes
    local current_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/pi-native-deployment)

    if [[ "$current_commit" == "$remote_commit" ]]; then
        log INFO "Already up to date with remote"
        return 0
    fi

    log INFO "Updating from $current_commit to $remote_commit"

    # Pull latest changes
    if git pull origin pi-native-deployment; then
        log SUCCESS "Code updated successfully"

        # Show what changed
        log INFO "Recent changes:"
        git log --oneline "$current_commit..HEAD" | head -5 | while read line; do
            log INFO "  $line"
        done
    else
        log ERROR "Failed to pull latest code"
        return 1
    fi
}

# Build operations
build_frontend() {
    log INFO "🔨 Building frontend with production configuration"

    cd "$PROJECT_ROOT"

    # Ensure environment file exists
    if [[ ! -f ".env.production" ]]; then
        log WARNING "Creating default .env.production file"
        cat > .env.production << EOF
VITE_API_URL=https://ts.crtvmkmn.space/api/v1
EOF
    fi

    # Clean previous build
    if [[ -d "dist" ]]; then
        rm -rf dist
        log DEBUG "Cleaned previous build"
    fi

    # Install dependencies if needed
    if [[ ! -d "node_modules" ]] || [[ "package.json" -nt "node_modules" ]]; then
        log INFO "Installing/updating dependencies"
        npm ci --production=false
    fi

    # Build with production environment
    log INFO "Building frontend (this may take 30-60 seconds)..."
    if VITE_API_URL=https://ts.crtvmkmn.space/api/v1 npm run build; then
        log SUCCESS "Frontend build completed"

        # Verify build output
        if [[ -f "dist/index.html" ]] && [[ -d "dist/assets" ]]; then
            local css_files=$(find dist/assets -name "*.css" | wc -l)
            local js_files=$(find dist/assets -name "*.js" | wc -l)
            log INFO "Build output: $css_files CSS files, $js_files JS files"

            # Show file sizes
            find dist/assets -name "*.css" -o -name "*.js" | while read file; do
                local size=$(stat -c%s "$file")
                local size_kb=$((size / 1024))
                log DEBUG "  $(basename "$file"): ${size_kb} KB"
            done
        else
            log ERROR "Build output validation failed"
            return 1
        fi
    else
        log ERROR "Frontend build failed"
        return 1
    fi
}

# Deployment operations
deploy_frontend() {
    log INFO "🚀 Deploying frontend to nginx"

    if [[ ! -d "$PROJECT_ROOT/dist" ]]; then
        log ERROR "Build directory not found: $PROJECT_ROOT/dist"
        return 1
    fi

    # Create nginx directory if it doesn't exist
    mkdir -p "$NGINX_DIST"

    # Clear old files
    rm -rf "$NGINX_DIST"/*

    # Copy new build files
    cp -r "$PROJECT_ROOT/dist"/* "$NGINX_DIST/"

    # Set proper permissions
    chown -R zulhelminasir:www-data "$NGINX_DIST"
    chmod -R 755 "$NGINX_DIST"

    log SUCCESS "Frontend deployed to nginx"
}

restart_services() {
    log INFO "🔄 Restarting services"

    # Test nginx configuration
    if ! sudo nginx -t; then
        log ERROR "Nginx configuration test failed"
        return 1
    fi

    # Reload nginx
    if sudo systemctl reload nginx; then
        log SUCCESS "Nginx reloaded successfully"
    else
        log ERROR "Failed to reload nginx"
        return 1
    fi

    # Check if PHP-FPM needs restart (optional)
    if systemctl is-active php8.2-fpm &>/dev/null; then
        log INFO "PHP-FPM is running, no restart needed"
    else
        log WARNING "PHP-FPM is not running, attempting to start"
        sudo systemctl start php8.2-fpm
    fi
}

# Verification functions
verify_deployment() {
    log INFO "✅ Verifying deployment"

    local errors=0

    # Check nginx is serving files
    if [[ -f "$NGINX_DIST/index.html" ]]; then
        log SUCCESS "Nginx files deployed"
    else
        log ERROR "Nginx index.html not found"
        ((errors++))
    fi

    # Check API connectivity
    log INFO "Testing API connectivity..."
    local api_test=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" || echo "000")

    if [[ "$api_test" == "200" ]]; then
        log SUCCESS "API endpoint responding (HTTP 200)"
    else
        log ERROR "API endpoint not responding (HTTP $api_test)"
        ((errors++))
    fi

    # Check frontend accessibility
    log INFO "Testing frontend accessibility..."
    local frontend_test=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" || echo "000")

    if [[ "$frontend_test" == "200" ]]; then
        log SUCCESS "Frontend accessible (HTTP 200)"
    else
        log ERROR "Frontend not accessible (HTTP $frontend_test)"
        ((errors++))
    fi

    # Check build file integrity
    local css_files=$(find "$NGINX_DIST/assets" -name "*.css" 2>/dev/null | wc -l)
    local js_files=$(find "$NGINX_DIST/assets" -name "*.js" 2>/dev/null | wc -l)

    if [[ $css_files -gt 0 ]] && [[ $js_files -gt 0 ]]; then
        log SUCCESS "Build assets present ($css_files CSS, $js_files JS)"
    else
        log ERROR "Missing build assets"
        ((errors++))
    fi

    # Check for correct API URL in JS files
    if grep -q "ts.crtvmkmn.space" "$NGINX_DIST/assets"/*.js 2>/dev/null; then
        log SUCCESS "Correct API URL found in build"
    else
        log ERROR "API URL not found in build files"
        ((errors++))
    fi

    if [[ $errors -eq 0 ]]; then
        log SUCCESS "All verification checks passed"
        return 0
    else
        log ERROR "Verification failed with $errors errors"
        return 1
    fi
}

# Main deployment function
main_deploy() {
    log INFO "🚀 Starting KDT CRM deployment"
    echo "========================================"
    echo "  KDT CRM - Pi Deployment Pipeline"
    echo "  $(date)"
    echo "========================================"

    # Capture initial state
    capture_full_state "before"

    # Create backup
    create_backup

    local deployment_failed=false

    # Execute deployment steps
    {
        validate_environment &&
        pull_latest_code &&
        build_frontend &&
        deploy_frontend &&
        restart_services
    } || {
        deployment_failed=true
        log ERROR "Deployment failed at step: ${BASH_COMMAND}"
    }

    # Capture final state
    capture_full_state "after"

    if [[ "$deployment_failed" == "true" ]]; then
        log ERROR "💥 Deployment failed - initiating rollback"
        restore_backup
        return 1
    fi

    # Verify deployment
    if verify_deployment; then
        log SUCCESS "🎉 Deployment completed successfully"
        compare_states

        # Cleanup old backups (keep last 5)
        find /tmp -name "kdt-backup-*" -type d | sort | head -n -5 | xargs rm -rf 2>/dev/null || true

        return 0
    else
        log ERROR "💥 Deployment verification failed - initiating rollback"
        restore_backup
        return 1
    fi
}

# Rollback function
rollback_deployment() {
    log INFO "🔄 Rolling back to previous deployment"

    local backup_path=$(cat "/tmp/kdt-last-backup" 2>/dev/null)

    if [[ -z "$backup_path" ]]; then
        log ERROR "No backup found for rollback"
        return 1
    fi

    restore_backup "$backup_path"

    if verify_deployment; then
        log SUCCESS "🎉 Rollback completed successfully"
    else
        log ERROR "💥 Rollback verification failed"
        return 1
    fi
}

# Dry run function
dry_run() {
    log INFO "🔍 Dry run - showing what would be done"
    echo "========================================"
    echo "  KDT CRM - Deployment Dry Run"
    echo "========================================"

    capture_full_state "dry_run"

    cd "$PROJECT_ROOT"
    local current_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/pi-native-deployment 2>/dev/null || echo "unknown")

    echo "Current state:"
    echo "  Git commit: $current_commit"
    echo "  Remote commit: $remote_commit"

    if [[ "$current_commit" != "$remote_commit" ]]; then
        echo "  📥 Would pull latest code"
        echo "  🔨 Would rebuild frontend"
        echo "  🚀 Would deploy to nginx"
        echo "  🔄 Would restart services"
        echo "  ✅ Would verify deployment"
    else
        echo "  ℹ️  No code changes detected"
        echo "  🔨 Would rebuild frontend anyway"
        echo "  🚀 Would redeploy to nginx"
    fi

    echo ""
    echo "Files that would be affected:"
    echo "  - $NGINX_DIST/*"
    echo "  - $PROJECT_ROOT/dist/*"
    echo ""
    echo "Services that would be restarted:"
    echo "  - nginx (reload)"
    echo ""
    echo "Use without --dry-run to execute deployment"
}

# Main execution
main() {
    # Ensure log directory exists
    mkdir -p "$(dirname "$LOG_FILE")"

    log INFO "KDT CRM Deployment Script v1.0.0"
    log INFO "Log file: $LOG_FILE"

    if [[ "$DRY_RUN" == "true" ]]; then
        dry_run
        exit 0
    fi

    if [[ "$ROLLBACK" == "true" ]]; then
        rollback_deployment
        exit $?
    fi

    # Execute main deployment
    if main_deploy; then
        echo ""
        echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
        echo "   Frontend: https://ts.crtvmkmn.space"
        echo "   API: https://ts.crtvmkmn.space/api/v1"
        echo "   Log: $LOG_FILE"

        # Show security reminder
        echo ""
        log INFO "🛡️ Showing security configuration reminder..."
        if [[ -f "$SCRIPT_DIR/security-reminder.sh" ]]; then
            bash "$SCRIPT_DIR/security-reminder.sh"
        else
            log WARNING "Security reminder script not found"
            echo -e "${YELLOW}⚠️ IMPORTANT: Please update your security settings in the application!${NC}"
            echo "   Go to: https://ts.crtvmkmn.space/settings?tab=security"
        fi

        exit 0
    else
        echo ""
        echo -e "${RED}💥 Deployment failed!${NC}"
        echo "   Check log: $LOG_FILE"
        echo "   Use --rollback to restore previous version"
        exit 1
    fi
}

# Execute main function
main "$@"
