#!/bin/bash

# KDT Remote Enhanced Deployment Script
# Executes enhanced deployment with data synchronization from Mac to Pi
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
PI_HOST="zulhelminasir@*************"
PI_PROJECT_PATH="/home/<USER>/Apps/ts-crm"
BRANCH="pi-native-deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
DRY_RUN=false
FORCE=false
SKIP_DATA_SYNC=false
SKIP_BACKUPS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --skip-data-sync)
            SKIP_DATA_SYNC=true
            shift
            ;;
        --skip-backups)
            SKIP_BACKUPS=true
            shift
            ;;
        --help|-h)
            echo "KDT Remote Enhanced Deployment Script"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --dry-run         Show what would be done without executing"
            echo "  --force           Force deployment even if validation fails"
            echo "  --skip-data-sync  Skip data synchronization"
            echo "  --skip-backups    Skip backup creation"
            echo "  --help            Show this help message"
            echo ""
            echo "This script executes the enhanced deployment pipeline remotely:"
            echo "  1. Creates development data and settings backups"
            echo "  2. Transfers backups to Pi"
            echo "  3. Pushes latest code to Pi"
            echo "  4. Executes enhanced deployment with data synchronization"
            echo "  5. Verifies deployment success"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        STEP)
            echo -e "${PURPLE}[STEP]${NC} $message"
            ;;
    esac
}

# Check SSH connectivity
check_ssh_connectivity() {
    log INFO "🔗 Testing SSH connectivity to Pi"
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$PI_HOST" "echo 'SSH connection successful'" &>/dev/null; then
        log SUCCESS "SSH connection to Pi established"
    else
        log ERROR "Cannot connect to Pi via SSH"
        log ERROR "Please ensure:"
        log ERROR "  1. Pi is powered on and connected to network"
        log ERROR "  2. SSH is enabled on Pi"
        log ERROR "  3. SSH keys are properly configured"
        log ERROR "  4. Hostname/IP is correct: $PI_HOST"
        exit 1
    fi
}

# Create local backups
create_local_backups() {
    if [[ "$SKIP_BACKUPS" == "true" ]]; then
        log INFO "⏭️  Skipping backup creation (--skip-backups flag)"
        return 0
    fi
    
    log STEP "💾 Creating local backups"
    
    # Create development data backup
    log INFO "Creating development data backup..."
    if ./deploy/pi-native/scripts/backup-dev-data.sh; then
        log SUCCESS "Development data backup created"
    else
        log ERROR "Failed to create development data backup"
        return 1
    fi
    
    # Create settings backup
    log INFO "Creating settings backup..."
    if ./deploy/pi-native/scripts/backup-settings.sh; then
        log SUCCESS "Settings backup created"
    else
        log ERROR "Failed to create settings backup"
        return 1
    fi
}

# Transfer backups to Pi
transfer_backups_to_pi() {
    if [[ "$SKIP_BACKUPS" == "true" ]]; then
        log INFO "⏭️  Skipping backup transfer (--skip-backups flag)"
        return 0
    fi
    
    log STEP "📤 Transferring backups to Pi"
    
    # Get latest backup locations
    local dev_backup=$(cat /tmp/kdt-last-dev-data-backup 2>/dev/null || echo "")
    local settings_backup=$(cat /tmp/kdt-last-settings-backup 2>/dev/null || echo "")
    
    if [[ -z "$dev_backup" ]] || [[ ! -d "$dev_backup" ]]; then
        log ERROR "Development data backup not found"
        return 1
    fi
    
    if [[ -z "$settings_backup" ]] || [[ ! -d "$settings_backup" ]]; then
        log ERROR "Settings backup not found"
        return 1
    fi
    
    # Transfer development data backup
    log INFO "Transferring development data backup..."
    if scp -r "$dev_backup" "$PI_HOST:/tmp/"; then
        log SUCCESS "Development data backup transferred"
    else
        log ERROR "Failed to transfer development data backup"
        return 1
    fi
    
    # Transfer settings backup
    log INFO "Transferring settings backup..."
    if scp -r "$settings_backup" "$PI_HOST:/tmp/"; then
        log SUCCESS "Settings backup transferred"
    else
        log ERROR "Failed to transfer settings backup"
        return 1
    fi
}

# Push code to Pi
push_code_to_pi() {
    log STEP "📤 Pushing latest code to Pi"
    
    cd "$PROJECT_ROOT"
    
    # Check for uncommitted changes
    if [[ $(git status --porcelain | wc -l) -gt 0 ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "Uncommitted changes detected. Commit changes or use --force"
        return 1
    fi
    
    # Push to remote
    log INFO "Pushing to origin/$BRANCH..."
    if git push origin "$BRANCH"; then
        log SUCCESS "Code pushed successfully"
    else
        log ERROR "Failed to push code"
        return 1
    fi
    
    # Pull on Pi
    log INFO "Pulling latest code on Pi..."
    if ssh "$PI_HOST" "cd $PI_PROJECT_PATH && git pull origin $BRANCH"; then
        log SUCCESS "Code pulled on Pi successfully"
    else
        log ERROR "Failed to pull code on Pi"
        return 1
    fi
}

# Execute enhanced deployment on Pi
execute_enhanced_deployment() {
    log STEP "🚀 Executing enhanced deployment on Pi"
    
    # Build deployment command
    local deploy_cmd="cd $PI_PROJECT_PATH && ./deploy/pi-native/scripts/deploy-with-settings.sh"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        deploy_cmd="$deploy_cmd --dry-run"
    fi
    
    if [[ "$FORCE" == "true" ]]; then
        deploy_cmd="$deploy_cmd --force"
    fi
    
    if [[ "$SKIP_DATA_SYNC" == "true" ]]; then
        deploy_cmd="$deploy_cmd --skip-data-sync"
    fi
    
    # Add backup file paths if available
    if [[ "$SKIP_BACKUPS" != "true" ]]; then
        local dev_backup_name=$(basename "$(cat /tmp/kdt-last-dev-data-backup 2>/dev/null || echo "")")
        local settings_backup_name=$(basename "$(cat /tmp/kdt-last-settings-backup 2>/dev/null || echo "")")
        
        if [[ -n "$dev_backup_name" ]]; then
            deploy_cmd="$deploy_cmd --dev-db-backup /tmp/$dev_backup_name/business_data.sql"
        fi
        
        if [[ -n "$settings_backup_name" ]]; then
            deploy_cmd="$deploy_cmd --settings-backup /tmp/$settings_backup_name"
        fi
    fi
    
    log INFO "Executing: $deploy_cmd"
    echo ""
    
    # Execute deployment on Pi with real-time output
    if ssh -t "$PI_HOST" "$deploy_cmd"; then
        log SUCCESS "Enhanced deployment completed successfully"
        return 0
    else
        log ERROR "Enhanced deployment failed"
        return 1
    fi
}

# Verify deployment success
verify_deployment() {
    log STEP "✅ Verifying deployment success"
    
    # Check application accessibility
    log INFO "Testing application accessibility..."
    local app_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" 2>/dev/null || echo "000")
    if [[ "$app_status" == "200" ]]; then
        log SUCCESS "Application accessible (HTTPS): HTTP $app_status"
    else
        log ERROR "Application not accessible (HTTPS): HTTP $app_status"
        return 1
    fi
    
    # Check API endpoint
    log INFO "Testing API endpoint..."
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" 2>/dev/null || echo "000")
    if [[ "$api_status" == "200" ]]; then
        log SUCCESS "API endpoint responding: HTTP $api_status"
    else
        log ERROR "API endpoint not responding: HTTP $api_status"
        return 1
    fi
    
    # Verify Pi deployment state
    log INFO "Verifying Pi deployment state..."
    if ssh "$PI_HOST" "cd $PI_PROJECT_PATH && git rev-parse HEAD" &>/dev/null; then
        local pi_commit=$(ssh "$PI_HOST" "cd $PI_PROJECT_PATH && git rev-parse HEAD")
        local local_commit=$(cd "$PROJECT_ROOT" && git rev-parse HEAD)
        
        if [[ "$pi_commit" == "$local_commit" ]]; then
            log SUCCESS "Pi is synchronized with local repository"
        else
            log WARNING "Pi commit ($pi_commit) differs from local ($local_commit)"
        fi
    fi
    
    log SUCCESS "Deployment verification completed"
}

# Dry run mode
dry_run() {
    log INFO "🔍 DRY RUN MODE - Showing what would be executed"
    echo ""
    echo "Enhanced Deployment Pipeline would execute:"
    echo "  1. 💾 Create local backups (dev data + settings)"
    echo "  2. 📤 Transfer backups to Pi"
    echo "  3. 📤 Push latest code to Pi"
    echo "  4. 🚀 Execute enhanced deployment on Pi with:"
    echo "     - Data synchronization from development to production"
    echo "     - Settings migration with production preservation"
    echo "     - Service restart and verification"
    echo "  5. ✅ Verify deployment success"
    echo ""
    echo "Pi deployment would:"
    echo "  - Backup current production database"
    echo "  - Synchronize business data from development"
    echo "  - Preserve production-specific settings"
    echo "  - Pull latest code and rebuild frontend"
    echo "  - Deploy to nginx and restart services"
    echo "  - Verify all systems operational"
    echo ""
    echo "Use without --dry-run to execute deployment"
}

# Main execution
main() {
    echo "========================================"
    echo "  KDT Enhanced Remote Deployment"
    echo "  $(date)"
    echo "========================================"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        dry_run
        exit 0
    fi
    
    # Execute deployment workflow
    if check_ssh_connectivity && \
       create_local_backups && \
       transfer_backups_to_pi && \
       push_code_to_pi && \
       execute_enhanced_deployment && \
       verify_deployment; then
        
        echo ""
        echo -e "${GREEN}🎉 Enhanced deployment pipeline completed successfully!${NC}"
        echo "   Frontend: https://ts.crtvmkmn.space"
        echo "   API: https://ts.crtvmkmn.space/api/v1"
        echo ""
        echo "✅ Data synchronization completed"
        echo "✅ Settings migration completed"
        echo "✅ Production environment updated"
        echo ""
        echo "Next steps:"
        echo "   - Test the application in your browser"
        echo "   - Verify data synchronization worked correctly"
        echo "   - Check that all features are working"
        exit 0
    else
        echo ""
        echo -e "${RED}💥 Enhanced deployment pipeline failed!${NC}"
        echo "   Check the logs for detailed error information"
        echo "   Use --dry-run to test the deployment process"
        exit 1
    fi
}

# Execute main function
main "$@"
