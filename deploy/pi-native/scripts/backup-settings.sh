#!/bin/bash

# KDT Settings Backup and Migration Script
# Backs up development settings and prepares them for production deployment
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
BACKUP_DIR="/tmp/kdt-settings-backup-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="/tmp/kdt-settings-backup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check if Docker is running
check_docker() {
    if ! docker ps &>/dev/null; then
        log ERROR "Docker is not running or accessible"
        exit 1
    fi
    
    if ! docker exec kdt-backend php --version &>/dev/null; then
        log ERROR "KDT backend container is not running"
        exit 1
    fi
    
    log SUCCESS "Docker environment is ready"
}

# Backup system settings from development database
backup_system_settings() {
    log INFO "📊 Backing up system settings from development database"

    mkdir -p "$BACKUP_DIR"
    # Set secure permissions on backup directory (owner read/write/execute only)
    chmod 700 "$BACKUP_DIR"
    
    # Export system settings to JSON
    docker exec kdt-backend php artisan tinker --execute="
        \$settings = \App\Models\SystemSetting::all(['key', 'value', 'type', 'description']);
        file_put_contents('/tmp/system_settings.json', \$settings->toJson(JSON_PRETTY_PRINT));
        echo 'Settings exported: ' . \$settings->count() . ' records';
    " > "$BACKUP_DIR/export.log" 2>&1
    
    # Copy the exported file from container
    docker cp kdt-backend:/tmp/system_settings.json "$BACKUP_DIR/system_settings.json"

    if [[ -f "$BACKUP_DIR/system_settings.json" ]]; then
        # Set secure permissions on backup file (owner read/write only)
        chmod 600 "$BACKUP_DIR/system_settings.json"
        local count=$(jq length "$BACKUP_DIR/system_settings.json")
        log SUCCESS "Exported $count system settings to backup"
    else
        log ERROR "Failed to export system settings"
        return 1
    fi

    # Clean up temporary file in container
    docker exec kdt-backend rm -f /tmp/system_settings.json
}

# Backup user data (for 2FA settings)
backup_user_data() {
    log INFO "👥 Backing up user data (2FA settings)"
    
    # Export user 2FA settings
    docker exec kdt-backend php artisan tinker --execute="
        \$users = \App\Models\User::all(['id', 'name', 'email', 'role', 'two_factor_enabled', 'is_active']);
        file_put_contents('/tmp/users_backup.json', \$users->toJson(JSON_PRETTY_PRINT));
        echo 'Users exported: ' . \$users->count() . ' records';
    " > "$BACKUP_DIR/users_export.log" 2>&1
    
    docker cp kdt-backend:/tmp/users_backup.json "$BACKUP_DIR/users_backup.json"

    if [[ -f "$BACKUP_DIR/users_backup.json" ]]; then
        # Set secure permissions on backup file (owner read/write only)
        chmod 600 "$BACKUP_DIR/users_backup.json"
        local count=$(jq length "$BACKUP_DIR/users_backup.json")
        log SUCCESS "Exported $count user records to backup"
    else
        log ERROR "Failed to export user data"
        return 1
    fi

    # Clean up temporary file in container
    docker exec kdt-backend rm -f /tmp/users_backup.json
}

# Create production-ready settings file
create_production_settings() {
    log INFO "🔧 Creating production-ready settings file"
    
    # Create a script to restore settings in production
    cat > "$BACKUP_DIR/restore_settings.php" << 'EOF'
<?php

/**
 * Production Settings Restoration Script
 * Run this script after deploying to production to restore development settings
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\SystemSetting;
use App\Models\User;

echo "🔧 Restoring KDT settings to production...\n";

// Load settings from backup
$settingsFile = __DIR__ . '/system_settings.json';
if (!file_exists($settingsFile)) {
    echo "❌ Settings backup file not found: $settingsFile\n";
    exit(1);
}

$settings = json_decode(file_get_contents($settingsFile), true);
if (!$settings) {
    echo "❌ Failed to parse settings backup file\n";
    exit(1);
}

echo "📊 Found " . count($settings) . " settings to restore\n";

// Restore settings
$restored = 0;
$skipped = 0;

foreach ($settings as $setting) {
    // Skip certain development-only settings
    $skipKeys = [
        'app_debug',
        'log_level',
        'mail_host',
        'mail_port',
        'mail_username',
        'mail_password'
    ];
    
    if (in_array($setting['key'], $skipKeys)) {
        echo "⏭️  Skipping development setting: {$setting['key']}\n";
        $skipped++;
        continue;
    }
    
    try {
        SystemSetting::updateOrCreate(
            ['key' => $setting['key']],
            [
                'value' => $setting['value'],
                'type' => $setting['type'],
                'description' => $setting['description']
            ]
        );
        
        echo "✅ Restored: {$setting['key']}\n";
        $restored++;
    } catch (Exception $e) {
        echo "❌ Failed to restore {$setting['key']}: " . $e->getMessage() . "\n";
    }
}

echo "\n📈 Restoration Summary:\n";
echo "   ✅ Restored: $restored settings\n";
echo "   ⏭️  Skipped: $skipped settings\n";

// Verify critical settings
echo "\n🔍 Verifying critical settings:\n";

$criticalSettings = [
    'two_factor_auth_enabled',
    'zoho_smtp_host',
    'zoho_smtp_username',
    'zoho_smtp_password'
];

foreach ($criticalSettings as $key) {
    $value = SystemSetting::get($key);
    if ($value !== null) {
        if ($key === 'zoho_smtp_password') {
            echo "   ✅ $key: [ENCRYPTED - " . strlen($value) . " chars]\n";
        } else {
            echo "   ✅ $key: $value\n";
        }
    } else {
        echo "   ❌ $key: NOT SET\n";
    }
}

echo "\n🎉 Settings restoration completed!\n";
echo "🔒 Remember to:\n";
echo "   1. Test email functionality\n";
echo "   2. Enable 2FA if desired\n";
echo "   3. Verify all settings in the admin panel\n";

EOF

    log SUCCESS "Created production settings restoration script"
}

# Create deployment checklist
create_deployment_checklist() {
    log INFO "📋 Creating deployment checklist"
    
    cat > "$BACKUP_DIR/DEPLOYMENT_CHECKLIST.md" << 'EOF'
# KDT Production Deployment Checklist

## Pre-Deployment (Development Environment)

- [x] Email settings configured and tested
- [x] 2FA settings configured
- [x] Settings backup created
- [ ] All code changes committed and pushed
- [ ] Frontend built and tested
- [ ] Database migrations tested

## Deployment Steps

1. **Backup Current Settings**
   ```bash
   ./deploy/pi-native/scripts/backup-settings.sh
   ```

2. **Deploy Code to Production**
   ```bash
   cd /home/<USER>/Apps/ts-crm
   ./deploy/pi-native/scripts/deploy.sh
   ```

3. **Restore Settings in Production**
   ```bash
   # Copy backup files to production
   scp -r /tmp/kdt-settings-backup-* pi@your-pi:/home/<USER>/Apps/ts-crm/
   
   # On production Pi
   cd /home/<USER>/Apps/ts-crm/backend
   php restore_settings.php
   ```

4. **Verify Production Environment**
   ```bash
   ./deploy/pi-native/scripts/verify-deployment.sh
   ```

## Post-Deployment Verification

- [ ] Application loads at https://ts.crtvmkmn.space
- [ ] Login functionality works
- [ ] Email settings preserved
- [ ] 2FA functionality works (if enabled)
- [ ] Database connections working
- [ ] All services running properly

## Security Checklist

- [ ] APP_DEBUG=false in production
- [ ] Strong database passwords set
- [ ] Email credentials properly encrypted
- [ ] 2FA enabled (if desired)
- [ ] HTTPS working properly
- [ ] Cloudflare tunnel configured

## Rollback Plan

If deployment fails:
```bash
./deploy/pi-native/scripts/deploy.sh --rollback
```

## Support

- Deployment logs: Check /tmp/kdt-deploy-*.log
- Settings backup: Available in backup directory
- Original settings: Preserved in development environment
EOF

    log SUCCESS "Created deployment checklist"
}

# Main backup function
main() {
    echo "========================================"
    echo "  KDT Settings Backup & Migration"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Starting settings backup process"
    log INFO "Backup directory: $BACKUP_DIR"
    log INFO "Log file: $LOG_FILE"
    
    # Execute backup steps
    check_docker
    backup_system_settings
    backup_user_data
    create_production_settings
    create_deployment_checklist
    
    # Create summary
    log SUCCESS "🎉 Settings backup completed successfully!"
    echo ""
    echo -e "${GREEN}📁 Backup Location:${NC} $BACKUP_DIR"
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "   1. Review the deployment checklist: $BACKUP_DIR/DEPLOYMENT_CHECKLIST.md"
    echo "   2. Copy backup to production server before deployment"
    echo "   3. Run the deployment script on production"
    echo "   4. Restore settings using the provided script"
    echo ""
    echo -e "${YELLOW}⚠️  Important:${NC} Keep this backup safe - it contains your encrypted email credentials!"
    
    # Save backup location for easy access
    echo "$BACKUP_DIR" > "/tmp/kdt-last-settings-backup"
}

# Execute main function
main "$@"
