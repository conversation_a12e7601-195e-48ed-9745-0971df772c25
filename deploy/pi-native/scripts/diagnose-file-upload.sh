#!/bin/bash

# File Upload Diagnostic Script for KDT Pi Deployment
# This script performs comprehensive diagnostics for file upload issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        ERROR)   echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        SUCCESS) echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
        WARNING) echo -e "${YELLOW}[WARNING]${NC} $timestamp - $message" ;;
        INFO)    echo -e "${BLUE}[INFO]${NC} $timestamp - $message" ;;
        DEBUG)   echo -e "${CYAN}[DEBUG]${NC} $timestamp - $message" ;;
    esac
}

print_header() {
    echo ""
    echo "=================================================="
    echo "  KDT File Upload Diagnostic Tool"
    echo "=================================================="
    echo ""
}

print_section() {
    echo ""
    echo -e "${CYAN}=== $1 ===${NC}"
    echo ""
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
BACKEND_PATH="$PROJECT_ROOT/backend"
LOG_PATH="$BACKEND_PATH/storage/logs"

print_header

log INFO "🔍 Starting comprehensive file upload diagnostics"
log INFO "Project root: $PROJECT_ROOT"
log INFO "Backend path: $BACKEND_PATH"

# Check 1: PHP Configuration
print_section "PHP Configuration Analysis"

log INFO "Checking PHP version and configuration..."
php_version=$(php -v | head -n 1)
echo "PHP Version: $php_version"

echo ""
echo "Critical PHP Settings:"
php -r "
echo 'upload_max_filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;
echo 'post_max_size: ' . ini_get('post_max_size') . PHP_EOL;
echo 'max_execution_time: ' . ini_get('max_execution_time') . PHP_EOL;
echo 'max_input_time: ' . ini_get('max_input_time') . PHP_EOL;
echo 'memory_limit: ' . ini_get('memory_limit') . PHP_EOL;
echo 'max_file_uploads: ' . ini_get('max_file_uploads') . PHP_EOL;
echo 'file_uploads: ' . (ini_get('file_uploads') ? 'On' : 'Off') . PHP_EOL;
echo 'tmp_upload_dir: ' . (ini_get('upload_tmp_dir') ?: 'Default') . PHP_EOL;
"

# Check 2: Nginx Configuration
print_section "Nginx Configuration Analysis"

log INFO "Checking nginx configuration..."
if sudo nginx -t &>/dev/null; then
    log SUCCESS "Nginx configuration is valid"
else
    log ERROR "Nginx configuration has errors"
    sudo nginx -t
fi

echo ""
echo "Checking client_max_body_size settings:"
if sudo nginx -T 2>/dev/null | grep -i "client_max_body_size"; then
    log SUCCESS "Found client_max_body_size settings"
else
    log WARNING "No client_max_body_size settings found"
fi

# Check 3: File System Permissions
print_section "File System Permissions"

log INFO "Checking critical directory permissions..."

directories=(
    "$BACKEND_PATH/storage"
    "$BACKEND_PATH/storage/app"
    "$BACKEND_PATH/storage/logs"
    "$BACKEND_PATH/storage/framework"
    "$BACKEND_PATH/storage/framework/cache"
    "$BACKEND_PATH/storage/framework/sessions"
    "$BACKEND_PATH/storage/framework/views"
    "/tmp"
)

for dir in "${directories[@]}"; do
    if [[ -d "$dir" ]]; then
        perms=$(stat -c "%a %U:%G" "$dir" 2>/dev/null || echo "unknown")
        echo "  $dir: $perms"
        
        # Check if writable
        if [[ -w "$dir" ]]; then
            echo -e "    ${GREEN}✓ Writable${NC}"
        else
            echo -e "    ${RED}✗ Not writable${NC}"
        fi
    else
        echo -e "  $dir: ${RED}Directory not found${NC}"
    fi
done

# Check 4: Disk Space
print_section "Disk Space Analysis"

log INFO "Checking available disk space..."
df -h /tmp
df -h "$BACKEND_PATH/storage"

# Check 5: Laravel Logs
print_section "Laravel Log Analysis"

log INFO "Checking Laravel application logs..."

if [[ -d "$LOG_PATH" ]]; then
    echo "Recent log files:"
    ls -la "$LOG_PATH"/*.log 2>/dev/null || echo "No log files found"
    
    echo ""
    echo "Recent errors related to file uploads:"
    if [[ -f "$LOG_PATH/laravel.log" ]]; then
        tail -50 "$LOG_PATH/laravel.log" | grep -i -E "(upload|file|422|validation|error)" || echo "No recent upload-related errors found"
    else
        echo "Laravel log file not found"
    fi
else
    log ERROR "Laravel logs directory not found: $LOG_PATH"
fi

# Check 6: PHP-FPM Configuration
print_section "PHP-FPM Configuration"

log INFO "Checking PHP-FPM pool configuration..."

if [[ -f "/etc/php/8.2/fpm/pool.d/kdt.conf" ]]; then
    echo "KDT PHP-FPM pool configuration:"
    grep -E "(upload_max_filesize|post_max_size|memory_limit|max_execution_time)" /etc/php/8.2/fpm/pool.d/kdt.conf || echo "No upload settings found in pool config"
else
    log WARNING "KDT PHP-FPM pool configuration not found"
fi

# Check 7: Service Status
print_section "Service Status"

log INFO "Checking service status..."

services=("nginx" "php8.2-fpm" "postgresql" "redis-kdt")

for service in "${services[@]}"; do
    if systemctl is-active "$service" &>/dev/null; then
        echo -e "  $service: ${GREEN}Active${NC}"
    else
        echo -e "  $service: ${RED}Inactive${NC}"
    fi
done

# Check 8: Test File Upload Capability
print_section "File Upload Test"

log INFO "Testing basic file upload capability..."

# Create a test CSV file
test_file="/tmp/test_upload.csv"
cat > "$test_file" << 'EOF'
name,email,phone
John Doe,<EMAIL>,+1234567890
Jane Smith,<EMAIL>,+0987654321
EOF

echo "Created test CSV file: $test_file"
echo "File size: $(stat -c%s "$test_file") bytes"

# Test if PHP can read the file
if php -r "
\$file = '$test_file';
if (is_readable(\$file)) {
    echo 'PHP can read test file: ' . filesize(\$file) . ' bytes' . PHP_EOL;
    \$content = file_get_contents(\$file);
    echo 'Content preview: ' . substr(\$content, 0, 100) . '...' . PHP_EOL;
} else {
    echo 'PHP cannot read test file' . PHP_EOL;
}
"; then
    log SUCCESS "PHP file reading test passed"
else
    log ERROR "PHP file reading test failed"
fi

# Cleanup
rm -f "$test_file"

# Check 9: Environment Configuration
print_section "Environment Configuration"

log INFO "Checking Laravel environment configuration..."

if [[ -f "$BACKEND_PATH/.env" ]]; then
    echo "Environment file exists"
    echo "APP_ENV: $(grep "^APP_ENV=" "$BACKEND_PATH/.env" | cut -d'=' -f2)"
    echo "APP_DEBUG: $(grep "^APP_DEBUG=" "$BACKEND_PATH/.env" | cut -d'=' -f2)"
    echo "LOG_CHANNEL: $(grep "^LOG_CHANNEL=" "$BACKEND_PATH/.env" | cut -d'=' -f2 || echo "default")"
else
    log ERROR "Laravel .env file not found"
fi

# Check 10: Laravel File Validation Test
print_section "Laravel File Validation Test"

log INFO "Testing Laravel file validation rules..."

# Test the specific validation rule that's failing
php -r "
require_once '$BACKEND_PATH/vendor/autoload.php';

// Test file validation
\$rules = [
    'file' => 'required|file|max:102400', // 100MB max
    'format' => 'string|in:csv,excel,json',
    'data_type' => 'string|in:clients,leads,deals,products,transactions'
];

echo 'Laravel validation rules:' . PHP_EOL;
foreach (\$rules as \$field => \$rule) {
    echo \"  \$field: \$rule\" . PHP_EOL;
}

// Check if file upload is enabled
if (ini_get('file_uploads')) {
    echo 'File uploads: Enabled' . PHP_EOL;
} else {
    echo 'File uploads: DISABLED - This is the problem!' . PHP_EOL;
}

// Check upload limits in bytes
\$upload_max = ini_get('upload_max_filesize');
\$post_max = ini_get('post_max_size');

function parse_size(\$size) {
    \$unit = preg_replace('/[^bkmgtpezy]/i', '', \$size);
    \$size = preg_replace('/[^0-9\.]/', '', \$size);
    if (\$unit) {
        return round(\$size * pow(1024, stripos('bkmgtpezy', \$unit[0])));
    } else {
        return round(\$size);
    }
}

\$upload_bytes = parse_size(\$upload_max);
\$post_bytes = parse_size(\$post_max);
\$laravel_max = 102400 * 1024; // 100MB in bytes

echo \"Upload limits comparison:\" . PHP_EOL;
echo \"  PHP upload_max_filesize: \$upload_max (\$upload_bytes bytes)\" . PHP_EOL;
echo \"  PHP post_max_size: \$post_max (\$post_bytes bytes)\" . PHP_EOL;
echo \"  Laravel validation max: 102400 KB (\$laravel_max bytes)\" . PHP_EOL;

if (\$upload_bytes < \$laravel_max) {
    echo \"  WARNING: PHP upload limit is smaller than Laravel limit!\" . PHP_EOL;
}
if (\$post_bytes < \$laravel_max) {
    echo \"  WARNING: PHP post limit is smaller than Laravel limit!\" . PHP_EOL;
}
"

# Check 11: Temporary Directory Test
print_section "Temporary Directory Test"

log INFO "Testing temporary directory access..."

temp_dirs=("/tmp" "$(php -r 'echo sys_get_temp_dir();')" "$(php -r 'echo ini_get(\"upload_tmp_dir\") ?: \"default\";')")

for temp_dir in "${temp_dirs[@]}"; do
    if [[ "$temp_dir" != "default" && -d "$temp_dir" ]]; then
        echo "Testing directory: $temp_dir"

        # Test write permission
        test_file="$temp_dir/kdt_upload_test_$$"
        if echo "test" > "$test_file" 2>/dev/null; then
            echo -e "  ${GREEN}✓ Writable${NC}"
            rm -f "$test_file"
        else
            echo -e "  ${RED}✗ Not writable${NC}"
        fi

        # Check space
        space=$(df "$temp_dir" | tail -1 | awk '{print $4}')
        echo "  Available space: ${space}KB"
    fi
done

# Summary and Recommendations
print_section "Summary and Recommendations"

echo "Diagnostic Summary:"
echo "=================="
echo ""
echo "MOST LIKELY CAUSES OF HTTP 422 'The file failed to upload' ERROR:"
echo ""
echo "1. 🔍 CHECK LARAVEL LOGS (MOST IMPORTANT):"
echo "   tail -f $LOG_PATH/laravel.log"
echo "   Look for specific validation errors or file upload failures"
echo ""
echo "2. 🔧 COMMON FIXES:"
echo "   a) Temporary directory permissions:"
echo "      sudo chmod 777 /tmp"
echo "      sudo chown www-data:www-data /tmp"
echo ""
echo "   b) Laravel storage permissions:"
echo "      sudo chown -R www-data:www-data $BACKEND_PATH/storage"
echo "      sudo chmod -R 775 $BACKEND_PATH/storage"
echo ""
echo "   c) Enable Laravel debug mode temporarily:"
echo "      Edit $BACKEND_PATH/.env and set APP_DEBUG=true"
echo "      Then restart PHP-FPM: sudo systemctl restart php8.2-fpm"
echo ""
echo "3. 🧪 TEST WITH CURL:"
echo "   curl -X POST -F 'file=@/path/to/test.csv' -F 'format=csv' \\"
echo "        -H 'Authorization: Bearer YOUR_TOKEN' \\"
echo "        https://ts.crtvmkmn.space/api/v1/csv-import/parse"
echo ""
echo "4. 📋 MONITOR LOGS IN REAL-TIME:"
echo "   # Terminal 1:"
echo "   tail -f $LOG_PATH/laravel.log"
echo "   # Terminal 2:"
echo "   sudo tail -f /var/log/php8.2-fpm.log"
echo "   # Terminal 3:"
echo "   sudo tail -f /var/log/nginx/error.log"
echo ""
echo "5. 🔍 CHECK PHP-FPM POOL CONFIGURATION:"
echo "   sudo nano /etc/php/8.2/fpm/pool.d/kdt.conf"
echo "   Ensure upload settings match your requirements"
echo ""

log SUCCESS "Diagnostic completed. Follow the recommendations above to resolve the issue."
