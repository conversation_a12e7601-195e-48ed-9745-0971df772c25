#!/bin/bash

# Fix Import Timeout Issues for Large CSV Files
# This script updates timeout configurations for handling large datasets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        ERROR)   echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        SUCCESS) echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
        WARNING) echo -e "${YELLOW}[WARNING]${NC} $timestamp - $message" ;;
        INFO)    echo -e "${BLUE}[INFO]${NC} $timestamp - $message" ;;
        DEBUG)   echo -e "${NC}[DEBUG]${NC} $timestamp - $message" ;;
    esac
}

print_header() {
    echo ""
    echo "=================================================="
    echo "  KDT Large CSV Import Timeout Fix"
    echo "=================================================="
    echo ""
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

print_header

log INFO "🔧 Fixing timeout configurations for large CSV imports"
log INFO "Target: Support imports up to 30 minutes (1800 seconds)"

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    log WARNING "Running as root. This is not recommended."
elif ! sudo -n true 2>/dev/null; then
    log INFO "This script requires sudo privileges for configuration updates"
    echo "You may be prompted for your password..."
fi

# Backup current configurations
log INFO "📦 Creating backup of current configurations"
backup_dir="/tmp/kdt-timeout-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$backup_dir"

sudo cp /etc/nginx/sites-available/kdt-frontend.conf "$backup_dir/" 2>/dev/null || true
sudo cp /etc/nginx/sites-available/kdt-backend.conf "$backup_dir/" 2>/dev/null || true
sudo cp /etc/php/8.2/fpm/pool.d/kdt-pool.conf "$backup_dir/" 2>/dev/null || true

log INFO "Backup created at: $backup_dir"

# Update nginx configurations
log INFO "📋 Updating nginx configurations"
sudo cp "$PROJECT_ROOT/deploy/pi-native/nginx/kdt-frontend.conf" /etc/nginx/sites-available/
sudo cp "$PROJECT_ROOT/deploy/pi-native/nginx/kdt-backend.conf" /etc/nginx/sites-available/

# Update PHP-FPM pool configuration
log INFO "🐘 Updating PHP-FPM pool configuration"
sudo cp "$PROJECT_ROOT/deploy/pi-native/php/kdt-pool.conf" /etc/php/8.2/fpm/pool.d/

# Test nginx configuration
log INFO "🧪 Testing nginx configuration"
if sudo nginx -t; then
    log SUCCESS "Nginx configuration test passed"
else
    log ERROR "Nginx configuration test failed"
    log INFO "Restoring backup configurations..."
    
    # Restore backups
    sudo cp "$backup_dir/kdt-frontend.conf" /etc/nginx/sites-available/ 2>/dev/null || true
    sudo cp "$backup_dir/kdt-backend.conf" /etc/nginx/sites-available/ 2>/dev/null || true
    sudo cp "$backup_dir/kdt-pool.conf" /etc/php/8.2/fpm/pool.d/ 2>/dev/null || true
    
    log ERROR "Configuration update failed. Backups restored."
    exit 1
fi

# Restart services
log INFO "🔄 Restarting services"

# Restart PHP-FPM first
if sudo systemctl restart php8.2-fpm; then
    log SUCCESS "PHP-FPM restarted successfully"
else
    log ERROR "Failed to restart PHP-FPM"
    exit 1
fi

# Reload nginx
if sudo systemctl reload nginx; then
    log SUCCESS "Nginx reloaded successfully"
else
    log ERROR "Failed to reload nginx"
    exit 1
fi

# Verify the changes
log INFO "✅ Verifying configuration changes"

# Check nginx timeouts
if sudo nginx -T 2>/dev/null | grep -q "proxy_read_timeout.*1800"; then
    log SUCCESS "Nginx proxy timeouts updated to 1800 seconds (30 minutes)"
else
    log WARNING "Could not verify nginx proxy timeouts"
fi

if sudo nginx -T 2>/dev/null | grep -q "fastcgi_read_timeout.*1800"; then
    log SUCCESS "Nginx FastCGI timeouts updated to 1800 seconds (30 minutes)"
else
    log WARNING "Could not verify nginx FastCGI timeouts"
fi

# Check PHP-FPM configuration
if grep -q "max_execution_time.*1800" /etc/php/8.2/fpm/pool.d/kdt-pool.conf 2>/dev/null; then
    log SUCCESS "PHP-FPM execution time updated to 1800 seconds (30 minutes)"
else
    log WARNING "Could not verify PHP-FPM execution time"
fi

if grep -q "memory_limit.*512M" /etc/php/8.2/fpm/pool.d/kdt-pool.conf 2>/dev/null; then
    log SUCCESS "PHP-FPM memory limit increased to 512MB"
else
    log WARNING "Could not verify PHP-FPM memory limit"
fi

# Check service status
log INFO "🔍 Checking service status"
if systemctl is-active nginx &>/dev/null; then
    log SUCCESS "Nginx service is running"
else
    log ERROR "Nginx service is not running"
    exit 1
fi

if systemctl is-active php8.2-fpm &>/dev/null; then
    log SUCCESS "PHP-FPM service is running"
else
    log ERROR "PHP-FPM service is not running"
    exit 1
fi

echo ""
echo "=================================================="
log SUCCESS "Timeout configuration update completed!"
echo ""
echo "Changes made:"
echo "  ✅ Nginx proxy timeouts: 300s → 1800s (30 minutes)"
echo "  ✅ Nginx FastCGI timeouts: 300s → 1800s (30 minutes)"
echo "  ✅ PHP-FPM execution time: 300s → 1800s (30 minutes)"
echo "  ✅ PHP-FPM memory limit: 256MB → 512MB"
echo "  ✅ PHP-FPM input time: 300s → 1800s (30 minutes)"
echo ""
echo "Recommendations for large CSV imports:"
echo "  📊 Use smaller batch sizes (50-100 records per batch)"
echo "  ⏱️  Imports up to 30 minutes are now supported"
echo "  💾 Memory limit increased to handle larger datasets"
echo ""
echo "Your 26,711 row CSV import should now complete successfully!"
echo "=================================================="
