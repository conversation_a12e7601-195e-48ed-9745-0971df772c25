#!/bin/bash

# KDT Native Installation Script for Raspberry Pi
# This script installs and configures KDT application natively on Raspberry Pi

set -e  # Exit on any error

echo "🚀 Starting KDT Native Installation on Raspberry Pi..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/home/<USER>/Apps/ts-crm"
DB_NAME="kdt_production"
DB_USER="kdt_user"

# Generate secure random passwords for production
# These will be unique for each installation and never stored in git
DB_PASSWORD="${KDT_DB_PASSWORD:-$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-25)}"
REDIS_PASSWORD="${KDT_REDIS_PASSWORD:-$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-25)}"

# Save passwords to secure file for reference (readable only by owner)
CREDENTIALS_FILE="/home/<USER>/.kdt-credentials"
echo "# KDT Production Credentials - Generated $(date)" > "$CREDENTIALS_FILE"
echo "# Keep this file secure and backed up" >> "$CREDENTIALS_FILE"
echo "KDT_DB_PASSWORD=\"$DB_PASSWORD\"" >> "$CREDENTIALS_FILE"
echo "KDT_REDIS_PASSWORD=\"$REDIS_PASSWORD\"" >> "$CREDENTIALS_FILE"
chmod 600 "$CREDENTIALS_FILE"

print_status "Generated secure credentials and saved to $CREDENTIALS_FILE"
print_status "Database password: ${#DB_PASSWORD} characters"
print_status "Redis password: ${#REDIS_PASSWORD} characters"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as correct user
if [ "$USER" != "zulhelminasir" ]; then
    print_error "This script must be run as user 'zulhelminasir'"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "deploy/pi-native/scripts/install.sh" ]; then
    print_error "Please run this script from the KDT project root directory"
    exit 1
fi

print_status "Step 1: Updating system packages..."
sudo apt update && sudo apt upgrade -y

print_status "Step 2: Installing required system packages..."
sudo apt install -y \
    nginx \
    postgresql-15 \
    postgresql-contrib \
    redis-server \
    php8.2 \
    php8.2-fpm \
    php8.2-cli \
    php8.2-pgsql \
    php8.2-mbstring \
    php8.2-xml \
    php8.2-curl \
    php8.2-zip \
    php8.2-gd \
    php8.2-bcmath \
    php8.2-redis \
    nodejs \
    npm \
    git \
    curl \
    unzip

print_success "System packages installed successfully"

print_status "Step 3: Installing Composer..."
if ! command -v composer &> /dev/null; then
    curl -sS https://getcomposer.org/installer | php
    sudo mv composer.phar /usr/local/bin/composer
    sudo chmod +x /usr/local/bin/composer
fi
print_success "Composer installed"

print_status "Step 4: Configuring PostgreSQL..."
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql -f deploy/pi-native/postgresql/init-kdt-db.sql

print_success "PostgreSQL configured"

print_status "Step 5: Configuring Redis..."
sudo cp deploy/pi-native/redis/redis-kdt.conf /etc/redis/redis-kdt.conf
sudo mkdir -p /var/lib/redis-kdt
sudo chown redis:redis /var/lib/redis-kdt
sudo mkdir -p /var/log/redis
sudo chown redis:redis /var/log/redis

# Create Redis systemd service
sudo tee /etc/systemd/system/redis-kdt.service > /dev/null << EOF
[Unit]
Description=Redis KDT Instance
After=network.target

[Service]
Type=notify
ExecStart=/usr/bin/redis-server /etc/redis/redis-kdt.conf
TimeoutStopSec=0
Restart=always
User=redis
Group=redis

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl start redis-kdt
sudo systemctl enable redis-kdt

print_success "Redis configured on port 4003"

print_status "Step 6: Configuring PHP-FPM..."
sudo cp deploy/pi-native/php/kdt-pool.conf /etc/php/8.2/fpm/pool.d/
sudo systemctl restart php8.2-fpm
sudo systemctl enable php8.2-fpm

print_success "PHP-FPM configured"

print_status "Step 7: Configuring Nginx..."
sudo cp deploy/pi-native/nginx/kdt-frontend.conf /etc/nginx/sites-available/
sudo cp deploy/pi-native/nginx/kdt-backend.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/kdt-frontend.conf /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/kdt-backend.conf /etc/nginx/sites-enabled/

# Remove default site
sudo rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl enable nginx

print_success "Nginx configured for ports 4000 (frontend) and 4001 (backend)"

print_status "Step 8: Installing Laravel dependencies..."
cd backend
composer install --no-dev --optimize-autoloader
cd ..

print_success "Laravel dependencies installed"

print_status "Step 9: Setting up Laravel environment..."
cp backend/.env.example backend/.env.production
# The configure-app.sh script will handle the .env configuration

print_success "Installation completed! Run configure-app.sh next."

print_status "Making scripts executable..."
chmod +x deploy/pi-native/scripts/*.sh

print_success "All scripts are ready!"
print_status "Next steps:"
print_status "1. Run: ./deploy/pi-native/scripts/configure-app.sh"
print_status "2. Run: ./deploy/pi-native/scripts/build-frontend.sh"
print_status "3. Run: ./deploy/pi-native/scripts/verify-deployment.sh"
