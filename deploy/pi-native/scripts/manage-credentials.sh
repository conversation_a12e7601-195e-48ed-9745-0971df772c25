#!/bin/bash

# KDT Secure Credentials Management Script
# Generates, stores, and retrieves production credentials securely
# Author: KDT Development Team

set -euo pipefail

# Configuration
CREDENTIALS_FILE="/home/<USER>/.kdt-credentials"
BACKUP_CREDENTIALS_FILE="/home/<USER>/.kdt-credentials.backup"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# Generate secure random password
generate_password() {
    local length=${1:-25}
    openssl rand -base64 32 | tr -d '=+/' | cut -c1-$length
}

# Create new credentials
create_credentials() {
    log INFO "🔐 Generating new production credentials"
    
    # Backup existing credentials if they exist
    if [[ -f "$CREDENTIALS_FILE" ]]; then
        cp "$CREDENTIALS_FILE" "$BACKUP_CREDENTIALS_FILE"
        log INFO "Backed up existing credentials to $BACKUP_CREDENTIALS_FILE"
    fi
    
    # Generate new passwords
    local db_password=$(generate_password 25)
    local redis_password=$(generate_password 25)
    
    # Create credentials file
    cat > "$CREDENTIALS_FILE" << EOF
# KDT Production Credentials - Generated $(date)
# Keep this file secure and backed up
# File permissions: 600 (owner read/write only)

# Database credentials
export KDT_DB_PASSWORD="$db_password"

# Redis credentials  
export KDT_REDIS_PASSWORD="$redis_password"

# Usage: source this file to load credentials into environment
# Example: source ~/.kdt-credentials
EOF

    # Set secure permissions
    chmod 600 "$CREDENTIALS_FILE"
    
    log SUCCESS "Generated new credentials:"
    log SUCCESS "  Database password: ${#db_password} characters"
    log SUCCESS "  Redis password: ${#redis_password} characters"
    log SUCCESS "  Saved to: $CREDENTIALS_FILE"
    log WARNING "Keep this file secure and create a backup!"
}

# Load existing credentials
load_credentials() {
    if [[ ! -f "$CREDENTIALS_FILE" ]]; then
        log ERROR "Credentials file not found: $CREDENTIALS_FILE"
        log INFO "Run: $0 create"
        return 1
    fi
    
    source "$CREDENTIALS_FILE"
    
    if [[ -z "${KDT_DB_PASSWORD:-}" ]] || [[ -z "${KDT_REDIS_PASSWORD:-}" ]]; then
        log ERROR "Invalid credentials file format"
        return 1
    fi
    
    log SUCCESS "Credentials loaded successfully"
    return 0
}

# Show credentials status
show_status() {
    log INFO "📊 Credentials Status"
    
    if [[ -f "$CREDENTIALS_FILE" ]]; then
        local file_perms=$(stat -c "%a" "$CREDENTIALS_FILE")
        local file_size=$(stat -c "%s" "$CREDENTIALS_FILE")
        local file_date=$(stat -c "%y" "$CREDENTIALS_FILE")
        
        echo "  File: $CREDENTIALS_FILE"
        echo "  Permissions: $file_perms $([ "$file_perms" = "600" ] && echo "✅ SECURE" || echo "⚠️ INSECURE")"
        echo "  Size: $file_size bytes"
        echo "  Modified: $file_date"
        
        if load_credentials 2>/dev/null; then
            echo "  Database password: ${#KDT_DB_PASSWORD} characters ✅"
            echo "  Redis password: ${#KDT_REDIS_PASSWORD} characters ✅"
        else
            echo "  Status: ❌ INVALID FORMAT"
        fi
    else
        echo "  Status: ❌ NOT FOUND"
        log WARNING "No credentials file found. Run: $0 create"
    fi
    
    if [[ -f "$BACKUP_CREDENTIALS_FILE" ]]; then
        echo "  Backup: ✅ Available at $BACKUP_CREDENTIALS_FILE"
    else
        echo "  Backup: ⚠️ No backup found"
    fi
}

# Test credentials
test_credentials() {
    log INFO "🧪 Testing credentials"
    
    if ! load_credentials; then
        return 1
    fi
    
    # Test database connection (if PostgreSQL is running)
    if systemctl is-active postgresql &>/dev/null; then
        log INFO "Testing database connection..."
        if PGPASSWORD="$KDT_DB_PASSWORD" psql -h 127.0.0.1 -p 4002 -U kdt_user -d kdt_production -c "SELECT 1;" &>/dev/null; then
            log SUCCESS "Database connection: ✅ WORKING"
        else
            log WARNING "Database connection: ⚠️ FAILED (may not be configured yet)"
        fi
    else
        log INFO "PostgreSQL not running - skipping database test"
    fi
    
    # Test Redis connection (if Redis is running)
    if systemctl is-active redis-server &>/dev/null; then
        log INFO "Testing Redis connection..."
        if redis-cli -p 4003 -a "$KDT_REDIS_PASSWORD" ping 2>/dev/null | grep -q "PONG"; then
            log SUCCESS "Redis connection: ✅ WORKING"
        else
            log WARNING "Redis connection: ⚠️ FAILED (may not be configured yet)"
        fi
    else
        log INFO "Redis not running - skipping Redis test"
    fi
}

# Show usage
show_usage() {
    echo "KDT Secure Credentials Management"
    echo "Usage: $0 {create|load|status|test|help}"
    echo ""
    echo "Commands:"
    echo "  create  - Generate new production credentials"
    echo "  load    - Load existing credentials into environment"
    echo "  status  - Show credentials file status"
    echo "  test    - Test credentials against services"
    echo "  help    - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 create                    # Generate new credentials"
    echo "  source <($0 load)           # Load credentials into current shell"
    echo "  $0 status                   # Check credentials status"
}

# Main function
main() {
    case "${1:-help}" in
        create)
            create_credentials
            ;;
        load)
            if load_credentials; then
                echo "export KDT_DB_PASSWORD=\"$KDT_DB_PASSWORD\""
                echo "export KDT_REDIS_PASSWORD=\"$KDT_REDIS_PASSWORD\""
            fi
            ;;
        status)
            show_status
            ;;
        test)
            test_credentials
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            log ERROR "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
