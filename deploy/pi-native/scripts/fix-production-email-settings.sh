#!/bin/bash

# Fix Production Email Settings Script
# Updates production database with correct Zoho SMTP configuration
# Run this script on the Raspberry Pi production server

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PRODUCTION_PATH="/home/<USER>/Apps/ts-crm"
LOG_FILE="/tmp/fix-email-settings-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check if running on production server
check_production_environment() {
    log INFO "🔍 Checking production environment"
    
    if [[ ! -d "$PRODUCTION_PATH" ]]; then
        log ERROR "Production path not found: $PRODUCTION_PATH"
        log ERROR "This script should be run on the production Raspberry Pi server"
        exit 1
    fi
    
    if [[ ! -f "$PRODUCTION_PATH/backend/artisan" ]]; then
        log ERROR "Laravel backend not found in production path"
        exit 1
    fi
    
    log SUCCESS "Production environment validated"
}

# Update database settings directly
update_database_settings() {
    log INFO "📝 Updating database email settings"
    
    cd "$PRODUCTION_PATH/backend"
    
    # Create PHP script to update settings
    cat > /tmp/update_email_settings.php << 'EOF'
<?php
require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SystemSetting;
use Illuminate\Support\Facades\DB;

echo "🔧 Updating Production Email Settings...\n\n";

try {
    DB::beginTransaction();

    // Correct email <NAME_EMAIL>
    $emailSettings = [
        // Zoho SMTP Settings (both old and new keys)
        'zoho_smtp_host' => 'smtp.zoho.com',
        'smtp_host' => 'smtp.zoho.com',
        'zoho_smtp_port' => 587,
        'smtp_port' => 587,
        'zoho_smtp_username' => '<EMAIL>',
        'smtp_username' => '<EMAIL>',
        'zoho_smtp_password' => 'PR411zmASdma',
        'smtp_password' => 'PR411zmASdma',
        'zoho_smtp_encryption' => 'tls',
        'smtp_encryption' => 'tls',
        
        // Email Sender Settings
        'email_from_address' => '<EMAIL>',
        'email_from_name' => 'Tarbiah Sentap CRM',
        'email_reply_to' => '<EMAIL>',
        
        // System Settings
        'two_factor_auth_enabled' => false,
        'app_debug' => false,
    ];

    echo "📝 Updating system settings...\n";
    
    foreach ($emailSettings as $key => $value) {
        $type = 'string';
        $description = '';
        
        // Set appropriate types and descriptions
        switch ($key) {
            case 'zoho_smtp_port':
            case 'smtp_port':
                $type = 'integer';
                $description = 'SMTP port number';
                break;
            case 'two_factor_auth_enabled':
                $type = 'boolean';
                $description = 'Enable or disable two-factor authentication system-wide';
                break;
            case 'app_debug':
                $type = 'boolean';
                $description = 'Application debug mode';
                break;
            case 'zoho_smtp_host':
            case 'smtp_host':
                $description = 'SMTP server hostname';
                break;
            case 'zoho_smtp_username':
            case 'smtp_username':
                $description = 'SMTP username (email address)';
                break;
            case 'zoho_smtp_password':
            case 'smtp_password':
                $description = 'SMTP password or app password';
                break;
            case 'zoho_smtp_encryption':
            case 'smtp_encryption':
                $description = 'SMTP encryption method';
                break;
            case 'email_from_address':
                $description = 'Default sender email address';
                break;
            case 'email_from_name':
                $description = 'Default sender name';
                break;
            case 'email_reply_to':
                $description = 'Default reply-to email address';
                break;
        }
        
        SystemSetting::set($key, $value, $type, $description);
        echo "  ✅ {$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
    }

    DB::commit();
    
    echo "\n🎉 Email settings updated successfully!\n\n";
    
    // Verify settings
    echo "📋 Current email configuration:\n";
    echo "  SMTP Host: " . SystemSetting::get('smtp_host') . "\n";
    echo "  SMTP Port: " . SystemSetting::get('smtp_port') . "\n";
    echo "  SMTP Username: " . SystemSetting::get('smtp_username') . "\n";
    echo "  SMTP Password: " . (SystemSetting::get('smtp_password') ? '[SET]' : '[NOT SET]') . "\n";
    echo "  From Address: " . SystemSetting::get('email_from_address') . "\n";
    echo "  From Name: " . SystemSetting::get('email_from_name') . "\n";
    echo "  2FA Enabled: " . (SystemSetting::get('two_factor_auth_enabled') ? 'true' : 'false') . "\n";
    echo "  Debug Mode: " . (SystemSetting::get('app_debug') ? 'true' : 'false') . "\n";
    
    echo "\n✅ Configuration complete!\n\n";

} catch (Exception $e) {
    DB::rollBack();
    echo "❌ Error updating settings: " . $e->getMessage() . "\n";
    exit(1);
}
EOF

    # Run the PHP script
    if php /tmp/update_email_settings.php; then
        log SUCCESS "Database settings updated successfully"
        rm -f /tmp/update_email_settings.php
    else
        log ERROR "Failed to update database settings"
        rm -f /tmp/update_email_settings.php
        exit 1
    fi
}

# Clear Laravel caches
clear_caches() {
    log INFO "🧹 Clearing Laravel caches"
    
    cd "$PRODUCTION_PATH/backend"
    
    # Clear various caches
    php artisan config:clear
    php artisan cache:clear
    php artisan route:clear
    php artisan view:clear
    
    log SUCCESS "Caches cleared"
}

# Restart services
restart_services() {
    log INFO "🔄 Restarting services"
    
    # Restart PHP-FPM
    if sudo systemctl restart php8.2-fpm; then
        log SUCCESS "PHP-FPM restarted"
    else
        log ERROR "Failed to restart PHP-FPM"
        exit 1
    fi
    
    # Reload Nginx
    if sudo systemctl reload nginx; then
        log SUCCESS "Nginx reloaded"
    else
        log ERROR "Failed to reload Nginx"
        exit 1
    fi
}

# Test email configuration
test_email_config() {
    log INFO "📧 Testing email configuration"
    
    cd "$PRODUCTION_PATH/backend"
    
    # Create test email script
    cat > /tmp/test_email.php << 'EOF'
<?php
require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Mail;
use App\Models\SystemSetting;

echo "🧪 Testing email configuration...\n";

try {
    // Get current settings
    $fromEmail = SystemSetting::get('email_from_address', '<EMAIL>');
    $fromName = SystemSetting::get('email_from_name', 'Tarbiah Sentap CRM');
    
    echo "From: {$fromName} <{$fromEmail}>\n";
    echo "To: <EMAIL>\n";
    echo "Subject: Email Configuration Test\n\n";
    
    // Send test email
    Mail::raw('Email configuration test successful - ' . date('Y-m-d H:i:s'), function($message) use ($fromEmail, $fromName) {
        $message->to('<EMAIL>')
                ->subject('Email Configuration Test - Production Fixed')
                ->from($fromEmail, $fromName);
    });
    
    echo "✅ Test email sent successfully!\n";
    echo "Check <EMAIL> for the test email.\n";
    
} catch (Exception $e) {
    echo "❌ Email test failed: " . $e->getMessage() . "\n";
    exit(1);
}
EOF

    # Run the test
    if php /tmp/test_email.php; then
        log SUCCESS "Email test completed"
        rm -f /tmp/test_email.php
    else
        log ERROR "Email test failed"
        rm -f /tmp/test_email.php
        return 1
    fi
}

# Main function
main() {
    echo "========================================"
    echo "  Production Email Settings Fix"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "🚀 Starting production email settings fix"
    log INFO "Log file: $LOG_FILE"
    
    # Execute fix steps
    check_production_environment
    update_database_settings
    clear_caches
    restart_services
    
    # Test the configuration
    if test_email_config; then
        log SUCCESS "🎉 Email configuration fix completed successfully!"
        echo ""
        echo "Next steps:"
        echo "1. Build and deploy frontend changes: npm run build"
        echo "2. Test 2FA login process"
        echo "3. Verify email settings in <NAME_EMAIL>"
        echo ""
        echo "Frontend should now show correct default values:"
        echo "- SMTP Username: <EMAIL>"
        echo "- From Address: <EMAIL>"
        echo "- Reply-To Address: <EMAIL>"
    else
        log WARNING "Email configuration updated but test failed"
        echo "Please check email settings manually"
    fi
    
    echo ""
    echo "Log file: $LOG_FILE"
}

# Execute main function
main "$@"
