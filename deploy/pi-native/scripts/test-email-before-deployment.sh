#!/bin/bash

# KDT Email Testing Script
# Tests email functionality before production deployment
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test email address
TEST_EMAIL=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --email)
            TEST_EMAIL="$2"
            shift 2
            ;;
        --help|-h)
            echo "KDT Email Testing Script"
            echo "Usage: $0 --email <EMAIL>"
            echo ""
            echo "This script tests email functionality before production deployment."
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

if [[ -z "$TEST_EMAIL" ]]; then
    echo -e "${RED}Error: Email address is required${NC}"
    echo "Usage: $0 --email <EMAIL>"
    exit 1
fi

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# Check if Docker is running
check_docker() {
    if ! docker ps &>/dev/null; then
        log ERROR "Docker is not running or accessible"
        exit 1
    fi
    
    if ! docker exec kdt-backend php --version &>/dev/null; then
        log ERROR "KDT backend container is not running"
        exit 1
    fi
    
    log SUCCESS "Docker environment is ready"
}

# Check email settings
check_email_settings() {
    log INFO "📧 Checking email configuration"
    
    local settings_output=$(docker exec kdt-backend php artisan tinker --execute="
        echo 'SMTP Host: ' . \App\Models\SystemSetting::get('zoho_smtp_host', 'not set') . PHP_EOL;
        echo 'SMTP Port: ' . \App\Models\SystemSetting::get('zoho_smtp_port', 'not set') . PHP_EOL;
        echo 'SMTP User: ' . \App\Models\SystemSetting::get('zoho_smtp_username', 'not set') . PHP_EOL;
        echo 'SMTP Encryption: ' . \App\Models\SystemSetting::get('zoho_smtp_encryption', 'not set') . PHP_EOL;
        \$password = \App\Models\SystemSetting::get('zoho_smtp_password', '');
        echo 'Password Set: ' . (strlen(\$password) > 0 ? 'Yes (' . strlen(\$password) . ' chars)' : 'No') . PHP_EOL;
    " 2>/dev/null)
    
    echo "$settings_output"
    
    # Check if all required settings are present
    if echo "$settings_output" | grep -q "not set"; then
        log ERROR "Some email settings are missing"
        return 1
    fi
    
    if echo "$settings_output" | grep -q "Password Set: No"; then
        log ERROR "Email password is not set"
        return 1
    fi
    
    log SUCCESS "All email settings are configured"
}

# Test email sending
test_email_sending() {
    log INFO "📤 Testing email sending to: $TEST_EMAIL"
    
    local test_result=$(docker exec kdt-backend php artisan tinker --execute="
        try {
            // Get email settings
            \$fromEmail = \App\Models\SystemSetting::get('email_from_address', '<EMAIL>');
            \$fromName = \App\Models\SystemSetting::get('email_from_name', 'Tarbiah Sentap CRM');
            
            // Configure mail settings
            \$smtpHost = \App\Models\SystemSetting::get('zoho_smtp_host', 'smtp.zoho.com');
            \$smtpPort = \App\Models\SystemSetting::get('zoho_smtp_port', 587);
            \$smtpUsername = \App\Models\SystemSetting::get('zoho_smtp_username', '');
            \$smtpEncryption = \App\Models\SystemSetting::get('zoho_smtp_encryption', 'tls');
            \$encryptedPassword = \App\Models\SystemSetting::get('zoho_smtp_password', '');
            
            \$smtpPassword = \$encryptedPassword;
            try {
                if (strlen(\$encryptedPassword) > 20) {
                    \$smtpPassword = decrypt(\$encryptedPassword);
                }
            } catch (Exception \$e) {
                \$smtpPassword = \$encryptedPassword;
            }
            
            // Configure mail transport
            config([
                'mail.mailers.smtp.host' => \$smtpHost,
                'mail.mailers.smtp.port' => \$smtpPort,
                'mail.mailers.smtp.username' => \$smtpUsername,
                'mail.mailers.smtp.password' => \$smtpPassword,
                'mail.mailers.smtp.encryption' => \$smtpEncryption,
                'mail.from.address' => \$fromEmail,
                'mail.from.name' => \$fromName,
            ]);
            
            // Send test email
            Mail::raw('This is a test email from KDT CRM system before production deployment.\n\nIf you receive this email, the email configuration is working correctly and ready for production deployment.\n\nTimestamp: ' . now()->toDateTimeString(), function (\$message) {
                \$message->to('$TEST_EMAIL')
                        ->subject('KDT CRM - Pre-Deployment Email Test');
            });
            
            echo 'SUCCESS: Test email sent successfully';
        } catch (Exception \$e) {
            echo 'ERROR: ' . \$e->getMessage();
        }
    " 2>&1)
    
    if echo "$test_result" | grep -q "SUCCESS:"; then
        log SUCCESS "Test email sent successfully to $TEST_EMAIL"
        log INFO "Please check your inbox (and spam folder) for the test email"
        return 0
    else
        log ERROR "Failed to send test email"
        echo "Error details: $test_result"
        return 1
    fi
}

# Test 2FA code generation
test_2fa_functionality() {
    log INFO "🔒 Testing 2FA functionality"
    
    local tfa_test=$(docker exec kdt-backend php artisan tinker --execute="
        try {
            // Check if 2FA is enabled
            \$systemEnabled = \App\Models\SystemSetting::get('two_factor_auth_enabled', false);
            echo '2FA System Status: ' . (\$systemEnabled ? 'Enabled' : 'Disabled') . PHP_EOL;
            
            // Get a test user
            \$user = \App\Models\User::first();
            if (\$user) {
                echo 'Test User: ' . \$user->email . PHP_EOL;
                
                // Test code generation
                \$code = \$user->generateTwoFactorCode();
                echo 'Generated 2FA Code: ' . \$code . PHP_EOL;
                echo 'Code Expires: ' . \$user->two_factor_expires_at . PHP_EOL;
                
                // Test code verification
                \$isValid = \$user->verifyTwoFactorCode(\$code);
                echo 'Code Verification: ' . (\$isValid ? 'Success' : 'Failed') . PHP_EOL;
                
                echo 'SUCCESS: 2FA functionality working';
            } else {
                echo 'ERROR: No users found for testing';
            }
        } catch (Exception \$e) {
            echo 'ERROR: ' . \$e->getMessage();
        }
    " 2>&1)
    
    echo "$tfa_test"
    
    if echo "$tfa_test" | grep -q "SUCCESS:"; then
        log SUCCESS "2FA functionality is working correctly"
        return 0
    else
        log ERROR "2FA functionality test failed"
        return 1
    fi
}

# Generate pre-deployment report
generate_report() {
    log INFO "📊 Generating pre-deployment report"
    
    local report_file="/tmp/kdt-pre-deployment-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
KDT CRM Pre-Deployment Email Test Report
========================================
Generated: $(date)
Test Email: $TEST_EMAIL

Email Configuration Status:
$(docker exec kdt-backend php artisan tinker --execute="
echo 'SMTP Host: ' . \App\Models\SystemSetting::get('zoho_smtp_host', 'not set') . PHP_EOL;
echo 'SMTP Port: ' . \App\Models\SystemSetting::get('zoho_smtp_port', 'not set') . PHP_EOL;
echo 'SMTP User: ' . \App\Models\SystemSetting::get('zoho_smtp_username', 'not set') . PHP_EOL;
echo 'SMTP Encryption: ' . \App\Models\SystemSetting::get('zoho_smtp_encryption', 'not set') . PHP_EOL;
\$password = \App\Models\SystemSetting::get('zoho_smtp_password', '');
echo 'Password Set: ' . (strlen(\$password) > 0 ? 'Yes (' . strlen(\$password) . ' chars)' : 'No') . PHP_EOL;
echo '2FA Enabled: ' . (\App\Models\SystemSetting::get('two_factor_auth_enabled', false) ? 'Yes' : 'No') . PHP_EOL;
echo 'Total Settings: ' . \App\Models\SystemSetting::count() . PHP_EOL;
echo 'Total Users: ' . \App\Models\User::count() . PHP_EOL;
" 2>/dev/null)

System Status:
- Docker: Running
- Backend Container: Running
- Database: Connected
- Email Test: $(test_email_sending &>/dev/null && echo "PASSED" || echo "FAILED")
- 2FA Test: $(test_2fa_functionality &>/dev/null && echo "PASSED" || echo "FAILED")

Deployment Readiness:
$([[ -f "/tmp/kdt-last-settings-backup" ]] && echo "- Settings Backup: Available ($(cat /tmp/kdt-last-settings-backup))" || echo "- Settings Backup: Not Found")
- Git Status: $(cd "$PROJECT_ROOT" && git status --porcelain | wc -l) uncommitted changes
- Current Branch: $(cd "$PROJECT_ROOT" && git branch --show-current)
- Latest Commit: $(cd "$PROJECT_ROOT" && git log -1 --oneline)

Recommendations:
- Ensure test email was received successfully
- Verify all settings are properly configured
- Create settings backup before deployment
- Test 2FA flow manually if enabled

EOF

    log SUCCESS "Pre-deployment report generated: $report_file"
    echo ""
    echo -e "${CYAN}📋 Report Summary:${NC}"
    cat "$report_file"
}

# Main function
main() {
    echo "========================================"
    echo "  KDT Email Pre-Deployment Test"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Testing email functionality before production deployment"
    log INFO "Test email will be sent to: $TEST_EMAIL"
    echo ""
    
    # Execute tests
    check_docker
    check_email_settings
    test_email_sending
    test_2fa_functionality
    generate_report
    
    echo ""
    log SUCCESS "🎉 Pre-deployment email testing completed!"
    echo ""
    echo -e "${GREEN}✅ Next Steps:${NC}"
    echo "   1. Check your email inbox for the test message"
    echo "   2. If email received successfully, proceed with deployment"
    echo "   3. If email failed, fix configuration before deploying"
    echo "   4. Create settings backup: ./deploy/pi-native/scripts/backup-settings.sh"
    echo "   5. Deploy to production: ./deploy/pi-native/scripts/deploy-with-settings.sh"
    echo ""
    echo -e "${YELLOW}⚠️  Important:${NC} Only proceed with deployment if the test email was received successfully!"
}

# Execute main function
main "$@"
