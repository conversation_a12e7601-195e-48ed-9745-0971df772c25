#!/bin/bash

# KDT Production Deployment with Comprehensive Data Synchronization
# Deploys code and synchronizes all business data while preserving production security settings
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
BACKEND_PATH="$PROJECT_ROOT/backend"
LOG_FILE="/tmp/kdt-production-deploy-$(date +%Y%m%d-%H%M%S).log"
SETTINGS_BACKUP_DIR=""
DATA_BACKUP_DIR=""
DEV_DB_BACKUP=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Flags
DRY_RUN=false
SKIP_SETTINGS=false
SKIP_DATA_SYNC=false
FORCE=false
PRESERVE_PROD_SETTINGS=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-settings)
            SKIP_SETTINGS=true
            shift
            ;;
        --skip-data-sync)
            SKIP_DATA_SYNC=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --settings-backup)
            SETTINGS_BACKUP_DIR="$2"
            shift 2
            ;;
        --data-backup)
            DATA_BACKUP_DIR="$2"
            shift 2
            ;;
        --dev-db-backup)
            DEV_DB_BACKUP="$2"
            shift 2
            ;;
        --no-preserve-prod-settings)
            PRESERVE_PROD_SETTINGS=false
            shift
            ;;
        --help|-h)
            echo "KDT Production Deployment with Comprehensive Data Synchronization"
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --dry-run                    Show what would be done without executing"
            echo "  --skip-settings              Skip settings migration"
            echo "  --skip-data-sync             Skip business data synchronization"
            echo "  --force                      Force deployment even if validation fails"
            echo "  --settings-backup DIR        Use specific settings backup directory"
            echo "  --data-backup DIR            Use specific data backup directory"
            echo "  --dev-db-backup FILE         Use specific development database backup file"
            echo "  --no-preserve-prod-settings  Don't preserve production-specific settings"
            echo "  --help                       Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Find latest settings backup
find_settings_backup() {
    if [[ -n "$SETTINGS_BACKUP_DIR" ]] && [[ -d "$SETTINGS_BACKUP_DIR" ]]; then
        log INFO "Using specified settings backup: $SETTINGS_BACKUP_DIR"
        return 0
    fi

    # Look for latest backup
    local latest_backup=$(cat "/tmp/kdt-last-settings-backup" 2>/dev/null || echo "")

    if [[ -n "$latest_backup" ]] && [[ -d "$latest_backup" ]]; then
        SETTINGS_BACKUP_DIR="$latest_backup"
        log INFO "Found latest settings backup: $SETTINGS_BACKUP_DIR"
        return 0
    fi

    # Look for backup directories
    local backup_dirs=($(find /tmp -name "kdt-settings-backup-*" -type d 2>/dev/null | sort -r))

    if [[ ${#backup_dirs[@]} -gt 0 ]]; then
        SETTINGS_BACKUP_DIR="${backup_dirs[0]}"
        log INFO "Found settings backup: $SETTINGS_BACKUP_DIR"
        return 0
    fi

    log WARNING "No settings backup found"
    return 1
}

# Find development database backup
find_dev_database_backup() {
    if [[ -n "$DEV_DB_BACKUP" ]] && [[ -f "$DEV_DB_BACKUP" ]]; then
        log INFO "Using specified development database backup: $DEV_DB_BACKUP"
        return 0
    fi

    # Look for latest development database backup
    local latest_backup=$(cat "/tmp/kdt-last-dev-db-backup" 2>/dev/null || echo "")

    if [[ -n "$latest_backup" ]] && [[ -f "$latest_backup" ]]; then
        DEV_DB_BACKUP="$latest_backup"
        log INFO "Found latest development database backup: $DEV_DB_BACKUP"
        return 0
    fi

    # Look for backup files
    local backup_files=($(find /tmp -name "kdt-dev-db-backup-*.sql" -type f 2>/dev/null | sort -r))

    if [[ ${#backup_files[@]} -gt 0 ]]; then
        DEV_DB_BACKUP="${backup_files[0]}"
        log INFO "Found development database backup: $DEV_DB_BACKUP"
        return 0
    fi

    log WARNING "No development database backup found"
    return 1
}

# Validate production environment
validate_production_environment() {
    log INFO "🔍 Validating production environment"
    
    local errors=0
    
    # Check if we're on the Pi
    if [[ ! -f "/etc/rpi-issue" ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "This script should only be run on a Raspberry Pi"
        ((errors++))
    fi
    
    # Check required services
    local required_services=("nginx" "php8.2-fpm" "postgresql" "redis-server")
    for service in "${required_services[@]}"; do
        if ! systemctl is-active "$service" &>/dev/null; then
            log ERROR "Required service not running: $service"
            ((errors++))
        fi
    done
    
    # Check database connectivity
    if ! sudo -u postgres psql -c '\l' &>/dev/null; then
        log ERROR "Cannot connect to PostgreSQL database"
        ((errors++))
    fi
    
    # Check backend directory
    if [[ ! -d "$BACKEND_PATH" ]]; then
        log ERROR "Backend directory not found: $BACKEND_PATH"
        ((errors++))
    fi
    
    if [[ $errors -gt 0 ]] && [[ "$FORCE" != "true" ]]; then
        log ERROR "Production environment validation failed with $errors errors"
        log INFO "Use --force to override validation errors"
        exit 1
    fi
    
    log SUCCESS "Production environment validation passed"
}

# Create comprehensive development database backup
create_dev_database_backup() {
    log INFO "💾 Creating comprehensive development database backup"

    local backup_file="/tmp/kdt-dev-db-backup-$(date +%Y%m%d-%H%M%S).sql"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would create development database backup"
        return 0
    fi

    # Create backup excluding sensitive configuration tables
    if docker exec kdt-postgres pg_dump -U kdt kdt \
        --exclude-table=system_settings \
        --exclude-table=password_resets \
        --exclude-table=personal_access_tokens \
        --data-only \
        --inserts > "$backup_file" 2>/dev/null; then

        log SUCCESS "Development database backed up to: $backup_file"
        echo "$backup_file" > "/tmp/kdt-last-dev-db-backup"
        DEV_DB_BACKUP="$backup_file"

        # Show backup statistics
        local tables_count=$(grep -c "INSERT INTO" "$backup_file" 2>/dev/null || echo "0")
        local file_size=$(du -h "$backup_file" | cut -f1)
        log INFO "Backup contains $tables_count table inserts, size: $file_size"
    else
        log ERROR "Failed to create development database backup"
        return 1
    fi
}

# Backup current production database
backup_production_database() {
    log INFO "💾 Creating production database backup"

    local backup_file="/tmp/kdt-production-backup-$(date +%Y%m%d-%H%M%S).sql"

    if sudo -u postgres pg_dump kdt_production > "$backup_file" 2>/dev/null; then
        log SUCCESS "Production database backed up to: $backup_file"
        echo "$backup_file" > "/tmp/kdt-last-production-backup"
    else
        log ERROR "Failed to backup production database"
        return 1
    fi
}

# Backup production sensitive settings
backup_production_sensitive_settings() {
    log INFO "🔒 Backing up production sensitive settings"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would backup production sensitive settings"
        return 0
    fi

    local settings_backup="/tmp/kdt-prod-sensitive-settings-$(date +%Y%m%d-%H%M%S).sql"

    # Backup only sensitive configuration tables
    if sudo -u postgres pg_dump kdt_production \
        --table=system_settings \
        --data-only \
        --inserts > "$settings_backup" 2>/dev/null; then

        log SUCCESS "Production sensitive settings backed up to: $settings_backup"
        echo "$settings_backup" > "/tmp/kdt-last-prod-sensitive-backup"

        # Show what was backed up
        local settings_count=$(grep -c "INSERT INTO system_settings" "$settings_backup" 2>/dev/null || echo "0")
        log INFO "Backed up $settings_count sensitive settings"
    else
        log WARNING "Failed to backup production sensitive settings"
    fi
}

# Synchronize business data from development to production
synchronize_business_data() {
    if [[ "$SKIP_DATA_SYNC" == "true" ]]; then
        log INFO "⏭️  Skipping business data synchronization (--skip-data-sync flag)"
        return 0
    fi

    if ! find_dev_database_backup; then
        log WARNING "No development database backup found - skipping data synchronization"
        return 0
    fi

    log INFO "🔄 Synchronizing business data from development to production"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would synchronize business data from $DEV_DB_BACKUP"
        return 0
    fi

    # Clear existing business data (preserve system settings and user accounts)
    log INFO "Clearing existing business data in production"

    cd "$BACKEND_PATH"

    # Create a script to clear business data while preserving sensitive settings
    cat > /tmp/clear_business_data.sql << 'EOF'
-- Clear business data while preserving system settings and core user data
TRUNCATE TABLE leads RESTART IDENTITY CASCADE;
TRUNCATE TABLE deals RESTART IDENTITY CASCADE;
TRUNCATE TABLE clients RESTART IDENTITY CASCADE;
TRUNCATE TABLE products RESTART IDENTITY CASCADE;
TRUNCATE TABLE quotations RESTART IDENTITY CASCADE;
TRUNCATE TABLE invoices RESTART IDENTITY CASCADE;
TRUNCATE TABLE transactions RESTART IDENTITY CASCADE;
TRUNCATE TABLE campaigns RESTART IDENTITY CASCADE;
TRUNCATE TABLE activity_logs RESTART IDENTITY CASCADE;
-- Note: system_settings and users tables are preserved
EOF

    # Execute the clear script
    if sudo -u postgres psql kdt_production < /tmp/clear_business_data.sql; then
        log SUCCESS "Cleared existing business data"
    else
        log ERROR "Failed to clear existing business data"
        return 1
    fi

    # Import development data
    log INFO "Importing development business data"

    if sudo -u postgres psql kdt_production < "$DEV_DB_BACKUP"; then
        log SUCCESS "Business data synchronization completed"

        # Show synchronization statistics
        local leads_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM leads;" | tr -d ' ')
        local deals_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM deals;" | tr -d ' ')
        local clients_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM clients;" | tr -d ' ')
        local products_count=$(sudo -u postgres psql kdt_production -t -c "SELECT COUNT(*) FROM products;" | tr -d ' ')

        log INFO "Synchronized data: $leads_count leads, $deals_count deals, $clients_count clients, $products_count products"
    else
        log ERROR "Failed to import development business data"
        return 1
    fi

    # Clean up temporary files
    rm -f /tmp/clear_business_data.sql
}

# Deploy code using existing script
deploy_code() {
    log INFO "🚀 Deploying code using pi-native deployment script"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would execute deployment script"
        return 0
    fi

    # Run the existing deployment script
    if bash "$SCRIPT_DIR/deploy.sh"; then
        log SUCCESS "Code deployment completed successfully"
    else
        log ERROR "Code deployment failed"
        return 1
    fi
}

# Restore production sensitive settings
restore_production_sensitive_settings() {
    if [[ "$PRESERVE_PROD_SETTINGS" != "true" ]]; then
        log INFO "⏭️  Not preserving production settings (--no-preserve-prod-settings flag)"
        return 0
    fi

    log INFO "🔒 Restoring production sensitive settings"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would restore production sensitive settings"
        return 0
    fi

    local prod_settings_backup=$(cat "/tmp/kdt-last-prod-sensitive-backup" 2>/dev/null || echo "")

    if [[ -n "$prod_settings_backup" ]] && [[ -f "$prod_settings_backup" ]]; then
        if sudo -u postgres psql kdt_production < "$prod_settings_backup"; then
            log SUCCESS "Production sensitive settings restored"
        else
            log WARNING "Failed to restore production sensitive settings"
        fi
    else
        log WARNING "No production sensitive settings backup found"
    fi
}

# Migrate settings to production
migrate_settings() {
    if [[ "$SKIP_SETTINGS" == "true" ]]; then
        log INFO "⏭️  Skipping settings migration (--skip-settings flag)"
        return 0
    fi

    if ! find_settings_backup; then
        log WARNING "No settings backup found - skipping settings migration"
        return 0
    fi

    log INFO "🔧 Migrating non-sensitive settings to production"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would migrate settings from $SETTINGS_BACKUP_DIR"
        return 0
    fi

    # Create enhanced restoration script that preserves production settings
    cat > "$BACKEND_PATH/restore_settings_enhanced.php" << 'EOF'
<?php

/**
 * Enhanced Production Settings Restoration Script
 * Restores development settings while preserving production-specific configurations
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\SystemSetting;

echo "🔧 Restoring KDT settings to production (preserving sensitive settings)...\n";

// Load settings from backup
$settingsFile = __DIR__ . '/system_settings.json';
if (!file_exists($settingsFile)) {
    echo "❌ Settings backup file not found: $settingsFile\n";
    exit(1);
}

$settings = json_decode(file_get_contents($settingsFile), true);
if (!$settings) {
    echo "❌ Failed to parse settings backup file\n";
    exit(1);
}

echo "📊 Found " . count($settings) . " settings to restore\n";

// Backup current production sensitive settings
$productionSensitiveSettings = [];
$sensitiveKeys = [
    'zoho_smtp_host',
    'zoho_smtp_port',
    'zoho_smtp_username',
    'zoho_smtp_password',
    'zoho_smtp_encryption',
    'zoho_from_address',
    'zoho_from_name',
    'app_debug',
    'log_level',
    'two_factor_auth_enabled'
];

foreach ($sensitiveKeys as $key) {
    $setting = SystemSetting::where('key', $key)->first();
    if ($setting) {
        $productionSensitiveSettings[$key] = $setting->toArray();
        echo "🔒 Preserved production setting: $key\n";
    }
}

// Restore settings
$restored = 0;
$skipped = 0;
$preserved = 0;

foreach ($settings as $setting) {
    // Skip sensitive settings that exist in production
    if (in_array($setting['key'], $sensitiveKeys) && isset($productionSensitiveSettings[$setting['key']])) {
        echo "🔒 Preserving production setting: {$setting['key']}\n";
        $preserved++;
        continue;
    }

    // Skip other development-only settings
    $skipKeys = [
        'mail_host',
        'mail_port',
        'mail_username',
        'mail_password'
    ];

    if (in_array($setting['key'], $skipKeys)) {
        echo "⏭️  Skipping development setting: {$setting['key']}\n";
        $skipped++;
        continue;
    }

    try {
        SystemSetting::updateOrCreate(
            ['key' => $setting['key']],
            [
                'value' => $setting['value'],
                'type' => $setting['type'],
                'description' => $setting['description']
            ]
        );

        echo "✅ Restored: {$setting['key']}\n";
        $restored++;
    } catch (Exception $e) {
        echo "❌ Failed to restore {$setting['key']}: " . $e->getMessage() . "\n";
    }
}

echo "\n📈 Restoration Summary:\n";
echo "   ✅ Restored: $restored settings\n";
echo "   🔒 Preserved: $preserved production settings\n";
echo "   ⏭️  Skipped: $skipped development settings\n";

echo "\n🎉 Enhanced settings restoration completed!\n";
EOF

    # Copy settings file and execute restoration
    if [[ -f "$SETTINGS_BACKUP_DIR/system_settings.json" ]]; then
        cp "$SETTINGS_BACKUP_DIR/system_settings.json" "$BACKEND_PATH/"

        log INFO "Executing enhanced settings restoration script"

        cd "$BACKEND_PATH"
        if php restore_settings_enhanced.php; then
            log SUCCESS "Settings migration completed successfully"

            # Clean up temporary files
            rm -f restore_settings_enhanced.php system_settings.json
        else
            log ERROR "Settings migration failed"
            return 1
        fi
    else
        log ERROR "Settings backup file not found"
        return 1
    fi
}

# Configure 2FA for production (disabled by default)
enable_2fa_production() {
    log INFO "🔒 Configuring 2FA for production (disabled by default)"

    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "DRY RUN: Would disable 2FA by default in production"
        return 0
    fi

    cd "$BACKEND_PATH"

    # Disable 2FA by default for fresh deployments
    log INFO "Setting 2FA to disabled by default - can be enabled later in admin panel"

    if php artisan tinker --execute="\App\Models\SystemSetting::set('two_factor_auth_enabled', false, 'boolean', 'Enable or disable two-factor authentication system-wide');" 2>/dev/null; then
        log SUCCESS "2FA disabled by default in production"
        log INFO "Administrators can enable 2FA later in Settings > Security"
    else
        log WARNING "Failed to set 2FA default state - can be configured manually later"
    fi
}

# Verify production deployment
verify_production_deployment() {
    log INFO "✅ Verifying production deployment"
    
    local errors=0
    
    # Check application accessibility
    local app_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" || echo "000")
    if [[ "$app_status" == "200" ]]; then
        log SUCCESS "Application accessible (HTTP 200)"
    else
        log ERROR "Application not accessible (HTTP $app_status)"
        ((errors++))
    fi
    
    # Check API endpoint
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" || echo "000")
    if [[ "$api_status" == "200" ]]; then
        log SUCCESS "API endpoint responding (HTTP 200)"
    else
        log ERROR "API endpoint not responding (HTTP $api_status)"
        ((errors++))
    fi
    
    # Check database connectivity from Laravel
    cd "$BACKEND_PATH"
    if php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected';" &>/dev/null; then
        log SUCCESS "Database connectivity verified"
    else
        log ERROR "Database connectivity failed"
        ((errors++))
    fi
    
    # Check settings migration
    if [[ "$SKIP_SETTINGS" != "true" ]]; then
        local settings_count=$(php artisan tinker --execute="echo \App\Models\SystemSetting::count();" 2>/dev/null || echo "0")
        if [[ "$settings_count" -gt "0" ]]; then
            log SUCCESS "Settings migration verified ($settings_count settings)"
        else
            log WARNING "No settings found in database"
        fi
    fi
    
    if [[ $errors -eq 0 ]]; then
        log SUCCESS "All verification checks passed"
        return 0
    else
        log ERROR "Verification failed with $errors errors"
        return 1
    fi
}

# Main deployment function
main() {
    echo "========================================"
    echo "  KDT Production Deployment"
    echo "  With Settings Migration"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "Starting production deployment with settings migration"
    log INFO "Log file: $LOG_FILE"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "🔍 DRY RUN MODE - No changes will be made"
    fi
    
    # Execute deployment steps
    validate_production_environment

    if [[ "$DRY_RUN" != "true" ]]; then
        backup_production_database
        backup_production_sensitive_settings
    fi

    # Create development database backup if needed
    if [[ "$SKIP_DATA_SYNC" != "true" ]] && [[ -z "$DEV_DB_BACKUP" ]]; then
        create_dev_database_backup
    fi

    deploy_code
    synchronize_business_data
    migrate_settings
    restore_production_sensitive_settings
    enable_2fa_production
    verify_production_deployment
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "🔍 Dry run completed - no changes were made"
        echo ""
        echo -e "${BLUE}Deployment Summary:${NC}"
        echo "  📦 Code deployment: Ready"
        echo "  🔄 Data synchronization: $([ "$SKIP_DATA_SYNC" == "true" ] && echo "Skipped" || echo "Ready")"
        echo "  🔧 Settings migration: $([ "$SKIP_SETTINGS" == "true" ] && echo "Skipped" || echo "Ready")"
        echo "  🔒 Production settings: $([ "$PRESERVE_PROD_SETTINGS" == "true" ] && echo "Will be preserved" || echo "Will be overwritten")"
        echo ""
        echo -e "${BLUE}To execute the deployment:${NC}"
        echo "  $0 $(echo "$@" | sed 's/--dry-run//')"
    else
        log SUCCESS "🎉 Comprehensive production deployment completed successfully!"
        echo ""
        echo -e "${GREEN}🌐 Application URL:${NC} https://ts.crtvmkmn.space"
        echo -e "${BLUE}📊 Admin Panel:${NC} https://ts.crtvmkmn.space/settings"
        echo -e "${PURPLE}📋 Log File:${NC} $LOG_FILE"
        echo ""
        echo -e "${CYAN}📈 Deployment Summary:${NC}"
        echo "  ✅ Code deployed and services restarted"
        echo "  ✅ Business data synchronized from development"
        echo "  ✅ Settings migrated (production settings preserved)"
        echo "  ✅ Security configurations maintained"
        echo ""
        echo -e "${YELLOW}🔧 Next Steps:${NC}"
        echo "  1. Test application functionality"
        echo "  2. Verify email settings in admin panel"
        echo "  3. Enable 2FA in Settings > Security (currently disabled by default)"
        echo "  4. Review synchronized data"

        # Show security reminder
        if [[ -f "$SCRIPT_DIR/security-reminder.sh" ]]; then
            echo ""
            log INFO "🛡️ Showing security configuration reminder..."
            bash "$SCRIPT_DIR/security-reminder.sh"
        fi
    fi
}

# Execute main function
main "$@"
