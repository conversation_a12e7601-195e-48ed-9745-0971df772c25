#!/bin/bash

# Frontend Build Script for Pi Native Deployment
set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_status "Building frontend for Pi native deployment..."

# Ensure we're in the project root
if [ ! -f "package.json" ]; then
    echo "Error: Must run from project root directory"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Create frontend dist directory
mkdir -p frontend/dist

# Build using Pi-specific configuration
print_status "Building production assets..."
npm run build:pi

# Verify build output
if [ ! -f "frontend/dist/index.html" ]; then
    echo "Error: Build failed - index.html not found"
    exit 1
fi

print_success "Frontend built successfully!"
print_status "Build output location: frontend/dist/"
print_status "Files created:"
ls -la frontend/dist/

# Create nginx-ready structure
print_status "Organizing files for Nginx..."
cd frontend/dist

# Ensure proper file permissions
find . -type f -name "*.html" -exec chmod 644 {} \;
find . -type f -name "*.js" -exec chmod 644 {} \;
find . -type f -name "*.css" -exec chmod 644 {} \;
find . -type f -name "*.png" -exec chmod 644 {} \;
find . -type f -name "*.jpg" -exec chmod 644 {} \;
find . -type f -name "*.svg" -exec chmod 644 {} \;

cd ../..

print_success "Frontend ready for Nginx deployment on port 4000!"
