#!/bin/bash

# Deployment Verification Script
set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

echo "🔍 KDT Native Deployment Verification"
echo "======================================"

# Check services
print_status "Checking system services..."

services=("nginx" "php8.2-fpm" "postgresql" "redis-kdt" "kdt-queue")
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        print_success "$service is running"
    else
        print_error "$service is not running"
    fi
done

# Check ports
print_status "Checking port availability..."

ports=("4000:Frontend" "4001:Backend" "4002:PostgreSQL" "4003:Redis")
for port_info in "${ports[@]}"; do
    port=$(echo $port_info | cut -d: -f1)
    name=$(echo $port_info | cut -d: -f2)
    
    if netstat -tuln | grep -q ":$port "; then
        print_success "Port $port ($name) is listening"
    else
        print_error "Port $port ($name) is not listening"
    fi
done

# Check application endpoints
print_status "Testing application endpoints..."

# Test frontend
if curl -s -o /dev/null -w "%{http_code}" http://localhost:4000 | grep -q "200"; then
    print_success "Frontend (port 4000) is responding"
else
    print_error "Frontend (port 4000) is not responding"
fi

# Test backend API
if curl -s -o /dev/null -w "%{http_code}" http://localhost:4001/api/v1/health | grep -q "200"; then
    print_success "Backend API (port 4001) is responding"
else
    print_warning "Backend API health check failed (may need configuration)"
fi

# Check database connection
print_status "Testing database connection..."
if sudo -u postgres psql -d kdt_production -c "SELECT 1;" > /dev/null 2>&1; then
    print_success "PostgreSQL database is accessible"
else
    print_error "PostgreSQL database connection failed"
fi

# Check Redis connection
print_status "Testing Redis connection..."

# Load Redis password from credentials file if available
CREDENTIALS_FILE="/home/<USER>/.kdt-credentials"
if [[ -f "$CREDENTIALS_FILE" ]]; then
    source "$CREDENTIALS_FILE"
    REDIS_AUTH_CMD="-a $KDT_REDIS_PASSWORD"
else
    # Fallback: try without password first, then prompt if needed
    REDIS_AUTH_CMD=""
fi

if redis-cli -p 4003 $REDIS_AUTH_CMD ping 2>/dev/null | grep -q "PONG"; then
    print_success "Redis is responding on port 4003"
elif [[ -z "$REDIS_AUTH_CMD" ]]; then
    # Try with environment variable if no credentials file
    if [[ -n "$KDT_REDIS_PASSWORD" ]]; then
        if redis-cli -p 4003 -a "$KDT_REDIS_PASSWORD" ping 2>/dev/null | grep -q "PONG"; then
            print_success "Redis is responding on port 4003 (with auth)"
        else
            print_error "Redis connection failed (auth required)"
        fi
    else
        print_error "Redis connection failed (credentials not found)"
        echo "  Hint: Ensure credentials are in $CREDENTIALS_FILE or set KDT_REDIS_PASSWORD"
    fi
else
    print_error "Redis connection failed"
fi

# Check file permissions
print_status "Checking file permissions..."
if [ -r "/home/<USER>/Apps/ts-crm/frontend/dist/index.html" ]; then
    print_success "Frontend files are readable"
else
    print_error "Frontend files permission issue"
fi

if [ -r "/home/<USER>/Apps/ts-crm/backend/public/index.php" ]; then
    print_success "Backend files are readable"
else
    print_error "Backend files permission issue"
fi

echo ""
echo "🎯 Verification complete!"
echo "If all checks pass, KDT is ready for use:"
echo "  Frontend: http://localhost:4000"
echo "  Backend:  http://localhost:4001"
