#!/bin/bash

# KDT Security Fixes Verification Script
# Verifies that all critical security vulnerabilities have been resolved
# Author: KDT Development Team

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
CRITICAL_ISSUES=0

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ((TESTS_PASSED++))
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message"
            ((TESTS_FAILED++))
            ;;
        CRITICAL)
            echo -e "${RED}[CRITICAL]${NC} $message"
            ((TESTS_FAILED++))
            ((CRITICAL_ISSUES++))
            ;;
    esac
}

# Test 1: Verify no hardcoded passwords in git-tracked files
test_hardcoded_passwords() {
    log INFO "🔍 Test 1: Checking for hardcoded passwords in git-tracked files"
    
    local issues_found=0
    
    # Check for specific hardcoded passwords that were problematic
    if git grep -n "kdt_secure_password_2024" -- '*.sh' '*.php' 2>/dev/null; then
        log CRITICAL "Found hardcoded database password in git-tracked files"
        ((issues_found++))
    fi
    
    if git grep -n "kdt_redis_password_2024" -- '*.sh' '*.php' 2>/dev/null; then
        log CRITICAL "Found hardcoded Redis password in git-tracked files"
        ((issues_found++))
    fi
    
    # Check for test passwords
    if git grep -n "test-zoho-password" -- '*.php' 2>/dev/null; then
        log CRITICAL "Found test passwords in git-tracked files"
        ((issues_found++))
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log SUCCESS "No hardcoded passwords found in git-tracked files"
    else
        log CRITICAL "Found $issues_found hardcoded password issues"
    fi
}

# Test 2: Verify test files are removed and gitignored
test_removed_files() {
    log INFO "🔍 Test 2: Checking test files are removed and gitignored"
    
    local issues_found=0
    
    # Check if test files still exist
    if [[ -f "test_email_settings.php" ]] || [[ -f "backend/test_email_settings.php" ]]; then
        log CRITICAL "Test files with hardcoded credentials still exist"
        ((issues_found++))
    fi
    
    # Check if test files are in gitignore
    if ! grep -q "test_email_settings.php" .gitignore; then
        log ERROR "test_email_settings.php not in .gitignore"
        ((issues_found++))
    fi
    
    # Check if credential files are in gitignore
    if ! grep -q ".kdt-credentials" .gitignore; then
        log ERROR ".kdt-credentials not in .gitignore"
        ((issues_found++))
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log SUCCESS "Test files properly removed and gitignored"
    else
        log ERROR "Found $issues_found issues with test file cleanup"
    fi
}

# Test 3: Verify EmailSettingsController password masking
test_password_masking() {
    log INFO "🔍 Test 3: Checking EmailSettingsController password masking"
    
    local issues_found=0
    
    # Check if password is masked in API response
    if grep -n "SystemSetting::get('zoho_smtp_password'" backend/app/Http/Controllers/EmailSettingsController.php | grep -v "maskCredential" >/dev/null 2>&1; then
        log CRITICAL "Email password not masked in API response"
        ((issues_found++))
    fi
    
    # Check if maskCredential method exists
    if ! grep -q "private function maskCredential" backend/app/Http/Controllers/EmailSettingsController.php; then
        log CRITICAL "maskCredential method not found in EmailSettingsController"
        ((issues_found++))
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log SUCCESS "Email password properly masked in API responses"
    else
        log CRITICAL "Found $issues_found password masking issues"
    fi
}

# Test 4: Verify secure file permissions in backup scripts
test_backup_security() {
    log INFO "🔍 Test 4: Checking backup script security"
    
    local issues_found=0
    
    # Check if backup directory permissions are set
    if ! grep -q "chmod 700" deploy/pi-native/scripts/backup-settings.sh; then
        log ERROR "Backup directory permissions not set securely"
        ((issues_found++))
    fi
    
    # Check if backup file permissions are set
    if ! grep -q "chmod 600.*system_settings.json" deploy/pi-native/scripts/backup-settings.sh; then
        log ERROR "Backup file permissions not set securely"
        ((issues_found++))
    fi
    
    # Check if temporary files are cleaned up
    if ! grep -q "rm -f /tmp/system_settings.json" deploy/pi-native/scripts/backup-settings.sh; then
        log WARNING "Temporary files may not be cleaned up properly"
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log SUCCESS "Backup scripts implement secure file permissions"
    else
        log ERROR "Found $issues_found backup security issues"
    fi
}

# Test 5: Verify credentials management system
test_credentials_management() {
    log INFO "🔍 Test 5: Checking credentials management system"
    
    local issues_found=0
    
    # Check if credentials management script exists
    if [[ ! -f "deploy/pi-native/scripts/manage-credentials.sh" ]]; then
        log ERROR "Credentials management script not found"
        ((issues_found++))
    elif [[ ! -x "deploy/pi-native/scripts/manage-credentials.sh" ]]; then
        log ERROR "Credentials management script not executable"
        ((issues_found++))
    fi
    
    # Check if deployment scripts use credentials system
    if ! grep -q "CREDENTIALS_FILE" deploy/pi-native/scripts/configure-app.sh; then
        log ERROR "configure-app.sh doesn't use credentials management system"
        ((issues_found++))
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log SUCCESS "Credentials management system properly implemented"
    else
        log ERROR "Found $issues_found credentials management issues"
    fi
}

# Test 6: Verify deployment pipeline functionality
test_deployment_pipeline() {
    log INFO "🔍 Test 6: Checking deployment pipeline functionality"
    
    local issues_found=0
    
    # Check if main deployment script exists and is executable
    if [[ ! -f "deploy/pi-native/scripts/deploy.sh" ]]; then
        log ERROR "Main deployment script not found"
        ((issues_found++))
    elif [[ ! -x "deploy/pi-native/scripts/deploy.sh" ]]; then
        log ERROR "Main deployment script not executable"
        ((issues_found++))
    fi
    
    # Check if verification script exists
    if [[ ! -f "deploy/pi-native/scripts/verify-deployment.sh" ]]; then
        log ERROR "Deployment verification script not found"
        ((issues_found++))
    fi
    
    # Check if scripts have proper error handling
    if ! grep -q "set -euo pipefail" deploy/pi-native/scripts/deploy.sh; then
        log WARNING "Main deployment script may not have proper error handling"
    fi
    
    if [[ $issues_found -eq 0 ]]; then
        log SUCCESS "Deployment pipeline scripts are functional"
    else
        log ERROR "Found $issues_found deployment pipeline issues"
    fi
}

# Test 7: Check for any remaining security vulnerabilities
test_remaining_vulnerabilities() {
    log INFO "🔍 Test 7: Scanning for remaining security vulnerabilities"
    
    local issues_found=0
    
    # Check for any remaining password patterns
    if git grep -i "password.*=" -- '*.sh' 2>/dev/null | grep -v "KDT_.*_PASSWORD" | grep -v "your-" | grep -v "example" | grep -v "GENERATE" >/dev/null 2>&1; then
        log WARNING "Found potential password assignments in scripts"
    fi

    # Check for any API keys or secrets
    if git grep -i "secret.*=" -- '*.sh' '*.php' 2>/dev/null | grep -v "your-" | grep -v "example" | grep -v "GENERATE" >/dev/null 2>&1; then
        log WARNING "Found potential secret assignments"
    fi

    # Check for any unencrypted credential storage
    if git grep -n "SystemSetting::set.*password" backend/ 2>/dev/null | grep -v "encrypt(" >/dev/null 2>&1; then
        log WARNING "Found potential unencrypted password storage"
    fi
    
    log SUCCESS "Security vulnerability scan completed"
}

# Generate security report
generate_report() {
    echo ""
    echo "========================================"
    echo "  KDT Security Fixes Verification Report"
    echo "  $(date)"
    echo "========================================"
    echo ""
    echo "Test Results:"
    echo "  ✅ Tests Passed: $TESTS_PASSED"
    echo "  ❌ Tests Failed: $TESTS_FAILED"
    echo "  🚨 Critical Issues: $CRITICAL_ISSUES"
    echo ""
    
    if [[ $CRITICAL_ISSUES -eq 0 ]] && [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL SECURITY FIXES VERIFIED SUCCESSFULLY!${NC}"
        echo -e "${GREEN}✅ Safe to proceed with production deployment${NC}"
        return 0
    elif [[ $CRITICAL_ISSUES -eq 0 ]]; then
        echo -e "${YELLOW}⚠️  Some non-critical issues found${NC}"
        echo -e "${YELLOW}✅ Safe to proceed with production deployment${NC}"
        return 0
    else
        echo -e "${RED}🚨 CRITICAL SECURITY ISSUES REMAIN!${NC}"
        echo -e "${RED}❌ DO NOT DEPLOY TO PRODUCTION${NC}"
        return 1
    fi
}

# Main function
main() {
    echo "========================================"
    echo "  KDT Security Fixes Verification"
    echo "  $(date)"
    echo "========================================"
    echo ""
    
    cd "$PROJECT_ROOT"
    
    # Run all tests
    test_hardcoded_passwords
    test_removed_files
    test_password_masking
    test_backup_security
    test_credentials_management
    test_deployment_pipeline
    test_remaining_vulnerabilities
    
    # Generate final report
    generate_report
}

# Execute main function
main "$@"
