#!/bin/bash

# Update Nginx Configuration for File Upload Support
# This script updates the nginx configurations to support larger file uploads

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        ERROR)   echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        SUCCESS) echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
        WARNING) echo -e "${YELLOW}[WARNING]${NC} $timestamp - $message" ;;
        INFO)    echo -e "${BLUE}[INFO]${NC} $timestamp - $message" ;;
        DEBUG)   echo -e "${NC}[DEBUG]${NC} $timestamp - $message" ;;
    esac
}

print_header() {
    echo ""
    echo "=================================="
    echo "  KDT Nginx Configuration Update"
    echo "=================================="
    echo ""
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

print_header

log INFO "🔧 Updating Nginx configurations for file upload support"

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    log WARNING "Running as root. This is not recommended."
elif ! sudo -n true 2>/dev/null; then
    log INFO "This script requires sudo privileges for nginx configuration updates"
    echo "You may be prompted for your password..."
fi

# Backup current configurations
log INFO "📦 Creating backup of current nginx configurations"
sudo cp /etc/nginx/sites-available/kdt-frontend.conf /etc/nginx/sites-available/kdt-frontend.conf.backup.$(date +%Y%m%d-%H%M%S) 2>/dev/null || true
sudo cp /etc/nginx/sites-available/kdt-backend.conf /etc/nginx/sites-available/kdt-backend.conf.backup.$(date +%Y%m%d-%H%M%S) 2>/dev/null || true

# Copy updated configurations
log INFO "📋 Copying updated nginx configurations"
sudo cp "$PROJECT_ROOT/deploy/pi-native/nginx/kdt-frontend.conf" /etc/nginx/sites-available/
sudo cp "$PROJECT_ROOT/deploy/pi-native/nginx/kdt-backend.conf" /etc/nginx/sites-available/

# Test nginx configuration
log INFO "🧪 Testing nginx configuration"
if sudo nginx -t; then
    log SUCCESS "Nginx configuration test passed"
else
    log ERROR "Nginx configuration test failed"
    log INFO "Restoring backup configurations..."
    
    # Restore backups
    latest_frontend_backup=$(ls -t /etc/nginx/sites-available/kdt-frontend.conf.backup.* 2>/dev/null | head -1)
    latest_backend_backup=$(ls -t /etc/nginx/sites-available/kdt-backend.conf.backup.* 2>/dev/null | head -1)
    
    if [[ -n "$latest_frontend_backup" ]]; then
        sudo cp "$latest_frontend_backup" /etc/nginx/sites-available/kdt-frontend.conf
    fi
    
    if [[ -n "$latest_backend_backup" ]]; then
        sudo cp "$latest_backend_backup" /etc/nginx/sites-available/kdt-backend.conf
    fi
    
    log ERROR "Configuration update failed. Backups restored."
    exit 1
fi

# Reload nginx
log INFO "🔄 Reloading nginx service"
if sudo systemctl reload nginx; then
    log SUCCESS "Nginx reloaded successfully"
else
    log ERROR "Failed to reload nginx"
    exit 1
fi

# Verify the changes
log INFO "✅ Verifying configuration changes"

# Check if client_max_body_size is set correctly
if sudo nginx -T 2>/dev/null | grep -q "client_max_body_size.*50M"; then
    log SUCCESS "File upload size limit updated to 50MB"
else
    log WARNING "Could not verify file upload size limit in configuration"
fi

# Check nginx status
if systemctl is-active nginx &>/dev/null; then
    log SUCCESS "Nginx service is running"
else
    log ERROR "Nginx service is not running"
    exit 1
fi

echo ""
echo "=================================="
log SUCCESS "Nginx configuration update completed!"
echo ""
echo "Changes made:"
echo "  ✅ Added client_max_body_size 50M to frontend nginx config"
echo "  ✅ Added client_max_body_size 50M to backend nginx config"
echo "  ✅ Added proxy timeouts for file uploads"
echo ""
echo "You can now upload CSV files up to 50MB in size."
echo "=================================="
