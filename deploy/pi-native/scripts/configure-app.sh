#!/bin/bash

# KDT Application Configuration Script
# Configures Laravel environment and builds frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DB_NAME="kdt_production"
DB_USER="kdt_user"

# Load or generate secure passwords for production
CREDENTIALS_FILE="/home/<USER>/.kdt-credentials"

if [[ -f "$CREDENTIALS_FILE" ]]; then
    print_status "Loading existing production credentials..."
    source "$CREDENTIALS_FILE"
    DB_PASSWORD="$KDT_DB_PASSWORD"
    REDIS_PASSWORD="$KDT_REDIS_PASSWORD"
    print_status "Loaded credentials from $CREDENTIALS_FILE"
else
    print_status "Generating new production credentials..."
    # Generate secure random passwords for production
    DB_PASSWORD="${KDT_DB_PASSWORD:-$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-25)}"
    REDIS_PASSWORD="${KDT_REDIS_PASSWORD:-$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-25)}"

    # Save credentials for future use
    cat > "$CREDENTIALS_FILE" << EOF
# KDT Production Credentials - Generated $(date)
export KDT_DB_PASSWORD="$DB_PASSWORD"
export KDT_REDIS_PASSWORD="$REDIS_PASSWORD"
EOF
    chmod 600 "$CREDENTIALS_FILE"
    print_status "Saved credentials to $CREDENTIALS_FILE"
fi

print_status "Using database password (${#DB_PASSWORD} characters)"
print_status "Using Redis password (${#REDIS_PASSWORD} characters)"

print_status "Configuring Laravel environment..."

# Create production environment file
cat > backend/.env << EOF
APP_NAME=KDT
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://ts.crtvmkmn.space
FRONTEND_URL=https://ts.crtvmkmn.space

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=4002
DB_DATABASE=${DB_NAME}
DB_USERNAME=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_PORT=4003

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="\${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="\${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="\${PUSHER_HOST}"
VITE_PUSHER_PORT="\${PUSHER_PORT}"
VITE_PUSHER_SCHEME="\${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="\${PUSHER_APP_CLUSTER}"
EOF

print_status "Generating application key..."
cd backend
php artisan key:generate --force

print_status "Running database migrations..."
php artisan migrate --force

print_status "Seeding database..."
php artisan db:seed --force

print_status "Caching configuration..."
php artisan config:cache
php artisan route:cache

print_status "Creating storage symlink..."
php artisan storage:link

print_status "Setting permissions..."
sudo chown -R zulhelminasir:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache

cd ..

print_status "Installing frontend dependencies..."
npm install

print_status "Building frontend for production..."
npm run build

print_status "Setting up systemd services..."
sudo cp deploy/pi-native/systemd/kdt-queue.service /etc/systemd/system/
sudo cp deploy/pi-native/systemd/kdt-scheduler.service /etc/systemd/system/
sudo cp deploy/pi-native/systemd/kdt-scheduler.timer /etc/systemd/system/

sudo systemctl daemon-reload
sudo systemctl enable kdt-queue
sudo systemctl enable kdt-scheduler.timer
sudo systemctl start kdt-queue
sudo systemctl start kdt-scheduler.timer

print_success "KDT application configured successfully!"
print_success "Frontend: http://localhost:4000"
print_success "Backend API: http://localhost:4001"
print_success "Database: PostgreSQL on port 4002"
print_success "Redis: Port 4003"

print_status "Checking service status..."
echo "=== Service Status ==="
sudo systemctl status nginx --no-pager -l
sudo systemctl status php8.2-fpm --no-pager -l
sudo systemctl status postgresql --no-pager -l
sudo systemctl status redis-kdt --no-pager -l
sudo systemctl status kdt-queue --no-pager -l
sudo systemctl status kdt-scheduler.timer --no-pager -l
