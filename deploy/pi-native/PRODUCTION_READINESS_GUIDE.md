# KDT Production Readiness Verification Guide

## Overview

The KDT production readiness verification system provides comprehensive assessment of your Raspberry Pi production environment before deployment. This ensures safe, reliable deployments by validating all critical system components and configurations.

## Scripts Overview

### 1. `verify-production-readiness.sh` (Pi-side)
**Purpose:** Comprehensive production environment assessment  
**Location:** Runs directly on the Raspberry Pi production server  
**Function:** Evaluates system health, services, database, application status, and configuration

### 2. `remote-verify-production.sh` (Mac-side)
**Purpose:** Remote execution of production readiness checks  
**Location:** Runs from Mac development environment  
**Function:** Connects to <PERSON> via SSH and executes production readiness verification

## Quick Start

### From Mac (Recommended)
```bash
# Basic verification
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@your-pi-ip

# Detailed assessment with report
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@************* --detailed

# Skip resource-intensive checks
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host <EMAIL> --skip-resources
```

### Directly on Pi
```bash
# Basic verification
./deploy/pi-native/scripts/verify-production-readiness.sh

# Detailed assessment
./deploy/pi-native/scripts/verify-production-readiness.sh --detailed

# Skip backup checks
./deploy/pi-native/scripts/verify-production-readiness.sh --skip-backups
```

## Verification Categories

### 🔍 **Environment Validation**
- ✅ Raspberry Pi detection
- ✅ Project directory existence
- ✅ File permissions and ownership

### 💾 **System Resources**
- ✅ Disk space usage and availability
- ✅ Memory usage and availability
- ✅ CPU load average
- ✅ CPU temperature (Pi-specific)
- ✅ Minimum free space requirements

### 🔧 **System Services**
- ✅ Nginx web server status
- ✅ PHP-FPM service status
- ✅ PostgreSQL database service
- ✅ Redis cache service
- ✅ Service auto-start configuration

### 🗄️ **Database Health**
- ✅ PostgreSQL connectivity
- ✅ Production database existence
- ✅ Database size and table count
- ✅ Recent activity monitoring
- ✅ Redis connectivity and memory usage

### 🌐 **Application Status**
- ✅ Frontend build files existence
- ✅ Backend application files
- ✅ HTTPS application accessibility
- ✅ API endpoint responsiveness
- ✅ SSL certificate validity

### 💾 **Backup Verification**
- ✅ Recent database backup existence
- ✅ Backup file age assessment
- ✅ Backup storage space availability

### ⚙️ **Configuration Validation**
- ✅ Nginx configuration syntax
- ✅ Environment file presence
- ✅ File permissions correctness
- ✅ Log directory accessibility

## Command Line Options

### `verify-production-readiness.sh` Options
```bash
--detailed        # Generate detailed assessment report
--skip-backups    # Skip backup verification checks
--skip-resources  # Skip system resource checks
--help           # Show help message
```

### `remote-verify-production.sh` Options
```bash
--pi-host HOST      # Pi hostname or IP (required)
--detailed          # Generate detailed assessment report
--skip-backups      # Skip backup verification checks
--skip-resources    # Skip system resource checks
--no-copy-logs      # Don't copy logs back to Mac
--help             # Show help message
```

## Output and Results

### Status Indicators
- 🟢 **[PASS]** - Check passed successfully
- 🟡 **[WARN]** - Warning detected, review recommended
- 🔴 **[FAIL]** - Critical issue, must be resolved
- 🔵 **[INFO]** - Informational message

### Exit Codes
- `0` - All checks passed (ready for deployment)
- `>0` - Number of failed critical checks

### Log Files
- **Location:** `/tmp/kdt-production-readiness-YYYYMMDD-HHMMSS.log`
- **Content:** Detailed verification results with timestamps
- **Retention:** Manual cleanup required

### Detailed Reports
- **Location:** `/tmp/kdt-production-readiness-report-YYYYMMDD-HHMMSS.md`
- **Content:** Comprehensive system assessment in Markdown format
- **Includes:** System info, service status, recommendations

## Thresholds and Limits

### System Resources
- **Disk Usage Warning:** 75%
- **Disk Usage Critical:** 85%
- **Memory Usage Warning:** 80%
- **Memory Usage Critical:** 90%
- **Load Average Warning:** 2.0
- **Minimum Free Space:** 2GB
- **CPU Temperature Warning:** 70°C
- **CPU Temperature Critical:** 80°C

### Backup Requirements
- **Recent Backup Age:** < 24 hours (warning if older)
- **Backup Storage:** > 1GB available space

## Integration with Deployment Pipeline

### Pre-Deployment Workflow
```bash
# 1. Verify production readiness
./deploy/pi-native/scripts/remote-verify-production.sh --pi-host pi@your-pi --detailed

# 2. If ready, create backups
./deploy/pi-native/scripts/backup-dev-data.sh
./deploy/pi-native/scripts/backup-settings.sh

# 3. Test deployment (dry run)
./deploy/pi-native/scripts/deploy-with-settings.sh --dry-run

# 4. Execute deployment
./deploy/pi-native/scripts/deploy-with-settings.sh
```

### Automated Integration
The production readiness check can be integrated into CI/CD pipelines:

```bash
# In your deployment script
if ./deploy/pi-native/scripts/remote-verify-production.sh --pi-host $PI_HOST; then
    echo "Production ready - proceeding with deployment"
    ./deploy/pi-native/scripts/deploy-with-settings.sh
else
    echo "Production not ready - deployment aborted"
    exit 1
fi
```

## Troubleshooting

### Common Issues

#### SSH Connection Failed
```bash
# Check SSH connectivity
ssh pi@your-pi-ip "echo 'test'"

# Verify SSH keys
ssh-copy-id pi@your-pi-ip
```

#### Service Not Running
```bash
# On Pi, check service status
sudo systemctl status nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### Database Connection Failed
```bash
# On Pi, check PostgreSQL
sudo systemctl status postgresql
sudo -u postgres psql -c '\l'
```

#### High Resource Usage
```bash
# Check running processes
top
htop

# Check disk usage
df -h
du -sh /var/log/*
```

### Recovery Actions

#### Disk Space Critical
```bash
# Clean log files
sudo journalctl --vacuum-time=7d
sudo find /var/log -name "*.log" -mtime +7 -delete

# Clean package cache
sudo apt clean
```

#### Memory Usage High
```bash
# Restart services
sudo systemctl restart nginx php8.2-fpm

# Check for memory leaks
ps aux --sort=-%mem | head
```

#### Temperature High
```bash
# Check cooling
vcgencmd measure_temp

# Reduce load temporarily
sudo systemctl stop non-essential-services
```

## Best Practices

### Regular Monitoring
- Run production readiness checks weekly
- Monitor system resources daily
- Review logs regularly for early warning signs

### Maintenance Schedule
- **Daily:** Quick health check
- **Weekly:** Full production readiness verification
- **Monthly:** Detailed system maintenance and cleanup

### Documentation
- Keep verification logs for troubleshooting
- Document any recurring issues and solutions
- Maintain deployment history and notes

## Security Considerations

### SSH Access
- Use SSH keys instead of passwords
- Limit SSH access to specific IP addresses
- Regularly rotate SSH keys

### Log Security
- Verification logs may contain sensitive system information
- Secure log files with appropriate permissions (600)
- Clean up old logs regularly

### Network Security
- Ensure Pi is behind firewall
- Use VPN for remote access when possible
- Monitor for unauthorized access attempts

## Support and Maintenance

### Log Analysis
```bash
# View recent verification results
tail -50 /tmp/kdt-production-readiness-*.log

# Search for specific issues
grep -i "error\|fail" /tmp/kdt-production-readiness-*.log
```

### Performance Monitoring
```bash
# System performance overview
./deploy/pi-native/scripts/verify-production-readiness.sh --detailed

# Resource usage trends
sar -u 1 10  # CPU usage
sar -r 1 10  # Memory usage
```

### Maintenance Commands
```bash
# Clean old verification files
find /tmp -name "kdt-production-readiness-*" -mtime +7 -delete

# Update verification scripts
cd /home/<USER>/Apps/ts-crm
git pull origin main
```

---

**Note:** This verification system is designed specifically for the KDT CRM application running on Raspberry Pi servers. Adapt thresholds and checks as needed for your specific environment and requirements.
