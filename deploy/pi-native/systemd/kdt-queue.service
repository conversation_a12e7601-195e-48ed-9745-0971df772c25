[Unit]
Description=KDT Laravel Queue Worker
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=zulhelminasir
Group=zulhelminasir
WorkingDirectory=/home/<USER>/Apps/ts-crm/backend
ExecStart=/usr/bin/php artisan queue:work --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=5

# Environment
Environment=LARAVEL_ENV=production

[Install]
WantedBy=multi-user.target
