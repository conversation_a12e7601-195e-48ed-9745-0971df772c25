# KDT Data Synchronization Guide

## Overview

This guide explains how to use the enhanced pi-native deployment pipeline for secure development-to-production data synchronization while maintaining security best practices.

## Key Features

### 🔄 Comprehensive Data Synchronization
- **Business Data**: Leads, deals, clients, products, quotations, invoices, transactions, campaigns
- **Activity Logs**: Complete audit trail synchronization
- **Selective Sync**: Excludes sensitive configuration and user passwords

### 🔒 Security-First Approach
- **Preserves Production Settings**: SMTP credentials, API keys, and security configurations remain untouched
- **Excludes Sensitive Data**: User passwords and production-specific settings are never transferred
- **Encrypted Backups**: All backup files use secure permissions (600/700)

### 🛡️ Production Safety
- **Automatic Backups**: Creates full production backup before any changes
- **Rollback Capability**: Can restore previous state if deployment fails
- **Validation Checks**: Verifies environment and data integrity before proceeding

## Workflow Overview

```mermaid
graph TD
    A[Development Environment] --> B[Create Data Backup]
    B --> C[Create Settings Backup]
    C --> D[Deploy Code to Production]
    D --> E[Backup Production Settings]
    E --> F[Synchronize Business Data]
    F --> G[Migrate Non-Sensitive Settings]
    G --> H[Restore Production Settings]
    H --> I[Verify Deployment]
```

## Scripts Overview

### 1. `backup-dev-data.sh`
Creates comprehensive backup of development business data.

**Usage:**
```bash
./deploy/pi-native/scripts/backup-dev-data.sh [OPTIONS]
```

**Options:**
- `--include-settings`: Include system settings in backup
- `--include-users`: Include user accounts in backup (passwords excluded)

**Example:**
```bash
# Basic business data backup
./deploy/pi-native/scripts/backup-dev-data.sh

# Include settings and users
./deploy/pi-native/scripts/backup-dev-data.sh --include-settings --include-users
```

### 2. `backup-settings.sh`
Creates backup of development system settings and configurations.

**Usage:**
```bash
./deploy/pi-native/scripts/backup-settings.sh
```

### 3. `deploy-with-settings.sh` (Enhanced)
Main deployment script with comprehensive data synchronization.

**Usage:**
```bash
./deploy/pi-native/scripts/deploy-with-settings.sh [OPTIONS]
```

**New Options:**
- `--skip-data-sync`: Skip business data synchronization
- `--data-backup DIR`: Use specific data backup directory
- `--dev-db-backup FILE`: Use specific development database backup file
- `--no-preserve-prod-settings`: Don't preserve production-specific settings

## Step-by-Step Deployment Process

### Phase 1: Prepare Development Environment

1. **Ensure Development is Ready**
   ```bash
   # Verify development environment
   docker ps
   docker exec kdt-backend php artisan migrate:status
   ```

2. **Create Data Backup**
   ```bash
   # Create comprehensive business data backup
   ./deploy/pi-native/scripts/backup-dev-data.sh
   ```

3. **Create Settings Backup**
   ```bash
   # Create settings backup
   ./deploy/pi-native/scripts/backup-settings.sh
   ```

### Phase 2: Deploy to Production

1. **Dry Run (Recommended)**
   ```bash
   # Test deployment without making changes
   ./deploy/pi-native/scripts/deploy-with-settings.sh --dry-run
   ```

2. **Full Deployment with Data Sync**
   ```bash
   # Deploy with automatic data synchronization
   ./deploy/pi-native/scripts/deploy-with-settings.sh
   ```

3. **Deploy with Specific Backups**
   ```bash
   # Use specific backup files
   ./deploy/pi-native/scripts/deploy-with-settings.sh \
       --dev-db-backup /tmp/kdt-dev-db-backup-20241231-120000.sql \
       --settings-backup /tmp/kdt-settings-backup-20241231-120000
   ```

### Phase 3: Verify Production

1. **Check Application**
   - Visit https://ts.crtvmkmn.space
   - Test login functionality
   - Verify data is present

2. **Verify Settings**
   - Check Settings > Email Configuration
   - Test email functionality
   - Verify 2FA settings if enabled

3. **Check Data Integrity**
   - Review leads, deals, clients
   - Verify product catalog
   - Check activity logs

## Advanced Usage Scenarios

### Scenario 1: Code-Only Deployment
Skip data synchronization when only deploying code changes:
```bash
./deploy/pi-native/scripts/deploy-with-settings.sh --skip-data-sync --skip-settings
```

### Scenario 2: Data-Only Synchronization
Synchronize data without deploying new code:
```bash
# Create fresh data backup
./deploy/pi-native/scripts/backup-dev-data.sh

# Deploy with existing code
./deploy/pi-native/scripts/deploy-with-settings.sh --skip-settings
```

### Scenario 3: Settings Migration Only
Migrate only settings without data synchronization:
```bash
./deploy/pi-native/scripts/deploy-with-settings.sh --skip-data-sync
```

### Scenario 4: Complete Fresh Deployment
Deploy everything including overwriting production settings:
```bash
./deploy/pi-native/scripts/deploy-with-settings.sh --no-preserve-prod-settings
```

## Security Considerations

### What Gets Synchronized
✅ **Business Data**
- Leads, deals, clients, products
- Quotations, invoices, transactions
- Campaigns and activity logs

✅ **Non-Sensitive Settings**
- UI preferences, feature flags
- Non-security related configurations

### What Gets Preserved
🔒 **Production Security Settings**
- SMTP credentials (Zoho configuration)
- API keys and tokens
- 2FA settings
- Debug and logging configurations

🔒 **User Security Data**
- User passwords (never transferred)
- Authentication tokens
- Session data

### Backup Security
- All backups use secure file permissions (600/700)
- Sensitive settings are encrypted in database
- Backup files are automatically cleaned up
- Production backups are created before any changes

## Troubleshooting

### Common Issues

1. **Docker Not Running**
   ```bash
   # Start Docker services
   docker-compose up -d
   ```

2. **Database Connection Issues**
   ```bash
   # Check database status
   docker exec kdt-postgres pg_isready -U kdt
   ```

3. **Permission Issues**
   ```bash
   # Fix script permissions
   chmod +x deploy/pi-native/scripts/*.sh
   ```

4. **Backup Not Found**
   ```bash
   # Check for existing backups
   ls -la /tmp/kdt-*backup*
   
   # Create new backup
   ./deploy/pi-native/scripts/backup-dev-data.sh
   ```

### Rollback Procedures

If deployment fails, you can rollback:

1. **Automatic Rollback**
   ```bash
   ./deploy/pi-native/scripts/deploy.sh --rollback
   ```

2. **Manual Database Restore**
   ```bash
   # Find production backup
   ls -la /tmp/kdt-production-backup-*
   
   # Restore database
   sudo -u postgres psql kdt_production < /tmp/kdt-production-backup-YYYYMMDD-HHMMSS.sql
   ```

## Best Practices

1. **Always Test First**
   - Use `--dry-run` flag before actual deployment
   - Test in staging environment if available

2. **Regular Backups**
   - Create backups before major changes
   - Keep multiple backup versions

3. **Verify After Deployment**
   - Test all critical functionality
   - Verify email settings work
   - Check data integrity

4. **Monitor Logs**
   - Check deployment logs for errors
   - Monitor application logs after deployment

5. **Security Hygiene**
   - Never commit sensitive credentials
   - Use environment variables for configuration
   - Regularly update passwords and API keys

## Support and Maintenance

### Log Files
- Deployment logs: `/tmp/kdt-production-deploy-*.log`
- Backup logs: `/tmp/kdt-dev-data-backup.log`
- Settings logs: `/tmp/kdt-settings-backup.log`

### Backup Locations
- Development data: `/tmp/kdt-dev-data-backup-*`
- Settings backup: `/tmp/kdt-settings-backup-*`
- Production backup: `/tmp/kdt-production-backup-*`

### Quick Reference
```bash
# Last backup locations
cat /tmp/kdt-last-dev-data-backup
cat /tmp/kdt-last-settings-backup
cat /tmp/kdt-last-production-backup
```
