# Redis configuration for KDT application
port 4003
bind 127.0.0.1
protected-mode yes
tcp-backlog 511
timeout 0
tcp-keepalive 300

# Memory optimization for Raspberry Pi
maxmemory 128mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice
logfile /var/log/redis/redis-kdt.log

# Working directory
dir /var/lib/redis-kdt

# Security
requirepass kdt_redis_password_2024
