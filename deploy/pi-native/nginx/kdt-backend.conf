server {
    listen 4001;
    server_name localhost;
    root /home/<USER>/Apps/ts-crm/backend/public;
    index index.php;

    # Increase client max body size for file uploads
    client_max_body_size 50M;

    # Laravel specific configurations
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm-kdt.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;

        # Increase timeouts for large CSV imports (up to 30 minutes)
        fastcgi_read_timeout 1800;
        fastcgi_send_timeout 1800;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
