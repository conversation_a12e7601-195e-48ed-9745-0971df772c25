[kdt]
user = zulhelminasir
group = zulhelminasir
listen = /var/run/php/php8.2-fpm-kdt.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Process management
pm = dynamic
pm.max_children = 8
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 4
pm.max_requests = 500

; PHP configuration optimized for Pi with large CSV import support
php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 1800
php_admin_value[upload_max_filesize] = 50M
php_admin_value[post_max_size] = 50M
php_admin_value[max_input_vars] = 5000
php_admin_value[max_input_time] = 1800

; Environment variables
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp
