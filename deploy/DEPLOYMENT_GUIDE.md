# KDT CRM - Production Deployment Pipeline

## 🚀 Quick Start

### Mac Development → Pi Production (2 Commands)

```bash
# 1. From Mac: Push and deploy
./deploy/mac-dev/deploy-to-pi.sh

# 2. Alternative: Pi-only deployment (if code already pushed)
# SSH to Pi and run:
./deploy/pi-native/scripts/deploy.sh
```

## 📋 Complete Workflow

### 1. Mac Development Environment

**Prerequisites:**
- Git repository with `pi-native-deployment` branch
- SSH access to Raspberry Pi configured
- Clean working directory (or use `--force`)

**Commands:**
```bash
# Standard deployment
./deploy/mac-dev/deploy-to-pi.sh

# Dry run (see what would happen)
./deploy/mac-dev/deploy-to-pi.sh --dry-run

# Force deployment (ignore validation warnings)
./deploy/mac-dev/deploy-to-pi.sh --force

# Rollback to previous version
./deploy/mac-dev/deploy-to-pi.sh --rollback
```

### 2. Raspberry Pi Production Environment

**Prerequisites:**
- Project cloned to `/home/<USER>/Apps/ts-crm/`
- <PERSON>inx configured and running
- Node.js and npm installed
- Required system packages (jq, curl, etc.)

**Commands:**
```bash
# Standard deployment
./deploy/pi-native/scripts/deploy.sh

# Dry run
./deploy/pi-native/scripts/deploy.sh --dry-run

# Force deployment
./deploy/pi-native/scripts/deploy.sh --force

# Rollback
./deploy/pi-native/scripts/deploy.sh --rollback

# Verbose output
./deploy/pi-native/scripts/deploy.sh --verbose
```

## 🔍 State Tracking & Validation

### Before/After State Comparison

The deployment script automatically captures and compares:

- **Git State**: Commit hashes, branch, uncommitted changes
- **Build State**: Asset files, sizes, timestamps
- **Service State**: Nginx, PHP-FPM status, configuration validity
- **Nginx Files**: Served assets, file sizes, index.html references

### Example Output:
```
📈 Deployment State Comparison
==================================
✅ Git Commit Updated:
   Before: 381cdd6
   After:  8391df9
✅ Build Assets Updated:
   New assets:
   - assets/index-BMvrF3zz.css (95172 bytes)
   - assets/index-CWRKoEbl.js (1222978 bytes)
🔧 Service Status:
   Nginx: active → active
✅ Nginx Served Files Updated:
   Currently serving:
   - ./assets/index-BMvrF3zz.css (95172 bytes)
   - ./assets/index-CWRKoEbl.js (1222978 bytes)
==================================
```

## ✅ Verification Checklist

### Automated Checks (Built into Scripts)

- [ ] **Git Synchronization**: Pi has latest commit from Mac
- [ ] **Build Integrity**: CSS/JS files present and correct size
- [ ] **API Connectivity**: `https://ts.crtvmkmn.space/api/v1/health` returns 200
- [ ] **Frontend Accessibility**: `https://ts.crtvmkmn.space/` returns 200
- [ ] **Environment Configuration**: Correct API URL in build files
- [ ] **Service Status**: Nginx and PHP-FPM running
- [ ] **File Permissions**: Proper ownership and permissions

### Manual Verification Steps

1. **Open Application**: Visit `https://ts.crtvmkmn.space`
2. **Test Login**: Use admin credentials
3. **Check Mobile Responsiveness**: 
   - Resize browser to mobile width (320px-767px)
   - Verify buttons are full-width
   - Check sidebar panels open full-width
   - Test touch-friendly interface
4. **Test Dark Mode**: Toggle dark mode and verify styling
5. **API Functionality**: Create/edit a client or lead
6. **Navigation**: Test all main pages (Clients, Leads, Deals, etc.)

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. SSH Connection Failed
```bash
# Test SSH connectivity
ssh <EMAIL> "echo 'Connection test'"

# If fails, check:
# - Pi is powered on and connected to network
# - SSH keys are configured
# - Hostname resolution works
```

#### 2. Git Push Failed
```bash
# Check git status
git status

# Commit changes if needed
git add .
git commit -m "Your commit message"

# Force push if necessary (use with caution)
git push origin pi-native-deployment --force
```

#### 3. Build Failed
```bash
# Check Node.js version
node --version  # Should be 18+

# Clear cache and rebuild
rm -rf node_modules dist
npm install
npm run build
```

#### 4. API Not Responding
```bash
# Check backend services on Pi
sudo systemctl status nginx
sudo systemctl status php8.2-fpm

# Check nginx configuration
sudo nginx -t

# Restart services
sudo systemctl restart nginx
sudo systemctl restart php8.2-fpm
```

#### 5. Frontend Not Loading
```bash
# Check nginx served files
ls -la /home/<USER>/Apps/ts-crm/frontend/dist/

# Check nginx error logs
sudo tail -f /var/log/nginx/error.log

# Verify file permissions
sudo chown -R zulhelminasir:www-data /home/<USER>/Apps/ts-crm/frontend/dist/
```

### Log Files

- **Deployment Logs**: `/tmp/kdt-deploy-YYYYMMDD-HHMMSS.log`
- **Nginx Logs**: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- **System Logs**: `journalctl -u nginx -f`

### State Files

- **Current State**: `/tmp/full_state_after.json`
- **Previous State**: `/tmp/full_state_before.json`
- **Backup Location**: `/tmp/kdt-backup-YYYYMMDD-HHMMSS/`

## 🔄 Rollback Procedures

### Automatic Rollback
If deployment fails, the script automatically rolls back to the previous version.

### Manual Rollback
```bash
# From Mac
./deploy/mac-dev/deploy-to-pi.sh --rollback

# From Pi
./deploy/pi-native/scripts/deploy.sh --rollback
```

### Emergency Rollback
```bash
# If scripts fail, manually restore from backup
BACKUP_DIR=$(cat /tmp/kdt-last-backup)
sudo cp -r "$BACKUP_DIR/nginx_dist"/* /home/<USER>/Apps/ts-crm/frontend/dist/
sudo systemctl reload nginx
```

## 📊 Performance Metrics

### Expected Deployment Times
- **Code Pull**: 5-15 seconds
- **Frontend Build**: 30-60 seconds
- **File Deployment**: 2-5 seconds
- **Service Restart**: 2-5 seconds
- **Verification**: 5-10 seconds
- **Total**: 45-95 seconds

### Build File Sizes (Current)
- **CSS**: ~95 KB (includes mobile responsiveness and dark mode)
- **JavaScript**: ~1.2 MB (includes all React components and libraries)
- **Total Assets**: ~1.3 MB

## 🔐 Security Considerations

- SSH keys used for authentication (no passwords)
- Deployment scripts validate environment before execution
- Automatic backup creation before deployment
- Rollback capability for failed deployments
- Proper file permissions and ownership
- Nginx security headers configured

## 📈 Monitoring

### Health Checks
- **API Health**: `curl https://ts.crtvmkmn.space/api/v1/health`
- **Frontend**: `curl https://ts.crtvmkmn.space/`
- **Service Status**: `systemctl status nginx php8.2-fpm`

### Automated Monitoring (Future Enhancement)
- Deployment success/failure notifications
- Performance monitoring
- Uptime monitoring
- Error rate tracking
