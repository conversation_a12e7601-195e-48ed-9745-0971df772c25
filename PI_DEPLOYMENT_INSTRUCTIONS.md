# 🚀 **Raspberry Pi Production Deployment Instructions**

## **✅ Pre-Deployment Completed on Mac:**
- ✅ Security fixes verified and implemented
- ✅ Email functionality tested successfully (test email <NAME_EMAIL>)
- ✅ All changes committed and pushed to pi-native-deployment branch
- ✅ 2FA functionality verified working

---

## **🔧 Execute These Commands on Your Raspberry Pi:**

### **Step 1: Connect and Navigate**
```bash
# SSH to your Raspberry Pi
ssh zulhelminasir@your-pi-ip

# Navigate to application directory
cd /home/<USER>/Apps/ts-crm
```

### **Step 2: Pull Latest Security-Hardened Code**
```bash
# Ensure you're on the correct branch
git checkout pi-native-deployment

# Pull the latest changes with all security fixes
git pull origin pi-native-deployment
```

### **Step 3: Generate Secure Production Credentials**
```bash
# Generate unique, secure passwords for production
./deploy/pi-native/scripts/manage-credentials.sh create
```
**Expected Output:**
```
🔐 Generating new production credentials
✅ Generated new credentials:
  Database password: 25 characters
  Redis password: 25 characters
  Saved to: /home/<USER>/.kdt-credentials
⚠️ Keep this file secure and create a backup!
```

### **Step 4: Execute Production Deployment**
```bash
# Run the main deployment script
./deploy/pi-native/scripts/deploy.sh
```
**This will take 2-5 minutes and will:**
- ✅ Validate environment
- ✅ Create backup of current deployment
- ✅ Pull latest code
- ✅ Build frontend with production configuration
- ✅ Deploy to nginx
- ✅ Restart services
- ✅ Verify deployment

### **Step 5: Verify Deployment Success**
```bash
# Run comprehensive verification
./deploy/pi-native/scripts/verify-deployment.sh
```
**Expected Results:**
- ✅ Application accessible at https://ts.crtvmkmn.space
- ✅ API endpoint responding
- ✅ Database connectivity working
- ✅ All services running

---

## **📧 Post-Deployment: Configure Email Settings**

### **Option A: Manual Configuration (Recommended - Most Secure)**
1. **Access Production Admin Panel:**
   - Navigate to: https://ts.crtvmkmn.space
   - Log in with your admin credentials

2. **Configure Email Settings:**
   - Go to **Settings > Email**
   - Enter your Zoho SMTP configuration:
     - **SMTP Host:** smtp.zoho.com
     - **SMTP Port:** 465
     - **SMTP Username:** <EMAIL>
     - **SMTP Password:** [Your Zoho password]
     - **Encryption:** SSL
   - Click **Save Settings**

3. **Test Email Functionality:**
   - Use the "Test Email" feature in the same page
   - Send a test email to verify configuration

### **Option B: Database Transfer (Alternative)**
```bash
# If you prefer to transfer development database with existing settings
# On your Mac (development):
docker exec kdt-postgres pg_dump -U kdt kdt > kdt_development_backup.sql
scp kdt_development_backup.sql zulhelminasir@your-pi-ip:/home/<USER>/

# On Pi (production):
sudo -u postgres psql kdt_production < /home/<USER>/kdt_development_backup.sql
rm /home/<USER>/kdt_development_backup.sql
```

---

## **🔒 Enable 2FA (Optional but Recommended)**
1. Go to **Settings > Security**
2. Enable **Two-Factor Authentication**
3. Test the 2FA flow with a test login

---

## **✅ Final Verification Checklist**

### **Test These After Deployment:**
- [ ] **Frontend Access:** https://ts.crtvmkmn.space loads correctly
- [ ] **Login Test:** Authentication works with your credentials
- [ ] **Email Test:** Send a test email (password reset, etc.)
- [ ] **2FA Test:** If enabled, test the 2FA flow
- [ ] **API Test:** Check https://ts.crtvmkmn.space/api/v1/health returns 200

### **Security Verification:**
```bash
# Check credentials are secure
./deploy/pi-native/scripts/manage-credentials.sh status

# Verify services are running
sudo systemctl status nginx php8.2-fpm postgresql redis-server
```

---

## **🚨 Troubleshooting Commands**

### **If Deployment Fails:**
```bash
# Check deployment logs
tail -f /tmp/kdt-deploy-*.log

# Rollback if needed
./deploy/pi-native/scripts/deploy.sh --rollback
```

### **If Email Not Working:**
```bash
# Check email settings in database
cd /home/<USER>/Apps/ts-crm/backend
php artisan tinker --execute="
echo 'SMTP Host: ' . \App\Models\SystemSetting::get('zoho_smtp_host') . PHP_EOL;
echo 'SMTP User: ' . \App\Models\SystemSetting::get('zoho_smtp_username') . PHP_EOL;
"
```

### **If Services Not Running:**
```bash
# Restart services
sudo systemctl restart nginx php8.2-fpm postgresql redis-server
```

---

## **🎉 Success Criteria**

**Your deployment is successful when:**
- ✅ https://ts.crtvmkmn.space loads the application
- ✅ Login functionality works
- ✅ Email sending works (test with password reset)
- ✅ All services are running and healthy
- ✅ No security vulnerabilities remain
- ✅ 2FA works (if enabled)

---

## **📞 Need Help?**

If you encounter any issues:
1. Check the troubleshooting commands above
2. Review deployment logs: `/tmp/kdt-deploy-*.log`
3. Verify service status: `sudo systemctl status nginx php8.2-fpm postgresql redis-server`
4. Test credentials: `./deploy/pi-native/scripts/manage-credentials.sh test`

**🚀 You're ready to deploy! Follow the steps above in sequence.**
