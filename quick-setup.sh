#!/bin/bash

# KDT Quick Setup Script for Raspberry Pi
# This script automates the initial setup process

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running on Raspberry Pi
check_raspberry_pi() {
    if [[ $(uname -m) == "aarch64" ]] || [[ $(uname -m) == "armv7l" ]]; then
        log "✓ Detected ARM architecture - Raspberry Pi compatible"
    else
        warning "Not running on ARM architecture. This script is optimized for Raspberry Pi."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check available memory
    AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [ "$AVAILABLE_MEM" -lt 3500 ]; then
        warning "Available memory is less than 4GB. Performance may be limited."
    else
        log "✓ Memory: ${AVAILABLE_MEM}MB"
    fi
    
    # Check disk space
    AVAILABLE_DISK=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$AVAILABLE_DISK" -lt 10 ]; then
        error "Available disk space is less than 10GB. Please free up space."
    else
        log "✓ Disk space: ${AVAILABLE_DISK}GB available"
    fi
    
    # Check internet connectivity
    if ping -c 1 ******* > /dev/null 2>&1; then
        log "✓ Internet connectivity"
    else
        error "No internet connectivity. Please check your network connection."
    fi
}

# Install Docker if not present
install_docker() {
    if command -v docker &> /dev/null; then
        log "✓ Docker is already installed"
        docker --version
    else
        log "Installing Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        rm get-docker.sh
        log "✓ Docker installed successfully"
    fi
    
    if command -v docker-compose &> /dev/null; then
        log "✓ Docker Compose is already installed"
        docker-compose --version
    else
        log "Installing Docker Compose..."
        sudo apt update
        sudo apt install -y docker-compose
        log "✓ Docker Compose installed successfully"
    fi
}

# Configure system for Docker
configure_system() {
    log "Configuring system for optimal Docker performance..."
    
    # Enable memory cgroup if not already enabled
    if ! grep -q "cgroup_enable=memory" /boot/cmdline.txt; then
        log "Enabling memory cgroup..."
        echo 'cgroup_enable=memory cgroup_memory=1' | sudo tee -a /boot/cmdline.txt
        warning "System reboot required for memory cgroup changes"
        REBOOT_REQUIRED=true
    fi
    
    # Configure swap if needed
    CURRENT_SWAP=$(free -m | awk 'NR==3{print $2}')
    if [ "$CURRENT_SWAP" -lt 2048 ]; then
        log "Configuring swap space..."
        sudo dphys-swapfile swapoff 2>/dev/null || true
        sudo sed -i 's/CONF_SWAPSIZE=.*/CONF_SWAPSIZE=2048/' /etc/dphys-swapfile
        sudo dphys-swapfile setup
        sudo dphys-swapfile swapon
        log "✓ Swap configured to 2GB"
    else
        log "✓ Swap space is adequate: ${CURRENT_SWAP}MB"
    fi
    
    # Set timezone to UTC
    if [ "$(timedatectl show -p Timezone --value)" != "UTC" ]; then
        log "Setting timezone to UTC..."
        sudo timedatectl set-timezone UTC
        log "✓ Timezone set to UTC"
    fi
}

# Generate secure environment configuration
generate_env_config() {
    log "Generating production environment configuration..."

    # Get Raspberry Pi IP address
    PI_IP=$(hostname -I | awk '{print $1}')

    # Check for Cloudflare tunnel configuration
    read -p "Are you using Cloudflare tunnel? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -p "Enter your domain (e.g., https://ts.crtvmkmn.space): " DOMAIN_URL
        DOMAIN_URL=${DOMAIN_URL:-https://ts.crtvmkmn.space}
        USE_CLOUDFLARE=true
        log "Configuring for Cloudflare tunnel with domain: $DOMAIN_URL"
        log "Application will run on localhost:3000 and be accessible via Cloudflare tunnel"
    else
        DOMAIN_URL="http://$PI_IP:3000"
        USE_CLOUDFLARE=false
        log "Configuring for local access with IP: $PI_IP"
    fi

    # Generate secure passwords and keys
    APP_KEY="base64:$(openssl rand -base64 32)"
    DB_PASSWORD=$(openssl rand -base64 32)
    
    if [ "$USE_CLOUDFLARE" = true ]; then
        # Cloudflare tunnel configuration
        API_URL="${DOMAIN_URL}/api/v1"
        cat > .env.production << EOF
# KDT Production Environment - Generated $(date)
# Configured for Cloudflare Tunnel
# Local: localhost:3000 -> Public: $DOMAIN_URL

# Application
APP_NAME=KDT
APP_ENV=production
APP_DEBUG=false
APP_KEY=$APP_KEY
APP_URL=$DOMAIN_URL

# Database
DB_CONNECTION=pgsql
DB_HOST=kdt-postgres
DB_PORT=5432
DB_DATABASE=kdt
DB_USERNAME=kdt
DB_PASSWORD=$DB_PASSWORD

# Redis
REDIS_HOST=kdt-redis
REDIS_PASSWORD=
REDIS_PORT=6379

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Frontend (Cloudflare Tunnel)
VITE_API_URL=$API_URL
VITE_APP_NAME=KDT
VITE_APP_ENV=production

# Cloudflare Tunnel Configuration
CLOUDFLARE_TUNNEL=true
TRUSTED_PROXIES=*
FORCE_HTTPS=true

# Performance (Raspberry Pi optimized)
PHP_MEMORY_LIMIT=256M
PHP_MAX_EXECUTION_TIME=300
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# Backup
BACKUP_RETENTION_DAYS=7
EOF
    else
        # Local IP configuration
        cat > .env.production << EOF
# KDT Production Environment - Generated $(date)
# Configured for Local Access

# Application
APP_NAME=KDT
APP_ENV=production
APP_DEBUG=false
APP_KEY=$APP_KEY
APP_URL=http://$PI_IP:3000

# Database
DB_CONNECTION=pgsql
DB_HOST=kdt-postgres
DB_PORT=5432
DB_DATABASE=kdt
DB_USERNAME=kdt
DB_PASSWORD=$DB_PASSWORD

# Redis
REDIS_HOST=kdt-redis
REDIS_PASSWORD=
REDIS_PORT=6379

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Frontend
VITE_API_URL=http://$PI_IP:8000/api/v1
VITE_APP_NAME=KDT
VITE_APP_ENV=production

# Performance (Raspberry Pi optimized)
PHP_MEMORY_LIMIT=256M
PHP_MAX_EXECUTION_TIME=300
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# Backup
BACKUP_RETENTION_DAYS=7
EOF
    fi
    
    log "✓ Environment configuration generated"
    if [ "$USE_CLOUDFLARE" = true ]; then
        info "Application will be available at: $DOMAIN_URL"
        info "API will be available at: $API_URL"
    else
        info "Application will be available at: http://$PI_IP:3000"
        info "API will be available at: http://$PI_IP:8000"
    fi
}

# Setup monitoring and backups
setup_monitoring() {
    log "Setting up monitoring and backup automation..."
    
    # Make scripts executable
    chmod +x deploy.sh
    chmod +x scripts/production/*.sh
    
    # Setup cron jobs
    ./scripts/production/setup-cron.sh
    
    log "✓ Monitoring and backup automation configured"
}

# Main setup function
main() {
    cat << EOF
╔══════════════════════════════════════════════════════════════╗
║                    KDT Quick Setup                           ║
║              Raspberry Pi Production Deployment             ║
╚══════════════════════════════════════════════════════════════╝

This script will:
1. Check system requirements
2. Install Docker and Docker Compose
3. Configure system for optimal performance
4. Generate secure environment configuration
5. Setup monitoring and backup automation

EOF
    
    read -p "Continue with setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 0
    fi
    
    REBOOT_REQUIRED=false
    
    check_raspberry_pi
    check_requirements
    install_docker
    configure_system
    generate_env_config
    setup_monitoring
    
    log "✓ Quick setup completed successfully!"
    
    cat << EOF

╔══════════════════════════════════════════════════════════════╗
║                        Setup Complete!                      ║
╚══════════════════════════════════════════════════════════════╝

Next steps:
1. Review the configuration: nano .env.production
2. Deploy the application: ./deploy.sh deploy
3. Check status: ./deploy.sh status
4. View logs: ./deploy.sh logs

Application URLs:
- Frontend: http://$(hostname -I | awk '{print $1}'):3000
- Backend API: http://$(hostname -I | awk '{print $1}'):8000

Monitoring:
- Health check: ./scripts/production/monitor.sh
- Create backup: ./scripts/production/backup.sh
- View logs: tail -f monitor.log

EOF
    
    if [ "$REBOOT_REQUIRED" = true ]; then
        warning "System reboot is required for some changes to take effect."
        read -p "Reboot now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo reboot
        else
            warning "Please reboot manually before deploying the application."
        fi
    fi
}

# Show help
show_help() {
    cat << EOF
KDT Quick Setup Script for Raspberry Pi

Usage: $0 [OPTIONS]

Options:
    --help          Show this help message
    --check-only    Only check requirements, don't install anything
    --docker-only   Only install Docker and Docker Compose
    --config-only   Only generate environment configuration

This script automates the initial setup process for deploying
KDT on a Raspberry Pi server.

EOF
}

# Parse command line arguments
case "${1:-setup}" in
    --help)
        show_help
        ;;
    --check-only)
        check_raspberry_pi
        check_requirements
        ;;
    --docker-only)
        install_docker
        ;;
    --config-only)
        generate_env_config
        ;;
    setup|*)
        main
        ;;
esac
