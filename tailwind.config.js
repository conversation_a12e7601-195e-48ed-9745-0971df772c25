/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      // Enhanced color system for dark mode
      colors: {
        // Semantic color tokens using CSS custom properties
        primary: {
          50: 'rgb(239 246 255)', // blue-50
          100: 'rgb(219 234 254)', // blue-100
          200: 'rgb(191 219 254)', // blue-200
          300: 'rgb(147 197 253)', // blue-300
          400: 'rgb(96 165 250)', // blue-400
          500: 'rgb(59 130 246)', // blue-500
          600: 'rgb(37 99 235)', // blue-600
          700: 'rgb(29 78 216)', // blue-700
          800: 'rgb(30 64 175)', // blue-800
          900: 'rgb(30 58 138)', // blue-900
        },
        // Semantic background colors
        surface: {
          primary: 'rgb(var(--color-bg-primary) / <alpha-value>)',
          secondary: 'rgb(var(--color-bg-secondary) / <alpha-value>)',
          tertiary: 'rgb(var(--color-bg-tertiary) / <alpha-value>)',
          accent: 'rgb(var(--color-bg-accent) / <alpha-value>)',
        },
        // Semantic text colors
        content: {
          primary: 'rgb(var(--color-text-primary) / <alpha-value>)',
          secondary: 'rgb(var(--color-text-secondary) / <alpha-value>)',
          tertiary: 'rgb(var(--color-text-tertiary) / <alpha-value>)',
          accent: 'rgb(var(--color-text-accent) / <alpha-value>)',
        },
        // Semantic border colors
        outline: {
          primary: 'rgb(var(--color-border-primary) / <alpha-value>)',
          secondary: 'rgb(var(--color-border-secondary) / <alpha-value>)',
          accent: 'rgb(var(--color-border-accent) / <alpha-value>)',
        },
        // Interactive states
        interactive: {
          hover: 'rgb(var(--color-interactive-hover) / <alpha-value>)',
          active: 'rgb(var(--color-interactive-active) / <alpha-value>)',
        },
        // Status colors
        status: {
          success: 'rgb(var(--color-success) / <alpha-value>)',
          warning: 'rgb(var(--color-warning) / <alpha-value>)',
          error: 'rgb(var(--color-error) / <alpha-value>)',
          info: 'rgb(var(--color-info) / <alpha-value>)',
        },
      },
      // Animation durations
      transitionDuration: {
        '1500': '1500ms',
        '1800': '1800ms',
        '2250': '2250ms',
      },
      // Enhanced spacing for consistent layouts
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Enhanced border radius
      borderRadius: {
        '4xl': '2rem',
      },
      // Enhanced shadows for dark mode
      boxShadow: {
        'dark': '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
        'dark-lg': '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
      },
    },
  },
  plugins: [],
};
