<?php

/**
 * Force disable 2FA by directly updating the database
 * This bypasses <PERSON><PERSON> and directly updates the system_settings table
 */

// Database configuration - update these values to match your setup
$host = 'localhost';
$port = '5432';
$dbname = 'kdt';
$username = 'kdt';
$password = 'kdt_password';

try {
    // Connect to PostgreSQL database
    $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔌 Connected to database successfully.\n";
    
    // Check current 2FA setting
    $stmt = $pdo->prepare("SELECT * FROM system_settings WHERE key = 'two_factor_auth_enabled'");
    $stmt->execute();
    $currentSetting = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($currentSetting) {
        echo "📋 Current 2FA setting: " . $currentSetting['value'] . "\n";
        
        // Update the setting to false
        $stmt = $pdo->prepare("UPDATE system_settings SET value = 'false', updated_at = NOW() WHERE key = 'two_factor_auth_enabled'");
        $stmt->execute();
        
        echo "✅ Updated existing 2FA setting to 'false'\n";
    } else {
        // Insert new setting
        $stmt = $pdo->prepare("INSERT INTO system_settings (key, value, type, description, created_at, updated_at) VALUES ('two_factor_auth_enabled', 'false', 'boolean', 'Enable or disable two-factor authentication system-wide', NOW(), NOW())");
        $stmt->execute();
        
        echo "✅ Inserted new 2FA setting as 'false'\n";
    }
    
    // Verify the change
    $stmt = $pdo->prepare("SELECT * FROM system_settings WHERE key = 'two_factor_auth_enabled'");
    $stmt->execute();
    $newSetting = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "🔍 Verified 2FA setting: " . $newSetting['value'] . "\n";
    echo "🎉 2FA has been successfully disabled!\n";
    echo "🔓 You can now log in without 2FA.\n";
    echo "📧 Remember to configure email settings and re-enable 2FA later.\n";
    
} catch (PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "\n";
    echo "\n💡 If connection failed, check your database credentials in this script.\n";
    echo "   Current settings: host=$host, port=$port, dbname=$dbname, username=$username\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
