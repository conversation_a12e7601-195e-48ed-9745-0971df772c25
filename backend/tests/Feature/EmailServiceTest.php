<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SystemSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;

class EmailServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user for testing
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
            'status' => 'active'
        ]);
    }

    /** @test */
    public function it_can_get_email_settings()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/api/v1/auth/settings/email');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'password',
                'smtpHost',
                'smtpPort',
                'smtpUsername',
                'smtpEncryption',
                'fromAddress',
                'fromName',
                'replyToAddress'
            ]);
    }

    /** @test */
    public function it_can_save_email_settings()
    {
        $settings = [
            'password' => 'test-zoho-password',
            'smtpHost' => 'smtp.zoho.com',
            'smtpPort' => 587,
            'smtpUsername' => '<EMAIL>',
            'smtpEncryption' => 'tls',
            'fromAddress' => '<EMAIL>',
            'fromName' => 'Test System',
            'replyToAddress' => '<EMAIL>'
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/auth/settings/email', $settings);

        $response->assertStatus(200)
            ->assertJson(['message' => 'Email settings saved successfully']);

        // Verify settings were saved
        $this->assertEquals('smtp.zoho.com', SystemSetting::get('zoho_smtp_host'));
        $this->assertEquals(587, SystemSetting::get('zoho_smtp_port'));
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $settings = [
            // Missing required fields
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/auth/settings/email', $settings);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'password',
                'smtpHost',
                'smtpPort',
                'smtpUsername',
                'smtpEncryption',
                'fromAddress',
                'fromName',
                'replyToAddress'
            ]);
    }

    /** @test */
    public function it_can_test_zoho_email_configuration()
    {
        // Set up Zoho email settings
        SystemSetting::set('zoho_smtp_host', 'smtp.zoho.com', 'string');
        SystemSetting::set('zoho_smtp_port', 587, 'integer');
        SystemSetting::set('zoho_smtp_username', '<EMAIL>', 'string');
        SystemSetting::set('zoho_smtp_password', encrypt('test-password'), 'encrypted');
        SystemSetting::set('zoho_smtp_encryption', 'tls', 'string');

        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/auth/settings/email/test', [
                'testEmail' => '<EMAIL>'
            ]);

        // Note: This will fail in testing environment without actual SMTP
        // but we're testing the endpoint structure
        $response->assertStatus(500); // Expected to fail without real SMTP
    }

    /** @test */
    public function it_requires_admin_role_for_email_settings()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)
            ->getJson('/api/v1/auth/settings/email');

        $response->assertStatus(403);
    }

    /** @test */
    public function it_can_test_email_configuration()
    {
        Mail::fake();

        // Set up email settings
        SystemSetting::set('email_from_address', '<EMAIL>', 'string');
        SystemSetting::set('email_from_name', 'Test System', 'string');

        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/auth/settings/email/test', [
                'testEmail' => '<EMAIL>'
            ]);

        $response->assertStatus(200)
            ->assertJson(['message' => 'Test email sent successfully']);

        Mail::assertSent(\Illuminate\Mail\Mailable::class);
    }
}
