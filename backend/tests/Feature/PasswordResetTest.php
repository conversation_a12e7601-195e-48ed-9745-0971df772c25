<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PasswordResetToken;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class PasswordResetTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    public function test_forgot_password_sends_email_for_valid_user()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'If an account with that email exists, we have sent a password reset link.'
                ]);

        // Check that a token was created
        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => '<EMAIL>',
            'used' => false,
        ]);

        // Verify email was sent
        Mail::assertSent(\Illuminate\Mail\Mailable::class);
    }

    public function test_forgot_password_does_not_reveal_non_existent_email()
    {
        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'If an account with that email exists, we have sent a password reset link.'
                ]);

        // Check that no token was created
        $this->assertDatabaseMissing('password_reset_tokens', [
            'email' => '<EMAIL>'
        ]);
    }

    public function test_forgot_password_fails_for_inactive_user()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => false,
        ]);

        $response = $this->postJson('/api/v1/auth/forgot-password', [
            'email' => '<EMAIL>'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_verify_reset_token_validates_correctly()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $token = PasswordResetToken::createToken('<EMAIL>');

        $response = $this->getJson('/api/v1/auth/verify-reset-token?' . http_build_query([
            'token' => $token,
            'email' => '<EMAIL>'
        ]));

        $response->assertStatus(200)
                ->assertJson([
                    'valid' => true,
                    'message' => 'Token is valid'
                ]);
    }

    public function test_verify_reset_token_fails_for_invalid_token()
    {
        $response = $this->getJson('/api/v1/auth/verify-reset-token?' . http_build_query([
            'token' => 'invalid-token',
            'email' => '<EMAIL>'
        ]));

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['token']);
    }

    public function test_reset_password_works_with_valid_token()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
            'password' => Hash::make('old-password'),
        ]);

        $token = PasswordResetToken::createToken('<EMAIL>');

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'Password has been reset successfully. You can now log in with your new password.'
                ]);

        // Verify password was changed
        $user->refresh();
        $this->assertTrue(Hash::check('NewPassword123!', $user->password));

        // Verify token was marked as used
        $this->assertDatabaseHas('password_reset_tokens', [
            'email' => '<EMAIL>',
            'used' => true,
        ]);
    }

    public function test_reset_password_fails_with_invalid_token()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'token' => 'invalid-token',
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['token']);
    }

    public function test_reset_password_fails_with_mismatched_passwords()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $token = PasswordResetToken::createToken('<EMAIL>');

        $response = $this->postJson('/api/v1/auth/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'DifferentPassword123!',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    public function test_token_expires_after_one_hour()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Create token and manually set it to expired
        $token = PasswordResetToken::createToken('<EMAIL>');
        PasswordResetToken::where('email', '<EMAIL>')
            ->update(['expires_at' => now()->subHour()]);

        $response = $this->getJson('/api/v1/auth/verify-reset-token?' . http_build_query([
            'token' => $token,
            'email' => '<EMAIL>'
        ]));

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['token']);
    }

    public function test_token_can_only_be_used_once()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $token = PasswordResetToken::createToken('<EMAIL>');

        // First reset should work
        $response = $this->postJson('/api/v1/auth/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'NewPassword123!',
            'password_confirmation' => 'NewPassword123!',
        ]);

        $response->assertStatus(200);

        // Second reset with same token should fail
        $response = $this->postJson('/api/v1/auth/reset-password', [
            'token' => $token,
            'email' => '<EMAIL>',
            'password' => 'AnotherPassword123!',
            'password_confirmation' => 'AnotherPassword123!',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['token']);
    }

    public function test_cleanup_expired_tokens()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Create expired token
        PasswordResetToken::create([
            'email' => '<EMAIL>',
            'token' => Hash::make('expired-token'),
            'created_at' => now()->subHours(2),
            'expires_at' => now()->subHour(),
            'used' => false,
        ]);

        // Create valid token
        PasswordResetToken::createToken('<EMAIL>');

        $deletedCount = PasswordResetToken::cleanupExpired();

        $this->assertEquals(1, $deletedCount);
        $this->assertDatabaseMissing('password_reset_tokens', [
            'expires_at' => now()->subHour(),
        ]);
    }
}
