<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\LeadController;
use App\Http\Controllers\DealController;
use App\Http\Controllers\QuotationController;
use App\Http\Controllers\TwoFactorController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\CsvImportController;
use App\Http\Controllers\PasswordResetController;
use App\Http\Controllers\EmailSettingsController;
use App\Http\Controllers\SecuritySettingsController;
use App\Http\Controllers\GeneralSettingsController;
use App\Http\Controllers\ImportHistoryController;

// Authentication routes (public)
Route::prefix('v1/auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);

    // Password reset routes (public)
    Route::middleware('password.reset.rate.limit')->group(function () {
        Route::post('/forgot-password', [PasswordResetController::class, 'forgotPassword']);
        Route::post('/reset-password', [PasswordResetController::class, 'resetPassword']);
        Route::get('/verify-reset-token', [PasswordResetController::class, 'verifyResetToken']);
    });

    // 2FA routes (public)
    Route::post('/2fa/send-code', [TwoFactorController::class, 'sendCode']);
    Route::post('/2fa/verify-code', [TwoFactorController::class, 'verifyCode']);



    // Protected auth routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
        Route::patch('/profile', [AuthController::class, 'updateProfile']);
        Route::patch('/change-password', [AuthController::class, 'changePassword']);

        // 2FA user settings
        Route::get('/2fa/status', [TwoFactorController::class, 'getStatus']);
        Route::post('/2fa/toggle', [TwoFactorController::class, 'toggleUserTwoFactor']);

        // Admin-only routes
        Route::middleware('admin')->group(function () {
            Route::get('/pending-users', [AuthController::class, 'pendingUsers']);
            Route::post('/approve-user/{user}', [AuthController::class, 'approveUser']);
            Route::post('/2fa/toggle-system', [TwoFactorController::class, 'toggleSystemTwoFactor']);
            Route::post('/cleanup-expired-tokens', [PasswordResetController::class, 'cleanupExpiredTokens']);

            // Email settings routes
            Route::get('/settings/email', [EmailSettingsController::class, 'getEmailSettings']);
            Route::post('/settings/email', [EmailSettingsController::class, 'saveEmailSettings']);
            Route::post('/settings/email/test', [EmailSettingsController::class, 'testEmailConfiguration']);
            Route::post('/settings/email/toggle-real-delivery', [EmailSettingsController::class, 'toggleRealEmailDelivery']);
            Route::post('/settings/email/validate-key', [EmailSettingsController::class, 'validateApiKey']);



            // General settings routes (admin only)
            Route::get('/settings/general', [GeneralSettingsController::class, 'getGeneralSettings']);
            Route::post('/settings/general', [GeneralSettingsController::class, 'saveGeneralSettings']);
        });
    });
});

// Public payment routes (no authentication required)
Route::prefix('v1/public')->group(function () {
    Route::get('/invoice/{invoiceId}', [App\Http\Controllers\PublicPaymentController::class, 'getInvoice']);
    Route::post('/invoice/{invoiceId}/pay', [App\Http\Controllers\PublicPaymentController::class, 'processPayment']);
    Route::get('/payment/{transactionId}/success', [App\Http\Controllers\PublicPaymentController::class, 'getPaymentSuccess']);
});

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public API v1 routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Health check - must be public for Docker health checks
    Route::get('/health', function () {
        return response()->json(['status' => 'ok', 'timestamp' => now()]);
    });

    // URL detection test endpoint
    Route::get('/test-url-detection', function (Request $request) {
        $debugInfo = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'origin' => $request->header('Origin'),
            'referer' => $request->header('Referer'),
            'host' => $request->header('Host'),
            'x_forwarded_host' => $request->header('X-Forwarded-Host'),
            'x_forwarded_proto' => $request->header('X-Forwarded-Proto'),
            'user_agent' => $request->header('User-Agent'),
            'app_url' => config('app.url'),
            'frontend_url_config' => config('app.frontend_url'),
            'app_env' => config('app.env'),
        ];

        // Simulate the URL detection logic
        $origin = $request->header('Origin') ?? $request->header('Referer');
        $detectedUrl = 'http://localhost:3000'; // default

        if ($origin) {
            $parsedUrl = parse_url($origin);
            if ($parsedUrl && isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
                $detectedUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
                if (isset($parsedUrl['port'])) {
                    $isStandardPort = ($parsedUrl['scheme'] === 'http' && $parsedUrl['port'] == 80) ||
                                     ($parsedUrl['scheme'] === 'https' && $parsedUrl['port'] == 443);
                    if (!$isStandardPort) {
                        $detectedUrl .= ':' . $parsedUrl['port'];
                    }
                }
            }
        } else {
            // Fallback logic
            $appUrl = config('app.url', 'http://localhost:4001');
            if (str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space')) {
                $detectedUrl = 'https://ts.crtvmkmn.space';
            } else {
                $detectedUrl = config('app.frontend_url', 'http://localhost:3000');
            }
        }

        return response()->json([
            'detected_frontend_url' => $detectedUrl,
            'debug_info' => $debugInfo,
        ]);
    });
});

// Protected API v1 routes
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Clients
    Route::apiResource('clients', ClientController::class);
    Route::get('clients-statistics', [ClientController::class, 'statistics']);
    Route::get('clients-dashboard-analytics', [ClientController::class, 'dashboardAnalytics']);
    
    // Products
    Route::apiResource('products', ProductController::class);
    
    // Transactions
    Route::apiResource('transactions', TransactionController::class);
    
    // Leads
    Route::apiResource('leads', LeadController::class);
    Route::post('leads/{lead}/convert-to-opportunity', [LeadController::class, 'convertToOpportunity']);
    Route::post('leads/{lead}/convert-to-client', [LeadController::class, 'convertToClient']);
    Route::post('leads/{lead}/convert-to-deal', [DealController::class, 'convertFromLead']);
    Route::post('leads/{lead}/recycle', [LeadController::class, 'recycle']);
    Route::patch('leads/{lead}/lifecycle', [LeadController::class, 'updateLifecycle']);
    Route::get('leads-stats', [LeadController::class, 'stats']);

    // Deals
    Route::apiResource('deals', DealController::class);
    Route::patch('deals/{deal}/move-stage', [DealController::class, 'moveStage']);
    Route::post('deals/{deal}/reopen', [DealController::class, 'reopen']);
    Route::get('deals-pipeline-stats', [DealController::class, 'pipelineStats']);

    // Quotations
    Route::apiResource('quotations', QuotationController::class);
    Route::post('quotations/{quotation}/send', [QuotationController::class, 'send']);
    Route::post('quotations/{quotation}/accept', [QuotationController::class, 'accept']);
    Route::post('quotations/{quotation}/reject', [QuotationController::class, 'reject']);
    Route::post('quotations/{quotation}/duplicate', [QuotationController::class, 'duplicate']);
    Route::post('quotations/{quotation}/convert-to-invoice', [QuotationController::class, 'convertToInvoice']);
    Route::get('quotations-statistics', [QuotationController::class, 'statistics']);

    // Users (Admin and Manager access)
    Route::middleware('manager')->group(function () {
        Route::apiResource('users', UserController::class);
        Route::get('users-stats', [UserController::class, 'stats']);
    });

    // Activity Logs (Admin only)
    Route::middleware('admin')->group(function () {
        Route::get('activity-logs', [App\Http\Controllers\ActivityLogController::class, 'index']);
        Route::delete('activity-logs/clear', [App\Http\Controllers\ActivityLogController::class, 'clearAll']);
    });

    // Invoices
    Route::apiResource('invoices', InvoiceController::class);
    Route::post('invoices/{invoice}/send', [InvoiceController::class, 'send']);
    Route::post('invoices/{invoice}/mark-paid', [InvoiceController::class, 'markAsPaid']);
    Route::post('invoices/{invoice}/add-payment', [InvoiceController::class, 'addPayment']);
    Route::post('invoices/{invoice}/refund', [InvoiceController::class, 'processRefund']);
    Route::get('invoices/{invoice}/payment-history', [InvoiceController::class, 'paymentHistory']);
    Route::get('invoices/client-analytics', [InvoiceController::class, 'clientAnalytics']);
    Route::post('quotations/{quotation}/convert-to-invoice', [InvoiceController::class, 'createFromQuotation']);
    Route::get('invoices-statistics', [InvoiceController::class, 'statistics']);
    
    // Dashboard stats
    Route::prefix('dashboard')->group(function () {
        Route::get('/stats', [App\Http\Controllers\DashboardController::class, 'getStats']);
        Route::get('/revenue-analytics', [App\Http\Controllers\DashboardController::class, 'getRevenueAnalytics']);
        Route::get('/lead-sources', [App\Http\Controllers\DashboardController::class, 'getLeadSources']);
        Route::get('/top-clients', [App\Http\Controllers\DashboardController::class, 'getTopClients']);
        Route::get('/deals-status', [App\Http\Controllers\DashboardController::class, 'getDealsStatus']);
    });

    // Notifications
    Route::get('notifications', [NotificationController::class, 'index']);
    Route::get('notifications/unread-count', [NotificationController::class, 'unreadCount']);
    Route::get('notifications/stats', [NotificationController::class, 'stats']);
    Route::post('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::delete('notifications/delete-all-read', [NotificationController::class, 'deleteAllRead']);
    Route::post('notifications/test', [NotificationController::class, 'createTest']);
    Route::patch('notifications/{notification}/read', [NotificationController::class, 'markAsRead']);
    Route::patch('notifications/{notification}/unread', [NotificationController::class, 'markAsUnread']);
    Route::delete('notifications/{notification}', [NotificationController::class, 'destroy']);

    // Security settings routes
    Route::get('/security-settings', [SecuritySettingsController::class, 'getSecuritySettings']);
    Route::post('/security-settings', [SecuritySettingsController::class, 'updateSecuritySettings']);
    Route::post('/security-settings/test-toyyibpay', [SecuritySettingsController::class, 'testToyyibPayConnection']);

    // CSV Import
    Route::prefix('csv-import')->group(function () {
        Route::get('/fields', [CsvImportController::class, 'getAvailableFields']);
        Route::post('/parse', [CsvImportController::class, 'parseFile']);
        Route::post('/import', [CsvImportController::class, 'import']);
    });

    // Import History
    Route::prefix('import-history')->group(function () {
        Route::get('/', [ImportHistoryController::class, 'index']);
        Route::get('/statistics', [ImportHistoryController::class, 'statistics']);
        Route::get('/{importHistory}', [ImportHistoryController::class, 'show']);
        Route::post('/', [ImportHistoryController::class, 'store']);
        Route::delete('/{importHistory}', [ImportHistoryController::class, 'destroy']);
        Route::delete('/', [ImportHistoryController::class, 'clearAll'])->middleware('admin');
    });

    // Data Management (Admin only)
    Route::middleware('admin')->prefix('data')->group(function () {
        Route::post('/export', [App\Http\Controllers\DataController::class, 'export']);
        Route::delete('/clear/{dataType}', [App\Http\Controllers\DataController::class, 'clear']);
    });
});
