<?php
/**
 * Fix Email Settings Script
 * Updates database with correct Zoho SMTP configuration
 * Run this script to fix email configuration issues
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SystemSetting;
use Illuminate\Support\Facades\DB;

echo "🔧 Fixing Email Settings Configuration...\n\n";

try {
    DB::beginTransaction();

    // Correct email <NAME_EMAIL>
    $emailSettings = [
        // Zoho SMTP Settings (both old and new keys)
        'zoho_smtp_host' => 'smtp.zoho.com',
        'smtp_host' => 'smtp.zoho.com',
        'zoho_smtp_port' => '587',
        'smtp_port' => '587',
        'zoho_smtp_username' => '<EMAIL>',
        'smtp_username' => '<EMAIL>',
        'zoho_smtp_password' => 'PR411zmASdma',
        'smtp_password' => 'PR411zmASdma',
        'zoho_smtp_encryption' => 'tls',
        'smtp_encryption' => 'tls',

        // Email Sender Settings
        'email_from_address' => '<EMAIL>',
        'email_from_name' => 'Tarbiah Sentap CRM',
        'email_reply_to' => '<EMAIL>',

        // System Settings
        'two_factor_auth_enabled' => 'false',
        'app_debug' => 'false',
    ];

    echo "📝 Updating system settings...\n";
    
    foreach ($emailSettings as $key => $value) {
        $type = 'string';
        $description = '';
        
        // Set appropriate types and descriptions
        switch ($key) {
            case 'zoho_smtp_port':
            case 'smtp_port':
                $type = 'integer';
                $description = 'SMTP port number';
                break;
            case 'two_factor_auth_enabled':
                $type = 'boolean';
                $description = 'Enable or disable two-factor authentication system-wide';
                break;
            case 'app_debug':
                $type = 'boolean';
                $description = 'Application debug mode';
                break;
            case 'zoho_smtp_host':
            case 'smtp_host':
                $description = 'SMTP server hostname';
                break;
            case 'zoho_smtp_username':
            case 'smtp_username':
                $description = 'SMTP username (email address)';
                break;
            case 'zoho_smtp_password':
            case 'smtp_password':
                $description = 'SMTP password or app password';
                break;
            case 'zoho_smtp_encryption':
            case 'smtp_encryption':
                $description = 'SMTP encryption method';
                break;
            case 'email_from_address':
                $description = 'Default sender email address';
                break;
            case 'email_from_name':
                $description = 'Default sender name';
                break;
            case 'email_reply_to':
                $description = 'Default reply-to email address';
                break;
        }
        
        SystemSetting::set($key, $value, $type, $description);
        echo "  ✅ {$key}: {$value}\n";
    }

    DB::commit();
    
    echo "\n🎉 Email settings updated successfully!\n\n";
    
    // Verify settings
    echo "📋 Current email configuration:\n";
    echo "  SMTP Host: " . SystemSetting::get('zoho_smtp_host') . "\n";
    echo "  SMTP Port: " . SystemSetting::get('zoho_smtp_port') . "\n";
    echo "  SMTP Username: " . SystemSetting::get('zoho_smtp_username') . "\n";
    echo "  SMTP Password: " . (SystemSetting::get('zoho_smtp_password') ? '[SET]' : '[NOT SET]') . "\n";
    echo "  From Address: " . SystemSetting::get('email_from_address') . "\n";
    echo "  From Name: " . SystemSetting::get('email_from_name') . "\n";
    echo "  2FA Enabled: " . (SystemSetting::get('two_factor_auth_enabled') ? 'true' : 'false') . "\n";
    echo "  Debug Mode: " . (SystemSetting::get('app_debug') ? 'true' : 'false') . "\n";
    
    echo "\n✅ Configuration complete! You can now:\n";
    echo "  1. Remove the temporary 2FA bypass from AuthController\n";
    echo "  2. Test email functionality\n";
    echo "  3. Test 2FA login process\n\n";

} catch (Exception $e) {
    DB::rollBack();
    echo "❌ Error updating settings: " . $e->getMessage() . "\n";
    exit(1);
}
