<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Client;
use Illuminate\Support\Facades\DB;

class RemoveDuplicateClients extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clients:remove-duplicates 
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--force : Force deletion without confirmation}';

    /**
     * The console description of the console command.
     *
     * @var string
     */
    protected $description = 'Remove duplicate client records based on exact matches (name, email, phone)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        $this->info('🔍 Analyzing duplicate client records...');
        
        // Find duplicate groups
        $duplicateGroups = Client::select('name', 'email', 'phone', DB::raw('count(*) as duplicate_count'))
            ->groupBy('name', 'email', 'phone')
            ->havingRaw('count(*) > 1')
            ->orderByRaw('count(*) desc')
            ->get();

        if ($duplicateGroups->isEmpty()) {
            $this->info('✅ No duplicate records found!');
            return 0;
        }

        $totalDuplicateRecords = 0;
        foreach ($duplicateGroups as $group) {
            $totalDuplicateRecords += ($group->duplicate_count - 1);
        }

        $this->info("📊 Found {$duplicateGroups->count()} duplicate groups");
        $this->info("🗑️  Total duplicate records to remove: {$totalDuplicateRecords}");
        $this->info("📈 Current total clients: " . Client::count());
        $this->info("📉 Expected final count: " . (Client::count() - $totalDuplicateRecords));

        if ($isDryRun) {
            $this->warn('🔍 DRY RUN MODE - No records will be deleted');
            $this->showDuplicateDetails($duplicateGroups);
            return 0;
        }

        // Confirmation
        if (!$isForced) {
            if (!$this->confirm("⚠️  Are you sure you want to delete {$totalDuplicateRecords} duplicate records?")) {
                $this->info('❌ Operation cancelled');
                return 0;
            }
        }

        $this->info('🚀 Starting duplicate removal process...');
        
        $deletedCount = 0;
        $progressBar = $this->output->createProgressBar($duplicateGroups->count());
        $progressBar->start();

        DB::beginTransaction();
        
        try {
            foreach ($duplicateGroups as $group) {
                $deletedCount += $this->removeDuplicatesForGroup($group);
                $progressBar->advance();
            }

            DB::commit();
            $progressBar->finish();
            
            $this->newLine(2);
            $this->info("✅ Successfully removed {$deletedCount} duplicate records");
            $this->info("📊 Final client count: " . Client::count());
            
        } catch (\Exception $e) {
            DB::rollback();
            $progressBar->finish();
            $this->newLine(2);
            $this->error("❌ Error occurred: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Remove duplicates for a specific group, keeping the oldest record
     */
    private function removeDuplicatesForGroup($group): int
    {
        $clients = Client::where('name', $group->name)
            ->where('email', $group->email)
            ->where('phone', $group->phone)
            ->orderBy('created_at', 'asc') // Keep the oldest record
            ->get();

        if ($clients->count() <= 1) {
            return 0;
        }

        // Keep the first (oldest) record, delete the rest
        $toDelete = $clients->slice(1);
        $deletedCount = 0;

        foreach ($toDelete as $client) {
            $client->delete();
            $deletedCount++;
        }

        return $deletedCount;
    }

    /**
     * Show details of duplicate records
     */
    private function showDuplicateDetails($duplicateGroups)
    {
        $this->newLine();
        $this->info('📋 Top 20 duplicate groups:');
        
        $headers = ['Name', 'Email', 'Phone', 'Count'];
        $rows = [];

        foreach ($duplicateGroups->take(20) as $group) {
            $rows[] = [
                $group->name ?: 'NULL',
                $group->email ?: 'NULL', 
                $group->phone ?: 'NULL',
                $group->duplicate_count
            ];
        }

        $this->table($headers, $rows);
        
        if ($duplicateGroups->count() > 20) {
            $this->info("... and " . ($duplicateGroups->count() - 20) . " more groups");
        }
    }
}
