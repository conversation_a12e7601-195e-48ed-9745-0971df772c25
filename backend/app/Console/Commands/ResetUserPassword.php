<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class ResetUserPassword extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:reset-password 
                            {email : The email of the user}
                            {--password= : The new password (if not provided, will be prompted)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset a user\'s password';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->option('password');

        // Find user
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        // Get password if not provided
        if (!$password) {
            $password = $this->secret('Enter new password (min 8 characters)');
            $confirmPassword = $this->secret('Confirm new password');

            if ($password !== $confirmPassword) {
                $this->error('Passwords do not match!');
                return 1;
            }
        }

        if (strlen($password) < 8) {
            $this->error('Password must be at least 8 characters long.');
            return 1;
        }

        try {
            $user->update([
                'password' => Hash::make($password),
            ]);

            $this->info('');
            $this->info('✅ Password reset successfully!');
            $this->table(['Field', 'Value'], [
                ['User', $user->name],
                ['Email', $user->email],
                ['Role', $user->role],
                ['Status', $user->is_active ? 'Active' : 'Inactive'],
                ['Last Login', $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never'],
            ]);

            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to reset password: ' . $e->getMessage());
            return 1;
        }
    }
}
