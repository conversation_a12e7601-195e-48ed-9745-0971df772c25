<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create 
                            {--name= : The name of the user}
                            {--email= : The email of the user}
                            {--password= : The password of the user}
                            {--role=user : The role of the user (admin, manager, staff, user)}
                            {--department= : The department of the user}
                            {--interactive : Run in interactive mode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new user account';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('interactive')) {
            return $this->handleInteractive();
        }

        $name = $this->option('name');
        $email = $this->option('email');
        $password = $this->option('password');
        $role = $this->option('role');
        $department = $this->option('department');

        if (!$name || !$email || !$password) {
            $this->error('Name, email, and password are required when not using interactive mode.');
            $this->info('Use --interactive flag for guided user creation.');
            return 1;
        }

        return $this->createUser($name, $email, $password, $role, $department);
    }

    /**
     * Handle interactive user creation
     */
    private function handleInteractive()
    {
        $this->info('=== Create New User ===');
        
        $name = $this->ask('Full name');
        $email = $this->ask('Email address');
        $password = $this->secret('Password (min 8 characters)');
        $confirmPassword = $this->secret('Confirm password');
        
        if ($password !== $confirmPassword) {
            $this->error('Passwords do not match!');
            return 1;
        }

        $role = $this->choice('Role', [
            User::ROLE_ADMIN => 'Admin (Full access)',
            User::ROLE_MANAGER => 'Manager (Team management)',
            User::ROLE_STAFF => 'Staff (Limited access)',
            User::ROLE_USER => 'User (Basic access)'
        ], User::ROLE_USER);

        $department = $this->ask('Department (optional)', null);

        return $this->createUser($name, $email, $password, $role, $department);
    }

    /**
     * Create the user
     */
    private function createUser($name, $email, $password, $role, $department)
    {
        // Validate input
        $validator = Validator::make([
            'name' => $name,
            'email' => $email,
            'password' => $password,
            'role' => $role,
        ], [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'role' => 'required|in:' . implode(',', [User::ROLE_ADMIN, User::ROLE_MANAGER, User::ROLE_STAFF, User::ROLE_USER]),
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        try {
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'role' => $role,
                'department' => $department,
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $this->info('');
            $this->info('✅ User created successfully!');
            $this->table(['Field', 'Value'], [
                ['ID', $user->id],
                ['Name', $user->name],
                ['Email', $user->email],
                ['Role', $user->role],
                ['Department', $user->department ?? 'Not specified'],
                ['Status', 'Active'],
            ]);

            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to create user: ' . $e->getMessage());
            return 1;
        }
    }
}
