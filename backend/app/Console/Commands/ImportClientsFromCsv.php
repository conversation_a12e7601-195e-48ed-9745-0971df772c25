<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Client;
use App\Models\ClientPhoneNumber;
use App\Models\ClientEmail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use League\Csv\Reader;

class ImportClientsFromCsv extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clients:import-csv {file} {--clear : Clear existing clients before import} {--batch=1000 : Batch size for processing} {--skip-duplicates : Skip records with duplicate emails} {--update-duplicates : Update existing records with duplicate emails}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import clients from CSV file with enhanced scoring and validation fields';

    /**
     * Array to track batch processing errors
     *
     * @var array
     */
    private $batchErrors = [];

    /**
     * Array to track duplicate email handling
     *
     * @var array
     */
    private $duplicateEmails = [];

    /**
     * Count of skipped duplicates
     *
     * @var int
     */
    private $skippedDuplicates = 0;

    /**
     * Count of updated duplicates
     *
     * @var int
     */
    private $updatedDuplicates = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $clearExisting = $this->option('clear');
        $batchSize = (int) $this->option('batch');
        $skipDuplicates = $this->option('skip-duplicates');
        $updateDuplicates = $this->option('update-duplicates');

        // Validate file exists
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        // Validate duplicate handling options
        if ($skipDuplicates && $updateDuplicates) {
            $this->error("Cannot use both --skip-duplicates and --update-duplicates options together.");
            return 1;
        }

        $this->info("Starting CSV import from: {$filePath}");

        // Clear existing clients if requested
        if ($clearExisting) {
            $this->warn('Clearing existing clients...');
            if ($this->confirm('Are you sure you want to delete all existing clients?')) {
                Client::truncate();
                $this->info('Existing clients cleared.');
            } else {
                $this->info('Import cancelled.');
                return 0;
            }
        }

        try {
            // Read CSV file
            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0);

            $records = $csv->getRecords();
            $totalRecords = iterator_count($csv->getRecords());

            $this->info("Found {$totalRecords} records to process");

            // Reset iterator
            $records = $csv->getRecords();

            $successCount = 0;
            $errorCount = 0;
            $batch = [];
            $errors = [];

            $progressBar = $this->output->createProgressBar($totalRecords);
            $progressBar->start();

            foreach ($records as $index => $record) {
                try {
                    $clientData = $this->transformCsvRecord($record);

                    // Validate the data
                    $validator = $this->validateClientData($clientData);

                    if ($validator->fails()) {
                        $errorCount++;
                        $errors[] = [
                            'row' => $index + 2, // +2 because CSV is 1-indexed and has header
                            'errors' => $validator->errors()->all(),
                            'data' => $record
                        ];
                        continue;
                    }

                    // Handle duplicate emails
                    $duplicateHandled = $this->handleDuplicateEmail($clientData, $skipDuplicates, $updateDuplicates, $index + 2);

                    if ($duplicateHandled === 'skip') {
                        continue; // Skip this record
                    } elseif ($duplicateHandled === 'update') {
                        continue; // Record was updated, don't add to batch
                    }

                    $batch[] = $clientData;

                    // Process batch when it reaches the specified size
                    if (count($batch) >= $batchSize) {
                        $successCount += $this->processBatch($batch);
                        $batch = [];
                    }

                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = [
                        'row' => $index + 2,
                        'errors' => [$e->getMessage()],
                        'data' => $record
                    ];
                }

                $progressBar->advance();
            }

            // Process remaining batch
            if (!empty($batch)) {
                $successCount += $this->processBatch($batch);
            }

            $progressBar->finish();
            $this->newLine(2);

            // Display comprehensive results
            $this->displayImportSummary($totalRecords, $successCount, $errorCount, $this->skippedDuplicates, $this->updatedDuplicates);

            $totalErrors = $errorCount + count($this->batchErrors);
            if ($totalErrors > 0 || !empty($this->duplicateEmails)) {
                if ($this->confirm('Would you like to see detailed reports?')) {
                    if (!empty($errors)) {
                        $this->info("\n=== VALIDATION ERRORS ===");
                        $this->displayErrors($errors);
                    }

                    if (!empty($this->batchErrors)) {
                        $this->info("\n=== DATABASE INSERT FAILURES ===");
                        $this->displayBatchErrors($this->batchErrors);
                    }

                    if (!empty($this->duplicateEmails)) {
                        $this->info("\n=== DUPLICATE EMAIL HANDLING ===");
                        $this->displayDuplicateEmails($this->duplicateEmails);
                    }
                }

                // Generate error report file
                $this->generateErrorReport($errors, $this->batchErrors, $this->duplicateEmails, $totalRecords);
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Transform CSV record to client data array
     */
    private function transformCsvRecord(array $record): array
    {
        // Clean and normalize phone numbers (handle multiple phones separated by " ::: ")
        $phoneNumbers = $this->parseMultiplePhoneNumbers($record['Phone'] ?? '');

        // Parse boolean values
        $phoneValidity = $this->parseBoolean($record['Phone Validity'] ?? 'FALSE');
        $emailVerified = $this->parseBoolean($record['Email Verified'] ?? 'FALSE');
        $phoneVerified = $this->parseBoolean($record['Phone Verified'] ?? 'FALSE');

        // Map data quality
        $dataQuality = $this->mapDataQuality($record['Data Quality'] ?? 'Poor');

        // Parse total spent (remove RM prefix and convert to decimal)
        $totalSpent = $this->parseCurrency($record['Total Spent'] ?? '0');

        return [
            'uuid' => trim($record['ID'] ?? ''), // Map CSV ID column to UUID
            'name' => trim($record['Name'] ?? ''),
            'email' => !empty(trim($record['Email'] ?? '')) ? trim($record['Email']) : null,
            'phone' => $phoneNumbers['primary'] ?? '', // Keep primary phone in main table for backward compatibility
            'phone_numbers' => $phoneNumbers, // Add phone numbers array for processing
            'status' => 'prospect', // Default status for imported clients
            'utm_source' => $record['utm_source'] ?? '',
            'tags' => !empty($record['tags']) ? json_encode(explode(',', $record['tags'])) : json_encode([]),
            'category' => $record['customer_category'] ?? 'First Timer',
            'ltv_segment' => $record['ltv_segment'] ?? 'Silver',
            'engagement_level' => $record['engagement_level'] ?? 'Cold',
            'priority' => $record['priority'] ?? 'Low',
            'suggested_action' => $record['suggested_action'] ?? '',
            'total_spent' => $totalSpent,
            'transaction_count' => (int) ($record['Transaction Count'] ?? 0),
            'email_verified' => $emailVerified,
            'phone_verified' => $phoneVerified,
            // New scoring fields
            'name_score' => (int) ($record['name_score'] ?? 0),
            'email_score' => (int) ($record['email_score'] ?? 0),
            'phone_score' => (int) ($record['phone_score'] ?? 0),
            'overall_score' => (int) ($record['Overall Score'] ?? 0),
            // New validation fields
            'email_deliverability' => $record['email_deliverability'] ?? null,
            'phone_validity' => $phoneValidity,
            'phone_carrier' => $record['Phone Carrier'] ?? null,
            // New categorization fields
            'data_quality' => $dataQuality,
            'customer_category' => $record['customer_category'] ?? null,
            'notes_remarks' => $record['notes_remarks'] ?? null,
            'suggested_next_action' => $record['suggested_next_action'] ?? null,
            // Personal information fields
            'ic_number' => trim($record['IC Number'] ?? '') ?: null,
            'birthday' => $this->parseDate($record['Birthday'] ?? ''),
            'gender' => $this->parseGender($record['Gender'] ?? ''),
            'religion' => $this->parseReligion($record['Religion'] ?? ''),
            // Financial information
            'income' => $this->parseCurrency($record['Income'] ?? ''),
            'income_category' => $this->parseIncomeCategory($record['Income category'] ?? ''),
            // Enhanced address fields
            'address_line_1' => trim($record['Address line 1'] ?? '') ?: null,
            'address_line_2' => trim($record['Address line 2'] ?? '') ?: null,
            'city' => trim($record['city'] ?? '') ?: null,
            'postcode' => trim($record['postcode'] ?? '') ?: null,
            'state' => trim($record['state'] ?? '') ?: null,
            'district' => null, // Not in CSV, will be null
            // Behavioral data
            'behaviour' => trim($record['behaviour'] ?? '') ?: null,
            'interest' => trim($record['interest'] ?? '') ?: null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Validate client data
     */
    private function validateClientData(array $data): \Illuminate\Validation\Validator
    {
        $rules = [
            'uuid' => 'nullable|string|max:36',
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive,prospect',
            'category' => 'nullable|in:First Timer,Retainer,Loyal,Advocator',
            'ltv_segment' => 'nullable|in:Silver,Gold,Gold+,Platinum',
            'engagement_level' => 'nullable|in:Hot,Warm,Cold,Frozen',
            'priority' => 'nullable|in:High,Medium,Low',
            'total_spent' => 'nullable|numeric|min:0',
            'transaction_count' => 'nullable|integer|min:0',
            'name_score' => 'nullable|integer|min:0|max:100',
            'email_score' => 'nullable|integer|min:0|max:100',
            'phone_score' => 'nullable|integer|min:0|max:100',
            'overall_score' => 'nullable|integer|min:0|max:100',
            'phone_validity' => 'nullable|boolean',
            'data_quality' => 'nullable|in:Poor,Fair,Good,Excellent',
            // Personal information validation
            'ic_number' => 'nullable|string|max:20',
            'birthday' => 'nullable|date',
            'gender' => 'nullable|in:Male,Female',
            'religion' => 'nullable|in:Muslim,Non-Muslim',
            // Financial information validation
            'income' => 'nullable|numeric|min:0',
            'income_category' => 'nullable|in:Low,Medium,High',
            // Enhanced address validation
            'address_line_1' => 'nullable|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'postcode' => 'nullable|string|max:10',
            'state' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            // Behavioral data validation
            'behaviour' => 'nullable|string',
            'interest' => 'nullable|string',
        ];

        return Validator::make($data, $rules);
    }

    /**
     * Process a batch of client data
     */
    private function processBatch(array $batch): int
    {
        $successCount = 0;

        // Process each client individually to handle phone numbers properly
        foreach ($batch as $clientData) {
            try {
                DB::transaction(function () use ($clientData, &$successCount) {
                    // Extract phone numbers data
                    $phoneNumbers = $clientData['phone_numbers'] ?? ['primary' => '', 'all' => []];
                    unset($clientData['phone_numbers']); // Remove from client data

                    // Create the client record
                    $client = Client::create($clientData);

                    // Create phone number records if any exist
                    if (!empty($phoneNumbers['all'])) {
                        foreach ($phoneNumbers['all'] as $index => $phoneNumber) {
                            if (!empty($phoneNumber)) {
                                ClientPhoneNumber::create([
                                    'client_id' => $client->id,
                                    'phone_number' => $phoneNumber,
                                    'is_primary' => $index === 0, // First phone is primary
                                    'phone_verified' => true, // Default to verified for imported data
                                    'phone_score' => $clientData['phone_score'] ?? 0,
                                    'phone_carrier' => $clientData['phone_carrier'] ?? null,
                                ]);
                            }
                        }
                    }

                    $successCount++;
                });
            } catch (\Exception $e) {
                // Log the specific failure
                $this->error("Failed to insert record: " . $e->getMessage());
                $this->error("Record data: " . json_encode([
                    'name' => $clientData['name'] ?? 'N/A',
                    'email' => $clientData['email'] ?? 'N/A',
                    'phone' => $clientData['phone'] ?? 'N/A'
                ]));

                // Add to errors array for detailed reporting
                $this->batchErrors[] = [
                    'data' => $clientData,
                    'error' => $e->getMessage(),
                    'type' => 'database_insert_failure'
                ];
            }
        }

        return $successCount;
    }

    /**
     * Parse multiple phone numbers separated by " ::: "
     */
    private function parseMultiplePhoneNumbers(string $phoneString): array
    {
        if (empty($phoneString)) {
            return ['primary' => '', 'all' => []];
        }

        // Split by " ::: " separator
        $phoneNumbers = explode(' ::: ', $phoneString);
        $normalizedPhones = [];

        foreach ($phoneNumbers as $phone) {
            $normalized = $this->normalizePhoneNumber(trim($phone));
            if (!empty($normalized)) {
                $normalizedPhones[] = $normalized;
            }
        }

        return [
            'primary' => $normalizedPhones[0] ?? '',
            'all' => $normalizedPhones
        ];
    }

    /**
     * Normalize phone number format
     */
    private function normalizePhoneNumber(string $phone): string
    {
        if (empty($phone)) {
            return '';
        }

        // Handle scientific notation (e.g., 6.01646E+11)
        if (strpos($phone, 'E+') !== false) {
            $phone = number_format((float)$phone, 0, '', '');
        }

        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Ensure Malaysian format (60 prefix)
        if (strlen($phone) >= 10 && !str_starts_with($phone, '60')) {
            $phone = '60' . $phone;
        }

        // Truncate to maximum 20 characters to fit database constraint
        if (strlen($phone) > 20) {
            $this->warn("Phone number truncated from " . strlen($phone) . " to 20 characters: " . substr($phone, 0, 10) . "...");
            $phone = substr($phone, 0, 20);
        }

        return $phone;
    }

    /**
     * Parse boolean values from CSV
     */
    private function parseBoolean(string $value): bool
    {
        $value = strtoupper(trim($value));
        return in_array($value, ['TRUE', '1', 'YES', 'Y']);
    }

    /**
     * Map data quality values
     */
    private function mapDataQuality(string $quality): string
    {
        $quality = trim($quality);
        $validQualities = ['Poor', 'Fair', 'Good', 'Excellent'];

        return in_array($quality, $validQualities) ? $quality : 'Poor';
    }

    /**
     * Parse currency values (remove RM prefix)
     */
    private function parseCurrency(string $amount): float
    {
        // Remove RM prefix and any non-numeric characters except decimal point
        $amount = preg_replace('/[^0-9.]/', '', $amount);
        return (float) $amount;
    }

    /**
     * Display import errors
     */
    private function displayErrors(array $errors): void
    {
        $this->newLine();
        $this->error('Import Errors:');

        foreach (array_slice($errors, 0, 10) as $error) { // Show first 10 errors
            $this->warn("Row {$error['row']}: " . implode(', ', $error['errors']));
        }

        if (count($errors) > 10) {
            $this->info('... and ' . (count($errors) - 10) . ' more errors.');
        }
    }

    /**
     * Display batch processing errors
     */
    private function displayBatchErrors(array $errors): void
    {
        $this->newLine();
        $this->error('Database Insert Failures:');

        foreach (array_slice($errors, 0, 10) as $error) { // Show first 10 errors
            $name = $error['data']['name'] ?? 'N/A';
            $email = $error['data']['email'] ?? 'N/A';
            $this->warn("Record: {$name} ({$email}) - Error: {$error['error']}");
        }

        if (count($errors) > 10) {
            $this->info('... and ' . (count($errors) - 10) . ' more database errors.');
        }
    }

    /**
     * Handle duplicate email logic
     *
     * @param array $clientData
     * @param bool $skipDuplicates
     * @param bool $updateDuplicates
     * @param int $rowNumber
     * @return string|null 'skip', 'update', or null to continue
     */
    private function handleDuplicateEmail(array $clientData, bool $skipDuplicates, bool $updateDuplicates, int $rowNumber): ?string
    {
        $email = $clientData['email'] ?? null;

        if (empty($email)) {
            return null; // No email to check
        }

        // Check if email already exists in database
        $existingClient = DB::table('clients')->where('email', $email)->first();

        if (!$existingClient) {
            return null; // No duplicate found
        }

        // Handle duplicate based on options
        if ($skipDuplicates) {
            $this->skippedDuplicates++;
            $this->duplicateEmails[] = [
                'row' => $rowNumber,
                'email' => $email,
                'name' => $clientData['name'] ?? 'N/A',
                'action' => 'skipped'
            ];
            return 'skip';
        }

        if ($updateDuplicates) {
            try {
                // Remove phone_numbers field as it doesn't exist in clients table
                $updateData = $clientData;
                unset($updateData['phone_numbers']);

                // Update existing record
                DB::table('clients')
                    ->where('email', $email)
                    ->update(array_merge($updateData, ['updated_at' => now()]));

                $this->updatedDuplicates++;
                $this->duplicateEmails[] = [
                    'row' => $rowNumber,
                    'email' => $email,
                    'name' => $clientData['name'] ?? 'N/A',
                    'action' => 'updated'
                ];
                return 'update';
            } catch (\Exception $e) {
                $this->error("Failed to update duplicate record at row {$rowNumber}: " . $e->getMessage());
                return 'skip';
            }
        }

        // If neither option is set, let it fail naturally (will be caught by batch processing)
        return null;
    }

    /**
     * Display duplicate email handling results
     */
    private function displayDuplicateEmails(array $duplicates): void
    {
        $this->newLine();
        $this->info('Duplicate Email Handling:');

        foreach (array_slice($duplicates, 0, 10) as $duplicate) { // Show first 10 duplicates
            $action = ucfirst($duplicate['action']);
            $this->info("Row {$duplicate['row']}: {$action} - {$duplicate['name']} ({$duplicate['email']})");
        }

        if (count($duplicates) > 10) {
            $this->info('... and ' . (count($duplicates) - 10) . ' more duplicate email actions.');
        }
    }

    /**
     * Display comprehensive import summary
     */
    private function displayImportSummary(int $totalRecords, int $successCount, int $errorCount, int $skippedDuplicates, int $updatedDuplicates): void
    {
        $this->newLine(2);
        $this->info("=== IMPORT SUMMARY ===");
        $this->info("Total records in CSV: {$totalRecords}");
        $this->info("Successfully imported: {$successCount}");

        if ($skippedDuplicates > 0) {
            $this->info("Skipped duplicates: {$skippedDuplicates}");
        }

        if ($updatedDuplicates > 0) {
            $this->info("Updated duplicates: {$updatedDuplicates}");
        }

        $totalProcessed = $successCount + $skippedDuplicates + $updatedDuplicates;
        $totalFailed = $errorCount + count($this->batchErrors);
        $totalAccountedFor = $totalProcessed + $totalFailed;

        if ($totalFailed > 0) {
            $this->warn("Validation errors: {$errorCount}");
            $this->warn("Database failures: " . count($this->batchErrors));
            $this->warn("Total failed: {$totalFailed}");
        }

        $this->info("Total processed: {$totalAccountedFor}");

        if ($totalAccountedFor != $totalRecords) {
            $missing = $totalRecords - $totalAccountedFor;
            $this->error("⚠️  DISCREPANCY DETECTED: {$missing} records unaccounted for!");
        } else {
            $this->info("✅ All records accounted for.");
        }
    }

    /**
     * Generate detailed error report file
     */
    private function generateErrorReport(array $validationErrors, array $batchErrors, array $duplicateEmails, int $totalRecords): void
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "import_error_report_{$timestamp}.json";
        $filepath = storage_path("logs/{$filename}");

        $report = [
            'import_timestamp' => now()->toISOString(),
            'total_records' => $totalRecords,
            'summary' => [
                'validation_errors' => count($validationErrors),
                'database_failures' => count($batchErrors),
                'duplicate_emails' => count($duplicateEmails),
                'skipped_duplicates' => $this->skippedDuplicates,
                'updated_duplicates' => $this->updatedDuplicates,
            ],
            'validation_errors' => $validationErrors,
            'database_failures' => $batchErrors,
            'duplicate_emails' => $duplicateEmails,
        ];

        file_put_contents($filepath, json_encode($report, JSON_PRETTY_PRINT));
        $this->info("\n📄 Detailed error report saved to: {$filepath}");
    }

    /**
     * Parse date string to Y-m-d format
     */
    private function parseDate(string $date): ?string
    {
        if (empty($date)) {
            return null;
        }

        try {
            $parsedDate = \DateTime::createFromFormat('Y-m-d', $date);
            if ($parsedDate) {
                return $parsedDate->format('Y-m-d');
            }

            // Try other common formats
            $formats = ['d/m/Y', 'm/d/Y', 'd-m-Y', 'm-d-Y'];
            foreach ($formats as $format) {
                $parsedDate = \DateTime::createFromFormat($format, $date);
                if ($parsedDate) {
                    return $parsedDate->format('Y-m-d');
                }
            }
        } catch (\Exception $e) {
            // Invalid date format
        }

        return null;
    }

    /**
     * Parse gender string
     */
    private function parseGender(string $gender): ?string
    {
        $gender = strtolower(trim($gender));

        if (in_array($gender, ['male', 'm', 'lelaki'])) {
            return 'Male';
        }

        if (in_array($gender, ['female', 'f', 'perempuan'])) {
            return 'Female';
        }

        return null;
    }

    /**
     * Parse religion string
     */
    private function parseReligion(string $religion): ?string
    {
        $religion = strtolower(trim($religion));

        if (in_array($religion, ['muslim', 'islam', 'islamic'])) {
            return 'Muslim';
        }

        if (!empty($religion)) {
            return 'Non-Muslim';
        }

        return null;
    }

    /**
     * Parse income category string
     */
    private function parseIncomeCategory(string $category): ?string
    {
        $category = strtolower(trim($category));

        if (in_array($category, ['low', 'rendah'])) {
            return 'Low';
        }

        if (in_array($category, ['medium', 'sederhana', 'middle'])) {
            return 'Medium';
        }

        if (in_array($category, ['high', 'tinggi'])) {
            return 'High';
        }

        return null;
    }
}
