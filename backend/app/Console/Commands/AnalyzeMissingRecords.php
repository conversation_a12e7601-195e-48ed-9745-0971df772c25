<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Client;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;

class AnalyzeMissingRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clients:analyze-missing {csv_file} {--output=missing_records.csv : Output file for missing records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze CSV file to identify records that were not imported to the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $csvFile = $this->argument('csv_file');
        $outputFile = $this->option('output');

        // Validate file exists
        if (!file_exists($csvFile)) {
            $this->error("CSV file not found: {$csvFile}");
            return 1;
        }

        $this->info("Analyzing missing records from: {$csvFile}");

        try {
            // Read CSV file
            $csv = Reader::createFromPath($csvFile, 'r');
            $csv->setHeaderOffset(0);
            $records = $csv->getRecords();
            
            $totalCsvRecords = iterator_count($csv->getRecords());
            $this->info("Total records in CSV: {$totalCsvRecords}");

            // Get current database count
            $dbCount = Client::count();
            $this->info("Total records in database: {$dbCount}");

            // Reset iterator
            $records = $csv->getRecords();

            $missingRecords = [];
            $foundCount = 0;
            $duplicateEmails = [];
            $invalidEmails = [];

            $progressBar = $this->output->createProgressBar($totalCsvRecords);
            $progressBar->start();

            // First pass: collect all emails and check for duplicates in CSV
            $csvEmails = [];
            $emailCounts = [];

            foreach ($records as $index => $record) {
                $email = trim($record['Email'] ?? '');
                if (!empty($email)) {
                    $csvEmails[] = [
                        'index' => $index,
                        'email' => $email,
                        'name' => trim($record['Name'] ?? ''),
                        'phone' => $record['Phone'] ?? '',
                        'record' => $record
                    ];
                    $emailCounts[$email] = ($emailCounts[$email] ?? 0) + 1;
                }
            }

            // Reset records iterator for second pass
            $records = $csv->getRecords();

            foreach ($records as $index => $record) {
                $email = trim($record['Email'] ?? '');
                $name = trim($record['Name'] ?? '');

                // Skip records without email
                if (empty($email)) {
                    $invalidEmails[] = [
                        'row' => $index + 2,
                        'name' => $name,
                        'reason' => 'Empty email'
                    ];
                    $progressBar->advance();
                    continue;
                }

                // Check if email exists in database
                $existsInDb = DB::table('clients')->where('email', $email)->exists();

                if ($existsInDb) {
                    $foundCount++;
                } else {
                    $csvDuplicateCount = $emailCounts[$email] ?? 1;

                    $missingRecords[] = [
                        'row' => $index + 2,
                        'name' => $name,
                        'email' => $email,
                        'phone' => $record['Phone'] ?? '',
                        'csv_duplicate_count' => $csvDuplicateCount,
                        'original_record' => $record
                    ];

                    if ($csvDuplicateCount > 1) {
                        $duplicateEmails[] = $email;
                    }
                }

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine(2);

            // Display analysis results
            $this->displayAnalysisResults($totalCsvRecords, $dbCount, $foundCount, count($missingRecords), count($invalidEmails), count(array_unique($duplicateEmails)));

            // Save missing records to CSV
            if (!empty($missingRecords)) {
                $this->saveMissingRecordsToFile($missingRecords, $outputFile);
            }

            // Display recommendations
            $this->displayRecommendations($missingRecords, $duplicateEmails, $invalidEmails);

            return 0;

        } catch (\Exception $e) {
            $this->error("Analysis failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Display analysis results
     */
    private function displayAnalysisResults(int $totalCsv, int $dbCount, int $foundCount, int $missingCount, int $invalidEmails, int $duplicateEmails): void
    {
        $this->info("=== ANALYSIS RESULTS ===");
        $this->info("CSV records: {$totalCsv}");
        $this->info("Database records: {$dbCount}");
        $this->info("CSV records found in DB: {$foundCount}");
        $this->info("CSV records missing from DB: {$missingCount}");
        $this->info("CSV records with invalid emails: {$invalidEmails}");
        $this->info("Duplicate emails in CSV: {$duplicateEmails}");
        
        $accountedFor = $foundCount + $missingCount + $invalidEmails;
        $this->info("Total accounted for: {$accountedFor}");
        
        if ($accountedFor != $totalCsv) {
            $discrepancy = $totalCsv - $accountedFor;
            $this->warn("⚠️  Analysis discrepancy: {$discrepancy} records");
        }
    }

    /**
     * Save missing records to CSV file
     */
    private function saveMissingRecordsToFile(array $missingRecords, string $outputFile): void
    {
        $csvContent = "Row,Name,Email,Phone,CSV_Duplicate_Count\n";
        
        foreach ($missingRecords as $record) {
            $csvContent .= sprintf(
                "%d,\"%s\",\"%s\",\"%s\",%d\n",
                $record['row'],
                str_replace('"', '""', $record['name']),
                str_replace('"', '""', $record['email']),
                str_replace('"', '""', $record['phone']),
                $record['csv_duplicate_count']
            );
        }

        file_put_contents($outputFile, $csvContent);
        $this->info("📄 Missing records saved to: {$outputFile}");
    }

    /**
     * Display recovery recommendations
     */
    private function displayRecommendations(array $missingRecords, array $duplicateEmails, array $invalidEmails): void
    {
        $this->newLine();
        $this->info("=== RECOVERY RECOMMENDATIONS ===");
        
        if (!empty($missingRecords)) {
            $this->info("1. Re-import missing records using:");
            $this->info("   php artisan clients:import-csv {$this->argument('csv_file')} --skip-duplicates");
            $this->info("   (This will skip existing records and only import missing ones)");
        }
        
        if (!empty($duplicateEmails)) {
            $this->warn("2. Handle duplicate emails in CSV:");
            $this->warn("   - " . count(array_unique($duplicateEmails)) . " emails appear multiple times in CSV");
            $this->warn("   - Consider data cleaning before re-import");
        }
        
        if (!empty($invalidEmails)) {
            $this->warn("3. Handle invalid emails:");
            $this->warn("   - " . count($invalidEmails) . " records have empty/invalid emails");
            $this->warn("   - These records cannot be imported due to email requirement");
        }
        
        $this->info("4. Verify import with enhanced error reporting:");
        $this->info("   - Check generated error report files");
        $this->info("   - Review validation and database failure logs");
    }
}
