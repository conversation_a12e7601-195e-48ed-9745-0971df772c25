<?php

namespace App\Console\Commands;

use App\Models\PasswordResetToken;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupExpiredPasswordResetTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'password-reset:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired password reset tokens';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired password reset tokens...');

        try {
            $deletedCount = PasswordResetToken::cleanupExpired();
            
            $this->info("Successfully cleaned up {$deletedCount} expired tokens.");
            
            Log::info('Password reset tokens cleanup completed', [
                'deleted_count' => $deletedCount,
                'executed_at' => now()
            ]);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to cleanup expired tokens: ' . $e->getMessage());
            
            Log::error('Password reset tokens cleanup failed', [
                'error' => $e->getMessage(),
                'executed_at' => now()
            ]);

            return Command::FAILURE;
        }
    }
}
