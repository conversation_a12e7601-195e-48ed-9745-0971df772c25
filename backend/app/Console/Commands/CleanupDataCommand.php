<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Client;
use App\Models\Lead;
use App\Models\Deal;
use App\Models\Quotation;
use App\Models\Transaction;
use App\Models\Product;
use Illuminate\Support\Facades\DB;

class CleanupDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'data:cleanup {--keep-default-users : Keep the default admin, manager, and user accounts}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up all data from the system, optionally keeping default users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting data cleanup...');

        try {
            // Clean up all data
            $this->cleanupTransactions();
            $this->cleanupQuotations();
            $this->cleanupDeals();
            $this->cleanupLeads();
            $this->cleanupClients();
            $this->cleanupProducts();
            $this->cleanupForms();
            $this->cleanupLogs();

            // Clean up users (optionally keep default users)
            $this->cleanupUsers();

            $this->info('Data cleanup completed successfully!');
        } catch (\Exception $e) {
            $this->error('Error during cleanup: ' . $e->getMessage());
        }
    }

    private function cleanupTransactions()
    {
        $count = Transaction::count();
        Transaction::truncate();
        $this->info("Cleaned up {$count} transactions");
    }

    private function cleanupQuotations()
    {
        $count = Quotation::count();
        Quotation::truncate();
        $this->info("Cleaned up {$count} quotations");
    }

    private function cleanupDeals()
    {
        $count = Deal::count();
        Deal::truncate();
        $this->info("Cleaned up {$count} deals");
    }

    private function cleanupLeads()
    {
        $count = Lead::count();
        Lead::truncate();
        $this->info("Cleaned up {$count} leads");
    }

    private function cleanupClients()
    {
        $count = Client::count();
        Client::truncate();
        $this->info("Cleaned up {$count} clients");
    }

    private function cleanupProducts()
    {
        $count = Product::count();
        Product::truncate();
        $this->info("Cleaned up {$count} products");
    }

    private function cleanupForms()
    {
        // Clean up forms table if it exists
        if (DB::getSchemaBuilder()->hasTable('forms')) {
            $count = DB::table('forms')->count();
            DB::table('forms')->truncate();
            $this->info("Cleaned up {$count} forms");
        }
    }

    private function cleanupLogs()
    {
        // Clean up logs table if it exists
        if (DB::getSchemaBuilder()->hasTable('logs')) {
            $count = DB::table('logs')->count();
            DB::table('logs')->truncate();
            $this->info("Cleaned up {$count} logs");
        }
    }

    private function cleanupUsers()
    {
        if ($this->option('keep-default-users')) {
            // Keep default users but remove all others
            $defaultEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
            $count = User::whereNotIn('email', $defaultEmails)->count();
            User::whereNotIn('email', $defaultEmails)->delete();
            $this->info("Cleaned up {$count} users (kept default users)");
        } else {
            // Remove all users
            $count = User::count();
            User::truncate();
            $this->info("Cleaned up {$count} users");
        }
    }
}
