<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class NotificationController extends Controller
{
    /**
     * Get notifications for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $query = Notification::where('user_id', auth()->id())
                            ->with(['createdBy:id,name'])
                            ->orderBy('created_at', 'desc');

        // Filter by read status
        if ($request->has('unread_only') && $request->boolean('unread_only')) {
            $query->unread();
        }

        // Filter by type
        if ($request->has('type')) {
            $query->byType($request->type);
        }

        // Filter by category
        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->byPriority($request->priority);
        }

        // Pagination
        $perPage = min($request->get('per_page', 20), 100);
        $notifications = $query->paginate($perPage);

        return response()->json($notifications);
    }

    /**
     * Get unread notification count
     */
    public function unreadCount(): JsonResponse
    {
        $count = Notification::where('user_id', auth()->id())
                           ->unread()
                           ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Mark a notification as read
     */
    public function markAsRead(Notification $notification): JsonResponse
    {
        // Ensure user can only mark their own notifications
        if ($notification->user_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $notification->markAsRead();

        return response()->json(['message' => 'Notification marked as read']);
    }

    /**
     * Mark a notification as unread
     */
    public function markAsUnread(Notification $notification): JsonResponse
    {
        // Ensure user can only mark their own notifications
        if ($notification->user_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $notification->markAsUnread();

        return response()->json(['message' => 'Notification marked as unread']);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        $count = Notification::where('user_id', auth()->id())
                           ->unread()
                           ->update(['read_at' => now()]);

        return response()->json([
            'message' => 'All notifications marked as read',
            'count' => $count
        ]);
    }

    /**
     * Delete a notification
     */
    public function destroy(Notification $notification): JsonResponse
    {
        // Ensure user can only delete their own notifications
        if ($notification->user_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $notification->delete();

        return response()->json(['message' => 'Notification deleted']);
    }

    /**
     * Delete all read notifications
     */
    public function deleteAllRead(): JsonResponse
    {
        $count = Notification::where('user_id', auth()->id())
                           ->read()
                           ->delete();

        return response()->json([
            'message' => 'All read notifications deleted',
            'count' => $count
        ]);
    }

    /**
     * Get notification statistics
     */
    public function stats(): JsonResponse
    {
        $userId = auth()->id();

        $stats = [
            'total' => Notification::where('user_id', $userId)->count(),
            'unread' => Notification::where('user_id', $userId)->unread()->count(),
            'by_category' => Notification::where('user_id', $userId)
                                       ->selectRaw('category, COUNT(*) as count')
                                       ->groupBy('category')
                                       ->pluck('count', 'category'),
            'by_priority' => Notification::where('user_id', $userId)
                                       ->selectRaw('priority, COUNT(*) as count')
                                       ->groupBy('priority')
                                       ->pluck('count', 'priority'),
            'recent_count' => Notification::where('user_id', $userId)
                                        ->where('created_at', '>=', now()->subDays(7))
                                        ->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Create a test notification (for development)
     */
    public function createTest(Request $request): JsonResponse
    {
        if (!app()->environment('local')) {
            return response()->json(['message' => 'Test notifications only available in local environment'], 403);
        }

        $notification = Notification::create([
            'user_id' => auth()->id(),
            'type' => $request->get('type', Notification::TYPE_SYSTEM_ALERT),
            'title' => $request->get('title', 'Test Notification'),
            'message' => $request->get('message', 'This is a test notification'),
            'priority' => $request->get('priority', Notification::PRIORITY_NORMAL),
            'category' => $request->get('category', Notification::CATEGORY_SYSTEM),
            'data' => $request->get('data', []),
            'action_url' => $request->get('action_url'),
            'created_by' => auth()->id(),
        ]);

        return response()->json($notification, 201);
    }
}
