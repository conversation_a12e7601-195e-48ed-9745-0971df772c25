<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;

class TwoFactorController extends Controller
{
    /**
     * Send 2FA code to user's email
     */
    public function sendCode(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = \App\Models\User::where('email', $request->email)->first();

        if (!$user || !$user->is_active) {
            throw ValidationException::withMessages([
                'email' => ['Invalid email address.'],
            ]);
        }

        // Check if 2FA is enabled system-wide
        if (!SystemSetting::get('two_factor_auth_enabled', false)) {
            return response()->json([
                'message' => 'Two-factor authentication is not enabled'
            ], 400);
        }

        // Generate and send 2FA code
        $code = $user->generateTwoFactorCode();

        // Send email with 2FA code
        try {
            // Get configurable email settings
            $fromEmail = SystemSetting::get('email_from_address', '<EMAIL>');
            $fromName = SystemSetting::get('email_from_name', 'KDT CRM System');
            $replyTo = SystemSetting::get('email_reply_to', '<EMAIL>');

            // Configure SMTP settings dynamically
            $this->configureMailSettings();

            Mail::raw("Your verification code is: {$code}\n\nThis code will expire in 10 minutes.", function ($message) use ($user, $fromEmail, $fromName, $replyTo) {
                $message->to($user->email)
                        ->from($fromEmail, $fromName)
                        ->replyTo($replyTo)
                        ->subject('Two-Factor Authentication Code - KDT CRM System');
            });
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'TwoFactorController Email error: ' . $e->getMessage() . ' | Class: ' . get_class($e)
            ], 500);
        }

        return response()->json([
            'message' => 'Verification code sent to your email',
            'expires_at' => $user->two_factor_expires_at
        ]);
    }

    /**
     * Verify 2FA code
     */
    public function verifyCode(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required|string|size:6',
        ]);

        $user = \App\Models\User::where('email', $request->email)->first();

        if (!$user || !$user->is_active) {
            throw ValidationException::withMessages([
                'email' => ['Invalid email address.'],
            ]);
        }

        if (!$user->verifyTwoFactorCode($request->code)) {
            throw ValidationException::withMessages([
                'code' => ['Invalid or expired verification code.'],
            ]);
        }

        // Update last login
        $user->update(['last_login_at' => now()]);

        // Create token
        $token = $user->createToken('auth-token')->plainTextToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
            'message' => 'Login successful'
        ]);
    }

    /**
     * Toggle user's 2FA setting
     */
    public function toggleUserTwoFactor(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $user->update([
            'two_factor_enabled' => !$user->two_factor_enabled
        ]);

        return response()->json([
            'message' => $user->two_factor_enabled ? '2FA enabled' : '2FA disabled',
            'two_factor_enabled' => $user->two_factor_enabled
        ]);
    }

    /**
     * Get 2FA status
     */
    public function getStatus(Request $request): JsonResponse
    {
        $user = $request->user();
        
        return response()->json([
            'system_enabled' => SystemSetting::get('two_factor_auth_enabled', false),
            'user_enabled' => $user->two_factor_enabled,
            'requires_2fa' => $user->requiresTwoFactor(),
        ]);
    }

    /**
     * Toggle system-wide 2FA setting (admin only)
     */
    public function toggleSystemTwoFactor(Request $request): JsonResponse
    {
        $currentValue = SystemSetting::get('two_factor_auth_enabled', false);
        $newValue = !$currentValue;
        
        SystemSetting::set('two_factor_auth_enabled', $newValue, 'boolean', 'Enable or disable two-factor authentication system-wide');

        return response()->json([
            'message' => $newValue ? 'System-wide 2FA enabled' : 'System-wide 2FA disabled',
            'two_factor_auth_enabled' => $newValue
        ]);
    }

    /**
     * Test email functionality (development only)
     */
    public function testEmail(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        try {
            // Get configurable email settings
            $fromEmail = SystemSetting::get('email_from_address', '<EMAIL>');
            $fromName = SystemSetting::get('email_from_name', 'KDT CRM System');
            $replyTo = SystemSetting::get('email_reply_to', '<EMAIL>');

            // Configure SMTP settings dynamically
            $this->configureMailSettings();

            Mail::raw("This is a test email from KDT CRM System.\n\nIf you receive this, email configuration is working correctly.", function ($message) use ($request, $fromEmail, $fromName, $replyTo) {
                $message->to($request->email)
                        ->from($fromEmail, $fromName)
                        ->replyTo($replyTo)
                        ->subject('Test Email - KDT CRM System');
            });

            return response()->json([
                'message' => 'Test email sent successfully',
                'email' => $request->email
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Configure mail settings based on environment and user preference
     */
    private function configureMailSettings(): void
    {
        // Check if user wants to force real email delivery (for testing)
        $forceRealEmail = SystemSetting::get('force_real_email_delivery', false);

        // Log configuration decision
        Log::info('TwoFactorController - Email Configuration Decision', [
            'force_real_email' => $forceRealEmail,
            'environment' => app()->environment(),
            'current_mail_host' => config('mail.mailers.smtp.host'),
            'is_local_dev' => app()->environment('local', 'development'),
            'is_mailpit_host' => config('mail.mailers.smtp.host') === 'kdt-mailpit'
        ]);

        // In development, use Mailpit unless real email is forced
        if (!$forceRealEmail && (app()->environment('local', 'development') || config('mail.mailers.smtp.host') === 'kdt-mailpit')) {
            Log::info('TwoFactorController - Using Mailpit for email delivery');
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => 'kdt-mailpit',
                'mail.mailers.smtp.port' => 1025,
                'mail.mailers.smtp.username' => null,
                'mail.mailers.smtp.password' => null,
                'mail.mailers.smtp.encryption' => null,
            ]);
            return;
        }

        // Use Zoho settings for production or when real email is forced
        $smtpHost = SystemSetting::get('zoho_smtp_host', 'smtp.zoho.com');
        $smtpPort = SystemSetting::get('zoho_smtp_port', 587);
        $smtpUsername = SystemSetting::get('zoho_smtp_username', '');
        $smtpEncryption = SystemSetting::get('zoho_smtp_encryption', 'tls');
        $smtpPassword = SystemSetting::get('zoho_smtp_password', '');

        Log::info('TwoFactorController - Using Zoho SMTP for email delivery', [
            'smtp_host' => $smtpHost,
            'smtp_port' => $smtpPort,
            'smtp_username' => $smtpUsername,
            'smtp_encryption' => $smtpEncryption,
            'password_set' => !empty($smtpPassword)
        ]);

        // Configure mail settings
        config([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => $smtpHost,
            'mail.mailers.smtp.port' => $smtpPort,
            'mail.mailers.smtp.username' => $smtpUsername,
            'mail.mailers.smtp.password' => $smtpPassword,
            'mail.mailers.smtp.encryption' => $smtpEncryption === 'none' ? null : $smtpEncryption,
        ]);
    }

    /**
     * Determine frontend URL based on request origin with comprehensive logging
     */
    private function determineFrontendUrl(Request $request): string
    {
        // Log all relevant request information for debugging
        $debugInfo = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'origin' => $request->header('Origin'),
            'referer' => $request->header('Referer'),
            'host' => $request->header('Host'),
            'x_forwarded_host' => $request->header('X-Forwarded-Host'),
            'x_forwarded_proto' => $request->header('X-Forwarded-Proto'),
            'user_agent' => $request->header('User-Agent'),
            'app_url' => config('app.url'),
            'frontend_url_config' => config('app.frontend_url'),
            'app_env' => config('app.env'),
        ];

        Log::info('2FA URL Detection Debug Info', $debugInfo);

        // Get the origin header from the request with multiple fallbacks
        $origin = $request->header('Origin')
               ?? $request->header('Referer')
               ?? $request->header('X-Forwarded-Host')
               ?? $request->header('Host');

        Log::info('2FA URL Detection - Origin Analysis', [
            'origin_header' => $request->header('Origin'),
            'referer_header' => $request->header('Referer'),
            'selected_origin' => $origin,
        ]);

        if ($origin) {
            // Parse the origin to get the base URL
            $parsedUrl = parse_url($origin);
            Log::info('2FA URL Detection - Parsed URL', [
                'original_origin' => $origin,
                'parsed_url' => $parsedUrl,
            ]);

            if ($parsedUrl && isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
                $frontendUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

                // Add port if it's not standard (80 for HTTP, 443 for HTTPS)
                if (isset($parsedUrl['port'])) {
                    $isStandardPort = ($parsedUrl['scheme'] === 'http' && $parsedUrl['port'] == 80) ||
                                     ($parsedUrl['scheme'] === 'https' && $parsedUrl['port'] == 443);

                    if (!$isStandardPort) {
                        $frontendUrl .= ':' . $parsedUrl['port'];
                    }
                }

                Log::info('2FA URL Detection - Origin Based URL', [
                    'detected_frontend_url' => $frontendUrl,
                    'source' => 'origin_header'
                ]);

                return $frontendUrl;
            }
        }

        // Fallback to config or environment-based detection
        $appUrl = config('app.url', 'http://localhost:4001');
        $frontendUrlConfig = config('app.frontend_url', 'http://localhost:3000');
        $requestHost = $request->header('Host');

        Log::info('2FA URL Detection - Fallback Analysis', [
            'app_url' => $appUrl,
            'frontend_url_config' => $frontendUrlConfig,
            'request_host' => $requestHost,
            'contains_production_domain' => str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space'),
            'host_is_production' => str_contains($requestHost ?? '', 'ts.crtvmkmn.space') || str_contains($requestHost ?? '', 'crtvmkmn.space'),
        ]);

        // Check if request is coming to production domain
        if ($requestHost && (str_contains($requestHost, 'ts.crtvmkmn.space') || str_contains($requestHost, 'crtvmkmn.space'))) {
            Log::info('2FA URL Detection - Production Host Detected', [
                'detected_frontend_url' => 'https://ts.crtvmkmn.space',
                'source' => 'request_host_detection'
            ]);
            return 'https://ts.crtvmkmn.space';
        }

        // If backend is on production domain, use production frontend
        if (str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space')) {
            Log::info('2FA URL Detection - Production Domain Detected', [
                'detected_frontend_url' => 'https://ts.crtvmkmn.space',
                'source' => 'production_domain_detection'
            ]);
            return 'https://ts.crtvmkmn.space';
        }

        // Check if frontend_url config is set to production
        if (str_contains($frontendUrlConfig, 'ts.crtvmkmn.space') || str_contains($frontendUrlConfig, 'crtvmkmn.space')) {
            Log::info('2FA URL Detection - Production Frontend Config', [
                'detected_frontend_url' => $frontendUrlConfig,
                'source' => 'frontend_url_config'
            ]);
            return $frontendUrlConfig;
        }

        // Default to development frontend
        Log::info('2FA URL Detection - Default Fallback', [
            'detected_frontend_url' => $frontendUrlConfig,
            'source' => 'default_fallback'
        ]);

        return $frontendUrlConfig;
    }
}
