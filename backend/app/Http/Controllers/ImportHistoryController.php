<?php

namespace App\Http\Controllers;

use App\Models\ImportHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ImportHistoryController extends Controller
{
    /**
     * Display a listing of import histories
     */
    public function index(Request $request): JsonResponse
    {
        $query = ImportHistory::with(['user'])
            ->orderBy('created_at', 'desc');

        // Filters
        if ($request->has('status')) {
            $query->byStatus($request->get('status'));
        }

        if ($request->has('data_type')) {
            $query->byDataType($request->get('data_type'));
        }

        if ($request->has('user_id')) {
            $query->byUser($request->get('user_id'));
        }

        // Date range filtering
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->get('start_date'));
        }

        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->get('end_date'));
        }

        // Search
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('file_name', 'like', "%{$search}%")
                  ->orWhere('data_type', 'like', "%{$search}%");
            });
        }

        // Pagination
        $perPage = $request->get('per_page', 20);
        $imports = $query->paginate($perPage);

        // Add computed attributes
        $imports->getCollection()->transform(function ($import) {
            $import->formatted_file_size = $import->formatted_file_size;
            $import->formatted_processing_time = $import->formatted_processing_time;
            $import->status_badge_color = $import->status_badge_color;
            return $import;
        });

        return response()->json($imports);
    }

    /**
     * Display the specified import history
     */
    public function show(ImportHistory $importHistory): JsonResponse
    {
        $importHistory->load(['user']);
        $importHistory->formatted_file_size = $importHistory->formatted_file_size;
        $importHistory->formatted_processing_time = $importHistory->formatted_processing_time;
        $importHistory->status_badge_color = $importHistory->status_badge_color;

        return response()->json($importHistory);
    }

    /**
     * Get import statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        
        $stats = [
            'total_imports' => ImportHistory::recent($days)->count(),
            'successful_imports' => ImportHistory::recent($days)->byStatus(ImportHistory::STATUS_COMPLETED)->count(),
            'failed_imports' => ImportHistory::recent($days)->byStatus(ImportHistory::STATUS_FAILED)->count(),
            'total_records_processed' => ImportHistory::recent($days)->sum('processed'),
            'total_records_created' => ImportHistory::recent($days)->sum('created'),
            'total_records_updated' => ImportHistory::recent($days)->sum('updated'),
            'average_processing_time' => ImportHistory::recent($days)
                ->whereNotNull('processing_time')
                ->avg('processing_time'),
            'average_success_rate' => ImportHistory::recent($days)
                ->whereNotNull('success_rate')
                ->avg('success_rate'),
        ];

        // Data type breakdown
        $dataTypeStats = ImportHistory::recent($days)
            ->selectRaw('data_type, COUNT(*) as count, SUM(created) as total_created')
            ->groupBy('data_type')
            ->get();

        $stats['data_type_breakdown'] = $dataTypeStats;

        // Recent activity (last 7 days)
        $recentActivity = ImportHistory::recent(7)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as imports, SUM(created) as records_created')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        $stats['recent_activity'] = $recentActivity;

        return response()->json($stats);
    }

    /**
     * Delete the specified import history
     */
    public function destroy(ImportHistory $importHistory): JsonResponse
    {
        $importHistory->delete();

        return response()->json([
            'message' => 'Import history deleted successfully'
        ]);
    }

    /**
     * Clear all import histories
     */
    public function clearAll(): JsonResponse
    {
        $count = ImportHistory::count();
        ImportHistory::truncate();

        return response()->json([
            'message' => "Successfully cleared {$count} import histories",
            'cleared_count' => $count
        ]);
    }

    /**
     * Create import history record
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'file_name' => 'required|string|max:255',
            'file_size' => 'nullable|integer',
            'data_type' => 'required|string|max:50',
            'file_format' => 'required|string|max:10',
            'total_rows' => 'required|integer|min:0',
            'processed' => 'required|integer|min:0',
            'created' => 'required|integer|min:0',
            'updated' => 'required|integer|min:0',
            'skipped' => 'required|integer|min:0',
            'errors_count' => 'required|integer|min:0',
            'duplicates_count' => 'nullable|integer|min:0',
            'processing_time' => 'nullable|numeric|min:0',
            'success_rate' => 'nullable|numeric|min:0|max:100',
            'error_details' => 'nullable|array',
            'import_options' => 'nullable|array',
            'field_mapping' => 'nullable|array',
            'started_at' => 'nullable|date',
            'completed_at' => 'nullable|date',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['status'] = ImportHistory::STATUS_COMPLETED;

        $importHistory = ImportHistory::create($validated);

        return response()->json([
            'message' => 'Import history created successfully',
            'data' => $importHistory
        ], 201);
    }
}
