<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TransactionController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Transaction::with('client');

        if ($request->has('client_id')) {
            $query->where('client_id', $request->get('client_id'));
        }

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        $transactions = $query->orderBy('created_at', 'desc')
                             ->paginate($request->get('per_page', 15));

        return response()->json($transactions);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'type' => 'required|in:invoice,quote,order,payment',
            'status' => 'required|in:draft,pending,completed,cancelled',
            'amount' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:50',
            'payment_status' => 'required|in:pending,paid,partial,overdue',
            'due_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        $validated['transaction_number'] = 'TXN-' . strtoupper(uniqid());
        $validated['total_amount'] = $validated['amount'] + ($validated['tax_amount'] ?? 0);

        $transaction = Transaction::create($validated);

        return response()->json($transaction->load('client'), 201);
    }

    public function show(Transaction $transaction): JsonResponse
    {
        return response()->json($transaction->load(['client', 'products']));
    }

    public function update(Request $request, Transaction $transaction): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'sometimes|required|in:invoice,quote,order,payment',
            'status' => 'sometimes|required|in:draft,pending,completed,cancelled',
            'amount' => 'sometimes|required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:50',
            'payment_status' => 'sometimes|required|in:pending,paid,partial,overdue',
            'due_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        if (isset($validated['amount']) || isset($validated['tax_amount'])) {
            $validated['total_amount'] = ($validated['amount'] ?? $transaction->amount) + 
                                       ($validated['tax_amount'] ?? $transaction->tax_amount ?? 0);
        }

        $transaction->update($validated);

        return response()->json($transaction->load('client'));
    }

    public function destroy(Transaction $transaction): JsonResponse
    {
        $transaction->delete();

        return response()->json(['message' => 'Transaction deleted successfully']);
    }
}
