<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::query();

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('email', 'ILIKE', "%{$search}%")
                  ->orWhere('department', 'ILIKE', "%{$search}%");
            });
        }

        if ($request->has('role')) {
            $query->where('role', $request->get('role'));
        }

        if ($request->has('department')) {
            $query->where('department', $request->get('department'));
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $users = $query->orderBy('created_at', 'desc')
                      ->paginate($request->get('per_page', 15));

        return response()->json($users);
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->where(function ($query) {
                    return $query->where('is_active', true);
                })
            ],
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
            'role' => ['required', Rule::in([User::ROLE_ADMIN, User::ROLE_STAFF, User::ROLE_MANAGER, User::ROLE_USER])],
            'department' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? true;
        $user = User::create($validated);

        return response()->json($user, 201);
    }

    /**
     * Display the specified user
     */
    public function show(User $user): JsonResponse
    {
        return response()->json($user->load(['assignedLeads', 'assignedDeals', 'createdQuotations']));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => [
                'sometimes',
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($user->id)->where(function ($query) {
                    return $query->where('is_active', true);
                })
            ],
            'password' => 'sometimes|required|string|min:8',
            'phone' => 'nullable|string|max:20',
            'role' => ['sometimes', 'required', Rule::in([User::ROLE_ADMIN, User::ROLE_STAFF, User::ROLE_MANAGER, User::ROLE_USER])],
            'department' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        $user->update($validated);

        return response()->json($user);
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): JsonResponse
    {
        $user->delete();

        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Get user statistics
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'inactive_users' => User::where('is_active', false)->count(),
            'by_role' => User::selectRaw('role, COUNT(*) as count')
                            ->groupBy('role')
                            ->pluck('count', 'role'),
            'by_department' => User::selectRaw('department, COUNT(*) as count')
                                 ->whereNotNull('department')
                                 ->groupBy('department')
                                 ->pluck('count', 'department'),
        ];

        return response()->json($stats);
    }
}
