<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PasswordResetToken;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

class PasswordResetController extends Controller
{
    /**
     * Send password reset email
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $email = $request->email;

        // Rate limiting check
        $key = 'password-reset:' . $email;
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            throw ValidationException::withMessages([
                'email' => ["Too many password reset attempts. Please try again in " . ceil($seconds / 60) . " minutes."],
            ]);
        }

        // Check if user exists and is active
        $user = User::where('email', $email)->first();
        if (!$user) {
            // Don't reveal if email exists or not for security
            return response()->json([
                'message' => 'If an account with that email exists, we have sent a password reset link.'
            ]);
        }

        if (!$user->is_active) {
            throw ValidationException::withMessages([
                'email' => ['Your account is not active. Please contact an administrator.'],
            ]);
        }

        // Additional rate limiting check using our model
        if (PasswordResetToken::isRateLimited($email)) {
            throw ValidationException::withMessages([
                'email' => ['Too many password reset requests. Please wait before requesting another.'],
            ]);
        }

        RateLimiter::hit($key, 3600); // 1 hour decay

        try {
            // Generate reset token
            $token = PasswordResetToken::createToken(
                $email,
                $request->ip(),
                $request->userAgent()
            );

            // Get email settings
            $fromEmail = SystemSetting::get('email_from_address', '<EMAIL>');
            $fromName = SystemSetting::get('email_from_name', 'KDT CRM System');
            $replyTo = SystemSetting::get('email_reply_to', '<EMAIL>');

            // Configure SMTP settings dynamically
            $this->configureMailSettings();

            // Create reset URL dynamically based on request origin
            $frontendUrl = $this->determineFrontendUrl($request);
            $resetUrl = $frontendUrl . '/reset-password?token=' . urlencode($token) . '&email=' . urlencode($email);

            // Send password reset email
            $emailContent = $this->getPasswordResetEmailContent($user->name, $resetUrl);

            Mail::raw($emailContent, function ($message) use ($user, $fromEmail, $fromName, $replyTo) {
                $message->to($user->email)
                        ->from($fromEmail, $fromName)
                        ->replyTo($replyTo)
                        ->subject('Password Reset Request - KDT CRM System');
            });

            Log::info('Password reset email sent', [
                'email' => $email,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send password reset email', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to send password reset email. Please try again later.'
            ], 500);
        }

        return response()->json([
            'message' => 'If an account with that email exists, we have sent a password reset link.'
        ]);
    }

    /**
     * Verify reset token
     */
    public function verifyResetToken(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
            'email' => 'required|email',
        ]);

        $resetToken = PasswordResetToken::verifyToken($request->email, $request->token);

        if (!$resetToken) {
            throw ValidationException::withMessages([
                'token' => ['Invalid or expired reset token.'],
            ]);
        }

        return response()->json([
            'valid' => true,
            'message' => 'Token is valid'
        ]);
    }

    /**
     * Reset password
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required|string',
            'email' => 'required|email',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $resetToken = PasswordResetToken::verifyToken($request->email, $request->token);

        if (!$resetToken) {
            throw ValidationException::withMessages([
                'token' => ['Invalid or expired reset token.'],
            ]);
        }

        // Get the user
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            throw ValidationException::withMessages([
                'email' => ['User not found.'],
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        // Mark token as used
        $resetToken->markAsUsed();

        // Clear any existing sessions/tokens for security
        $user->tokens()->delete();

        Log::info('Password reset completed', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return response()->json([
            'message' => 'Password has been reset successfully. You can now log in with your new password.'
        ]);
    }

    /**
     * Get password reset email content
     */
    private function getPasswordResetEmailContent(string $userName, string $resetUrl): string
    {
        return "Hello {$userName},

We received a request to reset your password for your Tarbiah Sentap CRM account.

Click the link below to reset your password:
{$resetUrl}

This link will expire in 1 hour for security reasons.

If you did not request a password reset, please ignore this email. Your password will remain unchanged.

For security reasons, please do not share this link with anyone.

Best regards,
Tarbiah Sentap CRM Team";
    }

    /**
     * Clean up expired tokens (can be called via scheduled task)
     */
    public function cleanupExpiredTokens(): JsonResponse
    {
        $deletedCount = PasswordResetToken::cleanupExpired();

        return response()->json([
            'message' => "Cleaned up {$deletedCount} expired tokens."
        ]);
    }

    /**
     * Determine frontend URL based on request origin with comprehensive logging
     */
    private function determineFrontendUrl(Request $request): string
    {
        // Log all relevant request information for debugging
        $debugInfo = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'origin' => $request->header('Origin'),
            'referer' => $request->header('Referer'),
            'host' => $request->header('Host'),
            'x_forwarded_host' => $request->header('X-Forwarded-Host'),
            'x_forwarded_proto' => $request->header('X-Forwarded-Proto'),
            'user_agent' => $request->header('User-Agent'),
            'all_headers' => $request->headers->all(),
            'app_url' => config('app.url'),
            'frontend_url_config' => config('app.frontend_url'),
            'app_env' => config('app.env'),
        ];

        Log::info('Password Reset URL Detection Debug Info', $debugInfo);

        // Get the origin header from the request with multiple fallbacks
        $origin = $request->header('Origin')
               ?? $request->header('Referer')
               ?? $request->header('X-Forwarded-Host')
               ?? $request->header('Host');

        Log::info('Password Reset URL Detection - Origin Analysis', [
            'origin_header' => $request->header('Origin'),
            'referer_header' => $request->header('Referer'),
            'selected_origin' => $origin,
        ]);

        if ($origin) {
            // Parse the origin to get the base URL
            $parsedUrl = parse_url($origin);
            Log::info('Password Reset URL Detection - Parsed URL', [
                'original_origin' => $origin,
                'parsed_url' => $parsedUrl,
            ]);

            if ($parsedUrl && isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
                $frontendUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

                // Add port if it's not standard (80 for HTTP, 443 for HTTPS)
                if (isset($parsedUrl['port'])) {
                    $isStandardPort = ($parsedUrl['scheme'] === 'http' && $parsedUrl['port'] == 80) ||
                                     ($parsedUrl['scheme'] === 'https' && $parsedUrl['port'] == 443);

                    if (!$isStandardPort) {
                        $frontendUrl .= ':' . $parsedUrl['port'];
                    }
                }

                Log::info('Password Reset URL Detection - Origin Based URL', [
                    'detected_frontend_url' => $frontendUrl,
                    'source' => 'origin_header'
                ]);

                return $frontendUrl;
            }
        }

        // Fallback to config or environment-based detection
        $appUrl = config('app.url', 'http://localhost:4001');
        $frontendUrlConfig = config('app.frontend_url', 'http://localhost:3000');
        $requestHost = $request->header('Host');

        Log::info('Password Reset URL Detection - Fallback Analysis', [
            'app_url' => $appUrl,
            'frontend_url_config' => $frontendUrlConfig,
            'request_host' => $requestHost,
            'contains_production_domain' => str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space'),
            'host_is_production' => str_contains($requestHost ?? '', 'ts.crtvmkmn.space') || str_contains($requestHost ?? '', 'crtvmkmn.space'),
        ]);

        // Check if request is coming to production domain
        if ($requestHost && (str_contains($requestHost, 'ts.crtvmkmn.space') || str_contains($requestHost, 'crtvmkmn.space'))) {
            Log::info('Password Reset URL Detection - Production Host Detected', [
                'detected_frontend_url' => 'https://ts.crtvmkmn.space',
                'source' => 'request_host_detection'
            ]);
            return 'https://ts.crtvmkmn.space';
        }

        // If backend is on production domain, use production frontend
        if (str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space')) {
            Log::info('Password Reset URL Detection - Production Domain Detected', [
                'detected_frontend_url' => 'https://ts.crtvmkmn.space',
                'source' => 'production_domain_detection'
            ]);
            return 'https://ts.crtvmkmn.space';
        }

        // Check if frontend_url config is set to production
        if (str_contains($frontendUrlConfig, 'ts.crtvmkmn.space') || str_contains($frontendUrlConfig, 'crtvmkmn.space')) {
            Log::info('Password Reset URL Detection - Production Frontend Config', [
                'detected_frontend_url' => $frontendUrlConfig,
                'source' => 'frontend_url_config'
            ]);
            return $frontendUrlConfig;
        }

        // Default to development frontend
        Log::info('Password Reset URL Detection - Default Fallback', [
            'detected_frontend_url' => $frontendUrlConfig,
            'source' => 'default_fallback'
        ]);

        return $frontendUrlConfig;
    }

    /**
     * Configure mail settings based on environment
     */
    private function configureMailSettings(): void
    {
        // In development, use Mailpit (local email testing)
        if (app()->environment('local', 'development') || config('mail.mailers.smtp.host') === 'kdt-mailpit') {
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => 'kdt-mailpit',
                'mail.mailers.smtp.port' => 1025,
                'mail.mailers.smtp.username' => null,
                'mail.mailers.smtp.password' => null,
                'mail.mailers.smtp.encryption' => null,
            ]);
            return;
        }

        // In production, use Zoho settings
        $smtpHost = SystemSetting::get('zoho_smtp_host', 'smtp.zoho.com');
        $smtpPort = SystemSetting::get('zoho_smtp_port', 587);
        $smtpUsername = SystemSetting::get('zoho_smtp_username', '');
        $smtpEncryption = SystemSetting::get('zoho_smtp_encryption', 'tls');

        // Decrypt Zoho password
        $encryptedPassword = SystemSetting::get('zoho_smtp_password', '');
        $smtpPassword = $encryptedPassword;
        try {
            if (strlen($encryptedPassword) > 20) {
                $smtpPassword = decrypt($encryptedPassword);
            }
        } catch (\Exception $e) {
            $smtpPassword = $encryptedPassword;
        }

        // Configure mail settings
        config([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => $smtpHost,
            'mail.mailers.smtp.port' => $smtpPort,
            'mail.mailers.smtp.username' => $smtpUsername,
            'mail.mailers.smtp.password' => $smtpPassword,
            'mail.mailers.smtp.encryption' => $smtpEncryption === 'none' ? null : $smtpEncryption,
        ]);
    }
}
