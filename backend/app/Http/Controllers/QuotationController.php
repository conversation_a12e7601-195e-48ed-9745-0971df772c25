<?php

namespace App\Http\Controllers;

use App\Models\Quotation;
use App\Models\QuotationItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class QuotationController extends Controller
{
    /**
     * Display a listing of quotations
     */
    public function index(Request $request): JsonResponse
    {
        $query = Quotation::with(['client', 'lead', 'deal', 'createdBy', 'assignedTo', 'items']);

        // Filters
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('client_id')) {
            $query->where('client_id', $request->get('client_id'));
        }

        if ($request->has('assigned_to')) {
            $query->where('assigned_to', $request->get('assigned_to'));
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        // Search
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('quotation_number', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhereHas('client', function($clientQuery) use ($search) {
                      $clientQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Date filters
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $quotations = $query->paginate($request->get('per_page', 15));

        return response()->json($quotations);
    }

    /**
     * Store a newly created quotation
     */
    public function store(Request $request): JsonResponse
    {
        // Convert empty strings to null for foreign key fields
        $input = $request->all();
        $foreignKeyFields = ['client_id', 'lead_id', 'deal_id', 'assigned_to'];
        foreach ($foreignKeyFields as $field) {
            if (isset($input[$field]) && $input[$field] === '') {
                $input[$field] = null;
            }
        }

        // Also handle items foreign keys
        if (isset($input['items']) && is_array($input['items'])) {
            foreach ($input['items'] as &$item) {
                if (isset($item['product_id']) && $item['product_id'] === '') {
                    $item['product_id'] = null;
                }
            }
        }

        $request->merge($input);

        $validated = $request->validate([
            'client_id' => 'nullable|exists:clients,id',
            'lead_id' => 'nullable|exists:leads,id',
            'deal_id' => 'nullable|exists:deals,id',
            'assigned_to' => 'nullable|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => ['nullable', Rule::in(['low', 'medium', 'high', 'urgent'])],
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'terms_conditions' => 'nullable|string',
            'notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'validity_days' => 'nullable|integer|min:1|max:365',
            'expected_close_date' => 'nullable|date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'nullable|exists:products,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.description' => 'nullable|string',
            'items.*.sku' => 'nullable|string|max:100',
            'items.*.unit' => 'nullable|string|max:20',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_rate' => 'nullable|numeric|min:0|max:100',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.line_total' => 'nullable|numeric|min:0',
            'items.*.sort_order' => 'nullable|integer|min:1',
        ]);

        DB::beginTransaction();
        try {
            // Handle lead to client conversion if needed and enhance data inheritance
            if (empty($validated['client_id']) && !empty($validated['lead_id'])) {
                $lead = \App\Models\Lead::findOrFail($validated['lead_id']);

                if ($lead->converted_to_client_id) {
                    // Lead already converted, use existing client
                    $validated['client_id'] = $lead->converted_to_client_id;
                } else {
                    // Convert lead to client
                    $client = $lead->convertToClient([
                        'notes' => 'Converted from lead during quotation creation'
                    ]);
                    $validated['client_id'] = $client->id;
                }

                // Enhance quotation with lead data if not already provided
                if (empty($validated['notes']) && $lead->notes) {
                    $validated['notes'] = "Lead Notes: " . $lead->notes;
                }

                // Inherit lead priority if not set
                if (empty($validated['priority']) && $lead->priority) {
                    $priorityMap = ['low' => 'low', 'medium' => 'medium', 'high' => 'high'];
                    $validated['priority'] = $priorityMap[strtolower($lead->priority)] ?? 'medium';
                }
            }

            // Create quotation
            $quotation = Quotation::create([
                ...$validated,
                'created_by' => auth()->id() ?? 1, // Default to user 1 if no auth
                'status' => Quotation::STATUS_DRAFT,
            ]);

            // Create quotation items
            foreach ($validated['items'] as $index => $itemData) {
                $item = new QuotationItem([
                    ...$itemData,
                    'sort_order' => $index + 1,
                ]);
                $quotation->items()->save($item);
            }

            // Calculate totals
            $quotation->calculateTotals();

            DB::commit();

            return response()->json($quotation->load(['client', 'lead', 'deal', 'createdBy', 'assignedTo', 'items']), 201);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Failed to create quotation', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified quotation
     */
    public function show(Quotation $quotation): JsonResponse
    {
        return response()->json($quotation->load(['client', 'lead', 'deal', 'createdBy', 'assignedTo', 'items.product']));
    }

    /**
     * Update the specified quotation
     */
    public function update(Request $request, Quotation $quotation): JsonResponse
    {
        // Convert empty strings to null for foreign key fields
        $input = $request->all();
        $foreignKeyFields = ['client_id', 'lead_id', 'deal_id', 'assigned_to'];
        foreach ($foreignKeyFields as $field) {
            if (isset($input[$field]) && $input[$field] === '') {
                $input[$field] = null;
            }
        }

        // Also handle items foreign keys
        if (isset($input['items']) && is_array($input['items'])) {
            foreach ($input['items'] as &$item) {
                if (isset($item['product_id']) && $item['product_id'] === '') {
                    $item['product_id'] = null;
                }
                if (isset($item['id']) && $item['id'] === '') {
                    $item['id'] = null;
                }
            }
        }

        $request->merge($input);

        $validated = $request->validate([
            'client_id' => 'nullable|exists:clients,id',
            'lead_id' => 'nullable|exists:leads,id',
            'deal_id' => 'nullable|exists:deals,id',
            'assigned_to' => 'nullable|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => ['nullable', Rule::in(['low', 'medium', 'high', 'urgent'])],
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'terms_conditions' => 'nullable|string',
            'notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'validity_days' => 'nullable|integer|min:1|max:365',
            'expected_close_date' => 'nullable|date',
            'items' => 'required|array|min:1',
            'items.*.id' => 'nullable|exists:quotation_items,id',
            'items.*.product_id' => 'nullable|exists:products,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.description' => 'nullable|string',
            'items.*.sku' => 'nullable|string|max:100',
            'items.*.unit' => 'nullable|string|max:20',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_rate' => 'nullable|numeric|min:0|max:100',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.line_total' => 'nullable|numeric|min:0',
            'items.*.sort_order' => 'nullable|integer|min:1',
        ]);

        DB::beginTransaction();
        try {
            // Update quotation
            $quotation->update($validated);

            // Handle items
            $existingItemIds = $quotation->items->pluck('id')->toArray();
            $submittedItemIds = collect($validated['items'])->pluck('id')->filter()->toArray();
            
            // Delete removed items
            $itemsToDelete = array_diff($existingItemIds, $submittedItemIds);
            QuotationItem::whereIn('id', $itemsToDelete)->delete();

            // Update or create items
            foreach ($validated['items'] as $index => $itemData) {
                $itemData['sort_order'] = $index + 1;
                
                if (isset($itemData['id'])) {
                    // Update existing item
                    $item = QuotationItem::find($itemData['id']);
                    $item->update($itemData);
                } else {
                    // Create new item
                    $quotation->items()->create($itemData);
                }
            }

            // Recalculate totals
            $quotation->calculateTotals();

            DB::commit();

            return response()->json($quotation->load(['client', 'lead', 'deal', 'createdBy', 'assignedTo', 'items.product']));
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Failed to update quotation', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified quotation
     */
    public function destroy(Quotation $quotation): JsonResponse
    {
        $quotation->delete();
        return response()->json(['message' => 'Quotation deleted successfully']);
    }

    /**
     * Send quotation to client
     */
    public function send(Quotation $quotation): JsonResponse
    {
        $quotation->send();
        
        // TODO: Send email notification to client
        
        return response()->json(['message' => 'Quotation sent successfully', 'quotation' => $quotation]);
    }

    /**
     * Accept quotation
     */
    public function accept(Quotation $quotation): JsonResponse
    {
        $quotation->accept();
        return response()->json(['message' => 'Quotation accepted', 'quotation' => $quotation]);
    }

    /**
     * Convert quotation to invoice
     */
    public function convertToInvoice(Quotation $quotation): JsonResponse
    {
        // Allow conversion from any status - removed restriction
        // if ($quotation->status !== Quotation::STATUS_ACCEPTED) {
        //     return response()->json(['message' => 'Only accepted quotations can be converted to invoices'], 400);
        // }

        if ($quotation->converted_to_invoice_id) {
            return response()->json(['message' => 'Quotation has already been converted to an invoice'], 400);
        }

        try {
            $invoice = \App\Models\Invoice::createFromQuotation($quotation);
            return response()->json([
                'message' => 'Quotation converted to invoice successfully',
                'quotation' => $quotation->fresh(),
                'invoice' => $invoice
            ], 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to convert quotation to invoice', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Reject quotation
     */
    public function reject(Quotation $quotation): JsonResponse
    {
        $quotation->reject();
        return response()->json(['message' => 'Quotation rejected', 'quotation' => $quotation]);
    }

    /**
     * Duplicate quotation
     */
    public function duplicate(Quotation $quotation): JsonResponse
    {
        DB::beginTransaction();
        try {
            // Create new quotation with replicated data
            $newQuotation = $quotation->replicate();

            // Reset fields that should be unique or reset for new quotation
            $newQuotation->quotation_number = Quotation::generateQuotationNumber();
            $newQuotation->status = Quotation::STATUS_DRAFT;
            $newQuotation->sent_at = null;
            $newQuotation->viewed_at = null;
            $newQuotation->accepted_at = null;
            $newQuotation->rejected_at = null;
            $newQuotation->view_count = 0;
            $newQuotation->secure_token = \Illuminate\Support\Str::random(32);
            $newQuotation->pdf_path = null;
            $newQuotation->pdf_generated_at = null;
            $newQuotation->converted_to_invoice_id = null;
            $newQuotation->converted_at = null;
            $newQuotation->converted_by = null;

            // Set valid_until based on validity_days
            if ($newQuotation->validity_days) {
                $newQuotation->valid_until = now()->addDays($newQuotation->validity_days);
            }

            $newQuotation->save();

            // Duplicate items
            foreach ($quotation->items as $item) {
                $newItem = $item->replicate();
                $newItem->quotation_id = $newQuotation->id;
                $newItem->save();
            }

            // Recalculate totals
            $newQuotation->calculateTotals();

            DB::commit();

            return response()->json($newQuotation->load(['client', 'lead', 'deal', 'createdBy', 'assignedTo', 'items.product']), 201);
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Failed to duplicate quotation: ' . $e->getMessage(), [
                'quotation_id' => $quotation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['message' => 'Failed to duplicate quotation', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get quotation statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_quotations' => Quotation::count(),
            'draft_quotations' => Quotation::where('status', Quotation::STATUS_DRAFT)->count(),
            'sent_quotations' => Quotation::where('status', Quotation::STATUS_SENT)->count(),
            'accepted_quotations' => Quotation::where('status', Quotation::STATUS_ACCEPTED)->count(),
            'rejected_quotations' => Quotation::where('status', Quotation::STATUS_REJECTED)->count(),
            'expired_quotations' => Quotation::expired()->count(),
            'total_value' => Quotation::sum('total_amount'),
            'accepted_value' => Quotation::where('status', Quotation::STATUS_ACCEPTED)->sum('total_amount'),
            'conversion_rate' => 0,
        ];

        if ($stats['sent_quotations'] > 0) {
            $stats['conversion_rate'] = round(($stats['accepted_quotations'] / $stats['sent_quotations']) * 100, 2);
        }

        return response()->json($stats);
    }
}
