<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GeneralSettingsController extends Controller
{
    /**
     * Get general settings
     */
    public function getGeneralSettings(): JsonResponse
    {
        $settings = [
            'organizationName' => SystemSetting::get('organization_name', 'Islamic Books & Merchandise'),
            'website' => SystemSetting::get('website', 'https://islamicbooks.com'),
            'contactEmail' => SystemSetting::get('contact_email', '<EMAIL>'),
            'phoneNumber' => SystemSetting::get('phone_number', '+60 12-345-6789'),
            'streetAddress' => SystemSetting::get('street_address', '123 Jalan Merdeka'),
            'city' => SystemSetting::get('city', 'Kuala Lumpur'),
            'state' => SystemSetting::get('state', 'Federal Territory'),
            'postalCode' => SystemSetting::get('postal_code', '50000'),
            'country' => SystemSetting::get('country', 'Malaysia'),
        ];

        return response()->json([
            'data' => $settings,
            'message' => 'General settings retrieved successfully'
        ]);
    }

    /**
     * Save general settings
     */
    public function saveGeneralSettings(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'organizationName' => 'required|string|max:255',
            'website' => 'nullable|url|max:255',
            'contactEmail' => 'nullable|email|max:255',
            'phoneNumber' => 'nullable|string|max:20',
            'streetAddress' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postalCode' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
        ]);

        // Save each setting
        foreach ($validated as $key => $value) {
            $settingKey = $this->convertToSnakeCase($key);
            SystemSetting::set($settingKey, $value);
        }

        return response()->json([
            'message' => 'General settings saved successfully',
            'data' => $validated
        ]);
    }

    /**
     * Convert camelCase to snake_case
     */
    private function convertToSnakeCase(string $input): string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $input));
    }
}
