<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\Quotation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class PublicPaymentController extends Controller
{
    /**
     * Get invoice details for public payment
     */
    public function getInvoice(Request $request, string $invoiceId): JsonResponse
    {
        try {
            // Find transaction by ID (this would be the invoice)
            $transaction = Transaction::with(['client', 'products'])
                ->where('id', $invoiceId)
                ->where('status', 'sent') // Only allow payment for sent invoices
                ->first();

            if (!$transaction) {
                return response()->json([
                    'message' => 'Invoice not found or not available for payment'
                ], 404);
            }

            // Return invoice details without sensitive information
            return response()->json([
                'invoice' => [
                    'id' => $transaction->id,
                    'invoice_number' => $transaction->reference_number,
                    'amount' => $transaction->amount,
                    'currency' => 'RM',
                    'description' => $transaction->description,
                    'due_date' => $transaction->created_at->addDays(30)->format('Y-m-d'),
                    'client' => [
                        'name' => $transaction->client->name,
                        'email' => $transaction->client->email,
                    ],
                    'items' => $transaction->products->map(function ($product) {
                        return [
                            'name' => $product->name,
                            'quantity' => $product->pivot->quantity ?? 1,
                            'price' => $product->price,
                            'total' => ($product->pivot->quantity ?? 1) * $product->price,
                        ];
                    }),
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving invoice details'
            ], 500);
        }
    }

    /**
     * Process payment for invoice
     */
    public function processPayment(Request $request, string $invoiceId): JsonResponse
    {
        $validated = $request->validate([
            'payment_method' => 'required|in:credit_card,bank_transfer,ewallet',
            'payment_details' => 'required|array',
            'payment_details.cardholder_name' => 'required_if:payment_method,credit_card|string|max:255',
            'payment_details.card_number' => 'required_if:payment_method,credit_card|string|max:20',
            'payment_details.expiry_month' => 'required_if:payment_method,credit_card|integer|min:1|max:12',
            'payment_details.expiry_year' => 'required_if:payment_method,credit_card|integer|min:2024',
            'payment_details.cvv' => 'required_if:payment_method,credit_card|string|size:3',
            'payment_details.bank_name' => 'required_if:payment_method,bank_transfer|string|max:255',
            'payment_details.account_number' => 'required_if:payment_method,bank_transfer|string|max:50',
            'payment_details.ewallet_type' => 'required_if:payment_method,ewallet|in:grabpay,tng,boost',
            'payment_details.phone_number' => 'required_if:payment_method,ewallet|string|max:20',
        ]);

        try {
            DB::beginTransaction();

            // Find the transaction
            $transaction = Transaction::where('id', $invoiceId)
                ->where('status', 'sent')
                ->lockForUpdate()
                ->first();

            if (!$transaction) {
                return response()->json([
                    'message' => 'Invoice not found or not available for payment'
                ], 404);
            }

            // Simulate payment processing (in real implementation, integrate with payment gateway)
            $paymentSuccess = $this->simulatePaymentProcessing($validated['payment_method'], $validated['payment_details']);

            if ($paymentSuccess) {
                // Update transaction status to paid
                $transaction->update([
                    'status' => 'paid',
                    'payment_method' => $validated['payment_method'],
                    'payment_details' => json_encode($validated['payment_details']),
                    'paid_at' => now(),
                ]);

                // Update client's total spent and transaction count
                $client = $transaction->client;
                $client->increment('total_spent', $transaction->amount);
                $client->increment('transaction_count');
                $client->touch('last_activity');

                DB::commit();

                return response()->json([
                    'message' => 'Payment processed successfully',
                    'payment_id' => 'PAY_' . strtoupper(uniqid()),
                    'transaction_id' => $transaction->id,
                    'amount_paid' => $transaction->amount,
                    'currency' => 'RM',
                    'payment_method' => $validated['payment_method'],
                    'paid_at' => $transaction->paid_at->format('Y-m-d H:i:s'),
                ]);
            } else {
                DB::rollback();
                return response()->json([
                    'message' => 'Payment processing failed. Please try again.'
                ], 400);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Error processing payment'
            ], 500);
        }
    }

    /**
     * Simulate payment processing
     */
    private function simulatePaymentProcessing(string $paymentMethod, array $paymentDetails): bool
    {
        // Simulate payment gateway processing
        // In real implementation, this would call actual payment gateway APIs
        
        // For demo purposes, we'll simulate a 95% success rate
        return rand(1, 100) <= 95;
    }

    /**
     * Get payment success page data
     */
    public function getPaymentSuccess(Request $request, string $transactionId): JsonResponse
    {
        try {
            $transaction = Transaction::with(['client'])
                ->where('id', $transactionId)
                ->where('status', 'paid')
                ->first();

            if (!$transaction) {
                return response()->json([
                    'message' => 'Payment record not found'
                ], 404);
            }

            return response()->json([
                'payment' => [
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'currency' => 'RM',
                    'payment_method' => $transaction->payment_method,
                    'paid_at' => $transaction->paid_at->format('Y-m-d H:i:s'),
                    'client_name' => $transaction->client->name,
                    'invoice_number' => $transaction->reference_number,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error retrieving payment details'
            ], 500);
        }
    }
}
