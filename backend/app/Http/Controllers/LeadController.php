<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\Client;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class LeadController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Lead::with(['client', 'convertedToClient']);

        // Filter by client
        if ($request->has('client_id')) {
            $query->where('client_id', $request->get('client_id'));
        }

        // Filter by lead type (capture vs opportunity)
        if ($request->has('lead_type')) {
            $query->where('lead_type', $request->get('lead_type'));
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by opportunity status (for sales opportunities)
        if ($request->has('opportunity_status')) {
            $query->where('opportunity_status', $request->get('opportunity_status'));
        }

        // Filter by engagement level
        if ($request->has('engagement_level')) {
            $query->where('engagement_level', $request->get('engagement_level'));
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        // Filter by UTM source
        if ($request->has('utm_source')) {
            $query->where('utm_source', $request->get('utm_source'));
        }

        // Filter by channel
        if ($request->has('channel')) {
            $query->where('channel', $request->get('channel'));
        }

        // Filter by assigned staff
        if ($request->has('assigned_to')) {
            $query->where('assigned_to', $request->get('assigned_to'));
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('email', 'ILIKE', "%{$search}%")
                  ->orWhere('phone', 'ILIKE', "%{$search}%")
                  ->orWhere('company', 'ILIKE', "%{$search}%")
                  ->orWhere('title', 'ILIKE', "%{$search}%");
            });
        }

        // Handle pagination
        $perPage = $request->get('per_page', 15);
        $perPage = min($perPage, 10000); // Allow large page sizes for frontend

        $leads = $query->orderBy('created_at', 'desc')
                      ->paginate($perPage);

        return response()->json($leads);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'client_id' => 'nullable|exists:clients,id',

            // Lead capture fields
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',

            // UTM and campaign tracking
            'utm_source' => 'nullable|string|max:100',
            'utm_campaign' => 'nullable|string|max:100',
            'utm_medium' => 'nullable|string|max:100',
            'utm_content' => 'nullable|string|max:100',
            'utm_term' => 'nullable|string|max:100',
            'channel' => 'nullable|string|max:100',

            // Lead classification
            'lead_type' => 'nullable|in:capture,opportunity',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',

            // Sales opportunity fields
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|in:new,contacted,engaged,qualified,converted,disqualified',
            'opportunity_status' => 'nullable|in:contacted,info_sent,negotiation,waiting_payment,closed_won,closed_lost',
            'priority' => 'nullable|in:low,medium,high',
            'engagement_level' => 'nullable|in:hot,warm,cold,frozen',

            // Sales tracking
            'source' => 'nullable|string|max:100',
            'assigned_to' => 'nullable|string|max:255',
            'estimated_value' => 'nullable|numeric|min:0',
            'probability' => 'nullable|integer|min:0|max:100',
            'expected_close_date' => 'nullable|date',

            // Activity and lifecycle tracking
            'notes' => 'nullable|string',
            'internal_remarks' => 'nullable|string',
            'suggested_action' => 'nullable|string|max:255',
            'lifecycle_stage' => 'nullable|integer|min:0|max:100',

            // Document uploads
            'documents' => 'nullable|array',
            'documents.*' => 'string',
        ]);

        // Set defaults
        $validated['lead_type'] = $validated['lead_type'] ?? 'capture';
        $validated['status'] = $validated['status'] ?? 'new';
        $validated['priority'] = $validated['priority'] ?? 'medium';
        $validated['engagement_level'] = $validated['engagement_level'] ?? 'cold';
        $validated['lifecycle_stage'] = $validated['lifecycle_stage'] ?? 0;
        $validated['last_activity'] = now();

        $lead = Lead::create($validated);

        return response()->json($lead->load(['client', 'convertedToClient']), 201);
    }

    public function show(Lead $lead): JsonResponse
    {
        return response()->json($lead->load('client'));
    }

    public function update(Request $request, Lead $lead): JsonResponse
    {
        $validated = $request->validate([
            'client_id' => 'nullable|exists:clients,id',
            'title' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'sometimes|required|in:new,contacted,qualified,proposal,negotiation,closed_won,closed_lost',
            'priority' => 'sometimes|required|in:low,medium,high,urgent',
            'source' => 'nullable|string|max:100',
            'assigned_to' => 'nullable|string|max:255',
            'estimated_value' => 'nullable|numeric|min:0',
            'probability' => 'nullable|integer|min:0|max:100',
            'expected_close_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        $lead->update($validated);

        return response()->json($lead->load('client'));
    }

    public function destroy(Lead $lead): JsonResponse
    {
        $lead->delete();

        return response()->json(['message' => 'Lead deleted successfully']);
    }

    /**
     * Convert lead capture to sales opportunity
     */
    public function convertToOpportunity(Request $request, Lead $lead): JsonResponse
    {
        if ($lead->lead_type !== 'capture') {
            return response()->json(['error' => 'Only lead captures can be converted to opportunities'], 400);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'estimated_value' => 'nullable|numeric|min:0',
            'probability' => 'nullable|integer|min:0|max:100',
            'expected_close_date' => 'nullable|date',
            'assigned_to' => 'nullable|string|max:255',
        ]);

        $lead->convertToOpportunity($validated);

        return response()->json($lead->load(['client', 'convertedToClient']));
    }

    /**
     * Convert lead directly to client (manual conversion)
     */
    public function convertToClient(Request $request, Lead $lead): JsonResponse
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:500', // Why bypassing the normal workflow
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Check if lead is already converted
            if ($lead->status === 'converted' || $lead->converted_to_client_id) {
                return response()->json(['error' => 'Lead has already been converted'], 400);
            }

            // Create client from lead data
            $client = Client::create([
                'name' => $lead->name,
                'email' => $lead->email,
                'phone' => $lead->phone,
                'company' => $lead->company,
                'address' => $lead->address,
                'utm_source' => $lead->utm_source,
                'tags' => $lead->tags,
                'category' => 'First Timer',
                'ltv_segment' => $lead->ltv_segment ?? 'new',
                'engagement_level' => $lead->engagement_level,
                'priority' => $lead->priority,
                'notes' => $lead->notes . ($validated['notes'] ? "\n\nConversion Notes: " . $validated['notes'] : ''),
                'suggested_action' => 'Follow up on direct conversion',
                'last_activity' => now(),
                'total_spent' => 0,
                'transaction_count' => 0,
                'custom_fields' => array_merge($lead->custom_fields ?? [], [
                    'converted_from_lead' => true,
                    'conversion_type' => 'direct',
                    'conversion_reason' => $validated['reason'] ?? 'Manual conversion',
                ]),
            ]);

            // Update lead with conversion info
            $lead->update([
                'status' => 'converted',
                'converted_at' => now(),
                'converted_to_client_id' => $client->id,
            ]);

            // Log the conversion activity
            ActivityLog::logConversion(
                'lead_to_client_direct_conversion',
                "Lead '{$lead->name}' was directly converted to client (bypassed deal workflow)",
                $lead,
                $client,
                [
                    'conversion_type' => 'direct',
                    'reason' => $validated['reason'] ?? 'Manual conversion',
                    'bypassed_stages' => ['deal', 'quotation', 'invoice', 'payment'],
                ],
                true // bypassed workflow
            );

            DB::commit();

            return response()->json([
                'message' => 'Lead successfully converted to client',
                'lead' => $lead->load(['convertedToClient']),
                'client' => $client,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to convert lead to client: ' . $e->getMessage()], 500);
        }
    }



    /**
     * Recycle disqualified lead
     */
    public function recycle(Lead $lead): JsonResponse
    {
        if ($lead->status !== 'disqualified') {
            return response()->json(['error' => 'Only disqualified leads can be recycled'], 400);
        }

        $lead->recycle();

        return response()->json($lead->load(['client', 'convertedToClient']));
    }

    /**
     * Update lifecycle stage
     */
    public function updateLifecycle(Request $request, Lead $lead): JsonResponse
    {
        $validated = $request->validate([
            'lifecycle_stage' => 'required|integer|min:0|max:100',
        ]);

        $lead->updateLifecycleStage($validated['lifecycle_stage']);

        return response()->json($lead->load(['client', 'convertedToClient']));
    }

    /**
     * Get lead statistics
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_leads' => Lead::count(),
            'captures' => Lead::captures()->count(),
            'opportunities' => Lead::opportunities()->count(),
            'hot_leads' => Lead::hot()->count(),
            'recyclable' => Lead::recyclable()->count(),
            'converted_this_month' => Lead::where('status', 'converted')
                ->whereMonth('converted_at', now()->month)
                ->count(),
            'by_status' => Lead::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'by_engagement' => Lead::selectRaw('engagement_level, count(*) as count')
                ->groupBy('engagement_level')
                ->pluck('count', 'engagement_level'),
            'by_channel' => Lead::selectRaw('channel, count(*) as count')
                ->whereNotNull('channel')
                ->groupBy('channel')
                ->pluck('count', 'channel'),
        ];

        return response()->json($stats);
    }
}
