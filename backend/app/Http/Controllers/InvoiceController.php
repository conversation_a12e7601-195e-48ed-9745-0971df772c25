<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Quotation;
use App\Models\Transaction;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }
    /**
     * Display a listing of invoices
     */
    public function index(Request $request): JsonResponse
    {
        $query = Invoice::with(['client', 'quotation', 'deal', 'createdBy', 'assignedTo', 'items.product']);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'ILIKE', "%{$search}%")
                  ->orWhere('title', 'ILIKE', "%{$search}%")
                  ->orWhereHas('client', function ($clientQuery) use ($search) {
                      $clientQuery->where('name', 'ILIKE', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by payment status
        if ($request->has('payment_status')) {
            $query->where('payment_status', $request->get('payment_status'));
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        // Filter by client
        if ($request->has('client_id')) {
            $query->where('client_id', $request->get('client_id'));
        }

        // Filter by assigned user
        if ($request->has('assigned_to')) {
            $query->where('assigned_to', $request->get('assigned_to'));
        }

        // Filter by date range
        if ($request->has('date_from')) {
            $query->where('issue_date', '>=', $request->get('date_from'));
        }

        if ($request->has('date_to')) {
            $query->where('issue_date', '<=', $request->get('date_to'));
        }

        // Filter overdue invoices
        if ($request->boolean('overdue_only')) {
            $query->overdue();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $invoices = $query->paginate($request->get('per_page', 15));

        return response()->json($invoices);
    }

    /**
     * Store a newly created invoice
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'client_id' => 'nullable|exists:clients,id',
            'lead_id' => 'nullable|exists:leads,id',
            'quotation_id' => 'nullable|exists:quotations,id',
            'deal_id' => 'nullable|exists:deals,id',
            'assigned_to' => 'nullable|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => ['nullable', Rule::in(['low', 'medium', 'high', 'urgent'])],
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'terms_conditions' => 'nullable|string',
            'notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'issue_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:issue_date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'nullable|exists:products,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.description' => 'nullable|string',
            'items.*.sku' => 'nullable|string|max:100',
            'items.*.unit' => 'nullable|string|max:20',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_rate' => 'nullable|numeric|min:0|max:100',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.line_total' => 'nullable|numeric|min:0',
            'items.*.sort_order' => 'nullable|integer|min:1',
        ]);

        // Ensure either client_id or lead_id is provided
        if (empty($validated['client_id']) && empty($validated['lead_id'])) {
            return response()->json(['error' => 'Either client_id or lead_id must be provided'], 422);
        }

        DB::beginTransaction();
        try {
            // Handle lead to client conversion if needed and enhance data inheritance
            if (empty($validated['client_id']) && !empty($validated['lead_id'])) {
                $lead = \App\Models\Lead::findOrFail($validated['lead_id']);

                // Check if lead is already converted to a client
                if ($lead->converted_to_client_id) {
                    $validated['client_id'] = $lead->converted_to_client_id;
                } else {
                    // Convert lead to client
                    $client = $lead->convertToClient([
                        'notes' => ($lead->notes ?? '') . "\n\nAutomatically converted to client for invoice creation."
                    ]);
                    $validated['client_id'] = $client->id;
                }

                // Enhance invoice with lead data if not already provided
                if (empty($validated['notes']) && $lead->notes) {
                    $validated['notes'] = "Lead Notes: " . $lead->notes;
                }

                // Inherit lead priority if not set
                if (empty($validated['priority']) && $lead->priority) {
                    $priorityMap = ['low' => 'low', 'medium' => 'medium', 'high' => 'high'];
                    $validated['priority'] = $priorityMap[strtolower($lead->priority)] ?? 'medium';
                }

                // Remove lead_id from validated data since Invoice model doesn't have this field
                unset($validated['lead_id']);
            }

            // Ensure we have a client_id
            if (empty($validated['client_id'])) {
                throw new \Exception('Either client_id or lead_id must be provided');
            }

            // Create invoice
            $invoice = Invoice::create([
                ...$validated,
                'created_by' => auth()->id() ?? 1, // Default to user 1 if no auth
                'status' => Invoice::STATUS_DRAFT,
                'payment_status' => Invoice::PAYMENT_PENDING,
            ]);

            // Create invoice items
            foreach ($validated['items'] as $index => $itemData) {
                $item = new InvoiceItem([
                    ...$itemData,
                    'sort_order' => $index + 1,
                ]);
                $invoice->items()->save($item);
            }

            // Calculate totals
            $invoice->calculateTotals();

            DB::commit();

            return response()->json($invoice->load(['client', 'quotation', 'deal', 'createdBy', 'assignedTo', 'items']), 201);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Failed to create invoice', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified invoice
     */
    public function show(Invoice $invoice): JsonResponse
    {
        return response()->json($invoice->load(['client', 'quotation', 'deal', 'createdBy', 'assignedTo', 'items.product', 'transactions']));
    }

    /**
     * Update the specified invoice
     */
    public function update(Request $request, Invoice $invoice): JsonResponse
    {
        $validated = $request->validate([
            'client_id' => 'sometimes|required|exists:clients,id',
            'quotation_id' => 'nullable|exists:quotations,id',
            'deal_id' => 'nullable|exists:deals,id',
            'assigned_to' => 'nullable|exists:users,id',
            'title' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'priority' => ['nullable', Rule::in(['low', 'medium', 'high', 'urgent'])],
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'terms_conditions' => 'nullable|string',
            'notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'issue_date' => 'nullable|date',
            'due_date' => 'nullable|date|after_or_equal:issue_date',
            'items' => 'sometimes|required|array|min:1',
            'items.*.id' => 'nullable|exists:invoice_items,id',
            'items.*.product_id' => 'nullable|exists:products,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.description' => 'nullable|string',
            'items.*.sku' => 'nullable|string|max:100',
            'items.*.unit' => 'nullable|string|max:20',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_rate' => 'nullable|numeric|min:0|max:100',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.line_total' => 'nullable|numeric|min:0',
            'items.*.sort_order' => 'nullable|integer|min:1',
        ]);

        DB::beginTransaction();
        try {
            // Update invoice
            $invoice->update($validated);

            // Update items if provided
            if (isset($validated['items'])) {
                // Delete existing items
                $invoice->items()->delete();

                // Create new items
                foreach ($validated['items'] as $index => $itemData) {
                    $item = new InvoiceItem([
                        ...$itemData,
                        'sort_order' => $index + 1,
                    ]);
                    $invoice->items()->save($item);
                }
            }

            // Recalculate totals
            $invoice->calculateTotals();

            DB::commit();

            return response()->json($invoice->load(['client', 'quotation', 'deal', 'createdBy', 'assignedTo', 'items.product']));
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Failed to update invoice', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified invoice
     */
    public function destroy(Invoice $invoice): JsonResponse
    {
        $invoice->delete();
        return response()->json(['message' => 'Invoice deleted successfully']);
    }

    /**
     * Send invoice to client
     */
    public function send(Invoice $invoice): JsonResponse
    {
        $invoice->markAsSent();
        
        // TODO: Send email notification to client
        
        return response()->json(['message' => 'Invoice sent successfully', 'invoice' => $invoice]);
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid(Request $request, Invoice $invoice): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $amount = $validated['amount'] ?? $invoice->total_amount;
            $paymentMethod = $validated['payment_method'] ?? 'Manual';
            $notes = $validated['notes'] ?? null;

            // Create transaction record
            $transaction = Transaction::create([
                'client_id' => $invoice->client_id,
                'invoice_id' => $invoice->id,
                'transaction_number' => 'TXN-' . strtoupper(uniqid()),
                'type' => 'payment',
                'status' => 'completed',
                'amount' => $amount,
                'tax_amount' => 0,
                'total_amount' => $amount,
                'payment_method' => $paymentMethod,
                'payment_status' => 'paid',
                'payment_date' => now(),
                'paid_at' => now(),
                'notes' => $notes,
            ]);

            // Mark invoice as paid
            $invoice->markAsPaid($amount, $paymentMethod, $notes);

            // Update client's computed fields (LTV segment, category, etc.)
            $client = $invoice->client;
            if ($client) {
                $client->updateComputedFields();

                // If this is a lead that was converted to client, ensure lead status is updated
                if ($invoice->lead_id) {
                    $lead = \App\Models\Lead::find($invoice->lead_id);
                    if ($lead && !$lead->converted_to_client_id) {
                        $lead->update([
                            'converted_to_client_id' => $client->id,
                            'client_id' => $client->id,
                            'status' => 'converted',
                            'converted_at' => now(),
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Invoice marked as paid and transaction created',
                'invoice' => $invoice->fresh()->load(['client', 'transactions']),
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Failed to mark invoice as paid',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add partial payment
     */
    public function addPayment(Request $request, Invoice $invoice): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
        ]);

        try {
            $transaction = $this->paymentService->recordPayment($invoice, $validated);

            return response()->json([
                'message' => 'Payment recorded successfully',
                'invoice' => $invoice->fresh(),
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to record payment', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Create invoice from quotation
     */
    public function createFromQuotation(Quotation $quotation): JsonResponse
    {
        // Allow conversion from any status - removed restriction
        // if ($quotation->status !== Quotation::STATUS_ACCEPTED) {
        //     return response()->json(['message' => 'Only accepted quotations can be converted to invoices'], 400);
        // }

        if ($quotation->converted_to_invoice_id) {
            return response()->json(['message' => 'Quotation has already been converted to an invoice'], 400);
        }

        try {
            $invoice = Invoice::createFromQuotation($quotation);
            return response()->json(['message' => 'Invoice created from quotation', 'invoice' => $invoice], 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to create invoice from quotation', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get invoice statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_invoices' => Invoice::count(),
            'draft_invoices' => Invoice::where('status', Invoice::STATUS_DRAFT)->count(),
            'sent_invoices' => Invoice::where('status', Invoice::STATUS_SENT)->count(),
            'paid_invoices' => Invoice::where('status', Invoice::STATUS_PAID)->count(),
            'overdue_invoices' => Invoice::overdue()->count(),
            'total_amount' => Invoice::sum('total_amount'),
            'paid_amount' => Invoice::sum('paid_amount'),
            'outstanding_amount' => Invoice::whereNotIn('payment_status', [Invoice::PAYMENT_PAID, Invoice::PAYMENT_CANCELLED])->sum('total_amount'),
            'by_status' => Invoice::selectRaw('status, COUNT(*) as count, SUM(total_amount) as total')
                                 ->groupBy('status')
                                 ->get(),
            'by_payment_status' => Invoice::selectRaw('payment_status, COUNT(*) as count, SUM(total_amount) as total')
                                         ->groupBy('payment_status')
                                         ->get(),
        ];

        return response()->json($stats);
    }

    /**
     * Get payment history for an invoice
     */
    public function paymentHistory(Invoice $invoice): JsonResponse
    {
        $history = $this->paymentService->getPaymentHistory($invoice);
        return response()->json($history);
    }

    /**
     * Process refund for an invoice
     */
    public function processRefund(Request $request, Invoice $invoice): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
        ]);

        try {
            $transaction = $this->paymentService->processRefund($invoice, $validated);

            return response()->json([
                'message' => 'Refund processed successfully',
                'invoice' => $invoice->fresh(),
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to process refund', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get client payment analytics
     */
    public function clientAnalytics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
        ]);

        $analytics = $this->paymentService->getClientPaymentAnalytics($validated['client_id']);
        return response()->json($analytics);
    }
}
