<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\ClientEmail;
use App\Models\ClientPhoneNumber;
use App\Models\Lead;
use App\Models\Deal;
use App\Models\Quotation;
use App\Models\QuotationItem;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Transaction;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DataController extends Controller
{
    /**
     * Export data in various formats
     */
    public function export(Request $request)
    {
        $request->validate([
            'data_type' => 'required|string|in:clients,leads,deals,quotations,invoices,transactions,products',
            'format' => 'required|string|in:csv,excel,json'
        ]);

        $dataType = $request->input('data_type');
        $format = $request->input('format');

        try {
            $data = $this->getData($dataType);
            
            switch ($format) {
                case 'csv':
                    return $this->exportCsv($data, $dataType);
                case 'excel':
                    return $this->exportExcel($data, $dataType);
                case 'json':
                    return $this->exportJson($data, $dataType);
                default:
                    return response()->json(['error' => 'Unsupported format'], 400);
            }
        } catch (\Exception $e) {
            Log::error('Export error: ' . $e->getMessage());
            return response()->json(['error' => 'Export failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Clear data by type
     */
    public function clear(Request $request, string $dataType): JsonResponse
    {
        $allowedTypes = ['clients', 'leads', 'deals', 'quotations', 'invoices', 'transactions', 'products', 'all'];

        if (!in_array($dataType, $allowedTypes)) {
            return response()->json(['error' => 'Invalid data type'], 400);
        }

        // Check if hard delete is requested
        $hardDelete = $request->query('hard', false) === 'true';

        try {
            DB::beginTransaction();

            // Log initial record counts for debugging
            $initialCounts = [
                'clients' => Client::withTrashed()->count(),
                'leads' => Lead::withTrashed()->count(),
                'deals' => Deal::withTrashed()->count(),
                'quotations' => Quotation::withTrashed()->count(),
                'invoices' => Invoice::withTrashed()->count(),
                'transactions' => Transaction::withTrashed()->count(),
                'products' => Product::withTrashed()->count(),
                'quotation_items' => QuotationItem::count(),
                'invoice_items' => InvoiceItem::count(),
                'client_emails' => ClientEmail::count(),
                'client_phone_numbers' => ClientPhoneNumber::count(),
            ];

            Log::info("Data Clear Started - Type: {$dataType}, Hard Delete: " . ($hardDelete ? 'true' : 'false'));
            Log::info('Initial Record Counts:', $initialCounts);

            $deletedCounts = [];
            $deleteType = $hardDelete ? 'hard' : 'soft';

            if ($dataType === 'all') {
                // Clear all data in proper order (respecting foreign key constraints)
                // Start with junction/pivot tables and items
                $deletedCounts['product_transaction'] = DB::table('product_transaction')->count();
                DB::table('product_transaction')->delete();

                $deletedCounts['invoice_items'] = InvoiceItem::count();
                InvoiceItem::query()->delete();

                $deletedCounts['quotation_items'] = QuotationItem::count();
                QuotationItem::query()->delete();

                // Then main tables in dependency order
                if ($hardDelete) {
                    $deletedCounts['transactions'] = Transaction::withTrashed()->count();
                    Transaction::withTrashed()->forceDelete();

                    $deletedCounts['invoices'] = Invoice::withTrashed()->count();
                    Invoice::withTrashed()->forceDelete();

                    $deletedCounts['quotations'] = Quotation::withTrashed()->count();
                    Quotation::withTrashed()->forceDelete();

                    $deletedCounts['deals'] = Deal::withTrashed()->count();
                    Deal::withTrashed()->forceDelete();

                    $deletedCounts['leads'] = Lead::withTrashed()->count();
                    Lead::withTrashed()->forceDelete();
                } else {
                    $deletedCounts['transactions'] = Transaction::count();
                    Transaction::query()->delete();

                    $deletedCounts['invoices'] = Invoice::count();
                    Invoice::query()->delete();

                    $deletedCounts['quotations'] = Quotation::count();
                    Quotation::query()->delete();

                    $deletedCounts['deals'] = Deal::count();
                    Deal::query()->delete();

                    $deletedCounts['leads'] = Lead::count();
                    Lead::query()->delete();
                }

                // Clear client-related tables
                $deletedCounts['client_emails'] = ClientEmail::count();
                ClientEmail::query()->delete();

                $deletedCounts['client_phone_numbers'] = ClientPhoneNumber::count();
                ClientPhoneNumber::query()->delete();

                if ($hardDelete) {
                    $deletedCounts['clients'] = Client::withTrashed()->count();
                    Client::withTrashed()->forceDelete();

                    $deletedCounts['products'] = Product::withTrashed()->count();
                    Product::withTrashed()->forceDelete();
                } else {
                    $deletedCounts['clients'] = Client::count();
                    Client::query()->delete();

                    $deletedCounts['products'] = Product::count();
                    Product::query()->delete();
                }

                // Reset auto-increment counters (PostgreSQL sequences)
                try {
                    DB::statement('ALTER SEQUENCE product_transaction_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE invoice_items_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE quotation_items_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE transactions_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE invoices_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE quotations_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE deals_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE leads_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE client_emails_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE client_phone_numbers_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE clients_id_seq RESTART WITH 1;');
                    DB::statement('ALTER SEQUENCE products_id_seq RESTART WITH 1;');
                } catch (\Exception $seqError) {
                    Log::warning('Failed to reset sequences: ' . $seqError->getMessage());
                }
            } else {
                $deletedCounts[$dataType] = $this->clearDataType($dataType, $hardDelete);
            }

            // Log final record counts for verification
            $finalCounts = [
                'clients' => Client::withTrashed()->count(),
                'leads' => Lead::withTrashed()->count(),
                'deals' => Deal::withTrashed()->count(),
                'quotations' => Quotation::withTrashed()->count(),
                'invoices' => Invoice::withTrashed()->count(),
                'transactions' => Transaction::withTrashed()->count(),
                'products' => Product::withTrashed()->count(),
                'quotation_items' => QuotationItem::count(),
                'invoice_items' => InvoiceItem::count(),
                'client_emails' => ClientEmail::count(),
                'client_phone_numbers' => ClientPhoneNumber::count(),
            ];

            Log::info('Data Clear Completed - Final Record Counts:', $finalCounts);
            Log::info('Data Clear Summary - Records Deleted:', $deletedCounts);

            $totalRemaining = array_sum($finalCounts);
            if ($totalRemaining > 0 && $hardDelete) {
                Log::warning("Hard delete incomplete: {$totalRemaining} records remaining", $finalCounts);
            }

            DB::commit();

            return response()->json([
                'message' => $hardDelete ? 'Data permanently deleted successfully' : 'Data soft deleted successfully',
                'delete_type' => $deleteType,
                'deleted_counts' => $deletedCounts,
                'debug' => [
                    'initial_counts' => $initialCounts,
                    'final_counts' => $finalCounts,
                    'total_deleted' => array_sum($deletedCounts),
                    'total_remaining' => $totalRemaining
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Clear data error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to clear data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get data for export
     */
    private function getData(string $dataType): array
    {
        switch ($dataType) {
            case 'clients':
                return Client::all()->toArray();
            case 'leads':
                return Lead::all()->toArray();
            case 'deals':
                return Deal::with(['client', 'lead'])->get()->toArray();
            case 'quotations':
                return Quotation::with(['client', 'deal'])->get()->toArray();
            case 'invoices':
                return Invoice::with(['client', 'deal'])->get()->toArray();
            case 'transactions':
                return Transaction::with(['client'])->get()->toArray();
            case 'products':
                return Product::all()->toArray();
            default:
                return [];
        }
    }

    /**
     * Clear specific data type
     */
    private function clearDataType(string $dataType, bool $hardDelete = false): int
    {
        switch ($dataType) {
            case 'clients':
                $count = $hardDelete ? Client::withTrashed()->count() : Client::count();
                if ($hardDelete) {
                    Client::withTrashed()->forceDelete();
                } else {
                    Client::query()->delete();
                }
                return $count;
            case 'leads':
                $count = $hardDelete ? Lead::withTrashed()->count() : Lead::count();
                if ($hardDelete) {
                    Lead::withTrashed()->forceDelete();
                } else {
                    Lead::query()->delete();
                }
                return $count;
            case 'deals':
                $count = $hardDelete ? Deal::withTrashed()->count() : Deal::count();
                if ($hardDelete) {
                    Deal::withTrashed()->forceDelete();
                } else {
                    Deal::query()->delete();
                }
                return $count;
            case 'quotations':
                $count = $hardDelete ? Quotation::withTrashed()->count() : Quotation::count();
                if ($hardDelete) {
                    Quotation::withTrashed()->forceDelete();
                } else {
                    Quotation::query()->delete();
                }
                return $count;
            case 'invoices':
                $count = $hardDelete ? Invoice::withTrashed()->count() : Invoice::count();
                if ($hardDelete) {
                    Invoice::withTrashed()->forceDelete();
                } else {
                    Invoice::query()->delete();
                }
                return $count;
            case 'transactions':
                $count = $hardDelete ? Transaction::withTrashed()->count() : Transaction::count();
                if ($hardDelete) {
                    Transaction::withTrashed()->forceDelete();
                } else {
                    Transaction::query()->delete();
                }
                return $count;
            case 'products':
                $count = $hardDelete ? Product::withTrashed()->count() : Product::count();
                if ($hardDelete) {
                    Product::withTrashed()->forceDelete();
                } else {
                    Product::query()->delete();
                }
                return $count;
            default:
                return 0;
        }
    }

    /**
     * Export data as CSV
     */
    private function exportCsv(array $data, string $dataType): StreamedResponse
    {
        $filename = $dataType . '_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        return new StreamedResponse(function() use ($data) {
            $handle = fopen('php://output', 'w');
            
            if (!empty($data)) {
                // Write headers
                fputcsv($handle, array_keys($data[0]));
                
                // Write data rows
                foreach ($data as $row) {
                    fputcsv($handle, $row);
                }
            }
            
            fclose($handle);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Export data as Excel (basic implementation)
     */
    private function exportExcel(array $data, string $dataType): Response
    {
        // For now, we'll export as CSV with Excel MIME type
        // In a production environment, you'd want to use a library like PhpSpreadsheet
        $filename = $dataType . '_export_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        $csvContent = '';
        if (!empty($data)) {
            // Headers
            $csvContent .= implode(',', array_keys($data[0])) . "\n";
            
            // Data rows
            foreach ($data as $row) {
                $csvContent .= implode(',', array_map(function($value) {
                    return '"' . str_replace('"', '""', $value) . '"';
                }, $row)) . "\n";
            }
        }
        
        return response($csvContent, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Export data as JSON
     */
    private function exportJson(array $data, string $dataType): Response
    {
        $filename = $dataType . '_export_' . date('Y-m-d_H-i-s') . '.json';

        return response(json_encode($data, JSON_PRETTY_PRINT), 200, [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
