<?php

namespace App\Http\Controllers;

use App\Models\Deal;
use App\Models\Lead;
use App\Models\Client;
use App\Models\ActivityLog;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class DealController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Deal::with(['lead', 'client', 'assignedTo', 'createdBy']);

        // Filter by pipeline stage
        if ($request->has('stage')) {
            $query->where('pipeline_stage', $request->stage);
        }

        // Filter by assigned user
        if ($request->has('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        // Filter by client
        if ($request->has('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        // Filter by deal type
        if ($request->has('deal_type')) {
            $query->where('deal_type', $request->deal_type);
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by value range
        if ($request->has('min_value')) {
            $query->where('value', '>=', $request->min_value);
        }
        if ($request->has('max_value')) {
            $query->where('value', '<=', $request->max_value);
        }

        // Filter by expected close date range
        if ($request->has('close_date_from')) {
            $query->where('expected_close_date', '>=', $request->close_date_from);
        }
        if ($request->has('close_date_to')) {
            $query->where('expected_close_date', '<=', $request->close_date_to);
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'ILIKE', "%{$search}%")
                  ->orWhere('description', 'ILIKE', "%{$search}%")
                  ->orWhere('deal_number', 'ILIKE', "%{$search}%")
                  ->orWhereHas('client', function ($clientQuery) use ($search) {
                      $clientQuery->where('name', 'ILIKE', "%{$search}%")
                                  ->orWhere('company', 'ILIKE', "%{$search}%");
                  });
            });
        }

        // Filter active deals only
        if ($request->boolean('active_only')) {
            $query->active();
        }

        // Filter overdue deals
        if ($request->boolean('overdue_only')) {
            $query->overdue();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = [
            'created_at', 'updated_at', 'title', 'value', 'expected_close_date',
            'probability', 'pipeline_stage', 'priority', 'deal_number'
        ];
        
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = min($request->get('per_page', 15), 100);
        $deals = $query->paginate($perPage);

        return response()->json($deals);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'lead_id' => 'nullable|exists:leads,id',
            'client_id' => 'nullable|exists:clients,id',
            'assigned_to' => 'nullable|exists:users,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'value' => 'required|numeric|min:0',
            'expected_revenue' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'pipeline_stage' => ['nullable', Rule::in([
                'prospecting', 'qualification', 'proposal', 'negotiation',
                'closing', 'won', 'lost', 'on_hold'
            ])],
            'probability' => 'nullable|integer|min:0|max:100',
            'deal_size' => ['nullable', Rule::in(['Small', 'Medium', 'Large', 'Enterprise'])],
            'expected_close_date' => 'nullable|date',
            'source' => 'nullable|string|max:255',
            'utm_source' => 'nullable|string|max:255',
            'utm_campaign' => 'nullable|string|max:255',
            'utm_medium' => 'nullable|string|max:255',
            'priority' => ['nullable', Rule::in(['Low', 'Medium', 'High', 'Critical'])],
            'deal_type' => ['nullable', Rule::in([
                'New Business', 'Upsell', 'Cross-sell', 'Renewal', 'Expansion'
            ])],
            'tags' => 'nullable|array',
            'competitors' => 'nullable|array',
            'competitive_advantage' => 'nullable|string',
            'win_probability_reason' => ['nullable', Rule::in([
                'Strong Relationship', 'Best Price', 'Superior Product',
                'Incumbent Advantage', 'Strategic Partnership', 'Unique Solution', 'Other'
            ])],
            'notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'next_follow_up' => 'nullable|date',
            'next_action' => 'nullable|string|max:255',
            'auto_follow_up_enabled' => 'nullable|boolean',
            'follow_up_frequency_days' => 'nullable|integer|min:1|max:365',
        ]);

        // Set defaults
        $validated['pipeline_stage'] = $validated['pipeline_stage'] ?? 'prospecting';
        $validated['priority'] = $validated['priority'] ?? 'Medium';
        $validated['deal_type'] = $validated['deal_type'] ?? 'New Business';
        $validated['currency'] = $validated['currency'] ?? 'MYR';
        $validated['created_by'] = auth()->id() ?? 1; // Default to user 1 if no auth
        $validated['assigned_to'] = $validated['assigned_to'] ?? auth()->id() ?? 1;
        $validated['last_activity'] = now();

        $deal = Deal::create($validated);

        // Log deal creation with lead association
        if ($deal->lead_id) {
            \Log::info("Deal created with lead association", [
                'deal_id' => $deal->id,
                'lead_id' => $deal->lead_id,
                'title' => $deal->title
            ]);
        }

        // Create notification for assigned user if different from creator
        if ($deal->assigned_to && $deal->assigned_to !== $deal->created_by) {
            $assignedUser = User::find($deal->assigned_to);
            if ($assignedUser) {
                Notification::createDealAssigned($assignedUser, $deal, auth()->user());
            }
        }

        return response()->json($deal->load(['lead', 'client', 'assignedTo', 'createdBy']), 201);
    }

    public function show(Deal $deal): JsonResponse
    {
        return response()->json($deal->load(['lead', 'client', 'assignedTo', 'createdBy']));
    }

    public function update(Request $request, Deal $deal): JsonResponse
    {
        // Log the incoming request data for debugging
        \Log::info('Deal update request received', [
            'deal_id' => $deal->id,
            'original_lead_id' => $deal->lead_id,
            'request_data' => $request->all()
        ]);

        $validated = $request->validate([
            'lead_id' => 'nullable|exists:leads,id',
            'client_id' => 'nullable|exists:clients,id',
            'assigned_to' => 'nullable|exists:users,id',
            'title' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'value' => 'sometimes|required|numeric|min:0',
            'expected_revenue' => 'nullable|numeric|min:0',
            'actual_revenue' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'pipeline_stage' => ['nullable', Rule::in([
                'prospecting', 'qualification', 'proposal', 'negotiation',
                'closing', 'won', 'lost', 'on_hold'
            ])],
            'probability' => 'nullable|integer|min:0|max:100',
            'deal_size' => ['nullable', Rule::in(['Small', 'Medium', 'Large', 'Enterprise'])],
            'expected_close_date' => 'nullable|date',
            'actual_close_date' => 'nullable|date',
            'source' => 'nullable|string|max:255',
            'utm_source' => 'nullable|string|max:255',
            'utm_campaign' => 'nullable|string|max:255',
            'utm_medium' => 'nullable|string|max:255',
            'priority' => ['nullable', Rule::in(['Low', 'Medium', 'High', 'Critical'])],
            'deal_type' => ['nullable', Rule::in([
                'New Business', 'Upsell', 'Cross-sell', 'Renewal', 'Expansion'
            ])],
            'tags' => 'nullable|array',
            'competitors' => 'nullable|array',
            'competitive_advantage' => 'nullable|string',
            'win_probability_reason' => ['nullable', Rule::in([
                'Strong Relationship', 'Best Price', 'Superior Product',
                'Incumbent Advantage', 'Strategic Partnership', 'Unique Solution', 'Other'
            ])],
            'notes' => 'nullable|string',
            'internal_notes' => 'nullable|string',
            'next_follow_up' => 'nullable|date',
            'next_action' => 'nullable|string|max:255',
            'auto_follow_up_enabled' => 'nullable|boolean',
            'follow_up_frequency_days' => 'nullable|integer|min:1|max:365',
            'loss_reason' => ['nullable', Rule::in([
                'Price Too High', 'Competitor Won', 'No Budget', 'No Decision',
                'Product Mismatch', 'Timing Issues', 'Lost Contact', 'Other'
            ])],
            'loss_details' => 'nullable|string',
            'competitor_won' => 'nullable|string|max:255',
        ]);

        // Store original lead_id to ensure it's preserved
        $originalLeadId = $deal->lead_id;

        // Log validated data
        \Log::info('Deal update validated data', [
            'deal_id' => $deal->id,
            'original_lead_id' => $originalLeadId,
            'validated_lead_id' => $validated['lead_id'] ?? 'not_provided',
            'validated_data' => $validated
        ]);

        // Update last activity timestamp
        $validated['last_activity'] = now();

        // Handle stage changes
        $oldStage = $deal->pipeline_stage;
        if (isset($validated['pipeline_stage']) && $validated['pipeline_stage'] !== $deal->pipeline_stage) {
            $validated['stage_changed_at'] = now();
            $validated['stage_changed_by'] = auth()->user()?->name ?? 'System';

            // Auto-set actual close date for won/lost deals
            if (in_array($validated['pipeline_stage'], ['won', 'lost']) && !$deal->actual_close_date) {
                $validated['actual_close_date'] = now();
            }

            // Set actual revenue for won deals
            if ($validated['pipeline_stage'] === 'won' && !$deal->actual_revenue) {
                $validated['actual_revenue'] = $deal->value;
            }
        }

        $deal->update($validated);

        // Log after first update
        \Log::info('Deal after first update', [
            'deal_id' => $deal->id,
            'lead_id_after_update' => $deal->lead_id,
            'original_lead_id' => $originalLeadId
        ]);

        // Ensure lead_id is preserved after update if it wasn't explicitly set or was set to null/empty
        if ($originalLeadId && !$deal->lead_id) {
            // Check if lead_id was explicitly set to a valid value
            $leadIdExplicitlySet = isset($validated['lead_id']) && !empty($validated['lead_id']);

            if (!$leadIdExplicitlySet) {
                \Log::info('Preserving original lead_id', [
                    'deal_id' => $deal->id,
                    'original_lead_id' => $originalLeadId,
                    'validated_lead_id' => $validated['lead_id'] ?? 'not_set'
                ]);
                $deal->update(['lead_id' => $originalLeadId]);
            }
        }

        // Create notifications for stage changes
        if (isset($validated['pipeline_stage']) && $validated['pipeline_stage'] !== $oldStage) {
            $assignedUser = $deal->assignedTo;
            if ($assignedUser) {
                if ($validated['pipeline_stage'] === 'won') {
                    Notification::createDealWon($assignedUser, $deal, auth()->user());
                } elseif ($validated['pipeline_stage'] === 'lost') {
                    $lossReason = $validated['loss_reason'] ?? 'No reason provided';
                    Notification::createDealLost($assignedUser, $deal, $lossReason, auth()->user());
                }
            }
        }

        // Handle assignment changes
        if (isset($validated['assigned_to']) && $validated['assigned_to'] !== $deal->assigned_to) {
            $newAssignedUser = User::find($validated['assigned_to']);
            if ($newAssignedUser && $newAssignedUser->id !== auth()->id()) {
                Notification::createDealAssigned($newAssignedUser, $deal, auth()->user());
            }
        }

        // Refresh the model to get the latest data
        $deal->refresh();

        // Log final state before returning
        \Log::info('Deal final state before response', [
            'deal_id' => $deal->id,
            'final_lead_id' => $deal->lead_id,
            'original_lead_id' => $originalLeadId
        ]);

        return response()->json($deal->load(['lead', 'client', 'assignedTo', 'createdBy']));
    }

    /**
     * Reopen a lost deal back to negotiation
     */
    public function reopen(Request $request, Deal $deal): JsonResponse
    {
        if ($deal->pipeline_stage !== 'lost') {
            return response()->json(['error' => 'Only lost deals can be reopened'], 400);
        }

        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $oldStage = $deal->pipeline_stage;

            // Update deal back to negotiation
            $deal->update([
                'pipeline_stage' => 'negotiation',
                'stage_changed_at' => now(),
                'stage_changed_by' => auth()->user()?->name ?? 'System',
                'loss_reason' => null,
                'loss_details' => null,
                'notes' => $deal->notes . "\n\n[REOPENED] " . ($validated['notes'] ?? 'Deal reopened for negotiation on ' . now()->format('Y-m-d H:i:s')),
                'last_activity' => now(),
            ]);

            // Log the reopening activity
            ActivityLog::logStageChange(
                'deal_reopened',
                "Deal '{$deal->title}' was reopened from lost to negotiation stage",
                $deal,
                ['pipeline_stage' => $oldStage],
                ['pipeline_stage' => 'negotiation'],
                [
                    'reason' => $validated['reason'] ?? 'Deal reopened for negotiation',
                    'reopened_at' => now()->toISOString(),
                ]
            );

            DB::commit();

            return response()->json([
                'message' => 'Deal successfully reopened for negotiation',
                'deal' => $deal->load(['lead', 'client', 'createdBy', 'assignedTo']),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to reopen deal: ' . $e->getMessage()], 500);
        }
    }

    public function destroy(Deal $deal): JsonResponse
    {
        $deal->delete();
        return response()->json(['message' => 'Deal deleted successfully']);
    }

    /**
     * Move deal to different pipeline stage
     */
    public function moveStage(Request $request, Deal $deal): JsonResponse
    {
        $validated = $request->validate([
            'pipeline_stage' => ['required', Rule::in([
                'prospecting', 'qualification', 'proposal', 'negotiation',
                'closing', 'won', 'lost', 'on_hold'
            ])],
            'lead_id' => 'nullable|exists:leads,id',
            'notes' => 'nullable|string',
            'loss_reason' => ['nullable', Rule::in([
                'Price Too High', 'Competitor Won', 'No Budget', 'No Decision',
                'Product Mismatch', 'Timing Issues', 'Lost Contact', 'Other'
            ])],
            'loss_details' => 'nullable|string',
            'competitor_won' => 'nullable|string|max:255',
        ]);

        // Store original lead_id to ensure it's preserved
        $originalLeadId = $deal->lead_id;

        $updateData = [
            'pipeline_stage' => $validated['pipeline_stage'],
            'stage_changed_at' => now(),
            'stage_changed_by' => auth()->user()?->name ?? 'System',
            'last_activity' => now(),
            'probability' => Deal::getDefaultProbabilityForStage($validated['pipeline_stage']),
        ];

        // Preserve lead_id - use provided value or original
        if (isset($validated['lead_id'])) {
            $updateData['lead_id'] = $validated['lead_id'];
        } elseif ($originalLeadId) {
            $updateData['lead_id'] = $originalLeadId;
        }

        // Handle won deals
        if ($validated['pipeline_stage'] === 'won') {
            $updateData['actual_close_date'] = now();
            $updateData['actual_revenue'] = $deal->value;
        }

        // Handle lost deals
        if ($validated['pipeline_stage'] === 'lost') {
            $updateData['actual_close_date'] = now();
            $updateData['actual_revenue'] = 0;

            if (isset($validated['loss_reason'])) {
                $updateData['loss_reason'] = $validated['loss_reason'];
            }
            if (isset($validated['loss_details'])) {
                $updateData['loss_details'] = $validated['loss_details'];
            }
            if (isset($validated['competitor_won'])) {
                $updateData['competitor_won'] = $validated['competitor_won'];
            }
        }

        // Add notes if provided
        if (isset($validated['notes'])) {
            $existingNotes = $deal->notes ? $deal->notes . "\n\n" : '';
            $updateData['notes'] = $existingNotes . "[" . now()->format('Y-m-d H:i') . "] Stage moved to " .
                                   ucfirst($validated['pipeline_stage']) . ": " . $validated['notes'];
        }

        $deal->update($updateData);

        // Ensure lead_id is preserved after update
        if ($originalLeadId && !$deal->lead_id) {
            $deal->update(['lead_id' => $originalLeadId]);
        }

        // Refresh the model to get the latest data
        $deal->refresh();

        return response()->json($deal->load(['lead', 'client', 'assignedTo', 'createdBy']));
    }

    /**
     * Get pipeline statistics
     */
    public function pipelineStats(Request $request): JsonResponse
    {
        $query = Deal::query();

        // Apply filters if provided
        if ($request->has('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->has('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total_deals' => $query->count(),
            'total_value' => $query->sum('value'),
            'won_deals' => $query->where('pipeline_stage', 'won')->count(),
            'won_value' => $query->where('pipeline_stage', 'won')->sum('actual_revenue'),
            'lost_deals' => $query->where('pipeline_stage', 'lost')->count(),
            'active_deals' => $query->active()->count(),
            'active_value' => $query->active()->sum('value'),
            'overdue_deals' => $query->overdue()->count(),
            'avg_deal_value' => $query->avg('value'),
            'conversion_rate' => 0,
        ];

        // Calculate conversion rate
        $totalClosed = $stats['won_deals'] + $stats['lost_deals'];
        if ($totalClosed > 0) {
            $stats['conversion_rate'] = round(($stats['won_deals'] / $totalClosed) * 100, 2);
        }

        // Stage breakdown
        $stageStats = $query->select('pipeline_stage', DB::raw('count(*) as count'), DB::raw('sum(value) as total_value'))
                           ->groupBy('pipeline_stage')
                           ->get()
                           ->keyBy('pipeline_stage');

        $stats['stages'] = $stageStats;

        return response()->json($stats);
    }

    /**
     * Convert lead to deal
     */
    public function convertFromLead(Request $request, Lead $lead): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'nullable|string|max:255',
            'value' => 'required|numeric|min:0',
            'expected_close_date' => 'nullable|date',
            'priority' => ['nullable', Rule::in(['Low', 'Medium', 'High', 'Critical'])],
            'deal_type' => ['nullable', Rule::in([
                'New Business', 'Upsell', 'Cross-sell', 'Renewal', 'Expansion'
            ])],
            'notes' => 'nullable|string',
        ]);

        // Check if lead is already converted
        if ($lead->converted_to_deal) {
            return response()->json(['error' => 'Lead has already been converted to a deal'], 422);
        }

        $deal = $lead->convertToDeal($validated);

        return response()->json($deal->load(['lead', 'client', 'assignedTo', 'createdBy']), 201);
    }
}
