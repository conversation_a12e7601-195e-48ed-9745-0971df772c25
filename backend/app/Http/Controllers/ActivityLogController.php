<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ActivityLogController extends Controller
{
    /**
     * Display a listing of activity logs
     */
    public function index(Request $request): JsonResponse
    {
        $query = ActivityLog::with(['user']);

        // Filters
        if ($request->has('action')) {
            $query->where('action', 'like', '%' . $request->get('action') . '%');
        }

        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        if ($request->has('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        if ($request->has('performed_by')) {
            $query->where('performed_by', 'like', '%' . $request->get('performed_by') . '%');
        }

        // Date range filtering
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->get('start_date'));
        }

        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->get('end_date'));
        }

        // Search across multiple fields
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('action', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('performed_by', 'like', "%{$search}%")
                  ->orWhere('subject_name', 'like', "%{$search}%")
                  ->orWhere('related_name', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 50);
        $logs = $query->paginate($perPage);

        return response()->json($logs);
    }

    /**
     * Clear all activity logs
     */
    public function clearAll(): JsonResponse
    {
        try {
            $count = ActivityLog::count();
            ActivityLog::truncate();
            
            return response()->json([
                'message' => "Successfully cleared {$count} activity logs",
                'cleared_count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to clear activity logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
