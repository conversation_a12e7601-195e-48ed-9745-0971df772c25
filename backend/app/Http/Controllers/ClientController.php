<?php

namespace App\Http\Controllers;

use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ClientController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Client::query();

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('email', 'ILIKE', "%{$search}%")
                  ->orWhere('phone', 'ILIKE', "%{$search}%")
                  ->orWhere('company', 'ILIKE', "%{$search}%")
                  ->orWhere('uuid', 'ILIKE', "%{$search}%");
            });
        }

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->has('ltv_segment')) {
            $query->where('ltv_segment', $request->get('ltv_segment'));
        }

        if ($request->has('engagement_level')) {
            $query->where('engagement_level', $request->get('engagement_level'));
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        if ($request->has('utm_source')) {
            $query->where('utm_source', $request->get('utm_source'));
        }

        // New filters for enhanced fields
        if ($request->has('data_quality')) {
            $query->where('data_quality', $request->get('data_quality'));
        }

        if ($request->has('phone_carrier')) {
            $query->where('phone_carrier', $request->get('phone_carrier'));
        }

        if ($request->has('overall_score_min')) {
            $query->where('overall_score', '>=', $request->get('overall_score_min'));
        }

        if ($request->has('overall_score_max')) {
            $query->where('overall_score', '<=', $request->get('overall_score_max'));
        }

        // Optimized pagination for large datasets
        $perPage = $request->get('per_page', 50); // Default to 50 for better performance
        $perPage = min($perPage, 500); // Maximum 500 records per page for performance

        // Order by overall_score desc, then created_at desc for better UX
        $clients = $query->with(['phoneNumbers', 'emails'])
                        ->orderBy('overall_score', 'desc')
                        ->orderBy('created_at', 'desc')
                        ->paginate($perPage);

        return response()->json($clients);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:clients,email',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive,prospect',
            'notes' => 'nullable|string',
            'utm_source' => 'nullable|string|max:100',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'category' => 'nullable|in:First Timer,Retainer,Loyal,Advocator',
            'ltv_segment' => 'nullable|in:Silver,Gold,Gold+,Platinum',
            'engagement_level' => 'nullable|in:Hot,Warm,Cold,Frozen',
            'priority' => 'nullable|in:High,Medium,Low',
            'suggested_action' => 'nullable|string|max:255',
            'last_activity' => 'nullable|date',
            'total_spent' => 'nullable|numeric|min:0',
            'transaction_count' => 'nullable|integer|min:0',
            'custom_fields' => 'nullable|array',
            'email_verified' => 'nullable|boolean',
            'phone_verified' => 'nullable|boolean',
            // New scoring fields
            'name_score' => 'nullable|integer|min:0|max:100',
            'email_score' => 'nullable|integer|min:0|max:100',
            'phone_score' => 'nullable|integer|min:0|max:100',
            'overall_score' => 'nullable|integer|min:0|max:100',
            // New validation fields
            'email_deliverability' => 'nullable|string|max:255',
            'phone_validity' => 'nullable|boolean',
            'phone_carrier' => 'nullable|string|max:255',
            // New categorization fields
            'data_quality' => 'nullable|in:Poor,Fair,Good,Excellent',
            'customer_category' => 'nullable|string|max:255',
            'notes_remarks' => 'nullable|string',
            'suggested_next_action' => 'nullable|string|max:255',
            // Personal information fields
            'ic_number' => 'nullable|string|max:20',
            'birthday' => 'nullable|date',
            'gender' => 'nullable|in:Male,Female',
            'religion' => 'nullable|in:Muslim,Non-Muslim',
            // Financial information
            'income' => 'nullable|numeric|min:0',
            'income_category' => 'nullable|in:Low,Medium,High',
            // Enhanced address fields
            'address_line_1' => 'nullable|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'postcode' => 'nullable|string|max:10',
            // Behavioral data
            'behaviour' => 'nullable|string',
            'interest' => 'nullable|string',
            // Multi-contact arrays
            'emails' => 'nullable|array',
            'emails.*.id' => 'nullable|string',
            'emails.*.email_address' => 'required_with:emails.*|email',
            'emails.*.is_primary' => 'nullable|boolean',
            'emails.*.email_verified' => 'nullable|boolean',
            'emails.*.email_score' => 'nullable|integer|min:0|max:100',
            'emails.*.email_deliverability' => 'nullable|string',
            'phone_numbers' => 'nullable|array',
            'phone_numbers.*.id' => 'nullable|string',
            'phone_numbers.*.phone_number' => 'required_with:phone_numbers.*|string|max:20',
            'phone_numbers.*.is_primary' => 'nullable|boolean',
            'phone_numbers.*.phone_verified' => 'nullable|boolean',
            'phone_numbers.*.phone_score' => 'nullable|integer|min:0|max:100',
            'phone_numbers.*.phone_carrier' => 'nullable|string',
        ]);

        // Custom validation: require at least email or phone
        $hasEmailInArray = !empty($validated['emails']) && count(array_filter($validated['emails'], fn($email) => !empty($email['email_address']))) > 0;
        $hasPhoneInArray = !empty($validated['phone_numbers']) && count(array_filter($validated['phone_numbers'], fn($phone) => !empty($phone['phone_number']))) > 0;

        if (empty($validated['email']) && empty($validated['phone']) && !$hasEmailInArray && !$hasPhoneInArray) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => [
                    'contact' => ['At least one contact method (email or phone) is required.']
                ]
            ], 422);
        }

        // Set defaults for new clients
        $validated['category'] = $validated['category'] ?? 'First Timer';
        $validated['ltv_segment'] = $validated['ltv_segment'] ?? 'Silver';
        $validated['engagement_level'] = $validated['engagement_level'] ?? 'Cold';
        $validated['priority'] = $validated['priority'] ?? 'Medium';
        $validated['last_activity'] = $validated['last_activity'] ?? now();
        $validated['total_spent'] = $validated['total_spent'] ?? 0;
        $validated['transaction_count'] = $validated['transaction_count'] ?? 0;

        // Set verification defaults to true for new clients
        $validated['email_verified'] = $validated['email_verified'] ?? true;
        $validated['phone_verified'] = $validated['phone_verified'] ?? true;

        // Remove multi-contact arrays from main client data before creating
        $emailsData = $validated['emails'] ?? [];
        $phoneNumbersData = $validated['phone_numbers'] ?? [];
        unset($validated['emails'], $validated['phone_numbers']);

        $client = Client::create($validated);

        // Handle multi-contact emails
        foreach ($emailsData as $emailData) {
            if (!empty($emailData['email_address'])) {
                $client->emails()->create([
                    'email_address' => $emailData['email_address'],
                    'is_primary' => $emailData['is_primary'] ?? false,
                    'email_verified' => $emailData['email_verified'] ?? true,
                    'email_score' => $emailData['email_score'] ?? 0,
                    'email_deliverability' => $emailData['email_deliverability'] ?? null,
                ]);
            }
        }

        // Handle multi-contact phone numbers
        foreach ($phoneNumbersData as $phoneData) {
            if (!empty($phoneData['phone_number'])) {
                $client->phoneNumbers()->create([
                    'phone_number' => $phoneData['phone_number'],
                    'is_primary' => $phoneData['is_primary'] ?? false,
                    'phone_verified' => $phoneData['phone_verified'] ?? true,
                    'phone_score' => $phoneData['phone_score'] ?? 0,
                    'phone_carrier' => $phoneData['phone_carrier'] ?? null,
                ]);
            }
        }

        return response()->json($client->load(['phoneNumbers', 'emails']), 201);
    }

    public function show(Client $client): JsonResponse
    {
        return response()->json($client->load(['transactions', 'leads', 'phoneNumbers', 'emails']));
    }

    public function update(Request $request, Client $client): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'nullable|email|unique:clients,email,' . $client->id,
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'status' => 'sometimes|required|in:active,inactive,prospect',
            'notes' => 'nullable|string',
            'utm_source' => 'nullable|string|max:100',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'category' => 'nullable|in:First Timer,Retainer,Loyal,Advocator',
            'ltv_segment' => 'nullable|in:Silver,Gold,Gold+,Platinum',
            'engagement_level' => 'nullable|in:Hot,Warm,Cold,Frozen',
            'priority' => 'nullable|in:High,Medium,Low',
            'suggested_action' => 'nullable|string|max:255',
            'last_activity' => 'nullable|date',
            'total_spent' => 'nullable|numeric|min:0',
            'transaction_count' => 'nullable|integer|min:0',
            'custom_fields' => 'nullable|array',
            'email_verified' => 'nullable|boolean',
            'phone_verified' => 'nullable|boolean',
            // New scoring fields
            'name_score' => 'nullable|integer|min:0|max:100',
            'email_score' => 'nullable|integer|min:0|max:100',
            'phone_score' => 'nullable|integer|min:0|max:100',
            'overall_score' => 'nullable|integer|min:0|max:100',
            // New validation fields
            'email_deliverability' => 'nullable|string|max:255',
            'phone_validity' => 'nullable|boolean',
            'phone_carrier' => 'nullable|string|max:255',
            // New categorization fields
            'data_quality' => 'nullable|in:Poor,Fair,Good,Excellent',
            'customer_category' => 'nullable|string|max:255',
            'notes_remarks' => 'nullable|string',
            'suggested_next_action' => 'nullable|string|max:255',
            // Personal information fields
            'ic_number' => 'nullable|string|max:20',
            'birthday' => 'nullable|date',
            'gender' => 'nullable|in:Male,Female',
            'religion' => 'nullable|in:Muslim,Non-Muslim',
            // Financial information
            'income' => 'nullable|numeric|min:0',
            'income_category' => 'nullable|in:Low,Medium,High',
            // Enhanced address fields
            'address_line_1' => 'nullable|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'postcode' => 'nullable|string|max:10',
            // Behavioral data
            'behaviour' => 'nullable|string',
            'interest' => 'nullable|string',
            // Multi-contact arrays
            'emails' => 'nullable|array',
            'emails.*.id' => 'nullable|string',
            'emails.*.email_address' => 'required_with:emails.*|email',
            'emails.*.is_primary' => 'nullable|boolean',
            'emails.*.email_verified' => 'nullable|boolean',
            'emails.*.email_score' => 'nullable|integer|min:0|max:100',
            'emails.*.email_deliverability' => 'nullable|string',
            'phone_numbers' => 'nullable|array',
            'phone_numbers.*.id' => 'nullable|string',
            'phone_numbers.*.phone_number' => 'required_with:phone_numbers.*|string|max:20',
            'phone_numbers.*.is_primary' => 'nullable|boolean',
            'phone_numbers.*.phone_verified' => 'nullable|boolean',
            'phone_numbers.*.phone_score' => 'nullable|integer|min:0|max:100',
            'phone_numbers.*.phone_carrier' => 'nullable|string',
        ]);

        // Custom validation: require at least email or phone (only if both are being updated)
        $currentEmail = $validated['email'] ?? $client->email;
        $currentPhone = $validated['phone'] ?? $client->phone;

        // Also check if there are emails/phones in the multi-contact arrays
        $hasEmailInArray = !empty($validated['emails']) && count(array_filter($validated['emails'], fn($email) => !empty($email['email_address']))) > 0;
        $hasPhoneInArray = !empty($validated['phone_numbers']) && count(array_filter($validated['phone_numbers'], fn($phone) => !empty($phone['phone_number']))) > 0;

        if (empty($currentEmail) && empty($currentPhone) && !$hasEmailInArray && !$hasPhoneInArray) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => [
                    'contact' => ['At least one contact method (email or phone) is required.']
                ]
            ], 422);
        }

        // Update the main client record
        $client->update($validated);

        // Handle multi-contact emails
        if (isset($validated['emails'])) {
            // Delete existing emails and recreate them
            $client->emails()->delete();

            foreach ($validated['emails'] as $emailData) {
                if (!empty($emailData['email_address'])) {
                    $client->emails()->create([
                        'email_address' => $emailData['email_address'],
                        'is_primary' => $emailData['is_primary'] ?? false,
                        'email_verified' => $emailData['email_verified'] ?? true,
                        'email_score' => $emailData['email_score'] ?? 0,
                        'email_deliverability' => $emailData['email_deliverability'] ?? null,
                    ]);
                }
            }
        }

        // Handle multi-contact phone numbers
        if (isset($validated['phone_numbers'])) {
            // Delete existing phone numbers and recreate them
            $client->phoneNumbers()->delete();

            foreach ($validated['phone_numbers'] as $phoneData) {
                if (!empty($phoneData['phone_number'])) {
                    $client->phoneNumbers()->create([
                        'phone_number' => $phoneData['phone_number'],
                        'is_primary' => $phoneData['is_primary'] ?? false,
                        'phone_verified' => $phoneData['phone_verified'] ?? true,
                        'phone_score' => $phoneData['phone_score'] ?? 0,
                        'phone_carrier' => $phoneData['phone_carrier'] ?? null,
                    ]);
                }
            }
        }

        return response()->json($client->load(['phoneNumbers', 'emails']));
    }

    public function destroy(Client $client): JsonResponse
    {
        $client->delete();

        return response()->json(['message' => 'Client deleted successfully']);
    }

    /**
     * Get client statistics for the clients page
     */
    public function statistics(): JsonResponse
    {
        $totalClients = Client::count();
        $activeClients = Client::whereIn('engagement_level', ['Hot', 'Warm'])->count();
        $totalRevenue = Client::sum('total_spent');
        $highPriorityClients = Client::where('priority', 'High')->count();

        return response()->json([
            'total_clients' => $totalClients,
            'active_clients' => $activeClients,
            'total_revenue' => $totalRevenue,
            'high_priority_clients' => $highPriorityClients,
        ]);
    }

    /**
     * Get comprehensive dashboard analytics
     */
    public function dashboardAnalytics(): JsonResponse
    {
        try {
            $totalClients = Client::count();
            $totalTransactions = Client::sum('transaction_count');
            $totalTransactionValue = Client::sum('total_spent');
            $averageOverallScore = Client::avg('overall_score');

            // Score distributions
            $phoneScoreDistribution = [
                ['range' => '90-100', 'count' => Client::whereBetween('phone_score', [90, 100])->count()],
                ['range' => '80-89', 'count' => Client::whereBetween('phone_score', [80, 89])->count()],
                ['range' => '70-79', 'count' => Client::whereBetween('phone_score', [70, 79])->count()],
                ['range' => '60-69', 'count' => Client::whereBetween('phone_score', [60, 69])->count()],
                ['range' => '50-59', 'count' => Client::whereBetween('phone_score', [50, 59])->count()],
                ['range' => '0-49', 'count' => Client::whereBetween('phone_score', [0, 49])->count()],
            ];

            $emailScoreDistribution = [
                ['range' => '90-100', 'count' => Client::whereBetween('email_score', [90, 100])->count()],
                ['range' => '80-89', 'count' => Client::whereBetween('email_score', [80, 89])->count()],
                ['range' => '70-79', 'count' => Client::whereBetween('email_score', [70, 79])->count()],
                ['range' => '60-69', 'count' => Client::whereBetween('email_score', [60, 69])->count()],
                ['range' => '50-59', 'count' => Client::whereBetween('email_score', [50, 59])->count()],
                ['range' => '0-49', 'count' => Client::whereBetween('email_score', [0, 49])->count()],
            ];

            $overallScoreDistribution = [
                ['range' => '90-100', 'count' => Client::whereBetween('overall_score', [90, 100])->count()],
                ['range' => '80-89', 'count' => Client::whereBetween('overall_score', [80, 89])->count()],
                ['range' => '70-79', 'count' => Client::whereBetween('overall_score', [70, 79])->count()],
                ['range' => '60-69', 'count' => Client::whereBetween('overall_score', [60, 69])->count()],
                ['range' => '50-59', 'count' => Client::whereBetween('overall_score', [50, 59])->count()],
                ['range' => '0-49', 'count' => Client::whereBetween('overall_score', [0, 49])->count()],
            ];

            // Add percentages to score distributions
            foreach ([$phoneScoreDistribution, $emailScoreDistribution, $overallScoreDistribution] as &$distribution) {
                foreach ($distribution as &$item) {
                    $item['percentage'] = $totalClients > 0 ? round(($item['count'] / $totalClients) * 100) : 0;
                }
            }

            return response()->json([
                'totalClients' => $totalClients,
                'totalTransactions' => $totalTransactions,
                'totalTransactionValue' => $totalTransactionValue,
                'averageOverallScore' => round($averageOverallScore, 1),
                'phoneScoreDistribution' => $phoneScoreDistribution,
                'emailScoreDistribution' => $emailScoreDistribution,
                'overallScoreDistribution' => $overallScoreDistribution,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch dashboard analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
