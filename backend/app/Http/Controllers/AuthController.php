<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Client;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rule;

class AuthController extends Controller
{
    /**
     * Login user
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        if (!$user->is_active) {
            throw ValidationException::withMessages([
                'email' => ['Your account is not active. Please contact an administrator.'],
            ]);
        }

        // Check if 2FA is required
        if ($user->requiresTwoFactor()) {
            // Generate and send 2FA code
            $code = $user->generateTwoFactorCode();

            // Send email with 2FA code
            try {
                // Get configurable email settings
                $fromEmail = \App\Models\SystemSetting::get('email_from_address', '<EMAIL>');
                $fromName = \App\Models\SystemSetting::get('email_from_name', 'KDT CRM System');
                $replyTo = \App\Models\SystemSetting::get('email_reply_to', '<EMAIL>');

                // Configure SMTP settings dynamically
                $this->configureMailSettings();

                Mail::raw("Your verification code is: {$code}\n\nThis code will expire in 10 minutes.", function ($message) use ($user, $fromEmail, $fromName, $replyTo) {
                    $message->to($user->email)
                            ->from($fromEmail, $fromName)
                            ->replyTo($replyTo)
                            ->subject('Two-Factor Authentication Code - KDT CRM System');
                });
            } catch (\Exception $e) {
                // Return the actual error for debugging in development
                throw ValidationException::withMessages([
                    'email' => ['AuthController Email error: ' . $e->getMessage() . ' | Class: ' . get_class($e) . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine()],
                ]);
            }

            return response()->json([
                'requires_2fa' => true,
                'message' => 'Verification code sent to your email',
                'email' => $user->email
            ]);
        }

        // Update last login
        $user->update(['last_login_at' => now()]);

        // Create token
        $token = $user->createToken('auth-token')->plainTextToken;

        return response()->json([
            'user' => $user,
            'token' => $token,
            'message' => 'Login successful'
        ]);
    }

    /**
     * Register new user (requires admin approval)
     */
    public function register(Request $request): JsonResponse
    {
        // First validate basic fields
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'department' => 'nullable|string|max:100',
        ]);

        $email = $request->email;

        // Check if email already exists as an active user
        $existingUser = User::where('email', $email)->where('is_active', true)->first();
        if ($existingUser) {
            throw ValidationException::withMessages([
                'email' => ['This email is already registered as an active user account.'],
            ]);
        }

        // Check if email exists as a client
        $existingClient = Client::where('email', $email)->first();
        if ($existingClient) {
            throw ValidationException::withMessages([
                'email' => ['This email is already in our system as a client. Please sign in and use "Forgot Password" to access your account.'],
            ]);
        }

        // Create user with default role 'user' and inactive status
        $user = User::create([
            'name' => $request->name,
            'email' => $email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone ?? null,
            'department' => $request->department ?? null,
            'role' => User::ROLE_USER,
            'is_active' => false, // Requires admin approval
        ]);

        // Notify all admin users about the new registration
        $adminUsers = User::where('role', User::ROLE_ADMIN)->where('is_active', true)->get();
        foreach ($adminUsers as $admin) {
            \App\Models\Notification::createUserRegistration($admin, $user);
        }

        return response()->json([
            'message' => 'Registration successful. Your account is pending admin approval.',
            'user' => $user->only(['id', 'name', 'email', 'phone', 'department', 'role', 'is_active'])
        ], 201);
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get current authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        return response()->json($request->user());
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => [
                'sometimes',
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($user->id)->where(function ($query) {
                    return $query->where('is_active', true);
                })
            ],
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'profile_picture' => 'nullable|string',
            'notification_preferences' => 'nullable|array',
            'privacy_settings' => 'nullable|array',
        ]);

        // Update basic user fields
        $userFields = array_intersect_key($validated, array_flip(['name', 'email', 'phone']));
        if (!empty($userFields)) {
            $user->update($userFields);
        }

        // Handle profile picture, bio, and preferences (these would typically be in a separate profile table)
        // For now, we'll store them as JSON in additional fields or handle them separately

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user->fresh()
        ]);
    }

    /**
     * Change user password
     */
    public function changePassword(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        // Verify current password
        if (!Hash::check($validated['current_password'], $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['The current password is incorrect.'],
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($validated['new_password']),
        ]);

        return response()->json([
            'message' => 'Password changed successfully'
        ]);
    }

    /**
     * Approve user (admin only)
     * Note: Admin middleware is applied in routes
     */
    public function approveUser(Request $request, User $user): JsonResponse
    {
        $user->update(['is_active' => true]);

        // Create notification for the approved user
        \App\Models\Notification::createUserApproval($user, $request->user());

        return response()->json([
            'message' => 'User approved successfully',
            'user' => $user
        ]);
    }

    /**
     * Get pending users (admin only)
     * Note: Admin middleware is applied in routes
     */
    public function pendingUsers(Request $request): JsonResponse
    {
        $pendingUsers = User::where('is_active', false)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($pendingUsers);
    }

    /**
     * Configure mail settings based on environment
     */
    private function configureMailSettings(): void
    {
        // Check if user wants to force real email delivery (for testing)
        $forceRealEmail = \App\Models\SystemSetting::get('force_real_email_delivery', false);

        // In development, use Mailpit unless real email is forced
        if (!$forceRealEmail && (app()->environment('local', 'development') || config('mail.mailers.smtp.host') === 'kdt-mailpit')) {
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => 'kdt-mailpit',
                'mail.mailers.smtp.port' => 1025,
                'mail.mailers.smtp.username' => null,
                'mail.mailers.smtp.password' => null,
                'mail.mailers.smtp.encryption' => null,
            ]);
            return;
        }

        // Use Zoho settings for production or when real email is forced
        $smtpHost = \App\Models\SystemSetting::get('zoho_smtp_host', 'smtp.zoho.com');
        $smtpPort = \App\Models\SystemSetting::get('zoho_smtp_port', 587);
        $smtpUsername = \App\Models\SystemSetting::get('zoho_smtp_username', '');
        $smtpEncryption = \App\Models\SystemSetting::get('zoho_smtp_encryption', 'tls');

        // Decrypt Zoho password - SystemSetting::get() handles decryption automatically
        $smtpPassword = \App\Models\SystemSetting::get('zoho_smtp_password', '');

        // Configure mail settings
        config([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => $smtpHost,
            'mail.mailers.smtp.port' => $smtpPort,
            'mail.mailers.smtp.username' => $smtpUsername,
            'mail.mailers.smtp.password' => $smtpPassword,
            'mail.mailers.smtp.encryption' => $smtpEncryption === 'none' ? null : $smtpEncryption,
        ]);
    }

    /**
     * Determine frontend URL based on request origin with comprehensive logging
     */
    private function determineFrontendUrl(Request $request): string
    {
        // Log all relevant request information for debugging
        $debugInfo = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'origin' => $request->header('Origin'),
            'referer' => $request->header('Referer'),
            'host' => $request->header('Host'),
            'x_forwarded_host' => $request->header('X-Forwarded-Host'),
            'x_forwarded_proto' => $request->header('X-Forwarded-Proto'),
            'user_agent' => $request->header('User-Agent'),
            'app_url' => config('app.url'),
            'frontend_url_config' => config('app.frontend_url'),
            'app_env' => config('app.env'),
        ];

        Log::info('Auth URL Detection Debug Info', $debugInfo);

        // Get the origin header from the request with multiple fallbacks
        $origin = $request->header('Origin')
               ?? $request->header('Referer')
               ?? $request->header('X-Forwarded-Host')
               ?? $request->header('Host');

        Log::info('Auth URL Detection - Origin Analysis', [
            'origin_header' => $request->header('Origin'),
            'referer_header' => $request->header('Referer'),
            'selected_origin' => $origin,
        ]);

        if ($origin) {
            // Parse the origin to get the base URL
            $parsedUrl = parse_url($origin);
            Log::info('Auth URL Detection - Parsed URL', [
                'original_origin' => $origin,
                'parsed_url' => $parsedUrl,
            ]);

            if ($parsedUrl && isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
                $frontendUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

                // Add port if it's not standard (80 for HTTP, 443 for HTTPS)
                if (isset($parsedUrl['port'])) {
                    $isStandardPort = ($parsedUrl['scheme'] === 'http' && $parsedUrl['port'] == 80) ||
                                     ($parsedUrl['scheme'] === 'https' && $parsedUrl['port'] == 443);

                    if (!$isStandardPort) {
                        $frontendUrl .= ':' . $parsedUrl['port'];
                    }
                }

                Log::info('Auth URL Detection - Origin Based URL', [
                    'detected_frontend_url' => $frontendUrl,
                    'source' => 'origin_header'
                ]);

                return $frontendUrl;
            }
        }

        // Fallback to config or environment-based detection
        $appUrl = config('app.url', 'http://localhost:4001');
        $frontendUrlConfig = config('app.frontend_url', 'http://localhost:3000');
        $requestHost = $request->header('Host');

        Log::info('Auth URL Detection - Fallback Analysis', [
            'app_url' => $appUrl,
            'frontend_url_config' => $frontendUrlConfig,
            'request_host' => $requestHost,
            'contains_production_domain' => str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space'),
            'host_is_production' => str_contains($requestHost ?? '', 'ts.crtvmkmn.space') || str_contains($requestHost ?? '', 'crtvmkmn.space'),
        ]);

        // Check if request is coming to production domain
        if ($requestHost && (str_contains($requestHost, 'ts.crtvmkmn.space') || str_contains($requestHost, 'crtvmkmn.space'))) {
            Log::info('Auth URL Detection - Production Host Detected', [
                'detected_frontend_url' => 'https://ts.crtvmkmn.space',
                'source' => 'request_host_detection'
            ]);
            return 'https://ts.crtvmkmn.space';
        }

        // If backend is on production domain, use production frontend
        if (str_contains($appUrl, 'ts.crtvmkmn.space') || str_contains($appUrl, 'crtvmkmn.space')) {
            Log::info('Auth URL Detection - Production Domain Detected', [
                'detected_frontend_url' => 'https://ts.crtvmkmn.space',
                'source' => 'production_domain_detection'
            ]);
            return 'https://ts.crtvmkmn.space';
        }

        // Check if frontend_url config is set to production
        if (str_contains($frontendUrlConfig, 'ts.crtvmkmn.space') || str_contains($frontendUrlConfig, 'crtvmkmn.space')) {
            Log::info('Auth URL Detection - Production Frontend Config', [
                'detected_frontend_url' => $frontendUrlConfig,
                'source' => 'frontend_url_config'
            ]);
            return $frontendUrlConfig;
        }

        // Default to development frontend
        Log::info('Auth URL Detection - Default Fallback', [
            'detected_frontend_url' => $frontendUrlConfig,
            'source' => 'default_fallback'
        ]);

        return $frontendUrlConfig;
    }
}
