<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class EmailSettingsController extends Controller
{
    /**
     * Get email settings
     */
    public function getEmailSettings(): JsonResponse
    {
        $settings = [
            // Zoho Email Settings - Password is masked for security
            'password' => $this->maskCredential(SystemSetting::get('zoho_smtp_password', '')),
            'smtpHost' => SystemSetting::get('zoho_smtp_host', 'smtp.zoho.com'),
            'smtpPort' => (int) SystemSetting::get('zoho_smtp_port', 587),
            'smtpUsername' => SystemSetting::get('zoho_smtp_username', ''),
            'smtpEncryption' => SystemSetting::get('zoho_smtp_encryption', 'tls'),

            // Email Settings
            'fromAddress' => SystemSetting::get('email_from_address', '<EMAIL>'),
            'fromName' => SystemSetting::get('email_from_name', 'Tarbiah Sentap CRM'),
            'replyToAddress' => SystemSetting::get('email_reply_to', '<EMAIL>'),

            // Environment and delivery settings
            'forceRealEmail' => SystemSetting::get('force_real_email_delivery', false),
            'environment' => app()->environment(),
            'isProduction' => app()->environment('production'),
        ];

        return response()->json($settings);
    }

    /**
     * Save email settings
     */
    public function saveEmailSettings(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'nullable|string',
            'smtpHost' => 'required|string|max:255',
            'smtpPort' => 'required|integer|min:1|max:65535',
            'smtpUsername' => 'nullable|string|max:255',
            'smtpEncryption' => 'required|in:ssl,tls,null',
            'fromAddress' => 'required|email',
            'fromName' => 'required|string|max:255',
            'replyToAddress' => 'required|email',
        ]);

        try {
            // Save Zoho settings
            $password = $request->password ?: '';
            SystemSetting::set('zoho_smtp_password', $password ? encrypt($password) : '', 'encrypted', 'Zoho SMTP password (encrypted)');
            SystemSetting::set('zoho_smtp_host', $request->smtpHost, 'string', 'Zoho SMTP server host');
            SystemSetting::set('zoho_smtp_port', $request->smtpPort, 'integer', 'Zoho SMTP server port');
            SystemSetting::set('zoho_smtp_username', $request->smtpUsername ?: '', 'string', 'Zoho SMTP username');
            SystemSetting::set('zoho_smtp_encryption', $request->smtpEncryption, 'string', 'Zoho SMTP encryption method');

            // Update active SMTP settings (for backward compatibility)
            SystemSetting::set('smtp_host', $request->smtpHost, 'string', 'SMTP server host');
            SystemSetting::set('smtp_port', $request->smtpPort, 'integer', 'SMTP server port');
            SystemSetting::set('smtp_username', $request->smtpUsername, 'string', 'SMTP username');
            SystemSetting::set('smtp_password', $password ? encrypt($password) : '', 'encrypted', 'SMTP password (encrypted)');
            SystemSetting::set('smtp_encryption', $request->smtpEncryption, 'string', 'SMTP encryption method');

            // Save email settings
            SystemSetting::set('email_from_address', $request->fromAddress, 'string', 'Email sender address');
            SystemSetting::set('email_from_name', $request->fromName, 'string', 'Email sender name');
            SystemSetting::set('email_reply_to', $request->replyToAddress, 'string', 'Email reply-to address');

            Log::info('Zoho email settings updated', [
                'from_address' => $request->fromAddress,
                'from_name' => $request->fromName,
                'reply_to' => $request->replyToAddress,
                'updated_by' => $request->user()->id ?? 'system'
            ]);

            return response()->json([
                'message' => 'Email settings saved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to save email settings', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()->id ?? 'system'
            ]);

            return response()->json([
                'message' => 'Failed to save email settings'
            ], 500);
        }
    }

    /**
     * Toggle real email delivery (for development testing)
     */
    public function toggleRealEmailDelivery(Request $request): JsonResponse
    {
        $request->validate([
            'enabled' => 'required|boolean',
        ]);

        try {
            SystemSetting::set('force_real_email_delivery', $request->enabled, 'boolean', 'Force real email delivery in development');

            $message = $request->enabled
                ? 'Real email delivery enabled. Emails will be sent via Zoho SMTP.'
                : 'Real email delivery disabled. Emails will be captured by Mailpit in development.';

            return response()->json([
                'message' => $message,
                'forceRealEmail' => $request->enabled
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to toggle real email delivery', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()->id ?? 'system'
            ]);

            return response()->json([
                'message' => 'Failed to update email delivery setting'
            ], 500);
        }
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration(Request $request): JsonResponse
    {
        $request->validate([
            'testEmail' => 'required|email',
        ]);

        try {
            // Get current email settings
            $fromEmail = SystemSetting::get('email_from_address', '<EMAIL>');
            $fromName = SystemSetting::get('email_from_name', 'Tarbiah Sentap CRM');
            $replyTo = SystemSetting::get('email_reply_to', '<EMAIL>');

            // Configure SMTP settings dynamically
            $this->configureMailSettings();

            $testContent = "This is a test email from KDT CRM System.\n\n";
            $testContent .= "If you received this email, your SMTP configuration is working correctly.\n\n";
            $testContent .= "Configuration Details:\n";
            $testContent .= "- SMTP Host: " . SystemSetting::get('smtp_host', 'smtp.zoho.com') . "\n";
            $testContent .= "- SMTP Port: " . SystemSetting::get('smtp_port', 465) . "\n";
            $testContent .= "- Encryption: " . SystemSetting::get('smtp_encryption', 'ssl') . "\n";
            $testContent .= "- From: {$fromName} <{$fromEmail}>\n\n";
            $testContent .= "Sent at: " . now()->format('Y-m-d H:i:s') . "\n";

            Mail::raw($testContent, function ($message) use ($request, $fromEmail, $fromName, $replyTo) {
                $message->to($request->testEmail)
                        ->from($fromEmail, $fromName)
                        ->replyTo($replyTo)
                        ->subject('Test Email - KDT CRM System Configuration');
            });

            Log::info('Test email sent successfully', [
                'to' => $request->testEmail,
                'from' => $fromEmail,
                'smtp_host' => SystemSetting::get('smtp_host'),
                'sent_by' => $request->user()->id ?? 'system'
            ]);

            return response()->json([
                'message' => 'Test email sent successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send test email', [
                'error' => $e->getMessage(),
                'to' => $request->testEmail,
                'user_id' => $request->user()->id ?? 'system'
            ]);

            return response()->json([
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Configure mail settings based on environment and user preference
     */
    private function configureMailSettings(): void
    {
        // Check if user wants to force real email delivery (for testing)
        $forceRealEmail = SystemSetting::get('force_real_email_delivery', false);

        // In development, use Mailpit unless real email is forced
        if (!$forceRealEmail && (app()->environment('local', 'development') || config('mail.mailers.smtp.host') === 'kdt-mailpit')) {
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => 'kdt-mailpit',
                'mail.mailers.smtp.port' => 1025,
                'mail.mailers.smtp.username' => null,
                'mail.mailers.smtp.password' => null,
                'mail.mailers.smtp.encryption' => null,
            ]);
            return;
        }

        // Use Zoho settings for production or when real email is forced
        $smtpHost = SystemSetting::get('zoho_smtp_host', 'smtp.zoho.com');
        $smtpPort = SystemSetting::get('zoho_smtp_port', 587);
        $smtpUsername = SystemSetting::get('zoho_smtp_username', '');
        $smtpEncryption = SystemSetting::get('zoho_smtp_encryption', 'tls');
        $smtpPassword = SystemSetting::get('zoho_smtp_password', '');

        // Configure mail settings
        config([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => $smtpHost,
            'mail.mailers.smtp.port' => $smtpPort,
            'mail.mailers.smtp.username' => $smtpUsername,
            'mail.mailers.smtp.password' => $smtpPassword,
            'mail.mailers.smtp.encryption' => $smtpEncryption === 'none' ? null : $smtpEncryption,
        ]);
    }

    /**
     * Mask sensitive credentials for display
     * Same implementation as SecuritySettingsController for consistency
     */
    private function maskCredential(string $credential): string
    {
        if (empty($credential)) {
            return '';
        }

        $length = strlen($credential);
        if ($length <= 4) {
            return str_repeat('*', $length);
        }

        return substr($credential, 0, 2) . str_repeat('*', $length - 4) . substr($credential, -2);
    }
}
