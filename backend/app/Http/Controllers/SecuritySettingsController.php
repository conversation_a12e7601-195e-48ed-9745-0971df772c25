<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SecuritySettingsController extends Controller
{
    /**
     * Get security settings (masked for security)
     */
    public function getSecuritySettings(): JsonResponse
    {
        $settings = [
            // Payment Gateway Settings
            'toyyibpay_secret_key' => $this->maskCredential(SystemSetting::get('toyyibpay_secret_key', '')),
            'toyyibpay_category_code' => SystemSetting::get('toyyibpay_category_code', ''),
            'toyyibpay_sandbox' => SystemSetting::get('toyyibpay_sandbox', true),

            // Application Security Settings
            'app_debug' => SystemSetting::get('app_debug', env('APP_DEBUG', false)),
            'log_level' => env('LOG_LEVEL', 'error'),
            'two_factor_auth_enabled' => SystemSetting::get('two_factor_auth_enabled', false),

            // Security Status Indicators
            'security_status' => $this->getSecurityStatus(),
        ];

        return response()->json([
            'settings' => $settings,
            'message' => 'Security settings retrieved successfully'
        ]);
    }

    /**
     * Update security settings with validation and encryption
     */
    public function updateSecuritySettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            // Payment Gateway Validation
            'toyyibpay_secret_key' => 'nullable|string|min:10|max:255',
            'toyyibpay_category_code' => 'nullable|string|min:5|max:50',
            'toyyibpay_sandbox' => 'boolean',

            // Security Settings
            'two_factor_auth_enabled' => 'boolean',
            'app_debug' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Update Payment Gateway Settings
            if ($request->has('toyyibpay_secret_key') && !empty($request->toyyibpay_secret_key)) {
                SystemSetting::set('toyyibpay_secret_key', encrypt($request->toyyibpay_secret_key), 'encrypted', 'ToyyibPay Secret Key');
            }
            
            if ($request->has('toyyibpay_category_code')) {
                SystemSetting::set('toyyibpay_category_code', $request->toyyibpay_category_code, 'string', 'ToyyibPay Category Code');
            }
            
            if ($request->has('toyyibpay_sandbox')) {
                SystemSetting::set('toyyibpay_sandbox', $request->toyyibpay_sandbox, 'boolean', 'ToyyibPay Sandbox Mode');
            }



            // Update Security Settings
            if ($request->has('two_factor_auth_enabled')) {
                SystemSetting::set('two_factor_auth_enabled', $request->two_factor_auth_enabled, 'boolean', '2FA System-wide Enable');
            }

            if ($request->has('app_debug')) {
                SystemSetting::set('app_debug', $request->app_debug, 'boolean', 'Application Debug Mode');
            }

            DB::commit();

            // Log security settings change
            Log::info('Security settings updated', [
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'updated_fields' => array_keys($request->only([
                    'toyyibpay_secret_key', 'toyyibpay_category_code', 'toyyibpay_sandbox',
                    'two_factor_auth_enabled', 'app_debug'
                ]))
            ]);

            return response()->json([
                'message' => 'Security settings updated successfully',
                'security_status' => $this->getSecurityStatus()
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update security settings', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'message' => 'Failed to update security settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }



    /**
     * Test ToyyibPay connection
     */
    public function testToyyibPayConnection(Request $request): JsonResponse
    {
        try {
            $secretKey = $this->getDecryptedSetting('toyyibpay_secret_key');
            $categoryCode = SystemSetting::get('toyyibpay_category_code', '');
            $sandbox = SystemSetting::get('toyyibpay_sandbox', true);

            if (empty($secretKey) || empty($categoryCode)) {
                return response()->json([
                    'message' => 'ToyyibPay credentials not configured',
                    'status' => 'not_configured'
                ], 400);
            }

            // Test API endpoint (this is a placeholder - implement actual ToyyibPay API test)
            $baseUrl = $sandbox ? 'https://dev.toyyibpay.com' : 'https://toyyibpay.com';
            
            // For now, just validate that credentials are set
            return response()->json([
                'message' => 'ToyyibPay credentials configured',
                'status' => 'configured',
                'sandbox_mode' => $sandbox
            ]);

        } catch (\Exception $e) {
            Log::error('ToyyibPay test failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'message' => 'ToyyibPay test failed: ' . $e->getMessage(),
                'status' => 'failed'
            ], 400);
        }
    }

    /**
     * Get security status indicators
     */
    private function getSecurityStatus(): array
    {
        $status = [
            'toyyibpay_configured' => !empty(SystemSetting::get('toyyibpay_secret_key', '')),
            'smtp_configured' => !empty(SystemSetting::get('zoho_smtp_password', '')) && !empty(SystemSetting::get('zoho_smtp_username', '')),
            'using_default_credentials' => $this->checkDefaultCredentials(),
            'debug_mode_enabled' => SystemSetting::get('app_debug', env('APP_DEBUG', false)),
            'ssl_enabled' => $this->checkSslEnabled(),
            'strong_passwords' => $this->checkPasswordStrength(),
        ];

        $status['overall_security_score'] = $this->calculateSecurityScore($status);
        
        return $status;
    }

    /**
     * Check if default/example credentials are being used
     */
    private function checkDefaultCredentials(): array
    {
        $defaults = [];
        
        // Check for default ToyyibPay credentials
        $toyyibpayKey = $this->getDecryptedSetting('toyyibpay_secret_key');
        if (in_array($toyyibpayKey, ['your-toyyibpay-secret-key-here', 'olmjxdaz-wx8z-1gl2-v8w8-xej3odua83j3'])) {
            $defaults[] = 'toyyibpay_secret_key';
        }



        return $defaults;
    }

    /**
     * Calculate overall security score
     */
    private function calculateSecurityScore(array $status): int
    {
        $score = 100;
        
        if (!$status['toyyibpay_configured']) $score -= 15;
        if (!$status['smtp_configured']) $score -= 15;
        if (!empty($status['using_default_credentials'])) $score -= 30;
        if ($status['debug_mode_enabled']) $score -= 20;
        if (!$status['ssl_enabled']) $score -= 10;
        if (!$status['strong_passwords']) $score -= 10;
        
        return max(0, $score);
    }

    /**
     * Mask sensitive credentials for display
     */
    private function maskCredential(string $credential): string
    {
        if (empty($credential)) {
            return '';
        }
        
        $length = strlen($credential);
        if ($length <= 4) {
            return str_repeat('*', $length);
        }
        
        return substr($credential, 0, 2) . str_repeat('*', $length - 4) . substr($credential, -2);
    }

    /**
     * Get decrypted setting value
     */
    private function getDecryptedSetting(string $key): string
    {
        $encryptedValue = SystemSetting::get($key, '');
        
        if (empty($encryptedValue)) {
            return '';
        }

        try {
            return decrypt($encryptedValue);
        } catch (\Exception $e) {
            // If decryption fails, assume it's not encrypted (backward compatibility)
            return $encryptedValue;
        }
    }



    /**
     * Check if SSL is enabled
     */
    private function checkSslEnabled(): bool
    {
        return request()->isSecure() || env('FORCE_HTTPS', false);
    }

    /**
     * Check password strength requirements
     */
    private function checkPasswordStrength(): bool
    {
        // This is a placeholder - implement actual password policy checking
        return true;
    }
}
