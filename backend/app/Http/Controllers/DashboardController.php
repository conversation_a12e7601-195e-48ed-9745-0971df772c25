<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Lead;
use App\Models\Transaction;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Get comprehensive dashboard statistics
     */
    public function getStats(): JsonResponse
    {
        $stats = [
            'overview' => $this->getOverviewStats(),
            'revenue' => $this->getRevenueStats(),
            'clients' => $this->getClientStats(),
            'leads' => $this->getLeadStats(),
            'conversion' => $this->getConversionStats(),
            'target' => $this->getTargetStats(),
        ];

        return response()->json($stats);
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        $totalClients = Client::count();
        $totalRevenue = Transaction::where('payment_status', 'paid')->sum('total_amount');
        $totalDeals = Transaction::count();
        $monthlyGrowth = $this->calculateMonthlyGrowth();

        return [
            'total_clients' => $totalClients,
            'total_revenue' => $totalRevenue,
            'total_deals' => $totalDeals,
            'monthly_growth' => $monthlyGrowth,
        ];
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(): JsonResponse
    {
        $weeklyRevenue = $this->getWeeklyRevenue();
        $monthlyRevenue = $this->getMonthlyRevenue();
        $revenueBySource = $this->getRevenueBySource();

        return response()->json([
            'weekly' => $weeklyRevenue,
            'monthly' => $monthlyRevenue,
            'by_source' => $revenueBySource,
        ]);
    }

    /**
     * Get lead sources distribution
     */
    public function getLeadSources(): JsonResponse
    {
        $sources = Client::select('utm_source')
            ->selectRaw('COUNT(*) as count')
            ->whereNotNull('utm_source')
            ->groupBy('utm_source')
            ->orderByDesc('count')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->utm_source ?: 'Direct',
                    'value' => $item->count,
                ];
            });

        // Add some default colors for the pie chart
        $colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff', '#00ffff'];
        $sources = $sources->map(function ($item, $index) use ($colors) {
            $item['color'] = $colors[$index % count($colors)];
            return $item;
        });

        return response()->json($sources);
    }

    /**
     * Get top clients
     */
    public function getTopClients(): JsonResponse
    {
        $topClients = Client::select('id', 'name', 'email', 'total_spent', 'transaction_count')
            ->orderByDesc('total_spent')
            ->limit(10)
            ->get()
            ->map(function ($client) {
                return [
                    'id' => $client->id,
                    'name' => $client->name,
                    'email' => $client->email,
                    'total_spent' => $client->total_spent,
                    'transaction_count' => $client->transaction_count,
                ];
            });

        return response()->json($topClients);
    }

    /**
     * Get deals status distribution
     */
    public function getDealsStatus(): JsonResponse
    {
        $dealsStatus = [
            'successful' => Transaction::where('payment_status', 'paid')->count(),
            'pending' => Transaction::where('payment_status', 'pending')->count(),
            'failed' => Transaction::where('payment_status', 'failed')->count(),
            'hot_leads' => Lead::where('status', 'qualified')->count(),
        ];

        $total = array_sum($dealsStatus);
        $successRate = $total > 0 ? round(($dealsStatus['successful'] / $total) * 100, 1) : 0;

        return response()->json([
            'status' => $dealsStatus,
            'success_rate' => $successRate,
            'total' => $total,
        ]);
    }

    /**
     * Get revenue statistics
     */
    private function getRevenueStats(): array
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        
        $currentMonthRevenue = Transaction::where('payment_status', 'paid')
            ->where('created_at', '>=', $currentMonth)
            ->sum('total_amount');
            
        $lastMonthRevenue = Transaction::where('payment_status', 'paid')
            ->where('created_at', '>=', $lastMonth)
            ->where('created_at', '<', $currentMonth)
            ->sum('total_amount');

        $growth = $lastMonthRevenue > 0 
            ? round((($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100, 1)
            : 0;

        return [
            'current_month' => $currentMonthRevenue,
            'last_month' => $lastMonthRevenue,
            'growth_percentage' => $growth,
        ];
    }

    /**
     * Get client statistics
     */
    private function getClientStats(): array
    {
        $totalClients = Client::count();
        $newClientsThisMonth = Client::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
        
        return [
            'total' => $totalClients,
            'new_this_month' => $newClientsThisMonth,
        ];
    }

    /**
     * Get lead statistics
     */
    private function getLeadStats(): array
    {
        $totalLeads = Lead::count();
        $hotLeads = Lead::where('status', 'qualified')->count();
        
        return [
            'total' => $totalLeads,
            'hot' => $hotLeads,
        ];
    }

    /**
     * Get conversion statistics
     */
    private function getConversionStats(): array
    {
        $totalLeads = Lead::count();
        $convertedLeads = Lead::where('status', 'closed_won')->count();
        $conversionRate = $totalLeads > 0 ? round(($convertedLeads / $totalLeads) * 100, 1) : 0;

        return [
            'total_leads' => $totalLeads,
            'converted' => $convertedLeads,
            'rate' => $conversionRate,
        ];
    }

    /**
     * Get target completion statistics
     */
    private function getTargetStats(): array
    {
        // Assuming a monthly target of RM100,000
        $monthlyTarget = 100000;
        $currentMonthRevenue = Transaction::where('payment_status', 'paid')
            ->where('created_at', '>=', Carbon::now()->startOfMonth())
            ->sum('total_amount');
            
        $completionPercentage = round(($currentMonthRevenue / $monthlyTarget) * 100, 1);

        return [
            'target' => $monthlyTarget,
            'achieved' => $currentMonthRevenue,
            'completion_percentage' => min($completionPercentage, 100),
        ];
    }

    /**
     * Calculate monthly growth
     */
    private function calculateMonthlyGrowth(): float
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        
        $currentMonthRevenue = Transaction::where('payment_status', 'paid')
            ->where('created_at', '>=', $currentMonth)
            ->sum('total_amount');
            
        $lastMonthRevenue = Transaction::where('payment_status', 'paid')
            ->where('created_at', '>=', $lastMonth)
            ->where('created_at', '<', $currentMonth)
            ->sum('total_amount');

        return $lastMonthRevenue > 0 
            ? round((($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100, 1)
            : 0;
    }

    /**
     * Get weekly revenue data
     */
    private function getWeeklyRevenue(): array
    {
        $weeks = [];
        for ($i = 6; $i >= 0; $i--) {
            $startOfWeek = Carbon::now()->subWeeks($i)->startOfWeek();
            $endOfWeek = Carbon::now()->subWeeks($i)->endOfWeek();
            
            $revenue = Transaction::where('payment_status', 'paid')
                ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
                ->sum('total_amount');
                
            $weeks[] = [
                'week' => $startOfWeek->format('M d'),
                'revenue' => $revenue,
            ];
        }
        
        return $weeks;
    }

    /**
     * Get monthly revenue data
     */
    private function getMonthlyRevenue(): array
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $startOfMonth = Carbon::now()->subMonths($i)->startOfMonth();
            $endOfMonth = Carbon::now()->subMonths($i)->endOfMonth();
            
            $revenue = Transaction::where('payment_status', 'paid')
                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->sum('total_amount');
                
            $months[] = [
                'month' => $startOfMonth->format('M'),
                'revenue' => $revenue,
            ];
        }
        
        return $months;
    }

    /**
     * Get revenue by source
     */
    private function getRevenueBySource(): array
    {
        return DB::table('transactions')
            ->join('clients', 'transactions.client_id', '=', 'clients.id')
            ->select('clients.utm_source')
            ->selectRaw('SUM(transactions.total_amount) as revenue')
            ->where('transactions.payment_status', 'paid')
            ->whereNotNull('clients.utm_source')
            ->groupBy('clients.utm_source')
            ->orderByDesc('revenue')
            ->get()
            ->map(function ($item) {
                return [
                    'source' => $item->utm_source ?: 'Direct',
                    'revenue' => $item->revenue,
                ];
            })
            ->toArray();
    }
}
