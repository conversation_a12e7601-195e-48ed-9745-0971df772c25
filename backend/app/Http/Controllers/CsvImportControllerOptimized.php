<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\Client;

class CsvImportControllerOptimized extends Controller
{
    /**
     * Optimized batch processing for large CSV imports
     * Reduces database queries from ~80k to ~300 for 26k records
     */
    private function processBatchOptimized(array $batch, array &$results): void
    {
        try {
            Log::info('Processing optimized batch', ['batch_size' => count($batch)]);
            
            // Separate records with and without emails for different processing strategies
            $recordsWithEmail = [];
            $recordsWithoutEmail = [];
            
            foreach ($batch as $record) {
                // Remove ID field if present to force new record creation
                if (isset($record['id'])) {
                    unset($record['id']);
                }
                
                // Add default values
                $record = $this->addDefaultValues($record);
                
                if (!empty($record['email'])) {
                    $recordsWithEmail[] = $record;
                } else {
                    $recordsWithoutEmail[] = $record;
                }
            }
            
            // Process records without email using bulk insert (fastest)
            if (!empty($recordsWithoutEmail)) {
                $this->bulkInsertRecords($recordsWithoutEmail, $results);
            }
            
            // Process records with email using PostgreSQL UPSERT
            if (!empty($recordsWithEmail)) {
                $this->bulkUpsertRecords($recordsWithEmail, $results);
            }
            
        } catch (\Exception $e) {
            Log::error('Optimized batch processing error: ' . $e->getMessage());
            
            // Fallback to individual processing only if bulk operations fail
            $this->fallbackIndividualProcessing($batch, $results);
        }
    }
    
    /**
     * Add default values to record
     */
    private function addDefaultValues(array $record): array
    {
        return array_merge($record, [
            'phone_validity' => $record['phone_validity'] ?? false,
            'category' => $record['category'] ?? 'First Timer',
            'status' => $record['status'] ?? 'prospect',
            'ltv_segment' => $record['ltv_segment'] ?? 'Silver',
            'engagement_level' => $record['engagement_level'] ?? 'Cold',
            'priority' => $record['priority'] ?? 'Medium',
            'data_quality' => $record['data_quality'] ?? 'Fair',
            'email_verified' => $record['email_verified'] ?? true,
            'phone_verified' => $record['phone_verified'] ?? true,
            'total_spent' => $record['total_spent'] ?? 0,
            'transaction_count' => $record['transaction_count'] ?? 0,
            'name_score' => $record['name_score'] ?? 0,
            'email_score' => $record['email_score'] ?? 0,
            'phone_score' => $record['phone_score'] ?? 0,
            'overall_score' => $record['overall_score'] ?? 0,
            'uuid' => Str::uuid(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
    
    /**
     * Bulk insert records without email (no duplicate checking needed)
     * Performance: Single query vs N queries
     */
    private function bulkInsertRecords(array $records, array &$results): void
    {
        try {
            // Single bulk insert operation - MASSIVE performance improvement
            DB::table('clients')->insert($records);
            
            $count = count($records);
            $results['created'] += $count;
            $results['processed'] += $count;
            
            Log::info('Bulk inserted records without email', ['count' => $count]);
            
        } catch (\Exception $e) {
            Log::error('Bulk insert failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Bulk upsert using PostgreSQL's native UPSERT capability
     * Performance: Single query per record vs 3 queries per record
     */
    private function bulkUpsertRecords(array $records, array &$results): void
    {
        try {
            // Prepare all records for bulk upsert
            $values = [];
            $bindings = [];
            
            foreach ($records as $record) {
                $values[] = "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $bindings = array_merge($bindings, [
                    $record['uuid'],
                    $record['name'] ?? null,
                    $record['email'],
                    $record['phone'] ?? null,
                    $record['overall_score'],
                    $record['name_score'],
                    $record['email_score'],
                    $record['phone_score'],
                    $record['email_deliverability'] ?? null,
                    $record['phone_validity'],
                    $record['phone_carrier'] ?? null,
                    $record['total_spent'],
                    $record['transaction_count'],
                    $record['data_quality'],
                    $record['utm_source'] ?? null,
                    $record['customer_category'] ?? null,
                    $record['ltv_segment'],
                    $record['engagement_level'],
                    $record['priority'],
                    $record['suggested_next_action'] ?? null,
                    $record['phone_verified'],
                    $record['email_verified'],
                    $record['status'],
                    $record['category'],
                    $record['gender'] ?? null,
                    $record['religion'] ?? null,
                    $record['income_category'] ?? null,
                    $record['income'] ?? null,
                    $record['birthday'] ?? null,
                    $record['ic_number'] ?? null,
                    $record['address_line_1'] ?? null,
                    $record['address_line_2'] ?? null,
                    $record['city'] ?? null,
                    $record['state'] ?? null,
                    $record['postcode'] ?? null,
                    $record['behaviour'] ?? null,
                    $record['interest'] ?? null,
                    $record['created_at'],
                    $record['updated_at']
                ]);
            }
            
            // Single bulk UPSERT query - MASSIVE performance improvement
            $sql = "
                INSERT INTO clients (uuid, name, email, phone, overall_score, name_score, email_score, phone_score,
                                   email_deliverability, phone_validity, phone_carrier, total_spent, transaction_count,
                                   data_quality, utm_source, customer_category, ltv_segment, engagement_level, priority,
                                   suggested_next_action, phone_verified, email_verified, status, category, gender,
                                   religion, income_category, income, birthday, ic_number, address_line_1, address_line_2,
                                   city, state, postcode, behaviour, interest, created_at, updated_at)
                VALUES " . implode(', ', $values) . "
                ON CONFLICT (email) 
                DO UPDATE SET 
                    name = EXCLUDED.name,
                    phone = EXCLUDED.phone,
                    overall_score = EXCLUDED.overall_score,
                    name_score = EXCLUDED.name_score,
                    email_score = EXCLUDED.email_score,
                    phone_score = EXCLUDED.phone_score,
                    updated_at = EXCLUDED.updated_at
            ";
            
            DB::statement($sql, $bindings);
            
            $count = count($records);
            $results['created'] += $count; // Simplified - assume all are new for performance
            $results['processed'] += $count;
            
            Log::info('Bulk upserted records with email', ['count' => $count]);
            
        } catch (\Exception $e) {
            Log::error('Bulk upsert failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Fallback to individual processing if bulk operations fail
     */
    private function fallbackIndividualProcessing(array $batch, array &$results): void
    {
        Log::warning('Using fallback individual processing');
        
        foreach ($batch as $record) {
            try {
                $record = $this->addDefaultValues($record);
                
                if (!empty($record['email'])) {
                    // Simple insert with ON CONFLICT handling
                    DB::statement("
                        INSERT INTO clients (uuid, name, email, phone, overall_score, name_score, email_score, phone_score,
                                           email_deliverability, phone_validity, phone_carrier, total_spent, transaction_count,
                                           data_quality, utm_source, customer_category, ltv_segment, engagement_level, priority,
                                           suggested_next_action, phone_verified, email_verified, status, category, gender,
                                           religion, income_category, income, birthday, ic_number, address_line_1, address_line_2,
                                           city, state, postcode, behaviour, interest, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ON CONFLICT (email) DO NOTHING
                    ", [
                        $record['uuid'], $record['name'], $record['email'], $record['phone'],
                        $record['overall_score'], $record['name_score'], $record['email_score'], $record['phone_score'],
                        $record['email_deliverability'], $record['phone_validity'], $record['phone_carrier'],
                        $record['total_spent'], $record['transaction_count'], $record['data_quality'],
                        $record['utm_source'], $record['customer_category'], $record['ltv_segment'],
                        $record['engagement_level'], $record['priority'], $record['suggested_next_action'],
                        $record['phone_verified'], $record['email_verified'], $record['status'], $record['category'],
                        $record['gender'], $record['religion'], $record['income_category'], $record['income'],
                        $record['birthday'], $record['ic_number'], $record['address_line_1'], $record['address_line_2'],
                        $record['city'], $record['state'], $record['postcode'], $record['behaviour'], $record['interest'],
                        $record['created_at'], $record['updated_at']
                    ]);
                } else {
                    DB::table('clients')->insert($record);
                }
                
                $results['created']++;
                $results['processed']++;
                
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'email' => $record['email'] ?? 'Unknown',
                    'error' => 'Fallback insert failed: ' . $e->getMessage()
                ];
            }
        }
    }
}
