<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Quotation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'quotation_number',
        'client_id',
        'lead_id',
        'deal_id',
        'created_by',
        'assigned_to',
        'title',
        'description',
        'status',
        'priority',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_rate',
        'discount_amount',
        'total_amount',
        'currency',
        'terms_conditions',
        'notes',
        'internal_notes',
        'valid_until',
        'expected_close_date',
        'validity_days',
        'sent_at',
        'viewed_at',
        'accepted_at',
        'rejected_at',
        'view_count',
        'pdf_path',
        'secure_token',
        'is_public',
        'pdf_generated_at',
        'converted_to_invoice_id',
        'converted_at',
        'converted_by',
        'custom_fields',
        'attachments',
        'template_used',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'valid_until' => 'date',
        'expected_close_date' => 'date',
        'sent_at' => 'datetime',
        'viewed_at' => 'datetime',
        'accepted_at' => 'datetime',
        'rejected_at' => 'datetime',
        'pdf_generated_at' => 'datetime',
        'converted_at' => 'datetime',
        'custom_fields' => 'array',
        'attachments' => 'array',
        'is_public' => 'boolean',
        'view_count' => 'integer',
        'validity_days' => 'integer',
    ];

    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_VIEWED = 'viewed';
    const STATUS_ACCEPTED = 'accepted';
    const STATUS_REJECTED = 'rejected';
    const STATUS_EXPIRED = 'expired';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Relationships
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function lead()
    {
        return $this->belongsTo(Lead::class);
    }

    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function items()
    {
        return $this->hasMany(QuotationItem::class)->orderBy('sort_order');
    }

    public function convertedToInvoice()
    {
        return $this->belongsTo(Invoice::class, 'converted_to_invoice_id');
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quotation) {
            if (empty($quotation->quotation_number)) {
                $quotation->quotation_number = static::generateQuotationNumber();
            }
            
            if (empty($quotation->secure_token)) {
                $quotation->secure_token = Str::random(32);
            }

            if (empty($quotation->valid_until) && $quotation->validity_days) {
                $quotation->valid_until = now()->addDays($quotation->validity_days);
            }
        });

        static::updating(function ($quotation) {
            // Update valid_until when validity_days changes
            if ($quotation->isDirty('validity_days') && $quotation->validity_days) {
                $quotation->valid_until = now()->addDays($quotation->validity_days);
            }
        });
    }

    /**
     * Generate unique quotation number
     */
    public static function generateQuotationNumber(): string
    {
        $prefix = 'QUO-' . date('Y') . '-';
        $maxAttempts = 10;
        $attempt = 0;

        while ($attempt < $maxAttempts) {
            try {
                // Use database transaction to ensure atomicity
                return \DB::transaction(function () use ($prefix) {
                    // Lock the table to prevent race conditions
                    // Include soft-deleted records to avoid number conflicts
                    // Get all quotations with the current year prefix and extract the numeric part
                    $quotations = static::withTrashed()
                        ->where('quotation_number', 'like', $prefix . '%')
                        ->lockForUpdate()
                        ->get(['quotation_number']);

                    $maxNumber = 0;
                    foreach ($quotations as $quotation) {
                        $numberPart = (int) substr($quotation->quotation_number, strlen($prefix));
                        if ($numberPart > $maxNumber) {
                            $maxNumber = $numberPart;
                        }
                    }

                    $newNumber = $maxNumber + 1;
                    $quotationNumber = $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);

                    // Double-check uniqueness (including soft-deleted records)
                    $exists = static::withTrashed()->where('quotation_number', $quotationNumber)->exists();
                    if ($exists) {
                        throw new \Exception('Quotation number already exists');
                    }

                    return $quotationNumber;
                });
            } catch (\Exception $e) {
                $attempt++;
                if ($attempt >= $maxAttempts) {
                    // Fallback to UUID-based number if all attempts fail
                    return $prefix . strtoupper(substr(uniqid(), -4));
                }
                // Wait a small random time before retrying
                usleep(rand(10000, 50000)); // 10-50ms
            }
        }

        // This should never be reached, but just in case
        return $prefix . strtoupper(substr(uniqid(), -4));
    }

    /**
     * Calculate totals based on items
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('line_total');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->save();
    }

    /**
     * Check if quotation is expired
     */
    public function isExpired(): bool
    {
        return $this->valid_until && $this->valid_until->isPast();
    }

    /**
     * Mark as viewed
     */
    public function markAsViewed(): void
    {
        $this->increment('view_count');
        
        if (!$this->viewed_at) {
            $this->update([
                'viewed_at' => now(),
                'status' => self::STATUS_VIEWED
            ]);
        }
    }

    /**
     * Accept quotation
     */
    public function accept(): void
    {
        $this->update([
            'status' => self::STATUS_ACCEPTED,
            'accepted_at' => now()
        ]);
    }

    /**
     * Reject quotation
     */
    public function reject(): void
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'rejected_at' => now()
        ]);
    }

    /**
     * Send quotation
     */
    public function send(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now()
        ]);
    }

    /**
     * Get public URL
     */
    public function getPublicUrl(): string
    {
        return url("/quotations/view/{$this->secure_token}");
    }

    /**
     * Scope for filtering
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeExpired($query)
    {
        return $query->where('valid_until', '<', now());
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', [self::STATUS_DRAFT, self::STATUS_SENT, self::STATUS_VIEWED])
                    ->where(function($q) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>=', now());
                    });
    }
}
