<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read_at',
        'priority',
        'category',
        'action_url',
        'created_by',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Notification types
    const TYPE_DEAL_ASSIGNED = 'deal_assigned';
    const TYPE_DEAL_WON = 'deal_won';
    const TYPE_DEAL_LOST = 'deal_lost';
    const TYPE_DEAL_STAGE_CHANGED = 'deal_stage_changed';
    const TYPE_QUOTATION_SENT = 'quotation_sent';
    const TYPE_QUOTATION_ACCEPTED = 'quotation_accepted';
    const TYPE_INVOICE_CREATED = 'invoice_created';
    const TYPE_PAYMENT_RECEIVED = 'payment_received';
    const TYPE_LEAD_ASSIGNED = 'lead_assigned';
    const TYPE_USER_REGISTRATION = 'user_registration';
    const TYPE_USER_APPROVED = 'user_approved';
    const TYPE_SYSTEM_ALERT = 'system_alert';

    // Priority levels
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Categories
    const CATEGORY_DEAL = 'deal';
    const CATEGORY_QUOTATION = 'quotation';
    const CATEGORY_INVOICE = 'invoice';
    const CATEGORY_LEAD = 'lead';
    const CATEGORY_USER = 'user';
    const CATEGORY_SYSTEM = 'system';
    const CATEGORY_GENERAL = 'general';

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Methods
     */
    public function markAsRead(): void
    {
        $this->update(['read_at' => now()]);
    }

    public function markAsUnread(): void
    {
        $this->update(['read_at' => null]);
    }

    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    /**
     * Static methods for creating notifications
     */
    public static function createDealAssigned(User $user, Deal $deal, ?User $assignedBy = null): self
    {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_DEAL_ASSIGNED,
            'title' => 'New Deal Assigned',
            'message' => "You have been assigned to deal: {$deal->title}",
            'data' => [
                'deal_id' => $deal->id,
                'deal_title' => $deal->title,
                'deal_value' => $deal->value,
            ],
            'priority' => self::PRIORITY_NORMAL,
            'category' => self::CATEGORY_DEAL,
            'action_url' => "/deals/{$deal->id}",
            'created_by' => $assignedBy?->id,
        ]);
    }

    public static function createDealWon(User $user, Deal $deal, ?User $updatedBy = null): self
    {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_DEAL_WON,
            'title' => 'Deal Won! 🎉',
            'message' => "Congratulations! Deal '{$deal->title}' has been won with value " . number_format($deal->value, 2),
            'data' => [
                'deal_id' => $deal->id,
                'deal_title' => $deal->title,
                'deal_value' => $deal->value,
            ],
            'priority' => self::PRIORITY_HIGH,
            'category' => self::CATEGORY_DEAL,
            'action_url' => "/deals/{$deal->id}",
            'created_by' => $updatedBy?->id,
        ]);
    }

    public static function createDealLost(User $user, Deal $deal, string $reason, ?User $updatedBy = null): self
    {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_DEAL_LOST,
            'title' => 'Deal Lost',
            'message' => "Deal '{$deal->title}' has been marked as lost. Reason: {$reason}",
            'data' => [
                'deal_id' => $deal->id,
                'deal_title' => $deal->title,
                'deal_value' => $deal->value,
                'loss_reason' => $reason,
            ],
            'priority' => self::PRIORITY_NORMAL,
            'category' => self::CATEGORY_DEAL,
            'action_url' => "/deals/{$deal->id}",
            'created_by' => $updatedBy?->id,
        ]);
    }

    public static function createQuotationSent(User $user, Quotation $quotation, ?User $sentBy = null): self
    {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_QUOTATION_SENT,
            'title' => 'Quotation Sent',
            'message' => "Quotation #{$quotation->quotation_number} has been sent to client",
            'data' => [
                'quotation_id' => $quotation->id,
                'quotation_number' => $quotation->quotation_number,
                'total_amount' => $quotation->total_amount,
            ],
            'priority' => self::PRIORITY_NORMAL,
            'category' => self::CATEGORY_QUOTATION,
            'action_url' => "/quotations/{$quotation->id}",
            'created_by' => $sentBy?->id,
        ]);
    }

    public static function createUserRegistration(User $admin, User $newUser): self
    {
        return self::create([
            'user_id' => $admin->id,
            'type' => self::TYPE_USER_REGISTRATION,
            'title' => 'New User Registration',
            'message' => "New user '{$newUser->name}' ({$newUser->email}) has registered and is awaiting approval",
            'data' => [
                'user_id' => $newUser->id,
                'user_name' => $newUser->name,
                'user_email' => $newUser->email,
                'user_department' => $newUser->department,
            ],
            'priority' => self::PRIORITY_NORMAL,
            'category' => self::CATEGORY_USER,
            'action_url' => "/settings?tab=users",
            'created_by' => $newUser->id,
        ]);
    }

    public static function createUserApproval(User $user, User $approvedBy): self
    {
        return self::create([
            'user_id' => $user->id,
            'type' => self::TYPE_USER_APPROVED,
            'title' => 'Account Approved! 🎉',
            'message' => "Your account has been approved by {$approvedBy->name}. You can now access the system.",
            'data' => [
                'approved_by_id' => $approvedBy->id,
                'approved_by_name' => $approvedBy->name,
            ],
            'priority' => self::PRIORITY_HIGH,
            'category' => self::CATEGORY_USER,
            'action_url' => "/dashboard",
            'created_by' => $approvedBy->id,
        ]);
    }
}
