<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClientEmail extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'email_address',
        'is_primary',
        'email_verified',
        'email_score',
        'email_deliverability',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'email_verified' => 'boolean',
        'email_score' => 'integer',
    ];

    /**
     * Get the client that owns this email address
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Scope to get primary emails only
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope to get verified emails only
     */
    public function scopeVerified($query)
    {
        return $query->where('email_verified', true);
    }
}
