<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Client extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'uuid',
        'name',
        'email',
        'phone',
        'company',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'status',
        'notes',
        'utm_source',
        'tags',
        'category',
        'ltv_segment',
        'engagement_level',
        'priority',
        'suggested_action',
        'last_activity',
        'total_spent',
        'transaction_count',
        'custom_fields',
        'email_verified',
        'phone_verified',
        // New scoring fields
        'name_score',
        'email_score',
        'phone_score',
        'overall_score',
        // New validation fields
        'email_deliverability',
        'phone_validity',
        'phone_carrier',
        // New categorization fields
        'data_quality',
        'customer_category',
        'notes_remarks',
        'suggested_next_action',
        // Personal information fields
        'ic_number',
        'birthday',
        'gender',
        'religion',
        // Financial information
        'income',
        'income_category',
        // Enhanced address fields
        'address_line_1',
        'address_line_2',
        'postcode',
        'district',
        // Behavioral data
        'behaviour',
        'interest',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'last_activity' => 'datetime',
        'birthday' => 'date',
        'tags' => 'array',
        'custom_fields' => 'array',
        'total_spent' => 'decimal:2',
        'income' => 'decimal:2',
        'transaction_count' => 'integer',
        'email_verified' => 'boolean',
        'phone_verified' => 'boolean',
        // New field casts
        'name_score' => 'integer',
        'email_score' => 'integer',
        'phone_score' => 'integer',
        'overall_score' => 'integer',
        'phone_validity' => 'boolean',
    ];

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function leads()
    {
        return $this->hasMany(Lead::class);
    }

    public function deals()
    {
        return $this->hasMany(Deal::class);
    }

    public function phoneNumbers()
    {
        return $this->hasMany(ClientPhoneNumber::class);
    }

    public function primaryPhoneNumber()
    {
        return $this->hasOne(ClientPhoneNumber::class)->where('is_primary', true);
    }

    public function emails()
    {
        return $this->hasMany(ClientEmail::class);
    }

    public function primaryEmail()
    {
        return $this->hasOne(ClientEmail::class)->where('is_primary', true);
    }

    /**
     * Update computed fields based on transactions
     */
    public function updateComputedFields(): void
    {
        $this->total_spent = $this->transactions()->sum('amount');
        $this->transaction_count = $this->transactions()->count();

        // Update LTV segment based on total spent
        if ($this->total_spent >= 2000) {
            $this->ltv_segment = 'Platinum';
        } elseif ($this->total_spent >= 1000) {
            $this->ltv_segment = 'Gold+';
        } elseif ($this->total_spent >= 600) {
            $this->ltv_segment = 'Gold';
        } else {
            $this->ltv_segment = 'Silver';
        }

        // Update category based on transaction count and status
        if ($this->status === 'active' && $this->transaction_count > 5) {
            $this->category = 'Loyal';
        } elseif ($this->status === 'active' && $this->transaction_count > 1) {
            $this->category = 'Retainer';
        } elseif ($this->transaction_count > 0) {
            $this->category = 'Retainer';
        } else {
            $this->category = 'First Timer';
        }

        $this->save();
    }

    /**
     * Scope for filtering by engagement level
     */
    public function scopeByEngagement($query, $level)
    {
        return $query->where('engagement_level', $level);
    }

    /**
     * Scope for filtering by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for filtering by LTV segment
     */
    public function scopeByLtvSegment($query, $segment)
    {
        return $query->where('ltv_segment', $segment);
    }
}
