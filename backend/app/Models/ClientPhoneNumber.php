<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClientPhoneNumber extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'phone_number',
        'is_primary',
        'phone_verified',
        'phone_score',
        'phone_carrier',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'phone_verified' => 'boolean',
        'phone_score' => 'integer',
    ];

    /**
     * Get the client that owns this phone number
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
