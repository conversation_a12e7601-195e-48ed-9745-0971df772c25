<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'role',
        'department',
        'is_active',
        'last_login_at',
        'two_factor_enabled',
        'two_factor_code',
        'two_factor_expires_at',
        'two_factor_verified_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_code',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'is_active' => 'boolean',
        'two_factor_enabled' => 'boolean',
        'two_factor_expires_at' => 'datetime',
        'two_factor_verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'password' => 'hashed',
    ];

    // Role constants
    const ROLE_ADMIN = 'admin';
    const ROLE_STAFF = 'staff';
    const ROLE_MANAGER = 'manager';
    const ROLE_USER = 'user';

    /**
     * Relationships
     */
    public function assignedLeads()
    {
        return $this->hasMany(Lead::class, 'assigned_to');
    }

    public function assignedDeals()
    {
        return $this->hasMany(Deal::class, 'assigned_to');
    }

    public function createdQuotations()
    {
        return $this->hasMany(Quotation::class, 'created_by');
    }

    public function assignedQuotations()
    {
        return $this->hasMany(Quotation::class, 'assigned_to');
    }

    public function createdDeals()
    {
        return $this->hasMany(Deal::class, 'created_by');
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * Check if user is staff
     */
    public function isStaff(): bool
    {
        return $this->role === self::ROLE_STAFF;
    }

    /**
     * Check if user is manager
     */
    public function isManager(): bool
    {
        return $this->role === self::ROLE_MANAGER;
    }

    /**
     * Check if user can manage deals
     */
    public function canManageDeals(): bool
    {
        return in_array($this->role, [self::ROLE_ADMIN, self::ROLE_MANAGER]);
    }

    /**
     * Check if user can delete individual data (staff and above)
     */
    public function canDeleteIndividualData(): bool
    {
        return in_array($this->role, [self::ROLE_ADMIN, self::ROLE_STAFF]);
    }

    /**
     * Check if user can perform bulk data operations (admin only)
     */
    public function canPerformBulkOperations(): bool
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * Check if user can manage users (admin and manager)
     */
    public function canManageUsers(): bool
    {
        return in_array($this->role, [self::ROLE_ADMIN, self::ROLE_MANAGER]);
    }

    /**
     * Generate and send 2FA code
     */
    public function generateTwoFactorCode(): string
    {
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        $this->update([
            'two_factor_code' => $code,
            'two_factor_expires_at' => now()->addMinutes(10), // Code expires in 10 minutes
            'two_factor_verified_at' => null,
        ]);

        return $code;
    }

    /**
     * Verify 2FA code
     */
    public function verifyTwoFactorCode(string $code): bool
    {
        if (!$this->two_factor_code || !$this->two_factor_expires_at) {
            return false;
        }

        if ($this->two_factor_expires_at->isPast()) {
            return false;
        }

        if ($this->two_factor_code !== $code) {
            return false;
        }

        $this->update([
            'two_factor_verified_at' => now(),
            'two_factor_code' => null,
            'two_factor_expires_at' => null,
        ]);

        return true;
    }

    /**
     * Check if 2FA is required for this user
     */
    public function requiresTwoFactor(): bool
    {
        // If system-wide 2FA is enabled, it's required for all users
        $systemWideEnabled = SystemSetting::get('two_factor_auth_enabled', false);

        if ($systemWideEnabled) {
            return true;
        }

        // If system-wide is disabled, check individual user preference
        return $this->two_factor_enabled;
    }

    /**
     * Check if 2FA has been verified for current session
     */
    public function hasTwoFactorVerified(): bool
    {
        return $this->two_factor_verified_at &&
               $this->two_factor_verified_at->isAfter(now()->subHours(24)); // Valid for 24 hours
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for users by role
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // When a user is soft-deleted, mark them as inactive
        static::deleting(function ($user) {
            if (!$user->isForceDeleting()) {
                $user->is_active = false;
                $user->saveQuietly(); // Save without triggering events
            }
        });
    }
}
