<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class PasswordResetToken extends Model
{
    protected $primaryKey = 'email';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'email',
        'token',
        'created_at',
        'expires_at',
        'used',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'expires_at' => 'datetime',
        'used' => 'boolean',
    ];

    /**
     * Generate a new password reset token
     */
    public static function createToken(string $email, string $ipAddress = null, string $userAgent = null): string
    {
        // Delete any existing tokens for this email
        static::where('email', $email)->delete();

        // Generate a secure token
        $token = Str::random(64);
        $hashedToken = Hash::make($token);

        // Create new token record
        static::create([
            'email' => $email,
            'token' => $hashedToken,
            'created_at' => now(),
            'expires_at' => now()->addHour(), // Token expires in 1 hour
            'used' => false,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);

        return $token;
    }

    /**
     * Verify a password reset token
     */
    public static function verifyToken(string $email, string $token): ?self
    {
        $resetToken = static::where('email', $email)
            ->where('used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (!$resetToken) {
            return null;
        }

        if (!Hash::check($token, $resetToken->token)) {
            return null;
        }

        return $resetToken;
    }

    /**
     * Mark token as used
     */
    public function markAsUsed(): void
    {
        $this->update(['used' => true]);
    }

    /**
     * Check if token is valid
     */
    public function isValid(): bool
    {
        return !$this->used && $this->expires_at->isFuture();
    }

    /**
     * Clean up expired tokens
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', now())->delete();
    }

    /**
     * Get the user associated with this token
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }

    /**
     * Check rate limiting for password reset requests
     */
    public static function isRateLimited(string $email, int $maxAttempts = 3, int $timeWindowMinutes = 60): bool
    {
        $recentAttempts = static::where('email', $email)
            ->where('created_at', '>', now()->subMinutes($timeWindowMinutes))
            ->count();

        return $recentAttempts >= $maxAttempts;
    }
}
