<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Deal extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'lead_id',
        'client_id',
        'assigned_to',
        'created_by',
        'title',
        'description',
        'deal_number',
        'value',
        'expected_revenue',
        'actual_revenue',
        'currency',
        'pipeline_stage',
        'stage_order',
        'stage_changed_at',
        'stage_changed_by',
        'probability',
        'deal_size',
        'expected_close_date',
        'actual_close_date',
        'days_in_pipeline',
        'days_in_current_stage',
        'source',
        'utm_source',
        'utm_campaign',
        'utm_medium',
        'priority',
        'deal_type',
        'tags',
        'competitors',
        'competitive_advantage',
        'win_probability_reason',
        'notes',
        'internal_notes',
        'last_activity',
        'next_follow_up',
        'next_action',
        'automation_triggers',
        'auto_follow_up_enabled',
        'follow_up_frequency_days',
        'documents',
        'proposal_documents',
        'contract_documents',
        'loss_reason',
        'loss_details',
        'competitor_won',
        'external_id',
        'custom_fields',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'expected_revenue' => 'decimal:2',
        'actual_revenue' => 'decimal:2',
        'probability' => 'integer',
        'stage_order' => 'integer',
        'days_in_pipeline' => 'integer',
        'days_in_current_stage' => 'integer',
        'follow_up_frequency_days' => 'integer',
        'auto_follow_up_enabled' => 'boolean',
        'expected_close_date' => 'datetime',
        'actual_close_date' => 'datetime',
        'stage_changed_at' => 'datetime',
        'last_activity' => 'datetime',
        'next_follow_up' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'tags' => 'array',
        'competitors' => 'array',
        'automation_triggers' => 'array',
        'documents' => 'array',
        'proposal_documents' => 'array',
        'contract_documents' => 'array',
        'custom_fields' => 'array',
    ];

    // Pipeline stage constants
    const STAGE_PROSPECTING = 'prospecting';
    const STAGE_QUALIFICATION = 'qualification';
    const STAGE_PROPOSAL = 'proposal';
    const STAGE_NEGOTIATION = 'negotiation';
    const STAGE_CLOSING = 'closing';
    const STAGE_WON = 'won';
    const STAGE_LOST = 'lost';
    const STAGE_ON_HOLD = 'on_hold';

    // Deal size constants
    const SIZE_SMALL = 'Small';
    const SIZE_MEDIUM = 'Medium';
    const SIZE_LARGE = 'Large';
    const SIZE_ENTERPRISE = 'Enterprise';

    // Priority constants
    const PRIORITY_LOW = 'Low';
    const PRIORITY_MEDIUM = 'Medium';
    const PRIORITY_HIGH = 'High';
    const PRIORITY_CRITICAL = 'Critical';

    // Deal type constants
    const TYPE_NEW_BUSINESS = 'New Business';
    const TYPE_UPSELL = 'Upsell';
    const TYPE_CROSS_SELL = 'Cross-sell';
    const TYPE_RENEWAL = 'Renewal';
    const TYPE_EXPANSION = 'Expansion';

    /**
     * Relationships
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($deal) {
            // Generate deal number if not provided
            if (empty($deal->deal_number)) {
                $deal->deal_number = static::generateDealNumber();
            }

            // Set initial stage timestamp
            $deal->stage_changed_at = now();
            
            // Calculate initial days in pipeline
            $deal->days_in_pipeline = 0;
            $deal->days_in_current_stage = 0;
        });

        static::updating(function ($deal) {
            // Check if pipeline stage changed
            if ($deal->isDirty('pipeline_stage')) {
                $deal->stage_changed_at = now();
                $deal->stage_changed_by = auth()->user()?->name ?? 'System';
                $deal->days_in_current_stage = 0;
                
                // Update probability based on stage
                $deal->probability = static::getDefaultProbabilityForStage($deal->pipeline_stage);
            }

            // Update days in pipeline and current stage
            $deal->updateDaysInPipeline();
        });
    }

    /**
     * Generate unique deal number
     */
    public static function generateDealNumber(): string
    {
        $prefix = 'DEAL';
        $year = date('Y');
        $month = date('m');
        
        // Get the last deal number for this month
        $lastDeal = static::withTrashed()
            ->where('deal_number', 'like', "{$prefix}-{$year}{$month}-%")
            ->orderBy('deal_number', 'desc')
            ->first();

        if ($lastDeal) {
            $lastNumber = (int) substr($lastDeal->deal_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s-%04d', $prefix, $year, $month, $newNumber);
    }

    /**
     * Get default probability for a pipeline stage
     */
    public static function getDefaultProbabilityForStage(string $stage): int
    {
        return match ($stage) {
            self::STAGE_PROSPECTING => 10,
            self::STAGE_QUALIFICATION => 25,
            self::STAGE_PROPOSAL => 50,
            self::STAGE_NEGOTIATION => 75,
            self::STAGE_CLOSING => 90,
            self::STAGE_WON => 100,
            self::STAGE_LOST => 0,
            self::STAGE_ON_HOLD => 25,
            default => 10,
        };
    }

    /**
     * Update days in pipeline and current stage
     */
    public function updateDaysInPipeline(): void
    {
        $this->days_in_pipeline = $this->created_at->diffInDays(now());
        
        if ($this->stage_changed_at) {
            $this->days_in_current_stage = Carbon::parse($this->stage_changed_at)->diffInDays(now());
        }
    }

    /**
     * Check if deal is active (not won, lost, or on hold)
     */
    public function isActive(): bool
    {
        return !in_array($this->pipeline_stage, [self::STAGE_WON, self::STAGE_LOST, self::STAGE_ON_HOLD]);
    }

    /**
     * Check if deal is closed (won or lost)
     */
    public function isClosed(): bool
    {
        return in_array($this->pipeline_stage, [self::STAGE_WON, self::STAGE_LOST]);
    }

    /**
     * Check if deal is won
     */
    public function isWon(): bool
    {
        return $this->pipeline_stage === self::STAGE_WON;
    }

    /**
     * Check if deal is overdue
     */
    public function isOverdue(): bool
    {
        return $this->expected_close_date && 
               $this->expected_close_date->isPast() && 
               !$this->isClosed();
    }

    /**
     * Get pipeline stages with their display names and colors
     */
    public static function getPipelineStages(): array
    {
        return [
            self::STAGE_PROSPECTING => [
                'name' => 'Prospecting',
                'color' => '#6B7280',
                'description' => 'Initial contact and qualification'
            ],
            self::STAGE_QUALIFICATION => [
                'name' => 'Qualification',
                'color' => '#3B82F6',
                'description' => 'Needs assessment and budget confirmation'
            ],
            self::STAGE_PROPOSAL => [
                'name' => 'Proposal',
                'color' => '#F59E0B',
                'description' => 'Proposal sent, awaiting response'
            ],
            self::STAGE_NEGOTIATION => [
                'name' => 'Negotiation',
                'color' => '#EF4444',
                'description' => 'Terms and pricing discussion'
            ],
            self::STAGE_CLOSING => [
                'name' => 'Closing',
                'color' => '#8B5CF6',
                'description' => 'Final approval and contract signing'
            ],
            self::STAGE_WON => [
                'name' => 'Won',
                'color' => '#10B981',
                'description' => 'Deal successfully closed'
            ],
            self::STAGE_LOST => [
                'name' => 'Lost',
                'color' => '#6B7280',
                'description' => 'Deal lost or cancelled'
            ],
            self::STAGE_ON_HOLD => [
                'name' => 'On Hold',
                'color' => '#F97316',
                'description' => 'Deal temporarily paused'
            ],
        ];
    }

    /**
     * Get stage display information
     */
    public function getStageInfo(): array
    {
        $stages = static::getPipelineStages();
        return $stages[$this->pipeline_stage] ?? $stages[self::STAGE_PROSPECTING];
    }

    /**
     * Scope for active deals
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('pipeline_stage', [self::STAGE_WON, self::STAGE_LOST, self::STAGE_ON_HOLD]);
    }

    /**
     * Scope for deals by stage
     */
    public function scopeByStage($query, string $stage)
    {
        return $query->where('pipeline_stage', $stage);
    }

    /**
     * Scope for overdue deals
     */
    public function scopeOverdue($query)
    {
        return $query->where('expected_close_date', '<', now())
                    ->whereNotIn('pipeline_stage', [self::STAGE_WON, self::STAGE_LOST]);
    }

    /**
     * Scope for deals assigned to user
     */
    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }
}
