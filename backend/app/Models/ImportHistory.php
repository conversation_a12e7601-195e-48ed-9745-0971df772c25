<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ImportHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'file_name',
        'file_size',
        'data_type',
        'file_format',
        'status',
        'total_rows',
        'processed',
        'created',
        'updated',
        'skipped',
        'errors_count',
        'duplicates_count',
        'processing_time',
        'success_rate',
        'error_details',
        'import_options',
        'field_mapping',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'total_rows' => 'integer',
        'processed' => 'integer',
        'created' => 'integer',
        'updated' => 'integer',
        'skipped' => 'integer',
        'errors_count' => 'integer',
        'duplicates_count' => 'integer',
        'processing_time' => 'float',
        'success_rate' => 'float',
        'error_details' => 'array',
        'import_options' => 'array',
        'field_mapping' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByDataType($query, string $dataType)
    {
        return $query->where('data_type', $dataType);
    }

    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getFormattedProcessingTimeAttribute(): string
    {
        $seconds = $this->processing_time;
        
        if ($seconds < 60) {
            return round($seconds, 2) . 's';
        } elseif ($seconds < 3600) {
            return round($seconds / 60, 1) . 'm';
        } else {
            return round($seconds / 3600, 1) . 'h';
        }
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_COMPLETED => 'green',
            self::STATUS_PROCESSING => 'blue',
            self::STATUS_PENDING => 'yellow',
            self::STATUS_FAILED => 'red',
            self::STATUS_CANCELLED => 'gray',
            default => 'gray'
        };
    }

    // Static methods
    public static function createFromImportResult(array $data): self
    {
        return self::create([
            'user_id' => auth()->id(),
            'file_name' => $data['file_name'] ?? 'Unknown',
            'file_size' => $data['file_size'] ?? 0,
            'data_type' => $data['data_type'] ?? 'unknown',
            'file_format' => $data['file_format'] ?? 'csv',
            'status' => self::STATUS_COMPLETED,
            'total_rows' => $data['total_rows'] ?? 0,
            'processed' => $data['processed'] ?? 0,
            'created' => $data['created'] ?? 0,
            'updated' => $data['updated'] ?? 0,
            'skipped' => $data['skipped'] ?? 0,
            'errors_count' => $data['errors_count'] ?? 0,
            'duplicates_count' => $data['duplicates_count'] ?? 0,
            'processing_time' => $data['processing_time'] ?? 0,
            'success_rate' => $data['success_rate'] ?? 0,
            'error_details' => $data['error_details'] ?? [],
            'import_options' => $data['import_options'] ?? [],
            'field_mapping' => $data['field_mapping'] ?? [],
            'started_at' => $data['started_at'] ?? now(),
            'completed_at' => $data['completed_at'] ?? now(),
        ]);
    }
}
