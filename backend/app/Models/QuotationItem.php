<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuotationItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'quotation_id',
        'product_id',
        'item_name',
        'description',
        'sku',
        'unit',
        'quantity',
        'unit_price',
        'discount_rate',
        'discount_amount',
        'line_total',
        'sort_order',
        'custom_fields',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'line_total' => 'decimal:2',
        'sort_order' => 'integer',
        'custom_fields' => 'array',
    ];

    /**
     * Relationships
     */
    public function quotation()
    {
        return $this->belongsTo(Quotation::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            $item->calculateLineTotal();
        });

        static::updating(function ($item) {
            if ($item->isDirty(['quantity', 'unit_price', 'discount_rate', 'discount_amount'])) {
                $item->calculateLineTotal();
            }
        });

        static::saved(function ($item) {
            // Recalculate quotation totals when item is saved
            $item->quotation->calculateTotals();
        });

        static::deleted(function ($item) {
            // Recalculate quotation totals when item is deleted
            $item->quotation->calculateTotals();
        });
    }

    /**
     * Calculate line total
     */
    public function calculateLineTotal(): void
    {
        $subtotal = $this->quantity * $this->unit_price;
        $discount = $this->discount_rate > 0 
            ? $subtotal * ($this->discount_rate / 100)
            : $this->discount_amount;
        
        $this->line_total = $subtotal - $discount;
    }

    /**
     * Create from product
     */
    public static function createFromProduct(Product $product, $quantity = 1): array
    {
        return [
            'product_id' => $product->id,
            'item_name' => $product->name,
            'description' => $product->description,
            'sku' => $product->sku,
            'unit' => 'pcs',
            'quantity' => $quantity,
            'unit_price' => $product->price,
            'discount_rate' => 0,
            'discount_amount' => 0,
        ];
    }
}
