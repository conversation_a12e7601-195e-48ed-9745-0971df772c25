<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lead extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'client_id',

        // Lead capture fields
        'name',
        'email',
        'phone',
        'company',

        // UTM and campaign tracking
        'utm_source',
        'utm_campaign',
        'utm_medium',
        'utm_content',
        'utm_term',
        'channel',

        // Lead classification
        'lead_type',
        'tags',

        // Sales opportunity fields
        'title',
        'description',
        'status',
        'opportunity_status',
        'priority',
        'engagement_level',

        // Sales tracking
        'source',
        'assigned_to',
        'estimated_value',
        'probability',
        'expected_close_date',

        // Activity and lifecycle tracking
        'notes',
        'internal_remarks',
        'suggested_action',
        'last_activity',
        'last_contacted',
        'lifecycle_stage',

        // Document uploads
        'documents',

        // Recycling and conversion tracking
        'is_recycled',
        'recycled_at',
        'converted_at',
        'converted_to_client_id',

        // Deal conversion tracking
        'converted_to_deal',
        'converted_to_deal_id',
        'deal_converted_at',
        'deal_readiness_score',
        'qualification_criteria',
        'budget_status',
        'decision_maker_access',
        'timeline_urgency',
    ];

    protected $casts = [
        'estimated_value' => 'decimal:2',
        'probability' => 'integer',
        'lifecycle_stage' => 'integer',
        'expected_close_date' => 'datetime',
        'last_activity' => 'datetime',
        'last_contacted' => 'datetime',
        'recycled_at' => 'datetime',
        'converted_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'tags' => 'array',
        'documents' => 'array',
        'is_recycled' => 'boolean',
        'converted_to_deal' => 'boolean',
        'deal_converted_at' => 'datetime',
        'deal_readiness_score' => 'integer',
        'qualification_criteria' => 'array',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function convertedToClient()
    {
        return $this->belongsTo(Client::class, 'converted_to_client_id');
    }

    public function convertedToDeal()
    {
        return $this->belongsTo(Deal::class, 'converted_to_deal_id');
    }

    public function deals()
    {
        return $this->hasMany(Deal::class);
    }

    /**
     * Convert lead capture to sales opportunity
     */
    public function convertToOpportunity(array $opportunityData = []): self
    {
        $this->update(array_merge([
            'lead_type' => 'opportunity',
            'status' => 'contacted',
            'opportunity_status' => 'contacted',
            'converted_at' => now(),
        ], $opportunityData));

        return $this;
    }

    /**
     * Convert lead to client
     */
    public function convertToClient(array $clientData = []): Client
    {
        // Convert engagement level from lead format (lowercase) to client format (capitalized)
        $engagementLevelMap = [
            'hot' => 'Hot',
            'warm' => 'Warm',
            'cold' => 'Cold',
            'frozen' => 'Frozen'
        ];

        // Convert priority from lead format (lowercase) to client format (capitalized)
        $priorityMap = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High'
        ];

        $client = Client::create(array_merge([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'company' => $this->company,
            'utm_source' => $this->utm_source,
            'tags' => $this->tags ?? [],
            'category' => 'First Timer',
            'ltv_segment' => 'Silver',
            'engagement_level' => $engagementLevelMap[$this->engagement_level] ?? 'Cold',
            'priority' => $priorityMap[$this->priority] ?? 'Medium',
            'notes' => $this->notes,
            'last_activity' => now(),
        ], $clientData));

        $this->update([
            'status' => 'converted',
            'converted_at' => now(),
            'converted_to_client_id' => $client->id,
            'client_id' => $client->id,
        ]);

        return $client;
    }

    /**
     * Convert lead to deal
     */
    public function convertToDeal(array $dealData = []): Deal
    {
        $deal = Deal::create(array_merge([
            'lead_id' => $this->id,
            'client_id' => $this->client_id,
            'title' => $this->title ?? "Deal from Lead: {$this->name}",
            'description' => $this->description,
            'value' => $this->estimated_value ?? 0,
            'expected_revenue' => $this->estimated_value,
            'probability' => $this->probability ?? 10,
            'expected_close_date' => $this->expected_close_date,
            'source' => $this->source,
            'utm_source' => $this->utm_source,
            'utm_campaign' => $this->utm_campaign,
            'utm_medium' => $this->utm_medium,
            'priority' => $this->priority ?? 'Medium',
            'deal_type' => 'New Business',
            'tags' => $this->tags ?? [],
            'notes' => $this->notes,
            'assigned_to' => auth()->id() ?? 1, // Default to user 1 if no auth
            'created_by' => auth()->id() ?? 1, // Default to user 1 if no auth
            'pipeline_stage' => 'prospecting',
            'currency' => 'MYR',
        ], $dealData));

        $this->update([
            'converted_to_deal' => true,
            'deal_converted_at' => now(),
            'converted_to_deal_id' => $deal->id,
            'status' => 'converted',
            'last_activity' => now(),
        ]);

        return $deal;
    }

    /**
     * Recycle disqualified lead
     */
    public function recycle(): self
    {
        $this->update([
            'is_recycled' => true,
            'recycled_at' => now(),
            'status' => 'new',
            'engagement_level' => 'cold',
            'lifecycle_stage' => 0,
        ]);

        return $this;
    }

    /**
     * Update lifecycle progress
     */
    public function updateLifecycleStage(int $stage): self
    {
        $this->update([
            'lifecycle_stage' => max(0, min(100, $stage)),
            'last_activity' => now(),
        ]);

        return $this;
    }

    /**
     * Calculate deal readiness score based on qualification criteria
     */
    public function calculateDealReadinessScore(): int
    {
        $score = 0;

        // Budget status scoring (25 points)
        $score += match ($this->budget_status) {
            'Budget Approved' => 25,
            'Budget Identified' => 15,
            'No Budget' => 0,
            default => 5,
        };

        // Decision maker access scoring (25 points)
        $score += match ($this->decision_maker_access) {
            'Direct Access' => 25,
            'Indirect Access' => 15,
            'No Access' => 0,
            default => 5,
        };

        // Timeline urgency scoring (20 points)
        $score += match ($this->timeline_urgency) {
            'Urgent' => 20,
            'Moderate' => 12,
            'No Urgency' => 5,
            default => 2,
        };

        // Engagement level scoring (15 points)
        $score += match ($this->engagement_level) {
            'hot' => 15,
            'warm' => 10,
            'cold' => 5,
            'frozen' => 0,
            default => 5,
        };

        // Lifecycle stage scoring (15 points)
        $score += (int) ($this->lifecycle_stage * 0.15);

        $this->update(['deal_readiness_score' => $score]);

        return $score;
    }

    /**
     * Check if lead is ready for deal conversion
     */
    public function isReadyForDeal(): bool
    {
        return $this->deal_readiness_score >= 60 ||
               ($this->budget_status === 'Budget Approved' &&
                $this->decision_maker_access === 'Direct Access');
    }

    /**
     * Scope for lead captures (potential customers)
     */
    public function scopeCaptures($query)
    {
        return $query->where('lead_type', 'capture');
    }

    /**
     * Scope for sales opportunities
     */
    public function scopeOpportunities($query)
    {
        return $query->where('lead_type', 'opportunity');
    }

    /**
     * Scope for hot leads
     */
    public function scopeHot($query)
    {
        return $query->where('engagement_level', 'hot');
    }

    /**
     * Scope for recyclable leads
     */
    public function scopeRecyclable($query)
    {
        return $query->where('status', 'disqualified')
                    ->where('is_recycled', false);
    }

    /**
     * Scope for deal-ready leads
     */
    public function scopeDealReady($query)
    {
        return $query->where('deal_readiness_score', '>=', 60)
                    ->where('converted_to_deal', false);
    }

    /**
     * Scope for converted leads
     */
    public function scopeConvertedToDeals($query)
    {
        return $query->where('converted_to_deal', true);
    }
}
