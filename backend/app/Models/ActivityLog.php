<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'action',
        'description',
        'type',
        'performed_by',
        'user_id',
        'subject_type',
        'subject_id',
        'subject_name',
        'related_type',
        'related_id',
        'related_name',
        'old_values',
        'new_values',
        'metadata',
        'workflow_stage',
        'is_conversion',
        'bypassed_workflow',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
        'is_conversion' => 'boolean',
        'bypassed_workflow' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function subject()
    {
        return $this->morphTo();
    }

    public function related()
    {
        return $this->morphTo();
    }

    // Static methods for logging different types of activities
    public static function logConversion(string $action, string $description, $subject, $related = null, array $metadata = [], bool $bypassedWorkflow = false)
    {
        return self::create([
            'action' => $action,
            'description' => $description,
            'type' => 'conversion',
            'performed_by' => auth()->user()?->name ?? 'System',
            'user_id' => auth()->id(),
            'subject_type' => get_class($subject),
            'subject_id' => $subject->id,
            'subject_name' => $subject->name ?? $subject->title ?? "#{$subject->id}",
            'related_type' => $related ? get_class($related) : null,
            'related_id' => $related?->id,
            'related_name' => $related ? ($related->name ?? $related->title ?? "#{$related->id}") : null,
            'metadata' => $metadata,
            'workflow_stage' => self::determineWorkflowStage($subject),
            'is_conversion' => true,
            'bypassed_workflow' => $bypassedWorkflow,
        ]);
    }

    public static function logStageChange(string $action, string $description, $subject, array $oldValues, array $newValues, array $metadata = [])
    {
        return self::create([
            'action' => $action,
            'description' => $description,
            'type' => 'stage_change',
            'performed_by' => auth()->user()?->name ?? 'System',
            'user_id' => auth()->id(),
            'subject_type' => get_class($subject),
            'subject_id' => $subject->id,
            'subject_name' => $subject->name ?? $subject->title ?? "#{$subject->id}",
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'metadata' => $metadata,
            'workflow_stage' => self::determineWorkflowStage($subject),
            'is_conversion' => false,
            'bypassed_workflow' => false,
        ]);
    }

    public static function logCreation(string $action, string $description, $subject, $related = null, array $metadata = [])
    {
        return self::create([
            'action' => $action,
            'description' => $description,
            'type' => 'creation',
            'performed_by' => auth()->user()?->name ?? 'System',
            'user_id' => auth()->id(),
            'subject_type' => get_class($subject),
            'subject_id' => $subject->id,
            'subject_name' => $subject->name ?? $subject->title ?? "#{$subject->id}",
            'related_type' => $related ? get_class($related) : null,
            'related_id' => $related?->id,
            'related_name' => $related ? ($related->name ?? $related->title ?? "#{$related->id}") : null,
            'metadata' => $metadata,
            'workflow_stage' => self::determineWorkflowStage($subject),
            'is_conversion' => false,
            'bypassed_workflow' => false,
        ]);
    }

    private static function determineWorkflowStage($subject): string
    {
        $className = class_basename($subject);
        
        return match($className) {
            'Lead' => 'lead',
            'Deal' => 'deal',
            'Quotation' => 'quotation',
            'Invoice' => 'invoice',
            'Transaction' => 'payment',
            'Client' => 'client',
            default => 'other'
        };
    }

    // Scopes
    public function scopeConversions($query)
    {
        return $query->where('is_conversion', true);
    }

    public function scopeBypassedWorkflow($query)
    {
        return $query->where('bypassed_workflow', true);
    }

    public function scopeForSubject($query, $subjectType, $subjectId)
    {
        return $query->where('subject_type', $subjectType)->where('subject_id', $subjectId);
    }

    public function scopeByWorkflowStage($query, string $stage)
    {
        return $query->where('workflow_stage', $stage);
    }
}
