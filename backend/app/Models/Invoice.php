<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'invoice_number',
        'client_id',
        'quotation_id',
        'deal_id',
        'created_by',
        'assigned_to',
        'title',
        'description',
        'status',
        'priority',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_rate',
        'discount_amount',
        'total_amount',
        'currency',
        'terms_conditions',
        'notes',
        'internal_notes',
        'issue_date',
        'due_date',
        'sent_at',
        'viewed_at',
        'paid_at',
        'view_count',
        'pdf_path',
        'secure_token',
        'is_public',
        'pdf_generated_at',
        'payment_status',
        'paid_amount',
        'payment_method',
        'payment_notes',
        'custom_fields',
        'attachments',
        'template_used',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'issue_date' => 'date',
        'due_date' => 'date',
        'sent_at' => 'datetime',
        'viewed_at' => 'datetime',
        'paid_at' => 'datetime',
        'pdf_generated_at' => 'datetime',
        'custom_fields' => 'array',
        'attachments' => 'array',
        'is_public' => 'boolean',
        'view_count' => 'integer',
    ];

    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_VIEWED = 'viewed';
    const STATUS_PAID = 'paid';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_CANCELLED = 'cancelled';

    // Payment status constants
    const PAYMENT_PENDING = 'pending';
    const PAYMENT_PARTIAL = 'partial';
    const PAYMENT_PAID = 'paid';
    const PAYMENT_OVERDUE = 'overdue';
    const PAYMENT_CANCELLED = 'cancelled';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            if (empty($invoice->invoice_number)) {
                $invoice->invoice_number = self::generateInvoiceNumber();
            }
            if (empty($invoice->secure_token)) {
                $invoice->secure_token = self::generateUniqueSecureToken();
            }
            if (empty($invoice->issue_date)) {
                $invoice->issue_date = now()->toDateString();
            }
            if (empty($invoice->due_date)) {
                $invoice->due_date = now()->addDays(30)->toDateString();
            }
        });
    }

    /**
     * Relationships
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function quotation()
    {
        return $this->belongsTo(Quotation::class);
    }

    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function items()
    {
        return $this->hasMany(InvoiceItem::class)->orderBy('sort_order');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'invoice_id');
    }

    /**
     * Generate unique secure token
     */
    public static function generateUniqueSecureToken(): string
    {
        $maxAttempts = 10;
        $attempts = 0;

        do {
            $token = Str::random(64);
            $exists = self::where('secure_token', $token)->exists();
            $attempts++;
        } while ($exists && $attempts < $maxAttempts);

        if ($exists) {
            throw new \Exception('Unable to generate unique secure token after ' . $maxAttempts . ' attempts');
        }

        return $token;
    }

    /**
     * Generate unique invoice number
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $year = date('Y');
        $month = date('m');

        // Use database lock to prevent race conditions
        return DB::transaction(function () use ($prefix, $year, $month) {
            $lastInvoice = self::withTrashed()
                              ->whereYear('created_at', $year)
                              ->whereMonth('created_at', $month)
                              ->lockForUpdate()
                              ->orderBy('id', 'desc')
                              ->first();

            $sequence = $lastInvoice ?
                intval(substr($lastInvoice->invoice_number, -4)) + 1 : 1;

            return sprintf('%s-%s%s-%04d', $prefix, $year, $month, $sequence);
        });
    }

    /**
     * Calculate totals
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum('line_total');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->save();
    }

    /**
     * Mark as sent
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now()
        ]);
    }

    /**
     * Mark as viewed
     */
    public function markAsViewed(): void
    {
        $this->increment('view_count');
        
        if ($this->status === self::STATUS_SENT) {
            $this->update([
                'status' => self::STATUS_VIEWED,
                'viewed_at' => now()
            ]);
        }
    }

    /**
     * Mark as paid
     */
    public function markAsPaid(float $amount = null, string $paymentMethod = null, string $notes = null): void
    {
        $amount = $amount ?? $this->total_amount;
        
        $this->update([
            'status' => self::STATUS_PAID,
            'payment_status' => self::PAYMENT_PAID,
            'paid_amount' => $amount,
            'paid_at' => now(),
            'payment_method' => $paymentMethod,
            'payment_notes' => $notes,
        ]);
    }

    /**
     * Add partial payment
     */
    public function addPartialPayment(float $amount, string $paymentMethod = null, string $notes = null): void
    {
        $newPaidAmount = $this->paid_amount + $amount;
        
        $paymentStatus = $newPaidAmount >= $this->total_amount 
            ? self::PAYMENT_PAID 
            : self::PAYMENT_PARTIAL;
            
        $status = $paymentStatus === self::PAYMENT_PAID 
            ? self::STATUS_PAID 
            : $this->status;

        $this->update([
            'status' => $status,
            'payment_status' => $paymentStatus,
            'paid_amount' => $newPaidAmount,
            'payment_method' => $paymentMethod,
            'payment_notes' => $notes,
        ]);

        if ($paymentStatus === self::PAYMENT_PAID) {
            $this->update(['paid_at' => now()]);
        }
    }

    /**
     * Check if overdue
     */
    public function isOverdue(): bool
    {
        return $this->due_date < now()->toDateString() && 
               !in_array($this->payment_status, [self::PAYMENT_PAID, self::PAYMENT_CANCELLED]);
    }

    /**
     * Get public URL
     */
    public function getPublicUrl(): string
    {
        return url("/invoices/view/{$this->secure_token}");
    }

    /**
     * Create from quotation
     */
    public static function createFromQuotation(Quotation $quotation): self
    {
        // Ensure we have a client_id - convert lead if necessary
        $clientId = $quotation->client_id;

        if (!$clientId && $quotation->lead_id) {
            $lead = \App\Models\Lead::findOrFail($quotation->lead_id);

            // Check if lead is already converted to a client
            if ($lead->converted_to_client_id) {
                $clientId = $lead->converted_to_client_id;
            } else {
                // Convert lead to client
                $client = $lead->convertToClient([
                    'notes' => ($lead->notes ?? '') . "\n\nAutomatically converted to client for invoice creation from quotation."
                ]);
                $clientId = $client->id;
            }
        }

        // Ensure we have required fields
        if (!$clientId) {
            throw new \Exception('Cannot create invoice: quotation must have either a client_id or lead_id');
        }

        if (!$quotation->created_by) {
            throw new \Exception('Cannot create invoice: quotation must have a created_by user');
        }

        $invoice = self::create([
            'client_id' => $clientId,
            'quotation_id' => $quotation->id,
            'deal_id' => $quotation->deal_id,
            'created_by' => $quotation->created_by,
            'assigned_to' => $quotation->assigned_to,
            'title' => $quotation->title ?: 'Invoice from Quotation',
            'description' => $quotation->description,
            'priority' => $quotation->priority ?: 'medium',
            'subtotal' => $quotation->subtotal ?: 0,
            'tax_rate' => $quotation->tax_rate ?: 0,
            'tax_amount' => $quotation->tax_amount ?: 0,
            'discount_rate' => $quotation->discount_rate ?: 0,
            'discount_amount' => $quotation->discount_amount ?: 0,
            'total_amount' => $quotation->total_amount ?: 0,
            'currency' => $quotation->currency ?: 'MYR',
            'terms_conditions' => $quotation->terms_conditions,
            'notes' => $quotation->notes,
            'internal_notes' => $quotation->internal_notes,
        ]);

        // Copy quotation items to invoice items
        foreach ($quotation->items as $quotationItem) {
            $invoice->items()->create([
                'product_id' => $quotationItem->product_id,
                'item_name' => $quotationItem->item_name,
                'description' => $quotationItem->description,
                'sku' => $quotationItem->sku,
                'unit' => $quotationItem->unit,
                'quantity' => $quotationItem->quantity,
                'unit_price' => $quotationItem->unit_price,
                'discount_rate' => $quotationItem->discount_rate,
                'discount_amount' => $quotationItem->discount_amount,
                'line_total' => $quotationItem->line_total,
                'sort_order' => $quotationItem->sort_order,
                'custom_fields' => $quotationItem->custom_fields,
            ]);
        }

        // Update quotation with conversion info
        $quotation->update([
            'converted_to_invoice_id' => $invoice->id,
            'converted_at' => now(),
            'converted_by' => auth()->id() ?? 1, // Default to user 1 if no auth
        ]);

        return $invoice;
    }

    /**
     * Scope for filtering
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPaymentStatus($query, $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now()->toDateString())
                    ->whereNotIn('payment_status', [self::PAYMENT_PAID, self::PAYMENT_CANCELLED]);
    }
}
