<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;

class PaymentService
{
    /**
     * Record payment for an invoice
     */
    public function recordPayment(Invoice $invoice, array $paymentData): Transaction
    {
        return DB::transaction(function () use ($invoice, $paymentData) {
            // Create transaction record
            $transaction = Transaction::create([
                'client_id' => $invoice->client_id,
                'invoice_id' => $invoice->id,
                'transaction_number' => $this->generateTransactionNumber(),
                'type' => 'payment',
                'status' => 'completed',
                'amount' => $paymentData['amount'],
                'tax_amount' => 0,
                'total_amount' => $paymentData['amount'],
                'payment_method' => $paymentData['payment_method'] ?? 'Unknown',
                'payment_status' => 'paid',
                'payment_date' => now(),
                'notes' => $paymentData['notes'] ?? null,
            ]);

            // Update invoice payment status
            if ($paymentData['amount'] >= $invoice->total_amount) {
                $invoice->markAsPaid($paymentData['amount'], $paymentData['payment_method'] ?? null, $paymentData['notes'] ?? null);
            } else {
                $invoice->addPartialPayment($paymentData['amount'], $paymentData['payment_method'] ?? null, $paymentData['notes'] ?? null);
            }

            // Update client's computed fields (LTV segment, category, etc.)
            $client = $invoice->client;
            if ($client) {
                $client->updateComputedFields();

                // If this is a lead that was converted to client, ensure lead status is updated
                if ($invoice->lead_id) {
                    $lead = \App\Models\Lead::find($invoice->lead_id);
                    if ($lead && !$lead->converted_to_client_id) {
                        $lead->update([
                            'converted_to_client_id' => $client->id,
                            'client_id' => $client->id,
                            'status' => 'converted',
                            'converted_at' => now(),
                        ]);
                    }
                }
            }

            return $transaction;
        });
    }

    /**
     * Generate unique transaction number
     */
    private function generateTransactionNumber(): string
    {
        $prefix = 'TXN';
        $year = date('Y');
        $month = date('m');
        
        $lastTransaction = Transaction::whereYear('created_at', $year)
                                   ->whereMonth('created_at', $month)
                                   ->orderBy('id', 'desc')
                                   ->first();
        
        $sequence = $lastTransaction ? 
            intval(substr($lastTransaction->transaction_number, -4)) + 1 : 1;
        
        return sprintf('%s-%s%s-%04d', $prefix, $year, $month, $sequence);
    }

    /**
     * Process refund for an invoice
     */
    public function processRefund(Invoice $invoice, array $refundData): Transaction
    {
        return DB::transaction(function () use ($invoice, $refundData) {
            // Create refund transaction
            $transaction = Transaction::create([
                'client_id' => $invoice->client_id,
                'invoice_id' => $invoice->id,
                'transaction_number' => $this->generateTransactionNumber(),
                'type' => 'payment',
                'status' => 'completed',
                'amount' => -$refundData['amount'], // Negative amount for refund
                'tax_amount' => 0,
                'total_amount' => -$refundData['amount'],
                'payment_method' => $refundData['payment_method'] ?? 'Unknown',
                'payment_status' => 'refunded',
                'payment_date' => now(),
                'notes' => $refundData['notes'] ?? null,
            ]);

            // Update invoice paid amount
            $newPaidAmount = max(0, $invoice->paid_amount - $refundData['amount']);
            
            $paymentStatus = $newPaidAmount == 0 
                ? Invoice::PAYMENT_PENDING 
                : ($newPaidAmount >= $invoice->total_amount ? Invoice::PAYMENT_PAID : Invoice::PAYMENT_PARTIAL);
                
            $status = $paymentStatus === Invoice::PAYMENT_PAID 
                ? Invoice::STATUS_PAID 
                : ($paymentStatus === Invoice::PAYMENT_PENDING ? Invoice::STATUS_SENT : $invoice->status);

            $invoice->update([
                'paid_amount' => $newPaidAmount,
                'payment_status' => $paymentStatus,
                'status' => $status,
            ]);

            return $transaction;
        });
    }

    /**
     * Get payment history for an invoice
     */
    public function getPaymentHistory(Invoice $invoice): array
    {
        $transactions = $invoice->transactions()
                              ->orderBy('created_at', 'desc')
                              ->get();

        return [
            'total_paid' => $invoice->paid_amount,
            'outstanding' => $invoice->total_amount - $invoice->paid_amount,
            'transactions' => $transactions,
            'payment_summary' => [
                'payments' => $transactions->where('type', 'payment')->sum('amount'),
                'refunds' => abs($transactions->where('type', 'payment')->where('amount', '<', 0)->sum('amount')),
                'net_paid' => $transactions->sum('amount'),
            ]
        ];
    }

    /**
     * Check if invoice is overdue and update status
     */
    public function checkAndUpdateOverdueStatus(Invoice $invoice): bool
    {
        if ($invoice->isOverdue() && $invoice->status !== Invoice::STATUS_OVERDUE) {
            $invoice->update(['status' => Invoice::STATUS_OVERDUE]);
            return true;
        }
        
        return false;
    }

    /**
     * Get payment analytics for a client
     */
    public function getClientPaymentAnalytics(int $clientId): array
    {
        $invoices = Invoice::where('client_id', $clientId)->get();
        
        return [
            'total_invoiced' => $invoices->sum('total_amount'),
            'total_paid' => $invoices->sum('paid_amount'),
            'outstanding' => $invoices->sum('total_amount') - $invoices->sum('paid_amount'),
            'overdue_amount' => $invoices->filter(fn($inv) => $inv->isOverdue())->sum('total_amount'),
            'average_payment_time' => $this->calculateAveragePaymentTime($invoices),
            'payment_history' => Transaction::where('client_id', $clientId)
                                          ->where('type', 'payment')
                                          ->orderBy('created_at', 'desc')
                                          ->limit(10)
                                          ->get(),
        ];
    }

    /**
     * Calculate average payment time for invoices
     */
    private function calculateAveragePaymentTime($invoices): ?int
    {
        $paidInvoices = $invoices->filter(fn($inv) => $inv->paid_at);
        
        if ($paidInvoices->isEmpty()) {
            return null;
        }

        $totalDays = $paidInvoices->sum(function ($invoice) {
            return $invoice->issue_date->diffInDays($invoice->paid_at);
        });

        return round($totalDays / $paidInvoices->count());
    }
}
