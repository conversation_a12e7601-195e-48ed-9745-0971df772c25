#!/bin/bash

# Wait for database to be ready
echo "Waiting for database to be ready..."
until php -r "try { new PDO('pgsql:host=kdt-postgres;port=5432;dbname=kdt', 'kdt', 'kdt_password'); exit(0); } catch(Exception \$e) { exit(1); }"; do
    echo "Database not ready, waiting..."
    sleep 2
done

echo "Database is ready!"

# Run migrations
echo "Running database migrations..."
php artisan migrate --force

# Run seeders (only if tables are empty)
echo "Checking if database needs seeding..."
CLIENT_COUNT=$(php artisan tinker --execute="echo \App\Models\Client::count();" 2>/dev/null || echo "0")
if [ "$CLIENT_COUNT" = "0" ]; then
    echo "Seeding database..."
    php artisan db:seed --force
else
    echo "Database already has data, skipping seeding."
fi

# Clear and cache config
echo "Optimizing application..."
php artisan config:cache
php artisan route:cache

echo "Application startup complete!"

# Start supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
