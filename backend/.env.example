APP_NAME=KDT
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=kdt-postgres
DB_PORT=5432
DB_DATABASE=kdt
DB_USERNAME=kdt
DB_PASSWORD=kdt_password

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=kdt-redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Development Email (Mailpit for testing)
MAIL_MAILER=smtp
MAIL_HOST=kdt-mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Tarbiah Sentap CRM"

# Production Email (Zoho) - Configure in Settings > Email
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.zoho.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-zoho-password
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="Tarbiah Sentap CRM"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
