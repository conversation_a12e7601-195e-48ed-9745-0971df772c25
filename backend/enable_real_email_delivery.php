<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\SystemSetting;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Enable Real Email Delivery Script ===\n";

try {
    DB::beginTransaction();

    // Enable force real email delivery
    SystemSetting::set('force_real_email_delivery', true, 'boolean', 'Force real email delivery in development');
    
    echo "✓ Enabled force_real_email_delivery setting\n";

    // Verify Zoho SMTP settings exist
    $zohoSettings = [
        'zoho_smtp_host' => 'smtp.zoho.com',
        'zoho_smtp_port' => '587',
        'zoho_smtp_username' => '<EMAIL>',
        'zoho_smtp_encryption' => 'tls',
        'email_from_address' => '<EMAIL>',
        'email_from_name' => 'Tarbiah Sentap CRM',
        'email_reply_to' => '<EMAIL>',
    ];

    foreach ($zohoSettings as $key => $value) {
        $existing = SystemSetting::get($key);
        if (empty($existing)) {
            SystemSetting::set($key, $value, 'string', "Zoho SMTP configuration for $key");
            echo "✓ Set $key to $value\n";
        } else {
            echo "- $key already exists: $existing\n";
        }
    }

    // Check if password exists (don't overwrite if it exists)
    $existingPassword = SystemSetting::get('zoho_smtp_password');
    if (empty($existingPassword)) {
        echo "⚠ WARNING: zoho_smtp_password is not set. Please configure it through the frontend.\n";
    } else {
        echo "✓ zoho_smtp_password is configured\n";
    }

    DB::commit();

    echo "\n=== Current Settings ===\n";
    echo "force_real_email_delivery: " . (SystemSetting::get('force_real_email_delivery', false) ? 'true' : 'false') . "\n";
    echo "zoho_smtp_host: " . SystemSetting::get('zoho_smtp_host', 'not set') . "\n";
    echo "zoho_smtp_username: " . SystemSetting::get('zoho_smtp_username', 'not set') . "\n";
    echo "email_from_address: " . SystemSetting::get('email_from_address', 'not set') . "\n";
    echo "Environment: " . app()->environment() . "\n";

    echo "\n✅ Real email delivery has been enabled!\n";
    echo "📧 2FA emails will now be sent via Zoho SMTP instead of Mailpit.\n";
    echo "🔧 Make sure to configure the Zoho password through the Settings > Email tab.\n";

} catch (\Exception $e) {
    DB::rollBack();
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
