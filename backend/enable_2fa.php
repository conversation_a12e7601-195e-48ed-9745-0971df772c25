<?php

/**
 * <PERSON><PERSON><PERSON> to re-enable system-wide 2FA
 * Run this after configuring email settings
 * 
 * Usage: php enable_2fa.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\SystemSetting;

try {
    // Enable system-wide 2FA
    SystemSetting::set('two_factor_auth_enabled', true, 'boolean', 'Enable or disable two-factor authentication system-wide');
    
    echo "✅ System-wide 2FA has been enabled.\n";
    echo "🔒 All users will now be required to use 2FA for login.\n";
    echo "📧 Make sure email settings are properly configured.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
