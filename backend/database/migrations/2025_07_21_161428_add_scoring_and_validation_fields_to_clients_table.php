<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Scoring fields
            $table->integer('name_score')->default(0)->after('phone_verified');
            $table->integer('email_score')->default(0)->after('name_score');
            $table->integer('phone_score')->default(0)->after('email_score');
            $table->integer('overall_score')->default(0)->after('phone_score');

            // Email validation fields
            $table->string('email_deliverability')->nullable()->after('overall_score');

            // Phone validation fields
            $table->boolean('phone_validity')->default(false)->after('email_deliverability');
            $table->string('phone_carrier')->nullable()->after('phone_validity');

            // Data quality enum
            $table->enum('data_quality', ['Poor', 'Fair', 'Good', 'Excellent'])
                  ->default('Poor')
                  ->after('phone_carrier');

            // Enhanced categorization
            $table->string('customer_category')->nullable()->after('data_quality');

            // Additional notes field
            $table->text('notes_remarks')->nullable()->after('customer_category');

            // Enhanced action tracking
            $table->string('suggested_next_action')->nullable()->after('notes_remarks');

            // Add indexes for performance
            $table->index(['overall_score', 'data_quality'], 'idx_clients_scores');
            $table->index(['phone_carrier', 'phone_validity'], 'idx_clients_carrier_validity');
            $table->index(['email_deliverability', 'email_verified'], 'idx_clients_deliverability');
            $table->index('overall_score', 'idx_clients_overall_score');
            $table->index('data_quality', 'idx_clients_data_quality');
            $table->index('phone_carrier', 'idx_clients_phone_carrier');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex('idx_clients_scores');
            $table->dropIndex('idx_clients_carrier_validity');
            $table->dropIndex('idx_clients_deliverability');
            $table->dropIndex('idx_clients_overall_score');
            $table->dropIndex('idx_clients_data_quality');
            $table->dropIndex('idx_clients_phone_carrier');

            // Drop columns
            $table->dropColumn([
                'name_score',
                'email_score',
                'phone_score',
                'overall_score',
                'email_deliverability',
                'phone_validity',
                'phone_carrier',
                'data_quality',
                'customer_category',
                'notes_remarks',
                'suggested_next_action',
            ]);
        });
    }
};
