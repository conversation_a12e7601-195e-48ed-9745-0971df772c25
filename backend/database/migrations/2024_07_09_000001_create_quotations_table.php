<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->string('quotation_number')->unique();
            
            // Relationships
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('lead_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('deal_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            
            // Basic Information
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('status', ['draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired'])->default('draft');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            
            // Financial Details
            $table->decimal('subtotal', 15, 2)->default(0);
            $table->decimal('tax_rate', 5, 2)->default(0); // Percentage
            $table->decimal('tax_amount', 15, 2)->default(0);
            $table->decimal('discount_rate', 5, 2)->default(0); // Percentage
            $table->decimal('discount_amount', 15, 2)->default(0);
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->string('currency', 3)->default('MYR');
            
            // Terms and Conditions
            $table->text('terms_conditions')->nullable();
            $table->text('notes')->nullable();
            $table->text('internal_notes')->nullable();
            
            // Validity and Timeline
            $table->date('valid_until')->nullable();
            $table->date('expected_close_date')->nullable();
            $table->integer('validity_days')->default(30);
            
            // Tracking
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('viewed_at')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->integer('view_count')->default(0);
            
            // PDF and Sharing
            $table->string('pdf_path')->nullable();
            $table->string('secure_token')->unique()->nullable();
            $table->boolean('is_public')->default(false);
            $table->timestamp('pdf_generated_at')->nullable();
            
            // Conversion Tracking (foreign key will be added later after invoices table is created)
            $table->unsignedBigInteger('converted_to_invoice_id')->nullable();
            $table->timestamp('converted_at')->nullable();
            $table->string('converted_by')->nullable();
            
            // Additional Fields
            $table->json('custom_fields')->nullable();
            $table->json('attachments')->nullable();
            $table->string('template_used')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['client_id', 'status']);
            $table->index(['created_by', 'status']);
            $table->index(['status', 'valid_until']);
            $table->index('secure_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotations');
    }
};
