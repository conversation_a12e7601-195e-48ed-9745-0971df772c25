<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('leads', 'name')) {
                // Lead capture fields
                $table->string('name')->nullable()->after('client_id');
                $table->string('email')->nullable()->after('name');
                $table->string('phone')->nullable()->after('email');
                $table->string('company')->nullable()->after('phone');
                
                // UTM and campaign tracking
                $table->string('utm_source')->nullable()->after('company');
                $table->string('utm_campaign')->nullable()->after('utm_source');
                $table->string('utm_medium')->nullable()->after('utm_campaign');
                $table->string('utm_content')->nullable()->after('utm_medium');
                $table->string('utm_term')->nullable()->after('utm_content');
                $table->string('channel')->nullable()->after('utm_term');
                
                // Lead classification
                $table->enum('lead_type', ['capture', 'opportunity'])->default('capture')->after('channel');
                $table->json('tags')->nullable()->after('lead_type');
                
                // Update existing status enum to include new values
                $table->dropColumn('status');
            }
        });

        // Add the new status column with updated enum values
        Schema::table('leads', function (Blueprint $table) {
            if (!Schema::hasColumn('leads', 'status')) {
                $table->enum('status', ['new', 'contacted', 'engaged', 'qualified', 'converted', 'disqualified'])->default('new')->after('tags');
                $table->enum('opportunity_status', ['contacted', 'info_sent', 'negotiation', 'waiting_payment', 'closed_won', 'closed_lost'])->nullable()->after('status');
                $table->enum('engagement_level', ['hot', 'warm', 'cold', 'frozen'])->default('cold')->after('priority');
            }
        });

        Schema::table('leads', function (Blueprint $table) {
            if (!Schema::hasColumn('leads', 'internal_remarks')) {
                // Activity and lifecycle tracking
                $table->text('internal_remarks')->nullable()->after('notes');
                $table->string('suggested_action')->nullable()->after('internal_remarks');
                $table->timestamp('last_activity')->nullable()->after('suggested_action');
                $table->timestamp('last_contacted')->nullable()->after('last_activity');
                $table->integer('lifecycle_stage')->default(0)->after('last_contacted');
                
                // Document uploads
                $table->json('documents')->nullable()->after('lifecycle_stage');
                
                // Recycling and conversion tracking
                $table->boolean('is_recycled')->default(false)->after('documents');
                $table->timestamp('recycled_at')->nullable()->after('is_recycled');
                $table->timestamp('converted_at')->nullable()->after('recycled_at');
                $table->foreignId('converted_to_client_id')->nullable()->constrained('clients')->onDelete('set null')->after('converted_at');
                
                // Add new indexes
                $table->index(['lead_type', 'status']);
                $table->index(['engagement_level', 'priority']);
                $table->index(['utm_source', 'utm_campaign']);
                $table->index(['channel', 'created_at']);
                $table->index('email');
                $table->index('phone');
                $table->index('last_activity');
            }
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Remove new columns
            $table->dropColumn([
                'name', 'email', 'phone', 'company',
                'utm_source', 'utm_campaign', 'utm_medium', 'utm_content', 'utm_term', 'channel',
                'lead_type', 'tags', 'opportunity_status', 'engagement_level',
                'internal_remarks', 'suggested_action', 'last_activity', 'last_contacted', 'lifecycle_stage',
                'documents', 'is_recycled', 'recycled_at', 'converted_at', 'converted_to_client_id'
            ]);
            
            // Remove new indexes
            $table->dropIndex(['lead_type', 'status']);
            $table->dropIndex(['engagement_level', 'priority']);
            $table->dropIndex(['utm_source', 'utm_campaign']);
            $table->dropIndex(['channel', 'created_at']);
            $table->dropIndex(['email']);
            $table->dropIndex(['phone']);
            $table->dropIndex(['last_activity']);
        });

        // Restore original status enum
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('leads', function (Blueprint $table) {
            $table->enum('status', ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost'])->default('new');
        });
    }
};
