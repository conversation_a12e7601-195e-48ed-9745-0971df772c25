<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_phone_numbers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->string('phone_number', 20);
            $table->boolean('is_primary')->default(false);
            $table->boolean('phone_verified')->default(true);
            $table->integer('phone_score')->default(0)->comment('Phone number quality score (0-100)');
            $table->string('phone_carrier')->nullable()->comment('Phone carrier/network provider');
            $table->timestamps();

            // Indexes for performance
            $table->index(['client_id', 'is_primary']);
            $table->index('phone_number');

            // Ensure only one primary phone per client
            $table->unique(['client_id', 'is_primary'], 'unique_primary_phone_per_client');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_phone_numbers');
    }
};
