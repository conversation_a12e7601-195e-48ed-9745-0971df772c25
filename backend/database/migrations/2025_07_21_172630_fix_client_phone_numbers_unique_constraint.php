<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_phone_numbers', function (Blueprint $table) {
            // Drop the problematic unique constraint
            $table->dropUnique('unique_primary_phone_per_client');
        });

        // Create a partial unique index using raw SQL (PostgreSQL specific)
        DB::statement('CREATE UNIQUE INDEX unique_primary_phone_per_client ON client_phone_numbers (client_id) WHERE is_primary = true');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the partial unique index
        DB::statement('DROP INDEX IF EXISTS unique_primary_phone_per_client');

        // Recreate the original unique constraint
        Schema::table('client_phone_numbers', function (Blueprint $table) {
            $table->unique(['client_id', 'is_primary'], 'unique_primary_phone_per_client');
        });
    }
};
