<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing unique constraint on email
        Schema::table('users', function (Blueprint $table) {
            $table->dropUnique(['email']);
        });

        // Add a partial unique index that only applies to active users
        // This allows multiple inactive users with the same email, but only one active user per email
        DB::statement('CREATE UNIQUE INDEX users_email_active_unique ON users (email) WHERE is_active = true');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the partial unique index
        DB::statement('DROP INDEX IF EXISTS users_email_active_unique');

        // Restore the original unique constraint
        Schema::table('users', function (Blueprint $table) {
            $table->unique('email');
        });
    }
};
