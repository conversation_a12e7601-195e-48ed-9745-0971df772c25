<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');

            // Lead capture fields (for potential customers)
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('company')->nullable();

            // UTM and campaign tracking
            $table->string('utm_source')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_content')->nullable();
            $table->string('utm_term')->nullable();
            $table->string('channel')->nullable(); // WhatsApp, Facebook, Instagram, TikTok, Website, etc.

            // Lead classification
            $table->enum('lead_type', ['capture', 'opportunity'])->default('capture'); // capture = potential customer, opportunity = sales deal
            $table->json('tags')->nullable(); // Custom interests/tags

            // Sales opportunity fields (existing functionality)
            $table->string('title')->nullable(); // For opportunities
            $table->text('description')->nullable();
            $table->enum('status', ['new', 'contacted', 'engaged', 'qualified', 'converted', 'disqualified'])->default('new');
            $table->enum('opportunity_status', ['contacted', 'info_sent', 'negotiation', 'waiting_payment', 'closed_won', 'closed_lost'])->nullable(); // For sales opportunities
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
            $table->enum('engagement_level', ['hot', 'warm', 'cold', 'frozen'])->default('cold');

            // Sales tracking
            $table->string('source')->nullable(); // Lead source
            $table->string('assigned_to')->nullable();
            $table->decimal('estimated_value', 12, 2)->nullable();
            $table->integer('probability')->nullable(); // 0-100
            $table->timestamp('expected_close_date')->nullable();

            // Activity and lifecycle tracking
            $table->text('notes')->nullable();
            $table->text('internal_remarks')->nullable();
            $table->string('suggested_action')->nullable();
            $table->timestamp('last_activity')->nullable();
            $table->timestamp('last_contacted')->nullable();
            $table->integer('lifecycle_stage')->default(0); // 0-100 progress bar

            // Document uploads (JSON array of file paths)
            $table->json('documents')->nullable();

            // Recycling and conversion tracking
            $table->boolean('is_recycled')->default(false);
            $table->timestamp('recycled_at')->nullable();
            $table->timestamp('converted_at')->nullable();
            $table->foreignId('converted_to_client_id')->nullable()->constrained('clients')->onDelete('set null');

            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['status', 'priority']);
            $table->index(['client_id', 'status']);
            $table->index(['lead_type', 'status']);
            $table->index(['engagement_level', 'priority']);
            $table->index(['utm_source', 'utm_campaign']);
            $table->index(['channel', 'created_at']);
            $table->index('source');
            $table->index('email');
            $table->index('phone');
            $table->index('last_activity');
            $table->index('expected_close_date');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('leads');
    }
};
