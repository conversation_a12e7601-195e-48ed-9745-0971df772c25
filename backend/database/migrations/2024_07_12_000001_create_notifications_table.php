<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // 'deal_assigned', 'deal_won', 'deal_lost', 'quotation_sent', etc.
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Additional data like deal_id, quotation_id, etc.
            $table->timestamp('read_at')->nullable();
            $table->string('priority')->default('normal'); // 'low', 'normal', 'high', 'urgent'
            $table->string('category')->default('general'); // 'deal', 'quotation', 'invoice', 'system', etc.
            $table->string('action_url')->nullable(); // URL to navigate when notification is clicked
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['user_id', 'read_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
