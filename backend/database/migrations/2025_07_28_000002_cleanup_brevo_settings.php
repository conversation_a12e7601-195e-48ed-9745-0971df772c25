<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove any old Brevo-specific settings
        DB::table('system_settings')->whereIn('key', [
            'brevo_api_key',
            'brevo_smtp_host',
            'brevo_smtp_port',
            'brevo_smtp_username',
            'brevo_smtp_encryption',
            'brevo_smtp_password',
        ])->delete();

        // Ensure Zoho default settings exist
        $zohoDefaults = [
            ['key' => 'zoho_smtp_host', 'value' => 'smtp.zoho.com', 'type' => 'string', 'description' => 'Zoho SMTP server host'],
            ['key' => 'zoho_smtp_port', 'value' => '587', 'type' => 'integer', 'description' => 'Zoho SMTP server port'],
            ['key' => 'zoho_smtp_encryption', 'value' => 'tls', 'type' => 'string', 'description' => 'Zoho SMTP encryption method'],
        ];

        foreach ($zohoDefaults as $setting) {
            DB::table('system_settings')->updateOrInsert(
                ['key' => $setting['key']],
                [
                    'value' => $setting['value'],
                    'type' => $setting['type'],
                    'description' => $setting['description'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible as we're cleaning up old data
        // If needed, Brevo settings would need to be manually recreated
    }
};
