<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Make title nullable since it's only needed for opportunities
            $table->string('title')->nullable()->change();
            
            // Make description nullable
            $table->text('description')->nullable()->change();
            
            // Update priority enum to match our new values
            $table->dropColumn('priority');
        });

        Schema::table('leads', function (Blueprint $table) {
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium')->after('opportunity_status');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Revert title to NOT NULL
            $table->string('title')->nullable(false)->change();
            
            // Revert description to NOT NULL
            $table->text('description')->nullable(false)->change();
            
            // Revert priority enum
            $table->dropColumn('priority');
        });

        Schema::table('leads', function (Blueprint $table) {
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
        });
    }
};
