<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing email data from clients table to client_emails table
        DB::statement("
            INSERT INTO client_emails (client_id, email_address, is_primary, email_verified, email_score, email_deliverability, created_at, updated_at)
            SELECT 
                id as client_id,
                email as email_address,
                true as is_primary,
                COALESCE(email_verified, true) as email_verified,
                COALESCE(email_score, 0) as email_score,
                email_deliverability,
                created_at,
                updated_at
            FROM clients 
            WHERE email IS NOT NULL AND email != ''
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Update clients table with primary email data
        DB::statement("
            UPDATE clients 
            SET 
                email = ce.email_address,
                email_verified = ce.email_verified,
                email_score = ce.email_score,
                email_deliverability = ce.email_deliverability
            FROM client_emails ce 
            WHERE clients.id = ce.client_id 
            AND ce.is_primary = true
        ");
        
        // Clear the client_emails table
        DB::table('client_emails')->truncate();
    }
};
