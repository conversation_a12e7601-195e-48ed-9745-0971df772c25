<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Disable 2FA temporarily until email settings are configured
        DB::table('system_settings')
            ->updateOrInsert(
                ['key' => 'two_factor_auth_enabled'],
                [
                    'value' => 'false',
                    'type' => 'boolean',
                    'description' => 'Enable or disable two-factor authentication system-wide',
                    'updated_at' => now(),
                    'created_at' => now(),
                ]
            );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-enable 2FA
        DB::table('system_settings')
            ->where('key', 'two_factor_auth_enabled')
            ->update([
                'value' => 'true',
                'updated_at' => now(),
            ]);
    }
};
