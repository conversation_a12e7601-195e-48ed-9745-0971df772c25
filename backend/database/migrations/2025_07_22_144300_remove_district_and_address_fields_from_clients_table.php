<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Remove the district field entirely
            $table->dropColumn('district');
            // Keep the old address field for now (will be handled in UI)
            // We'll remove it later after ensuring all data is migrated to structured fields
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Restore the district field
            $table->string('district')->nullable()->after('state');
        });
    }
};
