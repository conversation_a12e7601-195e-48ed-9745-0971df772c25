<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Make email nullable and remove unique constraint temporarily
            $table->dropUnique(['email']);
            $table->string('email')->nullable()->change();

            // Add unique constraint that allows nulls (PostgreSQL specific)
            DB::statement('CREATE UNIQUE INDEX clients_email_unique_not_null ON clients (email) WHERE email IS NOT NULL');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Drop the partial unique index
            DB::statement('DROP INDEX IF EXISTS clients_email_unique_not_null');

            // Make email required again and add back unique constraint
            $table->string('email')->nullable(false)->change();
            $table->unique('email');
        });
    }
};
