<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, boolean, integer, json
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Ensure 2FA is disabled by default - force update existing records
        DB::table('system_settings')
            ->updateOrInsert(
                ['key' => 'two_factor_auth_enabled'],
                [
                    'value' => 'false',
                    'type' => 'boolean',
                    'description' => 'Enable or disable two-factor authentication system-wide',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_settings');
    }
};
