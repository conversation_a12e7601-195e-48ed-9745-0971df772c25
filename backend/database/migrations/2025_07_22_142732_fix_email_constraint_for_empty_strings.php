<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing constraint that doesn't allow empty strings
        DB::statement('DROP INDEX IF EXISTS clients_email_unique_not_null');

        // Create a new constraint that allows both NULL and empty strings
        // Only enforce uniqueness for non-empty, non-null email addresses
        DB::statement('CREATE UNIQUE INDEX clients_email_unique_not_empty ON clients (email) WHERE email IS NOT NULL AND email != \'\'');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the new constraint
        DB::statement('DROP INDEX IF EXISTS clients_email_unique_not_empty');

        // Restore the original constraint (only allows NULL, not empty strings)
        DB::statement('CREATE UNIQUE INDEX clients_email_unique_not_null ON clients (email) WHERE email IS NOT NULL');
    }
};
