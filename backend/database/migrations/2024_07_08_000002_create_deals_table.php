<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('deals', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('lead_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Basic deal information
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('deal_number')->unique(); // Auto-generated deal reference
            
            // Financial information
            $table->decimal('value', 12, 2);
            $table->decimal('expected_revenue', 12, 2)->nullable();
            $table->decimal('actual_revenue', 12, 2)->nullable();
            $table->string('currency', 3)->default('MYR');
            
            // Pipeline and stage management
            $table->enum('pipeline_stage', [
                'prospecting',      // Initial contact and qualification
                'qualification',    // Needs assessment and budget confirmation
                'proposal',         // Proposal sent, awaiting response
                'negotiation',      // Terms and pricing discussion
                'closing',          // Final approval and contract signing
                'won',              // Deal successfully closed
                'lost',             // Deal lost to competitor or cancelled
                'on_hold'           // Deal temporarily paused
            ])->default('prospecting');
            
            $table->integer('stage_order')->default(1); // For custom stage ordering
            $table->timestamp('stage_changed_at')->nullable();
            $table->string('stage_changed_by')->nullable();
            
            // Probability and forecasting
            $table->integer('probability')->default(10); // 0-100 percentage
            $table->enum('deal_size', ['Small', 'Medium', 'Large', 'Enterprise'])->default('Medium');
            
            // Timeline management
            $table->timestamp('expected_close_date')->nullable();
            $table->timestamp('actual_close_date')->nullable();
            $table->integer('days_in_pipeline')->default(0); // Auto-calculated
            $table->integer('days_in_current_stage')->default(0); // Auto-calculated
            
            // Source and attribution
            $table->string('source')->nullable(); // How the deal originated
            $table->string('utm_source')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_medium')->nullable();
            
            // Deal classification and priority
            $table->enum('priority', ['Low', 'Medium', 'High', 'Critical'])->default('Medium');
            $table->enum('deal_type', ['New Business', 'Upsell', 'Cross-sell', 'Renewal', 'Expansion'])->default('New Business');
            $table->json('tags')->nullable(); // Flexible tagging system
            
            // Competition and market analysis
            $table->json('competitors')->nullable(); // Array of competitor names
            $table->text('competitive_advantage')->nullable();
            $table->enum('win_probability_reason', [
                'Strong Relationship',
                'Best Price',
                'Superior Product',
                'Incumbent Advantage',
                'Strategic Partnership',
                'Unique Solution',
                'Other'
            ])->nullable();
            
            // Communication and activity tracking
            $table->text('notes')->nullable();
            $table->text('internal_notes')->nullable();
            $table->timestamp('last_activity')->nullable();
            $table->timestamp('next_follow_up')->nullable();
            $table->string('next_action')->nullable();
            
            // Automation and workflow
            $table->json('automation_triggers')->nullable(); // Rules for automated actions
            $table->boolean('auto_follow_up_enabled')->default(true);
            $table->integer('follow_up_frequency_days')->default(7);
            
            // Document and attachment management
            $table->json('documents')->nullable(); // Array of document paths/URLs
            $table->json('proposal_documents')->nullable(); // Specific proposal files
            $table->json('contract_documents')->nullable(); // Contract and legal documents
            
            // Loss analysis (for lost deals)
            $table->enum('loss_reason', [
                'Price Too High',
                'Competitor Won',
                'No Budget',
                'No Decision',
                'Product Mismatch',
                'Timing Issues',
                'Lost Contact',
                'Other'
            ])->nullable();
            $table->text('loss_details')->nullable();
            $table->string('competitor_won')->nullable(); // Which competitor won
            
            // Integration fields
            $table->string('external_id')->nullable(); // For CRM integrations
            $table->json('custom_fields')->nullable(); // Flexible additional data
            
            // Audit and tracking
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['pipeline_stage', 'probability']);
            $table->index(['assigned_to', 'pipeline_stage']);
            $table->index(['client_id', 'pipeline_stage']);
            $table->index(['lead_id', 'created_at']);
            $table->index(['expected_close_date', 'pipeline_stage']);
            $table->index(['priority', 'pipeline_stage']);
            $table->index(['deal_type', 'pipeline_stage']);
            $table->index(['source', 'created_at']);
            $table->index(['utm_source', 'utm_campaign']);
            $table->index('deal_number');
            $table->index('last_activity');
            $table->index('next_follow_up');
            $table->index(['value', 'pipeline_stage']);
            $table->index(['created_by', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('deals');
    }
};
