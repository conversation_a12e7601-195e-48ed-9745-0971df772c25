<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_emails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->string('email_address');
            $table->boolean('is_primary')->default(false);
            $table->boolean('email_verified')->default(true);
            $table->integer('email_score')->default(0)->comment('Email quality score (0-100)');
            $table->string('email_deliverability')->nullable()->comment('Email deliverability status');
            $table->timestamps();

            // Indexes for performance
            $table->index(['client_id', 'is_primary']);
            $table->index('email_address');
            
            // Ensure unique email addresses across all clients
            $table->unique('email_address');
        });

        // Create a partial unique index to ensure only one primary email per client
        DB::statement('CREATE UNIQUE INDEX unique_primary_email_per_client ON client_emails (client_id) WHERE is_primary = true');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the partial unique index
        DB::statement('DROP INDEX IF EXISTS unique_primary_email_per_client');
        
        Schema::dropIfExists('client_emails');
    }
};
