<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // UTM and tracking fields
            $table->string('utm_source')->nullable()->after('notes');
            $table->json('tags')->nullable()->after('utm_source');
            
            // Client categorization
            $table->enum('category', ['First Timer', 'Retainer', 'Loyal', 'Advocator'])
                  ->default('First Timer')
                  ->after('tags');
            
            $table->enum('ltv_segment', ['Silver', 'Gold', 'Gold+', 'Platinum'])
                  ->default('Silver')
                  ->after('category');
            
            $table->enum('engagement_level', ['Hot', 'Warm', 'Cold', 'Frozen'])
                  ->default('Cold')
                  ->after('ltv_segment');
            
            $table->enum('priority', ['High', 'Medium', 'Low'])
                  ->default('Medium')
                  ->after('engagement_level');
            
            // Action and activity tracking
            $table->string('suggested_action')->nullable()->after('priority');
            $table->timestamp('last_activity')->nullable()->after('suggested_action');
            
            // Financial tracking (computed fields that can be cached)
            $table->decimal('total_spent', 10, 2)->default(0)->after('last_activity');
            $table->integer('transaction_count')->default(0)->after('total_spent');
            
            // Custom fields for additional data
            $table->json('custom_fields')->nullable()->after('transaction_count');
            
            // Add indexes for performance
            $table->index(['category', 'ltv_segment']);
            $table->index(['engagement_level', 'priority']);
            $table->index('utm_source');
            $table->index('last_activity');
        });
    }

    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex(['category', 'ltv_segment']);
            $table->dropIndex(['engagement_level', 'priority']);
            $table->dropIndex(['utm_source']);
            $table->dropIndex(['last_activity']);
            
            $table->dropColumn([
                'utm_source',
                'tags',
                'category',
                'ltv_segment',
                'engagement_level',
                'priority',
                'suggested_action',
                'last_activity',
                'total_spent',
                'transaction_count',
                'custom_fields',
            ]);
        });
    }
};
