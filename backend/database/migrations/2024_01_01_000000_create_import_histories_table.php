<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('file_name');
            $table->bigInteger('file_size')->nullable();
            $table->string('data_type'); // 'clients', 'leads', 'products', etc.
            $table->string('file_format')->default('csv'); // 'csv', 'xlsx', etc.
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            
            // Import statistics
            $table->integer('total_rows')->default(0);
            $table->integer('processed')->default(0);
            $table->integer('created')->default(0);
            $table->integer('updated')->default(0);
            $table->integer('skipped')->default(0);
            $table->integer('errors_count')->default(0);
            $table->integer('duplicates_count')->default(0);
            
            // Performance metrics
            $table->decimal('processing_time', 8, 2)->nullable(); // in seconds
            $table->decimal('success_rate', 5, 2)->nullable(); // percentage
            
            // Detailed information
            $table->json('error_details')->nullable();
            $table->json('import_options')->nullable();
            $table->json('field_mapping')->nullable();
            
            // Timestamps
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['data_type', 'status']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_histories');
    }
};
