<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Deal conversion tracking
            $table->boolean('converted_to_deal')->default(false)->after('converted_to_client_id');
            $table->foreignId('converted_to_deal_id')->nullable()->constrained('deals')->onDelete('set null')->after('converted_to_deal');
            $table->timestamp('deal_converted_at')->nullable()->after('converted_to_deal_id');
            
            // Deal readiness scoring
            $table->integer('deal_readiness_score')->default(0)->after('deal_converted_at'); // 0-100
            $table->json('qualification_criteria')->nullable()->after('deal_readiness_score'); // BANT, MEDDIC, etc.
            
            // Enhanced sales tracking for deal preparation
            $table->enum('budget_status', ['Unknown', 'No Budget', 'Budget Identified', 'Budget Approved'])->default('Unknown')->after('qualification_criteria');
            $table->enum('decision_maker_access', ['Unknown', 'No Access', 'Indirect Access', 'Direct Access'])->default('Unknown')->after('budget_status');
            $table->enum('timeline_urgency', ['Unknown', 'No Urgency', 'Moderate', 'Urgent'])->default('Unknown')->after('decision_maker_access');
            
            // Add indexes for new fields
            $table->index(['converted_to_deal', 'deal_readiness_score']);
            $table->index(['budget_status', 'decision_maker_access']);
            $table->index('deal_converted_at');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['converted_to_deal', 'deal_readiness_score']);
            $table->dropIndex(['budget_status', 'decision_maker_access']);
            $table->dropIndex(['deal_converted_at']);
            
            // Drop columns
            $table->dropColumn([
                'converted_to_deal',
                'converted_to_deal_id',
                'deal_converted_at',
                'deal_readiness_score',
                'qualification_criteria',
                'budget_status',
                'decision_maker_access',
                'timeline_urgency',
            ]);
        });
    }
};
