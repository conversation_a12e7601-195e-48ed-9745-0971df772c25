<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Personal Information Fields
            $table->string('ic_number')->nullable()->after('name')->comment('Malaysian Identity Card number');
            $table->date('birthday')->nullable()->after('ic_number');
            $table->enum('gender', ['Male', 'Female'])->nullable()->after('birthday');
            $table->enum('religion', ['Muslim', 'Non-Muslim'])->nullable()->after('gender');
            
            // Financial Information
            $table->decimal('income', 12, 2)->nullable()->after('religion')->comment('Monthly income in RM');
            $table->enum('income_category', ['Low', 'Medium', 'High'])->nullable()->after('income');
            
            // Enhanced Address Fields (replace single address field)
            $table->string('address_line_1')->nullable()->after('phone_verified');
            $table->string('address_line_2')->nullable()->after('address_line_1');
            // Note: city, state already exist, we'll modify them
            $table->string('postcode', 10)->nullable()->after('state');
            $table->string('district')->nullable()->after('postcode');
            
            // Behavioral Data
            $table->text('behaviour')->nullable()->after('suggested_next_action');
            $table->text('interest')->nullable()->after('behaviour');
            
            // Add indexes for performance
            $table->index('ic_number');
            $table->index(['gender', 'religion']);
            $table->index('income_category');
            $table->index(['state', 'city']);
            $table->index('postcode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['ic_number']);
            $table->dropIndex(['gender', 'religion']);
            $table->dropIndex(['income_category']);
            $table->dropIndex(['state', 'city']);
            $table->dropIndex(['postcode']);
            
            // Drop columns
            $table->dropColumn([
                'ic_number',
                'birthday',
                'gender',
                'religion',
                'income',
                'income_category',
                'address_line_1',
                'address_line_2',
                'postcode',
                'district',
                'behaviour',
                'interest',
            ]);
        });
    }
};
