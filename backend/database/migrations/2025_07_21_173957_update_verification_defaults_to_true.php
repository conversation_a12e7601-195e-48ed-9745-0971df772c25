<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Update verification defaults to true
            $table->boolean('email_verified')->default(true)->change();
            $table->boolean('phone_verified')->default(true)->change();
        });

        // Update existing records to have verified = true by default
        DB::table('clients')->where('email_verified', false)->orWhereNull('email_verified')->update(['email_verified' => true]);
        DB::table('clients')->where('phone_verified', false)->orWhereNull('phone_verified')->update(['phone_verified' => true]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Revert verification defaults to false
            $table->boolean('email_verified')->default(false)->change();
            $table->boolean('phone_verified')->default(false)->change();
        });
    }
};
