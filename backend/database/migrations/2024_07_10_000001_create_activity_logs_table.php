<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            
            // What happened
            $table->string('action'); // 'lead_to_client_conversion', 'deal_stage_change', 'quotation_created', etc.
            $table->string('description'); // Human readable description
            $table->enum('type', ['conversion', 'stage_change', 'creation', 'update', 'deletion', 'other'])->default('other');
            
            // Who did it
            $table->string('performed_by')->nullable(); // User name or 'System'
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            
            // What was affected
            $table->string('subject_type'); // 'Lead', 'Deal', 'Client', etc.
            $table->unsignedBigInteger('subject_id');
            $table->string('subject_name')->nullable(); // For easy reference
            
            // Related entities
            $table->string('related_type')->nullable(); // Related entity type
            $table->unsignedBigInteger('related_id')->nullable(); // Related entity ID
            $table->string('related_name')->nullable(); // Related entity name
            
            // Additional context
            $table->json('old_values')->nullable(); // Previous state
            $table->json('new_values')->nullable(); // New state
            $table->json('metadata')->nullable(); // Additional context data
            
            // Workflow tracking
            $table->string('workflow_stage')->nullable(); // 'lead', 'deal', 'quotation', 'invoice', 'payment', 'client'
            $table->boolean('is_conversion')->default(false); // Mark important conversions
            $table->boolean('bypassed_workflow')->default(false); // Mark manual/direct conversions
            
            $table->timestamps();
            
            // Indexes
            $table->index(['subject_type', 'subject_id']);
            $table->index(['related_type', 'related_id']);
            $table->index(['action', 'type']);
            $table->index(['user_id', 'created_at']);
            $table->index(['workflow_stage', 'is_conversion']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
