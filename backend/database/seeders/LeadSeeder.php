<?php

namespace Database\Seeders;

use App\Models\Lead;
use App\Models\Client;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class LeadSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing leads
        Lead::truncate();

        $this->command->info('Generating realistic leads for Islamic organization...');

        $clients = Client::all();

        // Generate 200-300 leads with realistic conversion patterns
        $totalLeads = rand(200, 300);
        $progressBar = $this->command->getOutput()->createProgressBar($totalLeads);
        $progressBar->start();

        $leads = [];

        for ($i = 0; $i < $totalLeads; $i++) {
            $leadData = $this->generateLeadData($clients);
            $leads[] = $leadData;

            $progressBar->advance();

            // Insert in batches
            if (count($leads) >= 50) {
                Lead::insert($leads);
                $leads = [];
            }
        }

        // Insert remaining leads
        if (!empty($leads)) {
            Lead::insert($leads);
        }

        $progressBar->finish();
        $this->command->newLine();
        $this->command->info("Successfully created {$totalLeads} leads!");
    }


    private function generateLeadData($clients): array
    {
        // Generate lead contact information
        $leadName = $this->generateMalaysianName();
        $leadEmail = $this->generateEmail($leadName);
        $leadPhone = $this->generateMalaysianPhone();
        $leadCompany = $this->generateCompany();

        // 30% of leads have existing client_id (warm leads), 70% are new prospects
        $clientId = null;
        $convertedToClientId = null;

        if (rand(1, 100) <= 30 && $clients->isNotEmpty()) {
            // Use existing client data for warm leads
            $existingClient = $clients->random();
            $clientId = $existingClient->id;
            $leadName = $existingClient->name;
            $leadEmail = $existingClient->email;
            $leadPhone = $existingClient->phone;
            $leadCompany = $existingClient->company;
        } else {
            // For new prospects, some may have been converted to clients
            if (rand(1, 100) <= 25) { // 25% of new leads converted to clients
                $matchingClient = $clients->where('email', $leadEmail)->first();
                if ($matchingClient) {
                    $convertedToClientId = $matchingClient->id;
                }
            }
        }

        $leadTypes = $this->getLeadTypes();
        $leadType = $leadTypes[array_rand($leadTypes)];

        $sources = ['facebook', 'instagram', 'tiktok', 'google', 'youtube', 'whatsapp', 'referral', 'website', 'email', 'phone'];
        $statuses = ['new', 'contacted', 'engaged', 'qualified', 'converted', 'disqualified'];
        $opportunityStatuses = ['contacted', 'info_sent', 'negotiation', 'waiting_payment', 'closed_won', 'closed_lost'];
        $priorities = ['low', 'medium', 'high'];
        $engagementLevels = ['hot', 'warm', 'cold', 'frozen'];
        $assignees = ['Ahmad Rahman', 'Fatimah Ali', 'Muhammad Hassan', 'Aishah Omar', 'Ibrahim Yusof'];

        // Generate realistic dates (leads from last 6 months)
        $createdAt = Carbon::now()->subDays(rand(1, 180));
        $expectedCloseDate = $createdAt->copy()->addDays(rand(7, 90));

        // Determine status based on lead age
        $status = $this->determineLeadStatus($createdAt);

        // Determine opportunity status for sales opportunities
        $opportunityStatus = null;
        if (rand(1, 100) <= 30) { // 30% are sales opportunities
            $opportunityStatus = $opportunityStatuses[array_rand($opportunityStatuses)];
        }

        // If converted to client, set status accordingly
        if ($convertedToClientId) {
            $status = 'converted';
            $opportunityStatus = 'closed_won'; // Converted leads are won opportunities
        }

        // Determine probability based on status and opportunity status
        $probability = $this->determineProbability($status, $opportunityStatus);

        // Determine priority based on estimated value and source
        $estimatedValue = $leadType['value'];
        $priority = $this->determinePriority($estimatedValue, $sources[array_rand($sources)]);

        // Generate UTM data
        $utmData = $this->generateUTMData();

        return [
            'client_id' => $clientId,
            'name' => $leadName,
            'email' => $leadEmail,
            'phone' => $leadPhone,
            'company' => $leadCompany,
            'utm_source' => $utmData['utm_source'],
            'utm_campaign' => $utmData['utm_campaign'],
            'utm_medium' => $utmData['utm_medium'],
            'utm_content' => $utmData['utm_content'],
            'utm_term' => $utmData['utm_term'],
            'channel' => $this->generateChannel(),
            'lead_type' => rand(1, 100) <= 70 ? 'capture' : 'opportunity',
            'tags' => json_encode($this->generateLeadTags()),
            'title' => $leadType['title'],
            'description' => $leadType['description'],
            'status' => $status,
            'opportunity_status' => $opportunityStatus,
            'priority' => $priority,
            'engagement_level' => $engagementLevels[array_rand($engagementLevels)],
            'source' => $sources[array_rand($sources)],
            'assigned_to' => $assignees[array_rand($assignees)],
            'estimated_value' => $estimatedValue,
            'probability' => $probability,
            'expected_close_date' => $expectedCloseDate,
            'notes' => $this->generateLeadNotes($status, $leadType['title']),
            'converted_to_client_id' => $convertedToClientId,
            'created_at' => $createdAt,
            'updated_at' => $createdAt->copy()->addDays(rand(0, 10)),
        ];
    }

    private function getLeadTypes(): array
    {
        return [
            [
                'title' => 'Islamic Education Course Enrollment',
                'description' => 'Interested in enrolling for comprehensive Islamic studies program',
                'value' => rand(200, 800),
            ],
            [
                'title' => 'Quran Learning Program',
                'description' => 'Wants to join Quran recitation and memorization classes',
                'value' => rand(150, 600),
            ],
            [
                'title' => 'Arabic Language Course',
                'description' => 'Interested in learning Arabic language for better Quran understanding',
                'value' => rand(300, 1000),
            ],
            [
                'title' => 'Hajj Preparation Program',
                'description' => 'Seeking guidance and preparation for upcoming Hajj pilgrimage',
                'value' => rand(500, 2000),
            ],
            [
                'title' => 'Islamic Finance Consultation',
                'description' => 'Needs advice on Islamic banking and Sharia-compliant investments',
                'value' => rand(400, 1500),
            ],
            [
                'title' => 'Youth Islamic Development Program',
                'description' => 'Parent interested in Islamic character development program for children',
                'value' => rand(250, 750),
            ],
            [
                'title' => 'Islamic Wedding Ceremony Services',
                'description' => 'Planning Islamic wedding ceremony with proper religious guidance',
                'value' => rand(800, 3000),
            ],
            [
                'title' => 'Zakat Calculation and Distribution',
                'description' => 'Needs help with proper Zakat calculation and distribution channels',
                'value' => rand(100, 500),
            ],
            [
                'title' => 'Islamic Books and Literature',
                'description' => 'Interested in purchasing Islamic books and educational materials',
                'value' => rand(50, 400),
            ],
            [
                'title' => 'Community Iftar Sponsorship',
                'description' => 'Wants to sponsor community Iftar during Ramadan',
                'value' => rand(1000, 5000),
            ],
            [
                'title' => 'Islamic Counseling Services',
                'description' => 'Seeking Islamic counseling for family and personal matters',
                'value' => rand(200, 800),
            ],
            [
                'title' => 'Mosque Donation Program',
                'description' => 'Interested in contributing to mosque construction or maintenance',
                'value' => rand(500, 10000),
            ],
        ];
    }

    private function determineLeadStatus(Carbon $createdAt): string
    {
        $daysOld = $createdAt->diffInDays(Carbon::now());

        if ($daysOld < 7) {
            // New leads
            $weights = ['new' => 60, 'contacted' => 30, 'engaged' => 10];
        } elseif ($daysOld < 30) {
            // Recent leads
            $weights = ['contacted' => 40, 'engaged' => 30, 'qualified' => 20, 'disqualified' => 10];
        } elseif ($daysOld < 60) {
            // Older leads
            $weights = ['qualified' => 35, 'engaged' => 25, 'converted' => 20, 'disqualified' => 20];
        } else {
            // Very old leads
            $weights = ['converted' => 40, 'disqualified' => 35, 'qualified' => 15, 'engaged' => 10];
        }

        return $this->weightedRandom($weights);
    }

    private function determineProbability(string $status, ?string $opportunityStatus = null): int
    {
        // If there's an opportunity status, use that for probability calculation
        if ($opportunityStatus) {
            $opportunityProbabilities = [
                'contacted' => rand(20, 40),
                'info_sent' => rand(30, 50),
                'negotiation' => rand(60, 80),
                'waiting_payment' => rand(80, 95),
                'closed_won' => 100,
                'closed_lost' => 0,
            ];
            return $opportunityProbabilities[$opportunityStatus] ?? rand(20, 50);
        }

        // Otherwise use lead status for probability
        $statusProbabilities = [
            'new' => rand(10, 30),
            'contacted' => rand(20, 40),
            'engaged' => rand(30, 50),
            'qualified' => rand(40, 70),
            'converted' => 100,
            'disqualified' => 0,
        ];

        return $statusProbabilities[$status] ?? 50;
    }

    private function determinePriority(float $estimatedValue, string $source): string
    {
        // Higher value leads get higher priority (only use: low, medium, high)
        if ($estimatedValue > 1000) {
            $weights = ['high' => 60, 'medium' => 30, 'low' => 10];
        } elseif ($estimatedValue > 500) {
            $weights = ['high' => 40, 'medium' => 40, 'low' => 20];
        } else {
            $weights = ['low' => 50, 'medium' => 40, 'high' => 10];
        }

        // Adjust based on source - referral sources get higher priority
        if (in_array($source, ['referral', 'whatsapp'])) {
            $weights['high'] = min(80, $weights['high'] + 20);
            $weights['medium'] = max(10, $weights['medium'] + 10);
            $weights['low'] = max(10, $weights['low'] - 30);
        }

        return $this->weightedRandom($weights);
    }

    private function generateLeadNotes(string $status, string $title): string
    {
        $notes = [
            'new' => [
                'Initial inquiry received, need to follow up',
                'Lead submitted through website form',
                'Phone inquiry, basic information collected',
                'Social media engagement, showing interest',
                'Referral from existing client',
            ],
            'contacted' => [
                'First contact made, scheduled follow-up call',
                'Email sent with program information',
                'WhatsApp message exchanged, positive response',
                'Phone conversation completed, gathering requirements',
                'Meeting scheduled for next week',
            ],
            'engaged' => [
                'Active communication established',
                'Showing genuine interest in services',
                'Regular follow-up conversations ongoing',
                'Engaged through multiple touchpoints',
                'Building relationship and trust',
            ],
            'qualified' => [
                'Budget confirmed, timeline discussed',
                'Strong interest shown, decision maker identified',
                'Requirements gathered, preparing proposal',
                'Previous experience with similar programs',
                'Urgent need identified, fast-track process',
            ],
            'converted' => [
                'Successfully converted to client',
                'Deal closed successfully, payment received',
                'Contract signed, program enrollment completed',
                'Registration confirmed, welcome package sent',
                'Payment plan agreed, first installment received',
                'Onboarding process initiated',
            ],
            'disqualified' => [
                'Budget constraints, unable to proceed',
                'Timing not suitable, may revisit later',
                'Chose alternative provider',
                'Internal decision to postpone',
                'Requirements changed, no longer suitable',
                'Not a good fit for our services',
            ],
        ];

        $statusNotes = $notes[$status] ?? $notes['new'];
        $baseNote = $statusNotes[array_rand($statusNotes)];

        return $baseNote . " - {$title}";
    }

    private function weightedRandom(array $weights): string
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        $cumulative = 0;

        foreach ($weights as $option => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $option;
            }
        }

        return array_key_first($weights);
    }

    private function generateMalaysianName(): string
    {
        $maleNames = [
            'Ahmad Zaki bin Hassan', 'Muhammad Amin bin Abdullah', 'Ali Rahman bin Omar',
            'Hassan bin Ibrahim', 'Omar bin Yusof', 'Ibrahim bin Ahmad', 'Yusof bin Ali',
            'Abdullah bin Hassan', 'Rahman bin Omar', 'Amin bin Abdullah'
        ];

        $femaleNames = [
            'Siti Nurhaliza binti Omar', 'Fatimah binti Ahmad', 'Aishah binti Hassan',
            'Khadijah binti Abdullah', 'Maryam binti Ibrahim', 'Zainab binti Yusof',
            'Aminah binti Ali', 'Hafsah binti Rahman', 'Safiyyah binti Omar'
        ];

        $allNames = array_merge($maleNames, $femaleNames);
        return $allNames[array_rand($allNames)];
    }

    private function generateEmail(string $name): string
    {
        $nameParts = explode(' ', strtolower($name));
        $firstName = $nameParts[0] ?? 'user';
        $lastName = $nameParts[1] ?? 'name';

        $domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
        $domain = $domains[array_rand($domains)];

        return $firstName . '.' . $lastName . rand(1, 999) . '@' . $domain;
    }

    private function generateMalaysianPhone(): string
    {
        $prefixes = ['012', '013', '014', '016', '017', '018', '019'];
        $prefix = $prefixes[array_rand($prefixes)];
        return $prefix . '-' . rand(100, 999) . rand(1000, 9999);
    }

    private function generateCompany(): ?string
    {
        if (rand(1, 100) <= 30) { // 30% have companies
            $companies = [
                'Islamic Education Centre', 'Masjid Al-Hidayah', 'Sekolah Agama Rakyat',
                'Islamic Bookstore Sdn Bhd', 'Pusat Tahfiz Al-Quran', 'Islamic Learning Hub',
                'Madrasah Al-Islamiah', 'Islamic Cultural Centre', 'Dar Al-Hikmah Institute'
            ];
            return $companies[array_rand($companies)];
        }
        return null;
    }

    private function generateUTMData(): array
    {
        $sources = ['facebook', 'instagram', 'google', 'tiktok', 'youtube', 'whatsapp'];
        $campaigns = ['ramadan2024', 'back_to_school', 'islamic_books', 'quran_course', 'arabic_class'];
        $mediums = ['social', 'cpc', 'organic', 'referral', 'email'];

        return [
            'utm_source' => $sources[array_rand($sources)],
            'utm_campaign' => $campaigns[array_rand($campaigns)],
            'utm_medium' => $mediums[array_rand($mediums)],
            'utm_content' => 'ad_' . rand(1, 10),
            'utm_term' => 'islamic+education'
        ];
    }

    private function generateChannel(): string
    {
        $channels = ['WhatsApp', 'Facebook', 'Instagram', 'TikTok', 'Website', 'Google', 'Referral'];
        return $channels[array_rand($channels)];
    }

    private function generateLeadTags(): array
    {
        $allTags = [
            'islamic-education', 'quran-learning', 'arabic-language', 'children-books',
            'adult-courses', 'online-learning', 'homeschool', 'masjid-program',
            'weekend-class', 'intensive-course'
        ];

        $numTags = rand(1, 3);
        return array_slice($allTags, 0, $numTags);
    }
}
