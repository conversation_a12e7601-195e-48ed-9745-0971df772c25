<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing products
        Product::truncate();

        $this->command->info('Generating Islamic books and merchandise products...');

        $products = [
            // Islamic Books
            [
                'name' => 'Al-Quran Al-Kareem with Translation',
                'description' => 'Complete Quran with Malay and English translation, beautiful calligraphy',
                'price' => 45.00,
                'cost' => 25.00,
                'sku' => 'QURAN-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 200,
                'min_stock_level' => 20,
            ],
            [
                'name' => 'Sahih Bukhari Collection',
                'description' => 'Complete collection of Sahih Bukhari hadith with commentary',
                'price' => 120.00,
                'cost' => 70.00,
                'sku' => 'BUKHARI-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 50,
                'min_stock_level' => 5,
            ],
            [
                'name' => '<PERSON><PERSON> - Prophet Biography',
                'description' => 'Comprehensive biography of Prophet <PERSON> (PBUH)',
                'price' => 35.00,
                'cost' => 20.00,
                'sku' => 'SIRAH-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 150,
                'min_stock_level' => 15,
            ],
            [
                'name' => 'Islamic Jurisprudence (Fiqh) Guide',
                'description' => 'Practical guide to Islamic jurisprudence for daily life',
                'price' => 55.00,
                'cost' => 30.00,
                'sku' => 'FIQH-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 100,
                'min_stock_level' => 10,
            ],
            [
                'name' => 'Tafsir Ibn Kathir (Abridged)',
                'description' => 'Abridged version of the famous Quran commentary by Ibn Kathir',
                'price' => 85.00,
                'cost' => 50.00,
                'sku' => 'TAFSIR-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 75,
                'min_stock_level' => 8,
            ],
            [
                'name' => 'Islamic History - Golden Age',
                'description' => 'Comprehensive history of Islamic civilization and golden age',
                'price' => 40.00,
                'cost' => 22.00,
                'sku' => 'HISTORY-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 120,
                'min_stock_level' => 12,
            ],
            [
                'name' => 'Dua and Dhikr Collection',
                'description' => 'Collection of authentic duas and dhikr with Arabic and translation',
                'price' => 25.00,
                'cost' => 15.00,
                'sku' => 'DUA-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 300,
                'min_stock_level' => 30,
            ],
            [
                'name' => 'Islamic Parenting Guide',
                'description' => 'Practical guide for raising children according to Islamic principles',
                'price' => 30.00,
                'cost' => 18.00,
                'sku' => 'PARENT-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 180,
                'min_stock_level' => 18,
            ],

            // Children's Islamic Books
            [
                'name' => 'My First Quran Stories',
                'description' => 'Beautiful illustrated Quran stories for children',
                'price' => 20.00,
                'cost' => 12.00,
                'sku' => 'CHILD-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 250,
                'min_stock_level' => 25,
            ],
            [
                'name' => 'Islamic Coloring Book',
                'description' => 'Educational coloring book with Islamic themes and Arabic letters',
                'price' => 15.00,
                'cost' => 8.00,
                'sku' => 'COLOR-001',
                'category' => 'Books',
                'status' => 'active',
                'stock_quantity' => 400,
                'min_stock_level' => 40,
            ],

            // Prayer Items
            [
                'name' => 'Premium Prayer Mat (Sajadah)',
                'description' => 'High-quality prayer mat with beautiful Islamic patterns',
                'price' => 65.00,
                'cost' => 35.00,
                'sku' => 'PRAYER-001',
                'category' => 'Prayer Items',
                'status' => 'active',
                'stock_quantity' => 150,
                'min_stock_level' => 15,
            ],
            [
                'name' => 'Digital Quran Speaker',
                'description' => 'Portable Quran recitation speaker with multiple reciters',
                'price' => 120.00,
                'cost' => 70.00,
                'sku' => 'SPEAKER-001',
                'category' => 'Electronics',
                'status' => 'active',
                'stock_quantity' => 80,
                'min_stock_level' => 8,
            ],
            [
                'name' => 'Prayer Beads (Tasbih)',
                'description' => 'Traditional 99-bead tasbih for dhikr and prayer',
                'price' => 25.00,
                'cost' => 12.00,
                'sku' => 'TASBIH-001',
                'category' => 'Prayer Items',
                'status' => 'active',
                'stock_quantity' => 200,
                'min_stock_level' => 20,
            ],
            [
                'name' => 'Qibla Compass',
                'description' => 'Accurate compass for finding Qibla direction',
                'price' => 35.00,
                'cost' => 20.00,
                'sku' => 'COMPASS-001',
                'category' => 'Prayer Items',
                'status' => 'active',
                'stock_quantity' => 100,
                'min_stock_level' => 10,
            ],

            // Islamic Clothing
            [
                'name' => 'Men\'s Thobe (Jubah)',
                'description' => 'Traditional Islamic robe for men, high-quality cotton',
                'price' => 85.00,
                'cost' => 45.00,
                'sku' => 'THOBE-001',
                'category' => 'Clothing',
                'status' => 'active',
                'stock_quantity' => 60,
                'min_stock_level' => 6,
            ],
            [
                'name' => 'Women\'s Abaya',
                'description' => 'Elegant black abaya with beautiful embroidery',
                'price' => 95.00,
                'cost' => 50.00,
                'sku' => 'ABAYA-001',
                'category' => 'Clothing',
                'status' => 'active',
                'stock_quantity' => 80,
                'min_stock_level' => 8,
            ],
            [
                'name' => 'Prayer Cap (Kopiah)',
                'description' => 'Traditional Islamic prayer cap for men',
                'price' => 20.00,
                'cost' => 10.00,
                'sku' => 'KOPIAH-001',
                'category' => 'Clothing',
                'status' => 'active',
                'stock_quantity' => 150,
                'min_stock_level' => 15,
            ],
            [
                'name' => 'Hijab Collection Set',
                'description' => 'Set of 5 high-quality hijabs in different colors',
                'price' => 75.00,
                'cost' => 40.00,
                'sku' => 'HIJAB-001',
                'category' => 'Clothing',
                'status' => 'active',
                'stock_quantity' => 100,
                'min_stock_level' => 10,
            ],

            // Islamic Art & Decor
            [
                'name' => 'Islamic Calligraphy Wall Art',
                'description' => 'Beautiful framed Islamic calligraphy for home decoration',
                'price' => 150.00,
                'cost' => 80.00,
                'sku' => 'ART-001',
                'category' => 'Decor',
                'status' => 'active',
                'stock_quantity' => 50,
                'min_stock_level' => 5,
            ],
            [
                'name' => 'Kaaba Model (Replica)',
                'description' => 'Detailed miniature replica of the Holy Kaaba',
                'price' => 200.00,
                'cost' => 120.00,
                'sku' => 'KAABA-001',
                'category' => 'Decor',
                'status' => 'active',
                'stock_quantity' => 30,
                'min_stock_level' => 3,
            ],
            [
                'name' => 'Islamic Wall Clock',
                'description' => 'Wall clock with Islamic design and prayer times display',
                'price' => 80.00,
                'cost' => 45.00,
                'sku' => 'CLOCK-001',
                'category' => 'Decor',
                'status' => 'active',
                'stock_quantity' => 70,
                'min_stock_level' => 7,
            ],

            // Educational Materials
            [
                'name' => 'Arabic Alphabet Learning Set',
                'description' => 'Interactive learning set for Arabic alphabet with audio',
                'price' => 45.00,
                'cost' => 25.00,
                'sku' => 'ARABIC-001',
                'category' => 'Education',
                'status' => 'active',
                'stock_quantity' => 120,
                'min_stock_level' => 12,
            ],
            [
                'name' => 'Islamic Studies Curriculum',
                'description' => 'Complete Islamic studies curriculum for homeschooling',
                'price' => 180.00,
                'cost' => 100.00,
                'sku' => 'CURRICULUM-001',
                'category' => 'Education',
                'status' => 'active',
                'stock_quantity' => 40,
                'min_stock_level' => 4,
            ],

            // Gifts & Accessories
            [
                'name' => 'Islamic Gift Set',
                'description' => 'Beautiful gift set with Quran, prayer mat, and tasbih',
                'price' => 120.00,
                'cost' => 70.00,
                'sku' => 'GIFT-001',
                'category' => 'Gifts',
                'status' => 'active',
                'stock_quantity' => 60,
                'min_stock_level' => 6,
            ],
            [
                'name' => 'Eid Celebration Package',
                'description' => 'Special Eid celebration package with decorations and gifts',
                'price' => 85.00,
                'cost' => 50.00,
                'sku' => 'EID-001',
                'category' => 'Gifts',
                'status' => 'active',
                'stock_quantity' => 90,
                'min_stock_level' => 9,
            ],
        ];

        $progressBar = $this->command->getOutput()->createProgressBar(count($products));
        $progressBar->start();

        foreach ($products as $product) {
            Product::create($product);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->command->newLine();
        $this->command->info('Successfully created ' . count($products) . ' Islamic products!');
    }
}
