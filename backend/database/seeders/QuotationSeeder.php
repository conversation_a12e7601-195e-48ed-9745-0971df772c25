<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Quotation;
use App\Models\QuotationItem;
use App\Models\Client;
use App\Models\Lead;
use App\Models\Deal;
use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;

class QuotationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Generating realistic quotations for Islamic organization...');

        $clients = Client::all();
        $products = Product::all();
        $users = User::all();

        if ($clients->isEmpty() || $products->isEmpty() || $users->isEmpty()) {
            $this->command->warn('Please run ClientSeeder, ProductSeeder, and UserSeeder first.');
            return;
        }

        $quotationTitles = [
            'Islamic Education Program Package',
            'Hajj & Umrah Travel Services',
            'Mosque Construction Consultation',
            'Islamic Book Publishing Services',
            'Halal Certification Consultation',
            'Islamic Wedding Event Planning',
            'Zakat Management System',
            'Islamic Finance Advisory',
            'Community Center Development',
            'Islamic Art & Calligraphy Commission',
            'Madrasah Curriculum Development',
            'Islamic Conference Organization',
            'Halal Restaurant Setup',
            'Islamic Banking Software',
            'Quran Recitation Training',
            'Islamic Legal Consultation',
            'Mosque Audio System Installation',
            'Islamic Children Education Program',
            'Halal Food Distribution',
            'Islamic Charity Management System'
        ];

        $statuses = [
            Quotation::STATUS_DRAFT => 15,
            Quotation::STATUS_SENT => 25,
            Quotation::STATUS_VIEWED => 20,
            Quotation::STATUS_ACCEPTED => 15,
            Quotation::STATUS_REJECTED => 10,
            Quotation::STATUS_EXPIRED => 5,
        ];

        $priorities = ['low', 'medium', 'high', 'urgent'];
        $currencies = ['MYR', 'USD', 'SGD'];

        $quotationCount = 0;
        $totalQuotations = 150;

        $progressBar = $this->command->getOutput()->createProgressBar($totalQuotations);

        foreach ($statuses as $status => $count) {
            for ($i = 0; $i < $count; $i++) {
                $client = $clients->random();
                $user = $users->random();
                $assignedUser = $users->random();

                // Random date within last 6 months
                $createdAt = Carbon::now()->subDays(rand(1, 180));
                
                $quotation = Quotation::create([
                    'client_id' => rand(1, 10) <= 8 ? $client->id : null, // 80% have clients
                    'lead_id' => null, // No leads for now
                    'deal_id' => null, // No deals for now
                    'created_by' => $user->id,
                    'assigned_to' => $assignedUser->id,
                    'title' => $quotationTitles[array_rand($quotationTitles)],
                    'description' => $this->generateDescription(),
                    'status' => $status,
                    'priority' => $priorities[array_rand($priorities)],
                    'tax_rate' => rand(0, 1) ? 6.00 : 0.00, // SST in Malaysia
                    'discount_amount' => rand(0, 1) ? rand(50, 500) : 0,
                    'currency' => $currencies[array_rand($currencies)],
                    'terms_conditions' => $this->generateTermsConditions(),
                    'notes' => rand(0, 1) ? $this->generateNotes() : null,
                    'internal_notes' => rand(0, 1) ? 'Internal: ' . $this->generateNotes() : null,
                    'validity_days' => [15, 30, 45, 60][array_rand([15, 30, 45, 60])],
                    'expected_close_date' => $createdAt->copy()->addDays(rand(7, 60)),
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt->copy()->addDays(rand(0, 5)),
                ]);

                // Set status-specific timestamps
                $this->setStatusTimestamps($quotation, $status, $createdAt);

                // Add 2-5 items to each quotation
                $itemCount = rand(2, 5);
                for ($j = 0; $j < $itemCount; $j++) {
                    $product = $products->random();
                    $quantity = rand(1, 10);
                    $unitPrice = $product->price * (rand(80, 120) / 100); // ±20% variation
                    
                    QuotationItem::create([
                        'quotation_id' => $quotation->id,
                        'product_id' => rand(0, 1) ? $product->id : null,
                        'item_name' => $product->name,
                        'description' => $product->description,
                        'sku' => $product->sku,
                        'unit' => ['pcs', 'hours', 'sessions', 'months'][array_rand(['pcs', 'hours', 'sessions', 'months'])],
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'discount_rate' => rand(0, 1) ? rand(5, 15) : 0,
                        'discount_amount' => 0,
                        'sort_order' => $j + 1,
                    ]);
                }

                // Calculate totals
                $quotation->calculateTotals();

                $quotationCount++;
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->command->newLine();
        $this->command->info("Generated {$quotationCount} quotations successfully.");
    }

    private function setStatusTimestamps(Quotation $quotation, string $status, Carbon $createdAt): void
    {
        switch ($status) {
            case Quotation::STATUS_SENT:
                $quotation->update(['sent_at' => $createdAt->copy()->addHours(rand(1, 48))]);
                break;
            case Quotation::STATUS_VIEWED:
                $sentAt = $createdAt->copy()->addHours(rand(1, 24));
                $viewedAt = $sentAt->copy()->addHours(rand(1, 72));
                $quotation->update([
                    'sent_at' => $sentAt,
                    'viewed_at' => $viewedAt,
                    'view_count' => rand(1, 5),
                ]);
                break;
            case Quotation::STATUS_ACCEPTED:
                $sentAt = $createdAt->copy()->addHours(rand(1, 24));
                $viewedAt = $sentAt->copy()->addHours(rand(1, 48));
                $acceptedAt = $viewedAt->copy()->addHours(rand(1, 168));
                $quotation->update([
                    'sent_at' => $sentAt,
                    'viewed_at' => $viewedAt,
                    'accepted_at' => $acceptedAt,
                    'view_count' => rand(2, 8),
                ]);
                break;
            case Quotation::STATUS_REJECTED:
                $sentAt = $createdAt->copy()->addHours(rand(1, 24));
                $viewedAt = $sentAt->copy()->addHours(rand(1, 48));
                $rejectedAt = $viewedAt->copy()->addHours(rand(1, 336));
                $quotation->update([
                    'sent_at' => $sentAt,
                    'viewed_at' => $viewedAt,
                    'rejected_at' => $rejectedAt,
                    'view_count' => rand(1, 4),
                ]);
                break;
            case Quotation::STATUS_EXPIRED:
                $sentAt = $createdAt->copy()->addHours(rand(1, 24));
                $quotation->update([
                    'sent_at' => $sentAt,
                    'valid_until' => $createdAt->copy()->subDays(rand(1, 30)),
                    'view_count' => rand(0, 2),
                ]);
                break;
        }
    }

    private function generateDescription(): string
    {
        $descriptions = [
            'Comprehensive Islamic services package tailored to your organization\'s needs.',
            'Professional consultation and implementation services for Islamic institutions.',
            'Complete solution including planning, execution, and ongoing support.',
            'Customized package designed according to Islamic principles and best practices.',
            'Full-service offering with dedicated support and Islamic compliance.',
            'End-to-end solution with emphasis on quality and Islamic values.',
            'Professional services with experienced Islamic consultants and specialists.',
            'Integrated approach combining traditional Islamic knowledge with modern solutions.',
        ];

        return $descriptions[array_rand($descriptions)];
    }

    private function generateTermsConditions(): string
    {
        return "1. Payment terms: 50% advance, 50% upon completion\n" .
               "2. All services will be conducted according to Islamic principles\n" .
               "3. Delivery timeline as specified in project schedule\n" .
               "4. Any changes to scope will require written approval\n" .
               "5. Confidentiality will be maintained for all client information\n" .
               "6. Dispute resolution through Islamic arbitration if required";
    }

    private function generateNotes(): string
    {
        $notes = [
            'Client requested expedited delivery for upcoming Islamic event.',
            'Special consideration for Ramadan and Eid schedules.',
            'Requires approval from Islamic advisory board.',
            'Budget constraints discussed, flexible payment options available.',
            'Follow-up meeting scheduled to discuss implementation details.',
            'Client emphasized importance of Islamic compliance throughout.',
            'Coordination required with local Islamic authorities.',
            'Timeline adjusted to accommodate prayer times and Islamic holidays.',
        ];

        return $notes[array_rand($notes)];
    }
}
