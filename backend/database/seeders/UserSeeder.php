<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing users
        User::truncate();

        $this->command->info('Creating users for Islamic organization...');

        $users = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456789',
                'role' => 'admin',
                'department' => 'Management',
                'is_active' => true,
                'last_login_at' => now()->subDays(1),
                'created_at' => now()->subMonths(6),
                'updated_at' => now()->subDays(1),
            ],
            [
                'name' => 'Fatimah Ali',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456790',
                'role' => 'manager',
                'department' => 'Sales',
                'is_active' => true,
                'last_login_at' => now()->subHours(2),
                'created_at' => now()->subMonths(5),
                'updated_at' => now()->subHours(2),
            ],
            [
                'name' => 'Muhammad Hassan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456791',
                'role' => 'user',
                'department' => 'Sales',
                'is_active' => true,
                'last_login_at' => now()->subHours(4),
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subHours(4),
            ],
            [
                'name' => 'Aishah Omar',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456792',
                'role' => 'user',
                'department' => 'Marketing',
                'is_active' => true,
                'last_login_at' => now()->subHours(6),
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subHours(6),
            ],
            [
                'name' => 'Ibrahim Yusof',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456793',
                'role' => 'user',
                'department' => 'Customer Service',
                'is_active' => true,
                'last_login_at' => now()->subHours(8),
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subHours(8),
            ],
            [
                'name' => 'Zainab Mahmud',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456794',
                'role' => 'manager',
                'department' => 'Operations',
                'is_active' => true,
                'last_login_at' => now()->subDays(2),
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subDays(2),
            ],
            [
                'name' => 'Omar Abdullah',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456795',
                'role' => 'user',
                'department' => 'Sales',
                'is_active' => true,
                'last_login_at' => now()->subDays(1),
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subDays(1),
            ],
            [
                'name' => 'Khadijah Ismail',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+60123456796',
                'role' => 'user',
                'department' => 'Finance',
                'is_active' => true,
                'last_login_at' => now()->subHours(12),
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subHours(12),
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }

        $this->command->info('Created ' . count($users) . ' users successfully.');
    }
}
