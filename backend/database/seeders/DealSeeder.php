<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Deal;
use App\Models\Lead;
use App\Models\Client;
use App\Models\User;
use Carbon\Carbon;

class DealSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing deals
        Deal::truncate();

        $this->command->info('Generating realistic deals for Islamic organization...');

        $leads = Lead::all();
        $clients = Client::all();
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        // Generate 150-200 deals with realistic distribution
        $totalDeals = rand(150, 200);
        $progressBar = $this->command->getOutput()->createProgressBar($totalDeals);
        $progressBar->start();

        $deals = [];

        for ($i = 0; $i < $totalDeals; $i++) {
            $dealData = $this->generateDealData($leads, $clients, $users);
            $deals[] = $dealData;

            $progressBar->advance();

            // Insert in batches
            if (count($deals) >= 25) {
                Deal::insert($deals);
                $deals = [];
            }
        }

        // Insert remaining deals
        if (!empty($deals)) {
            Deal::insert($deals);
        }

        $progressBar->finish();
        $this->command->newLine();
        $this->command->info("Generated {$totalDeals} deals successfully.");
    }

    private function generateDealData($leads, $clients, $users): array
    {
        // Ensure proper data relationships
        $leadId = null;
        $clientId = null;

        // 60% of deals come from converted leads (ensuring name consistency)
        if (rand(1, 100) <= 60 && $leads->isNotEmpty()) {
            $selectedLead = $leads->random();
            $leadId = $selectedLead->id;

            // If lead has a converted client, use that client
            if ($selectedLead->converted_to_client_id) {
                $clientId = $selectedLead->converted_to_client_id;
            } else {
                // Find a client with the same name/email as the lead
                $matchingClient = $clients->where('email', $selectedLead->email)->first();
                if ($matchingClient) {
                    $clientId = $matchingClient->id;
                }
            }
        } else {
            // 40% are direct deals with existing clients (no lead conversion)
            if ($clients->isNotEmpty()) {
                $clientId = $clients->random()->id;
            }
        }

        $assignedTo = $users->random()->id;
        $createdBy = $users->random()->id;

        $dealTypes = $this->getDealTypes();
        $dealType = $dealTypes[array_rand($dealTypes)];

        $stages = ['prospecting', 'qualification', 'proposal', 'negotiation', 'closing', 'won', 'lost', 'on_hold'];
        $priorities = ['Low', 'Medium', 'High', 'Critical'];
        $dealSizes = ['Small', 'Medium', 'Large', 'Enterprise'];
        $sources = ['facebook', 'instagram', 'tiktok', 'google', 'youtube', 'whatsapp', 'referral', 'website', 'email', 'phone', 'event'];

        // Create realistic timeline (deals created over last 18 months)
        $createdAt = Carbon::now()->subDays(rand(0, 540));
        
        // Determine stage based on age
        $stage = $this->determineDealStage($createdAt);
        
        // Set probability based on stage
        $probability = Deal::getDefaultProbabilityForStage($stage);
        
        // Add some variance to probability
        $probability = max(0, min(100, $probability + rand(-15, 15)));

        // Generate deal value based on size and type
        $value = $this->generateDealValue($dealType['size'], $stage);
        
        $expectedCloseDate = $this->generateExpectedCloseDate($createdAt, $stage);
        $actualCloseDate = in_array($stage, ['won', 'lost']) ? $this->generateActualCloseDate($expectedCloseDate) : null;
        
        $priority = $this->determinePriority($value, $stage);
        
        // Generate UTM data for some deals
        $utmData = $this->generateUTMData();

        $tags = $this->generateTags($dealType['category']);
        $competitors = $this->generateCompetitors();

        $stageChangedAt = $createdAt->copy()->addDays(rand(1, 30));
        $lastActivity = $createdAt->copy()->addDays(rand(0, 60));
        $nextFollowUp = $stage !== 'won' && $stage !== 'lost' ? $lastActivity->copy()->addDays(rand(1, 14)) : null;

        $dealNumber = $this->generateDealNumber($createdAt);

        // Get the user who changed the stage (should be assigned user or creator)
        $stageChangedByUser = $users->find($assignedTo) ?? $users->find($createdBy) ?? $users->first();

        return [
            'lead_id' => $leadId,
            'client_id' => $clientId,
            'assigned_to' => $assignedTo,
            'created_by' => $createdBy,
            'title' => $dealType['title'],
            'description' => $dealType['description'],
            'deal_number' => $dealNumber,
            'value' => $value,
            'expected_revenue' => $value,
            'actual_revenue' => $stage === 'won' ? $value : ($stage === 'lost' ? 0 : null),
            'currency' => 'MYR',
            'pipeline_stage' => $stage,
            'stage_order' => array_search($stage, $stages) + 1,
            'stage_changed_at' => $stageChangedAt,
            'stage_changed_by' => $stageChangedByUser->name,
            'probability' => $probability,
            'deal_size' => $dealType['size'],
            'expected_close_date' => $expectedCloseDate,
            'actual_close_date' => $actualCloseDate,
            'days_in_pipeline' => $createdAt->diffInDays(Carbon::now()),
            'days_in_current_stage' => $stageChangedAt->diffInDays(Carbon::now()),
            'source' => $sources[array_rand($sources)],
            'utm_source' => $utmData['utm_source'],
            'utm_campaign' => $utmData['utm_campaign'],
            'utm_medium' => $utmData['utm_medium'],
            'priority' => $priority,
            'deal_type' => $dealType['type'],
            'tags' => json_encode($tags),
            'competitors' => json_encode($competitors),
            'competitive_advantage' => $this->generateCompetitiveAdvantage(),
            'win_probability_reason' => $this->generateWinReason($stage),
            'notes' => $this->generateDealNotes($stage, $dealType['title']),
            'internal_notes' => $this->generateInternalNotes($stage),
            'last_activity' => $lastActivity,
            'next_follow_up' => $nextFollowUp,
            'next_action' => $this->generateNextAction($stage),
            'automation_triggers' => json_encode($this->generateAutomationTriggers()),
            'auto_follow_up_enabled' => rand(0, 1),
            'follow_up_frequency_days' => rand(3, 14),
            'documents' => json_encode($this->generateDocuments($stage)),
            'proposal_documents' => json_encode($this->generateProposalDocuments($stage)),
            'contract_documents' => json_encode($this->generateContractDocuments($stage)),
            'loss_reason' => $stage === 'lost' ? $this->generateLossReason() : null,
            'loss_details' => $stage === 'lost' ? $this->generateLossDetails() : null,
            'competitor_won' => $stage === 'lost' && rand(0, 1) ? $this->generateCompetitorWon() : null,
            'external_id' => null,
            'custom_fields' => json_encode($this->generateCustomFields()),
            'created_at' => $createdAt,
            'updated_at' => $lastActivity,
        ];
    }

    private function getDealTypes(): array
    {
        return [
            [
                'title' => 'Islamic Book Publishing Package',
                'description' => 'Comprehensive book publishing and distribution package for Islamic literature',
                'category' => 'publishing',
                'type' => 'New Business',
                'size' => 'Medium'
            ],
            [
                'title' => 'Quran Memorization Program',
                'description' => 'Complete Quran memorization program with certified instructors',
                'category' => 'education',
                'type' => 'New Business',
                'size' => 'Large'
            ],
            [
                'title' => 'Islamic Event Management',
                'description' => 'Full-service Islamic event planning and management',
                'category' => 'events',
                'type' => 'New Business',
                'size' => 'Medium'
            ],
            [
                'title' => 'Halal Certification Consulting',
                'description' => 'Halal certification process guidance and consulting services',
                'category' => 'consulting',
                'type' => 'New Business',
                'size' => 'Small'
            ],
            [
                'title' => 'Islamic Finance Training',
                'description' => 'Professional Islamic finance and banking training program',
                'category' => 'training',
                'type' => 'New Business',
                'size' => 'Large'
            ],
            [
                'title' => 'Mosque Construction Project',
                'description' => 'Complete mosque construction and interior design project',
                'category' => 'construction',
                'type' => 'New Business',
                'size' => 'Enterprise'
            ],
            [
                'title' => 'Islamic School Curriculum',
                'description' => 'Comprehensive Islamic education curriculum development',
                'category' => 'education',
                'type' => 'New Business',
                'size' => 'Large'
            ],
            [
                'title' => 'Hajj & Umrah Package',
                'description' => 'Premium Hajj and Umrah pilgrimage travel package',
                'category' => 'travel',
                'type' => 'New Business',
                'size' => 'Medium'
            ],
            [
                'title' => 'Islamic Art Exhibition',
                'description' => 'Islamic art and calligraphy exhibition organization',
                'category' => 'events',
                'type' => 'New Business',
                'size' => 'Small'
            ],
            [
                'title' => 'Zakat Management System',
                'description' => 'Digital zakat collection and distribution management system',
                'category' => 'technology',
                'type' => 'New Business',
                'size' => 'Large'
            ],
        ];
    }

    private function determineDealStage(Carbon $createdAt): string
    {
        $daysOld = $createdAt->diffInDays(Carbon::now());

        if ($daysOld < 30) {
            // New deals
            $weights = ['prospecting' => 40, 'qualification' => 30, 'proposal' => 20, 'negotiation' => 10];
        } elseif ($daysOld < 90) {
            // Recent deals
            $weights = ['qualification' => 25, 'proposal' => 30, 'negotiation' => 25, 'closing' => 15, 'won' => 3, 'lost' => 2];
        } elseif ($daysOld < 180) {
            // Older deals
            $weights = ['proposal' => 15, 'negotiation' => 20, 'closing' => 20, 'won' => 30, 'lost' => 12, 'on_hold' => 3];
        } else {
            // Very old deals
            $weights = ['won' => 60, 'lost' => 30, 'on_hold' => 10];
        }

        return $this->weightedRandom($weights);
    }

    private function generateDealValue(string $size, string $stage): float
    {
        $baseRanges = [
            'Small' => [1000, 10000],
            'Medium' => [10000, 50000],
            'Large' => [50000, 200000],
            'Enterprise' => [200000, 1000000],
        ];

        [$min, $max] = $baseRanges[$size];
        
        // Add some variance
        $variance = 0.3;
        $actualMin = $min * (1 - $variance);
        $actualMax = $max * (1 + $variance);
        
        return round(rand($actualMin, $actualMax), 2);
    }

    private function generateExpectedCloseDate(Carbon $createdAt, string $stage): ?Carbon
    {
        if (in_array($stage, ['won', 'lost'])) {
            return $createdAt->copy()->addDays(rand(30, 120));
        }

        $daysToAdd = match ($stage) {
            'prospecting' => rand(60, 180),
            'qualification' => rand(45, 120),
            'proposal' => rand(30, 90),
            'negotiation' => rand(15, 60),
            'closing' => rand(7, 30),
            'on_hold' => rand(90, 365),
            default => rand(30, 90),
        };

        return $createdAt->copy()->addDays($daysToAdd);
    }

    private function generateActualCloseDate(?Carbon $expectedDate): ?Carbon
    {
        if (!$expectedDate) return null;
        
        // 70% close on time, 30% have delays
        if (rand(1, 100) <= 70) {
            return $expectedDate->copy()->addDays(rand(-7, 7));
        } else {
            return $expectedDate->copy()->addDays(rand(1, 30));
        }
    }

    private function determinePriority(float $value, string $stage): string
    {
        if ($value > 100000) return 'Critical';
        if ($value > 50000) return 'High';
        if ($value > 10000) return 'Medium';
        return 'Low';
    }

    private function generateUTMData(): array
    {
        $sources = ['facebook', 'google', 'instagram', 'tiktok', 'youtube', 'email', 'referral', null];
        $campaigns = ['ramadan2024', 'hajj_package', 'book_launch', 'education_drive', 'winter_sale', null];
        $mediums = ['social', 'email', 'cpc', 'organic', 'referral', null];

        return [
            'utm_source' => $sources[array_rand($sources)],
            'utm_campaign' => $campaigns[array_rand($campaigns)],
            'utm_medium' => $mediums[array_rand($mediums)],
        ];
    }

    private function generateTags(string $category): array
    {
        $tagsByCategory = [
            'publishing' => ['books', 'literature', 'islamic-content', 'education'],
            'education' => ['learning', 'quran', 'islamic-studies', 'certification'],
            'events' => ['conference', 'seminar', 'workshop', 'community'],
            'consulting' => ['advisory', 'halal', 'compliance', 'business'],
            'training' => ['professional', 'skills', 'islamic-finance', 'development'],
            'construction' => ['mosque', 'architecture', 'islamic-design', 'community-center'],
            'travel' => ['hajj', 'umrah', 'pilgrimage', 'spiritual-journey'],
            'technology' => ['digital', 'software', 'automation', 'zakat'],
        ];

        $baseTags = $tagsByCategory[$category] ?? ['islamic', 'service'];
        $commonTags = ['high-priority', 'follow-up', 'qualified', 'hot-lead', 'referral'];
        
        $selectedTags = array_slice($baseTags, 0, rand(1, 3));
        if (rand(1, 100) <= 30) {
            $selectedTags[] = $commonTags[array_rand($commonTags)];
        }

        return $selectedTags;
    }

    private function generateCompetitors(): array
    {
        $competitors = [
            'Islamic Publishing House',
            'Al-Hidayah Education Center',
            'Barakah Event Management',
            'Halal Certification Board',
            'Islamic Finance Institute',
            'Masjid Construction Co',
            'Noor Travel Agency',
            'Ummah Tech Solutions',
        ];

        $count = rand(0, 3);
        if ($count === 0) return [];

        return array_slice(array_unique(array_map(function() use ($competitors) {
            return $competitors[array_rand($competitors)];
        }, range(1, $count))), 0, $count);
    }

    private function generateCompetitiveAdvantage(): ?string
    {
        $advantages = [
            'Strong community relationships and trust',
            'Comprehensive Islamic knowledge and expertise',
            'Competitive pricing with flexible payment terms',
            'Proven track record in Islamic services',
            'Personalized approach and dedicated support',
            'High-quality standards and certifications',
            'Extensive network of Islamic scholars',
            'Innovative technology solutions',
            'Fast delivery and reliable service',
            'Exclusive partnerships and resources',
        ];

        return rand(1, 100) <= 70 ? $advantages[array_rand($advantages)] : null;
    }

    private function generateWinReason(string $stage): ?string
    {
        if (!in_array($stage, ['proposal', 'negotiation', 'closing', 'won'])) {
            return null;
        }

        $reasons = [
            'Strong Relationship',
            'Best Price',
            'Superior Product',
            'Incumbent Advantage',
            'Strategic Partnership',
            'Unique Solution',
        ];

        return $reasons[array_rand($reasons)];
    }

    private function generateDealNotes(string $stage, string $title): string
    {
        $notesByStage = [
            'prospecting' => [
                "Initial contact made regarding {$title}. Client shows strong interest.",
                "Prospect identified through referral. Scheduling discovery call.",
                "Lead generated from website inquiry. Following up on requirements.",
                "Cold outreach successful. Client interested in learning more.",
            ],
            'qualification' => [
                "Budget confirmed for {$title}. Decision maker identified.",
                "Requirements gathering completed. Strong fit for our services.",
                "BANT qualification passed. Moving to proposal stage.",
                "Client has urgent need. Timeline discussed and agreed.",
            ],
            'proposal' => [
                "Detailed proposal sent for {$title}. Awaiting client feedback.",
                "Presentation scheduled with decision makers next week.",
                "Proposal customized based on specific requirements.",
                "Competitive analysis provided. Highlighting our advantages.",
            ],
            'negotiation' => [
                "Negotiating terms and pricing for {$title}.",
                "Client requesting payment plan options. Working on solution.",
                "Timeline adjustments being discussed. Flexible approach needed.",
                "Final contract terms under review. Close to agreement.",
            ],
            'closing' => [
                "Contract ready for signature. Final approval pending.",
                "All terms agreed. Waiting for final decision maker approval.",
                "Legal review completed. Ready to close this week.",
                "Payment terms finalized. Closing scheduled for next week.",
            ],
            'won' => [
                "Deal successfully closed! {$title} contract signed.",
                "Client very satisfied with proposal. Project starting next month.",
                "Excellent outcome. Client already discussing future opportunities.",
                "Smooth closing process. Implementation team notified.",
            ],
            'lost' => [
                "Deal lost to competitor. Price was the main factor.",
                "Client decided to postpone project due to budget constraints.",
                "Lost contact with decision maker. Unable to progress.",
                "Client chose different approach. Maintaining relationship for future.",
            ],
            'on_hold' => [
                "Project temporarily on hold due to internal changes.",
                "Client requested to pause while reviewing budget.",
                "Waiting for new fiscal year to restart discussions.",
                "On hold pending regulatory approval. Monitoring situation.",
            ],
        ];

        $notes = $notesByStage[$stage] ?? ["General notes for {$title}."];
        return $notes[array_rand($notes)];
    }

    private function generateInternalNotes(string $stage): ?string
    {
        $internalNotes = [
            "High-value client. Ensure premium service delivery.",
            "Referred by existing client. Handle with extra care.",
            "Competitive situation. Monitor competitor activities.",
            "Budget sensitive. Focus on value proposition.",
            "Quick decision maker. Fast response required.",
            "Technical requirements complex. Involve specialist team.",
            "Long sales cycle expected. Maintain regular contact.",
            "Strategic account. Senior management involvement needed.",
        ];

        return rand(1, 100) <= 60 ? $internalNotes[array_rand($internalNotes)] : null;
    }

    private function generateNextAction(string $stage): ?string
    {
        if (in_array($stage, ['won', 'lost'])) {
            return null;
        }

        $actionsByStage = [
            'prospecting' => ['Schedule discovery call', 'Send information packet', 'Follow up on initial inquiry'],
            'qualification' => ['Conduct needs assessment', 'Prepare detailed proposal', 'Schedule stakeholder meeting'],
            'proposal' => ['Follow up on proposal', 'Schedule presentation', 'Address client questions'],
            'negotiation' => ['Revise contract terms', 'Schedule final meeting', 'Prepare payment options'],
            'closing' => ['Send final contract', 'Schedule signing meeting', 'Coordinate implementation team'],
            'on_hold' => ['Check project status', 'Maintain relationship', 'Monitor for restart signals'],
        ];

        $actions = $actionsByStage[$stage] ?? ['Follow up with client'];
        return $actions[array_rand($actions)];
    }

    private function generateAutomationTriggers(): array
    {
        $triggers = [];
        
        if (rand(1, 100) <= 30) {
            $triggers[] = [
                'type' => 'follow_up_reminder',
                'days' => rand(3, 7),
                'action' => 'send_email'
            ];
        }
        
        if (rand(1, 100) <= 20) {
            $triggers[] = [
                'type' => 'stage_timeout',
                'days' => rand(14, 30),
                'action' => 'notify_manager'
            ];
        }

        return $triggers;
    }

    private function generateDocuments(string $stage): array
    {
        $documents = [];
        
        if (rand(1, 100) <= 40) {
            $documents[] = 'client_requirements.pdf';
        }
        
        if (in_array($stage, ['proposal', 'negotiation', 'closing', 'won']) && rand(1, 100) <= 60) {
            $documents[] = 'initial_proposal.pdf';
        }
        
        if (in_array($stage, ['closing', 'won']) && rand(1, 100) <= 80) {
            $documents[] = 'signed_contract.pdf';
        }

        return $documents;
    }

    private function generateProposalDocuments(string $stage): array
    {
        if (!in_array($stage, ['proposal', 'negotiation', 'closing', 'won'])) {
            return [];
        }

        $docs = [];
        if (rand(1, 100) <= 80) $docs[] = 'detailed_proposal.pdf';
        if (rand(1, 100) <= 60) $docs[] = 'pricing_breakdown.xlsx';
        if (rand(1, 100) <= 40) $docs[] = 'project_timeline.pdf';

        return $docs;
    }

    private function generateContractDocuments(string $stage): array
    {
        if (!in_array($stage, ['closing', 'won'])) {
            return [];
        }

        $docs = [];
        if (rand(1, 100) <= 90) $docs[] = 'service_agreement.pdf';
        if (rand(1, 100) <= 70) $docs[] = 'terms_and_conditions.pdf';
        if (rand(1, 100) <= 50) $docs[] = 'payment_schedule.pdf';

        return $docs;
    }

    private function generateLossReason(): string
    {
        $reasons = [
            'Price Too High',
            'Competitor Won',
            'No Budget',
            'No Decision',
            'Product Mismatch',
            'Timing Issues',
            'Lost Contact',
        ];

        return $reasons[array_rand($reasons)];
    }

    private function generateLossDetails(): string
    {
        $details = [
            'Client found more competitive pricing elsewhere',
            'Budget was reallocated to other priorities',
            'Decision maker left the organization',
            'Project requirements changed significantly',
            'Competitor offered better terms',
            'Client decided to handle internally',
            'Regulatory changes affected project viability',
        ];

        return $details[array_rand($details)];
    }

    private function generateCompetitorWon(): string
    {
        $competitors = [
            'Islamic Publishing House',
            'Al-Hidayah Education Center',
            'Barakah Event Management',
            'Halal Certification Board',
            'Islamic Finance Institute',
            'Masjid Construction Co',
            'Noor Travel Agency',
            'Ummah Tech Solutions',
        ];

        return $competitors[array_rand($competitors)];
    }

    private function generateCustomFields(): array
    {
        $fields = [];
        
        if (rand(1, 100) <= 30) {
            $fields['special_requirements'] = 'Halal certification required';
        }
        
        if (rand(1, 100) <= 20) {
            $fields['referral_source'] = 'Existing client referral';
        }
        
        if (rand(1, 100) <= 15) {
            $fields['language_preference'] = ['English', 'Malay', 'Arabic'][array_rand(['English', 'Malay', 'Arabic'])];
        }

        return $fields;
    }

    private function generateDealNumber(Carbon $createdAt): string
    {
        $prefix = 'DEAL';
        $year = $createdAt->format('Y');
        $month = $createdAt->format('m');
        $sequence = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return "{$prefix}-{$year}{$month}-{$sequence}";
    }

    private function weightedRandom(array $weights): string
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($weights as $item => $weight) {
            $currentWeight += $weight;
            if ($random <= $currentWeight) {
                return $item;
            }
        }
        
        return array_key_first($weights);
    }
}
