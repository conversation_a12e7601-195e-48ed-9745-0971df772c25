<?php

namespace Database\Seeders;

use App\Models\Transaction;
use App\Models\Client;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class TransactionSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing transactions
        Transaction::truncate();

        $this->command->info('Generating realistic transactions for clients over 2 years...');

        $clients = Client::all();
        $products = Product::all();

        if ($clients->isEmpty()) {
            $this->command->error('No clients found. Please run ClientSeeder first.');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->error('No products found. Please run ProductSeeder first.');
            return;
        }

        $totalTransactions = 0;
        $progressBar = $this->command->getOutput()->createProgressBar($clients->count());
        $progressBar->start();


        $transactions = [];

        foreach ($clients as $client) {
            // Generate transactions based on client's transaction_count and total_spent
            $targetTransactionCount = $client->transaction_count;
            $targetTotalSpent = $client->total_spent;

            if ($targetTransactionCount > 0 && $targetTotalSpent > 0) {
                $clientTransactions = $this->generateClientTransactions(
                    $client,
                    $products,
                    $targetTransactionCount,
                    $targetTotalSpent
                );

                $transactions = array_merge($transactions, $clientTransactions);
                $totalTransactions += count($clientTransactions);
            }

            $progressBar->advance();

            // Insert in batches to avoid memory issues
            if (count($transactions) >= 500) {
                Transaction::insert($transactions);
                $transactions = [];
            }
        }

        // Insert remaining transactions
        if (!empty($transactions)) {
            Transaction::insert($transactions);
        }

        $progressBar->finish();
        $this->command->newLine();
        $this->command->info("Successfully created {$totalTransactions} transactions!");

        // Update client computed fields to match actual transaction data
        $this->updateClientComputedFields();
    }

    private function generateClientTransactions(Client $client, $products, int $targetCount, float $targetTotal): array
    {
        $transactions = [];
        $clientCreatedAt = Carbon::parse($client->created_at);
        $now = Carbon::now();

        // Distribute transactions over time since client creation
        $daysSinceCreation = $clientCreatedAt->diffInDays($now);
        $maxDaysBack = min($daysSinceCreation, 730); // Max 2 years

        // Generate transaction dates with realistic patterns
        $transactionDates = $this->generateTransactionDates($clientCreatedAt, $targetCount, $maxDaysBack);

        // Distribute amounts across transactions
        $amounts = $this->distributeAmounts($targetTotal, $targetCount);

        for ($i = 0; $i < $targetCount; $i++) {
            $transactionDate = $transactionDates[$i];
            $amount = $amounts[$i];

            // Determine transaction characteristics based on client profile
            $transactionData = $this->generateTransactionData(
                $client,
                $products,
                $amount,
                $transactionDate
            );

            $transactions[] = $transactionData;
        }

        return $transactions;
    }

    private function generateTransactionDates(Carbon $clientCreatedAt, int $count, int $maxDaysBack): array
    {
        $dates = [];
        $now = Carbon::now();

        // Create realistic transaction patterns
        for ($i = 0; $i < $count; $i++) {
            // More recent transactions are more likely
            $daysBack = $this->getWeightedRandomDays($maxDaysBack);
            $transactionDate = $now->copy()->subDays($daysBack);

            // Ensure transaction is after client creation
            if ($transactionDate->lt($clientCreatedAt)) {
                $transactionDate = $clientCreatedAt->copy()->addDays(rand(1, 30));
            }

            $dates[] = $transactionDate;
        }

        // Sort dates chronologically
        usort($dates, function($a, $b) {
            return $a->timestamp - $b->timestamp;
        });

        return $dates;
    }

    private function getWeightedRandomDays(int $maxDays): int
    {
        // Weight recent transactions more heavily
        $weights = [];
        for ($i = 0; $i <= $maxDays; $i++) {
            $weight = max(1, 100 - ($i / $maxDays * 80)); // Recent days have higher weight
            $weights[$i] = $weight;
        }

        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        $cumulative = 0;

        foreach ($weights as $days => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $days;
            }
        }

        return rand(0, $maxDays);
    }

    private function distributeAmounts(float $totalAmount, int $count): array
    {
        if ($count === 1) {
            return [$totalAmount];
        }

        $amounts = [];
        $remaining = $totalAmount;

        // Generate random proportions
        $proportions = [];
        for ($i = 0; $i < $count; $i++) {
            $proportions[] = rand(1, 100);
        }

        $totalProportions = array_sum($proportions);

        // Distribute amounts based on proportions
        for ($i = 0; $i < $count - 1; $i++) {
            $amount = ($proportions[$i] / $totalProportions) * $totalAmount;
            $amount = round($amount, 2);
            $amounts[] = $amount;
            $remaining -= $amount;
        }

        // Last amount gets the remainder
        $amounts[] = round($remaining, 2);

        return $amounts;
    }

    private function generateTransactionData(Client $client, $products, float $amount, Carbon $date): array
    {
        $types = ['invoice', 'order', 'quote'];
        $statuses = ['completed', 'pending', 'draft', 'cancelled'];
        $paymentMethods = ['credit_card', 'bank_transfer', 'paypal', 'cash', 'online_banking'];
        $paymentStatuses = ['paid', 'pending', 'partial', 'overdue']; // Valid enum values only

        $type = $types[array_rand($types)];

        // Determine status based on transaction age and client engagement
        $daysOld = $date->diffInDays(Carbon::now());
        $status = $this->determineTransactionStatus($daysOld, $client->engagement_level);

        // Determine payment status based on transaction status
        $paymentStatus = $this->determinePaymentStatus($status, $daysOld);
        $paymentMethod = $paymentStatus === 'paid' ? $paymentMethods[array_rand($paymentMethods)] : null;

        // Calculate tax (6% GST for Malaysia)
        $taxAmount = round($amount * 0.06, 2);
        $totalAmount = $amount + $taxAmount;

        // Generate payment and due dates
        $paymentDate = null;
        $dueDate = $date->copy()->addDays(rand(7, 30));

        if ($paymentStatus === 'paid') {
            $paymentDate = $date->copy()->addDays(rand(0, 15));
        }

        return [
            'client_id' => $client->id,
            'transaction_number' => $this->generateTransactionNumber($date),
            'type' => $type,
            'status' => $status,
            'amount' => $amount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
            'payment_method' => $paymentMethod,
            'payment_status' => $paymentStatus,
            'payment_date' => $paymentDate,
            'due_date' => $dueDate,
            'notes' => $this->generateTransactionNotes($type, $amount, $client),
            'created_at' => $date,
            'updated_at' => $date->copy()->addDays(rand(0, 5)),
        ];
    }

    private function determineTransactionStatus(int $daysOld, string $engagementLevel): string
    {
        // Older transactions are more likely to be completed
        if ($daysOld > 90) {
            $weights = ['completed' => 80, 'cancelled' => 15, 'pending' => 5];
        } elseif ($daysOld > 30) {
            $weights = ['completed' => 70, 'pending' => 20, 'cancelled' => 10];
        } else {
            $weights = ['completed' => 50, 'pending' => 40, 'draft' => 10];
        }

        // Adjust based on engagement level
        if ($engagementLevel === 'Hot') {
            $weights['completed'] = ($weights['completed'] ?? 0) + 20;
            $weights['cancelled'] = max(0, ($weights['cancelled'] ?? 0) - 10);
        } elseif ($engagementLevel === 'Frozen') {
            $weights['cancelled'] = ($weights['cancelled'] ?? 0) + 20;
            $weights['completed'] = max(0, ($weights['completed'] ?? 0) - 15);
        }

        return $this->weightedRandom($weights);
    }

    private function determinePaymentStatus(string $status, int $daysOld): string
    {
        if ($status === 'cancelled') return 'pending'; // Cancelled transactions remain pending
        if ($status === 'draft') return 'pending';

        if ($status === 'completed') {
            // Completed transactions must be paid (business logic constraint)
            return 'paid';
        } else {
            // Pending transactions
            if ($daysOld > 60) {
                $weights = ['paid' => 50, 'pending' => 25, 'overdue' => 20, 'partial' => 5];
            } else {
                $weights = ['pending' => 60, 'paid' => 30, 'partial' => 8, 'overdue' => 2];
            }

            return $this->weightedRandom($weights);
        }
    }

    private function weightedRandom(array $weights): string
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        $cumulative = 0;

        foreach ($weights as $option => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $option;
            }
        }

        return array_key_first($weights);
    }

    private function generateTransactionNumber(Carbon $date): string
    {
        $year = $date->format('Y');
        $month = $date->format('m');
        $random = strtoupper(substr(uniqid(), -6));
        return "TXN-{$year}{$month}-{$random}";
    }

    private function generateTransactionNotes(string $type, float $amount, Client $client): string
    {
        $notes = [
            'invoice' => [
                'Islamic books purchase - ' . $client->name,
                'Monthly donation contribution',
                'Quran learning course fee',
                'Islamic education program payment',
                'Zakat contribution',
                'Sadaqah donation',
                'Hajj preparation course fee',
                'Arabic language course payment',
                'Islamic finance consultation',
                'Halal certification service',
            ],
            'order' => [
                'Islamic book order for ' . $client->name,
                'Prayer mat and accessories order',
                'Islamic calligraphy artwork',
                'Quran recitation audio set',
                'Islamic children books collection',
                'Halal food products order',
                'Islamic clothing and accessories',
                'Mosque donation box contribution',
                'Islamic educational materials',
                'Religious ceremony supplies',
            ],
            'quote' => [
                'Islamic education program quote',
                'Hajj travel package quotation',
                'Islamic wedding ceremony services',
                'Mosque construction consultation',
                'Islamic finance advisory services',
                'Halal restaurant setup consultation',
                'Islamic school curriculum development',
                'Community center event planning',
                'Islamic art commission quote',
                'Religious book publishing services',
            ],
        ];

        $typeNotes = $notes[$type] ?? $notes['invoice'];
        $baseNote = $typeNotes[array_rand($typeNotes)];

        if ($amount > 1000) {
            $baseNote .= ' - Premium service package';
        } elseif ($amount > 500) {
            $baseNote .= ' - Standard package';
        } else {
            $baseNote .= ' - Basic package';
        }

        return $baseNote;
    }

    private function updateClientComputedFields(): void
    {
        $this->command->info('Updating client computed fields...');

        $clients = Client::all();
        foreach ($clients as $client) {
            $client->updateComputedFields();
            $client->save();
        }

        $this->command->info('Client computed fields updated successfully!');
    }
}
