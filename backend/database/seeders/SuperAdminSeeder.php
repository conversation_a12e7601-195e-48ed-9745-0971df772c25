<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create System Administrator
        $systemAdmin = User::where('email', '<EMAIL>')->first();

        if (!$systemAdmin) {
            User::create([
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => User::ROLE_ADMIN,
                'department' => 'Administration',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $this->command->info('System Administrator created successfully.');
        } else {
            $this->command->info('System Administrator already exists.');
        }

        // Create Personal Admin Account for Zulhelmi
        $personalAdmin = User::where('email', '<EMAIL>')->first();

        if (!$personalAdmin) {
            User::create([
                'name' => 'Zulhelmi Nasir',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => User::ROLE_ADMIN,
                'department' => 'Development',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $this->command->info('Personal admin account created successfully.');
        } else {
            $this->command->info('Personal admin account already exists.');
        }
    }
}
