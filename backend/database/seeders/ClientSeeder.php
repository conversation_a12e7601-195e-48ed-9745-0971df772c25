<?php

namespace Database\Seeders;

use App\Models\Client;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ClientSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing clients
        Client::truncate();

        $this->command->info('Generating 1000 clients with realistic Malaysian data...');

        // Configuration arrays
        $utmSources = ['facebook', 'instagram', 'tiktok', 'google', 'youtube', 'whatsapp', 'referral', 'direct'];
        $tags = ['Student', 'Parent', 'Teacher', 'Activist', 'Donor', 'Youth', 'Adult', 'Scholar', 'Professional', 'Retiree'];
        $categories = ['First Timer', 'Retainer', 'Loyal', 'Advocator'];
        $ltvSegments = ['Silver', 'Gold', 'Gold+', 'Platinum'];
        $engagementLevels = ['Hot', 'Warm', 'Cold', 'Frozen'];
        $priorities = ['High', 'Medium', 'Low'];
        $statuses = ['active', 'prospect', 'inactive'];

        // Malaysian names and data
        $malaysianNames = $this->getMalaysianNames();
        $malaysianCities = $this->getMalaysianCities();
        $companies = $this->getCompanies();
        $campaigns = $this->getCampaigns();

        // Generate 1000 clients
        $clients = [];
        $usedEmails = []; // Track used emails to prevent duplicates
        $progressBar = $this->command->getOutput()->createProgressBar(1000);
        $progressBar->start();

        for ($i = 0; $i < 1000; $i++) {
            $name = $malaysianNames[array_rand($malaysianNames)];
            $city = $malaysianCities[array_rand($malaysianCities)];
            $company = rand(1, 100) <= 70 ? $companies[array_rand($companies)] : null; // 70% have companies
            $campaign = $campaigns[array_rand($campaigns)];

            // Generate realistic creation date (last 2 years)
            $createdAt = Carbon::now()->subDays(rand(1, 730)); // 2 years back

            // Generate realistic activity patterns
            $daysSinceCreated = $createdAt->diffInDays(Carbon::now());
            $lastActivityDays = rand(1, min($daysSinceCreated, 180)); // Max 6 months ago

            // Determine engagement and spending patterns
            $engagementLevel = $engagementLevels[array_rand($engagementLevels)];
            $category = $this->determineCategory($daysSinceCreated);
            $ltvSegment = $this->determineLTVSegment($engagementLevel, $category);
            $priority = $this->determinePriority($engagementLevel, $ltvSegment);
            $status = $this->determineStatus($engagementLevel, $lastActivityDays);

            // Generate spending data
            $spendingData = $this->generateSpendingData($ltvSegment, $category, $daysSinceCreated);

            // Generate unique email
            $email = $this->generateUniqueEmail($name, $usedEmails, $i);
            $usedEmails[] = $email;

            $clients[] = [
                'name' => $name,
                'email' => $email,
                'phone' => $this->generateMalaysianPhone(),
                'company' => $company,
                'address' => $this->generateAddress(),
                'city' => $city['name'],
                'state' => $city['state'],
                'country' => 'Malaysia',
                'postal_code' => $city['postal_code'],
                'status' => $status,
                'notes' => $this->generateNotes($category, $engagementLevel),
                'utm_source' => $utmSources[array_rand($utmSources)],
                'tags' => json_encode($this->generateTags($tags)),
                'category' => $category,
                'ltv_segment' => $ltvSegment,
                'engagement_level' => $engagementLevel,
                'priority' => $priority,
                'suggested_action' => $this->generateSuggestedAction($engagementLevel, $status),
                'last_activity' => Carbon::now()->subDays($lastActivityDays),
                'total_spent' => $spendingData['total_spent'],
                'transaction_count' => $spendingData['transaction_count'],
                'custom_fields' => json_encode([
                    'preferred_language' => rand(1, 100) <= 60 ? 'Bahasa Malaysia' : 'English',
                    'interests' => $this->generateInterests(),
                    'source_campaign' => $campaign,
                    'age_group' => $this->generateAgeGroup(),
                    'registration_year' => $createdAt->year,
                ]),
                'created_at' => $createdAt,
                'updated_at' => Carbon::now()->subDays(rand(0, $lastActivityDays)),
            ];

            $progressBar->advance();

            // Insert in batches of 100 to avoid memory issues
            if (count($clients) >= 100) {
                Client::insert($clients);
                $clients = [];
            }
        }

        // Insert remaining clients
        if (!empty($clients)) {
            Client::insert($clients);
        }

        $progressBar->finish();
        $this->command->newLine();
        $this->command->info('Successfully created 1000 clients!');
    }

    private function getMalaysianNames(): array
    {
        return [
            // Male names
            'Ahmad bin Abdullah', 'Muhammad bin Hassan', 'Ali bin Omar', 'Ibrahim bin Yusof',
            'Ismail bin Ahmad', 'Hassan bin Ali', 'Omar bin Ibrahim', 'Yusof bin Ismail',
            'Abdullah bin Muhammad', 'Zakaria bin Hassan', 'Rashid bin Omar', 'Farid bin Ali',
            'Hafiz bin Abdullah', 'Kamal bin Yusof', 'Nazir bin Ahmad', 'Razak bin Ibrahim',
            'Salleh bin Muhammad', 'Tahir bin Hassan', 'Wahid bin Omar', 'Zain bin Ali',
            'Azman bin Abdullah', 'Baharuddin bin Yusof', 'Daud bin Ahmad', 'Fauzi bin Ibrahim',
            'Halim bin Muhammad', 'Jamal bin Hassan', 'Kamil bin Omar', 'Latif bin Ali',
            'Mohd bin Abdullah', 'Nizam bin Yusof', 'Othman bin Ahmad', 'Ramli bin Ibrahim',
            'Sulaiman bin Muhammad', 'Taufik bin Hassan', 'Umar bin Omar', 'Wan bin Ali',
            'Yahya bin Abdullah', 'Zainal bin Yusof', 'Aziz bin Ahmad', 'Bakri bin Ibrahim',

            // Female names
            'Aminah binti Abdullah', 'Fatimah binti Hassan', 'Khadijah binti Omar', 'Aishah binti Yusof',
            'Zainab binti Ahmad', 'Maryam binti Ali', 'Hafsah binti Ibrahim', 'Ruqayyah binti Ismail',
            'Ummu binti Muhammad', 'Safiyyah binti Hassan', 'Zahra binti Omar', 'Layla binti Ali',
            'Noor binti Abdullah', 'Siti binti Yusof', 'Farah binti Ahmad', 'Iman binti Ibrahim',
            'Nur binti Muhammad', 'Sarah binti Hassan', 'Mariam binti Omar', 'Halimah binti Ali',
            'Asma binti Abdullah', 'Balkis binti Yusof', 'Dahlia binti Ahmad', 'Fatin binti Ibrahim',
            'Hidayah binti Muhammad', 'Jamilah binti Hassan', 'Kamila binti Omar', 'Latifah binti Ali',
            'Nadhirah binti Abdullah', 'Qistina binti Yusof', 'Ramlah binti Ahmad', 'Sumayyah binti Ibrahim',
            'Taqwa binti Muhammad', 'Ummul binti Hassan', 'Wardah binti Omar', 'Yasmin binti Ali',
            'Zulaikha binti Abdullah', 'Amal binti Yusof', 'Bushra binti Ahmad', 'Dina binti Ibrahim',
        ];
    }

    private function getMalaysianCities(): array
    {
        return [
            ['name' => 'Kuala Lumpur', 'state' => 'Kuala Lumpur', 'postal_code' => '50000'],
            ['name' => 'Petaling Jaya', 'state' => 'Selangor', 'postal_code' => '47400'],
            ['name' => 'Shah Alam', 'state' => 'Selangor', 'postal_code' => '40000'],
            ['name' => 'Subang Jaya', 'state' => 'Selangor', 'postal_code' => '47500'],
            ['name' => 'Klang', 'state' => 'Selangor', 'postal_code' => '41000'],
            ['name' => 'Johor Bahru', 'state' => 'Johor', 'postal_code' => '80000'],
            ['name' => 'Penang', 'state' => 'Pulau Pinang', 'postal_code' => '10000'],
            ['name' => 'Ipoh', 'state' => 'Perak', 'postal_code' => '30000'],
            ['name' => 'Kuching', 'state' => 'Sarawak', 'postal_code' => '93000'],
            ['name' => 'Kota Kinabalu', 'state' => 'Sabah', 'postal_code' => '88000'],
            ['name' => 'Malacca', 'state' => 'Melaka', 'postal_code' => '75000'],
            ['name' => 'Alor Setar', 'state' => 'Kedah', 'postal_code' => '05000'],
            ['name' => 'Kuantan', 'state' => 'Pahang', 'postal_code' => '25000'],
            ['name' => 'Kuala Terengganu', 'state' => 'Terengganu', 'postal_code' => '20000'],
            ['name' => 'Kota Bharu', 'state' => 'Kelantan', 'postal_code' => '15000'],
            ['name' => 'Seremban', 'state' => 'Negeri Sembilan', 'postal_code' => '70000'],
            ['name' => 'Cyberjaya', 'state' => 'Selangor', 'postal_code' => '63000'],
            ['name' => 'Putrajaya', 'state' => 'Putrajaya', 'postal_code' => '62000'],
            ['name' => 'Ampang', 'state' => 'Selangor', 'postal_code' => '68000'],
            ['name' => 'Cheras', 'state' => 'Selangor', 'postal_code' => '56000'],
        ];
    }

    private function getCompanies(): array
    {
        return [
            'Islamic Education Center', 'Dakwah Foundation', 'Youth Islamic Society',
            'Women Empowerment NGO', 'Islamic Relief Malaysia', 'Tabung Haji',
            'Islamic Banking Corporation', 'Halal Development Corporation',
            'Islamic University of Malaysia', 'Masjid Al-Hidayah',
            'Islamic Welfare Organization', 'Muslim Community Center',
            'Islamic Studies Institute', 'Quran Learning Center',
            'Islamic Finance Solutions', 'Halal Food Industries',
            'Islamic Art & Culture Foundation', 'Muslim Youth Development',
            'Islamic Healthcare Services', 'Zakat Collection Center',
            'Islamic Media Group', 'Muslim Professional Network',
            'Islamic Technology Solutions', 'Halal Tourism Board',
            'Islamic Research Institute', 'Muslim Family Services',
        ];
    }

    private function getCampaigns(): array
    {
        return [
            'Ramadan 2024', 'Ramadan 2023', 'Eid Campaign 2024', 'Eid Campaign 2023',
            'Hajj Preparation 2024', 'Hajj Preparation 2023', 'Islamic New Year 2024',
            'Maulid Campaign 2024', 'Isra Miraj 2024', 'Gaza Relief Campaign',
            'Palestine Support 2024', 'Flood Relief Malaysia', 'Zakat Campaign 2024',
            'Sadaqah Drive 2024', 'Orphan Support Program', 'Education Fund 2024',
            'Mosque Building Fund', 'Islamic School Support', 'Quran Distribution',
            'Iftar Sponsorship 2024', 'Eid Gift Campaign', 'Winter Relief 2023',
            'Back to School 2024', 'Youth Development Program', 'Women Empowerment 2024',
            'Senior Care Program', 'Healthcare Support Fund', 'Clean Water Project',
            'Facebook Ads 2024', 'Instagram Campaign 2024', 'Google Ads Islamic',
            'TikTok Outreach 2024', 'YouTube Islamic Content', 'WhatsApp Referral Program',
        ];
    }

    private function generateEmail(string $name): string
    {
        $nameParts = explode(' ', strtolower($name));
        $firstName = $nameParts[0];
        $lastName = isset($nameParts[2]) ? $nameParts[2] : $nameParts[1];

        $domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        $domain = $domains[array_rand($domains)];

        $separators = ['.', '_', ''];
        $separator = $separators[array_rand($separators)];

        return $firstName . $separator . $lastName . rand(1, 999) . '@' . $domain;
    }

    private function generateUniqueEmail(string $name, array $usedEmails, int $index): string
    {
        $nameParts = explode(' ', strtolower($name));
        $firstName = $nameParts[0];
        $lastName = isset($nameParts[2]) ? $nameParts[2] : $nameParts[1];

        $domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        $domain = $domains[array_rand($domains)];

        $separators = ['.', '_', ''];
        $separator = $separators[array_rand($separators)];

        // Use index + timestamp to ensure uniqueness
        $uniqueNumber = $index + 1 + time() % 1000;
        $email = $firstName . $separator . $lastName . $uniqueNumber . '@' . $domain;

        // If still duplicate (very unlikely), add more uniqueness
        $counter = 1;
        while (in_array($email, $usedEmails)) {
            $email = $firstName . $separator . $lastName . $uniqueNumber . $counter . '@' . $domain;
            $counter++;
        }

        return $email;
    }

    private function generateMalaysianPhone(): string
    {
        $prefixes = ['012', '013', '014', '016', '017', '018', '019'];
        $prefix = $prefixes[array_rand($prefixes)];
        return '+60' . $prefix . rand(1000000, 9999999);
    }

    private function generateAddress(): string
    {
        $streetNumbers = [rand(1, 999), rand(1, 99) . '-' . rand(1, 99)];
        $streetNumber = $streetNumbers[array_rand($streetNumbers)];

        $streetNames = [
            'Jalan Merdeka', 'Jalan Bangsar', 'Jalan Damansara', 'Jalan Ampang',
            'Jalan Cheras', 'Jalan Bukit Bintang', 'Jalan Tun Razak', 'Jalan Sultan',
            'Jalan Raja', 'Jalan Putra', 'Jalan Masjid', 'Jalan Sekolah',
            'Jalan Pasar', 'Jalan Taman', 'Jalan Indah', 'Jalan Harmoni',
        ];

        return $streetNumber . ' ' . $streetNames[array_rand($streetNames)];
    }

    private function determineCategory(int $daysSinceCreated): string
    {
        if ($daysSinceCreated < 30) return 'First Timer';
        if ($daysSinceCreated < 180) return 'Retainer';
        if ($daysSinceCreated < 365) return 'Loyal';
        return 'Advocator';
    }

    private function determineLTVSegment(string $engagementLevel, string $category): string
    {
        $weights = [
            'Hot' => ['Platinum' => 40, 'Gold+' => 35, 'Gold' => 20, 'Silver' => 5],
            'Warm' => ['Platinum' => 15, 'Gold+' => 30, 'Gold' => 40, 'Silver' => 15],
            'Cold' => ['Platinum' => 5, 'Gold+' => 15, 'Gold' => 35, 'Silver' => 45],
            'Frozen' => ['Platinum' => 2, 'Gold+' => 8, 'Gold' => 25, 'Silver' => 65],
        ];

        $categoryMultiplier = [
            'First Timer' => ['Silver' => 0.7, 'Gold' => 0.2, 'Gold+' => 0.08, 'Platinum' => 0.02],
            'Retainer' => ['Silver' => 0.5, 'Gold' => 0.3, 'Gold+' => 0.15, 'Platinum' => 0.05],
            'Loyal' => ['Silver' => 0.3, 'Gold' => 0.4, 'Gold+' => 0.25, 'Platinum' => 0.05],
            'Advocator' => ['Silver' => 0.2, 'Gold' => 0.3, 'Gold+' => 0.35, 'Platinum' => 0.15],
        ];

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($weights[$engagementLevel] as $segment => $weight) {
            $adjustedWeight = $weight * $categoryMultiplier[$category][$segment] * 100;
            $cumulative += $adjustedWeight;
            if ($rand <= $cumulative) {
                return $segment;
            }
        }

        return 'Silver';
    }

    private function determinePriority(string $engagementLevel, string $ltvSegment): string
    {
        if ($engagementLevel === 'Hot' && in_array($ltvSegment, ['Platinum', 'Gold+'])) return 'High';
        if ($engagementLevel === 'Hot' || $ltvSegment === 'Platinum') return 'High';
        if ($engagementLevel === 'Warm' && in_array($ltvSegment, ['Gold+', 'Gold'])) return 'High';
        if ($engagementLevel === 'Frozen') return 'Low';
        return 'Medium';
    }

    private function determineStatus(string $engagementLevel, int $lastActivityDays): string
    {
        if ($engagementLevel === 'Frozen' || $lastActivityDays > 90) return 'inactive';
        if ($lastActivityDays > 30 || $engagementLevel === 'Cold') return 'prospect';
        return 'active';
    }

    private function generateSpendingData(string $ltvSegment, string $category, int $daysSinceCreated): array
    {
        $baseSpending = [
            'Silver' => ['min' => 0, 'max' => 500],
            'Gold' => ['min' => 500, 'max' => 1200],
            'Gold+' => ['min' => 1200, 'max' => 2500],
            'Platinum' => ['min' => 2500, 'max' => 5000],
        ];

        $categoryMultiplier = [
            'First Timer' => 0.3,
            'Retainer' => 0.6,
            'Loyal' => 1.0,
            'Advocator' => 1.5,
        ];

        $timeMultiplier = min(1.0, $daysSinceCreated / 365); // Scale with time

        $minSpend = $baseSpending[$ltvSegment]['min'] * $categoryMultiplier[$category] * $timeMultiplier;
        $maxSpend = $baseSpending[$ltvSegment]['max'] * $categoryMultiplier[$category] * $timeMultiplier;

        $totalSpent = rand($minSpend, $maxSpend);
        $transactionCount = $totalSpent > 0 ? rand(1, max(1, intval($totalSpent / 100))) : 0;

        return [
            'total_spent' => round($totalSpent, 2),
            'transaction_count' => $transactionCount,
        ];
    }

    private function generateTags(array $availableTags): array
    {
        $numTags = rand(1, 4);
        $selectedTags = array_rand(array_flip($availableTags), $numTags);
        return is_array($selectedTags) ? $selectedTags : [$selectedTags];
    }

    private function generateNotes(string $category, string $engagementLevel): string
    {
        $notes = [
            'First Timer' => [
                'Hot' => 'New member showing high interest in Islamic programs.',
                'Warm' => 'Recently joined, exploring available courses and activities.',
                'Cold' => 'New prospect, needs follow-up to gauge interest.',
                'Frozen' => 'Registered but not yet engaged with content.',
            ],
            'Retainer' => [
                'Hot' => 'Regular participant in Islamic activities and events.',
                'Warm' => 'Consistent engagement with moderate participation.',
                'Cold' => 'Occasional participation, may need re-engagement.',
                'Frozen' => 'Previously active but engagement has declined.',
            ],
            'Loyal' => [
                'Hot' => 'Long-term supporter with high engagement and contributions.',
                'Warm' => 'Loyal member with steady participation over time.',
                'Cold' => 'Established member with reduced recent activity.',
                'Frozen' => 'Long-term member currently inactive.',
            ],
            'Advocator' => [
                'Hot' => 'Champion of Islamic causes, actively promotes programs.',
                'Warm' => 'Influential member who supports and advocates for initiatives.',
                'Cold' => 'Former advocate with reduced involvement.',
                'Frozen' => 'Previously very active advocate, now dormant.',
            ],
        ];

        return $notes[$category][$engagementLevel];
    }

    private function generateSuggestedAction(string $engagementLevel, string $status): string
    {
        $actions = [
            'Hot' => [
                'Invite to premium programs',
                'Schedule personal consultation',
                'Offer leadership opportunities',
                'Send exclusive content access',
            ],
            'Warm' => [
                'Send follow-up email about new courses',
                'Invite to upcoming events',
                'Share relevant Islamic content',
                'Schedule check-in call',
            ],
            'Cold' => [
                'Send re-engagement email campaign',
                'Offer special discount or incentive',
                'Share success stories and testimonials',
                'Send WhatsApp message with course info',
            ],
            'Frozen' => [
                'Launch win-back campaign',
                'Send personalized re-engagement offer',
                'Conduct feedback survey',
                'Offer free consultation session',
            ],
        ];

        return $actions[$engagementLevel][array_rand($actions[$engagementLevel])];
    }

    private function generateInterests(): array
    {
        $interests = [
            'Islamic Studies', 'Quran Recitation', 'Hadith Studies', 'Islamic History',
            'Arabic Language', 'Islamic Finance', 'Halal Lifestyle', 'Islamic Art',
            'Community Service', 'Youth Development', 'Women Empowerment', 'Education',
            'Charity Work', 'Social Justice', 'Family Values', 'Parenting',
            'Leadership', 'Entrepreneurship', 'Healthcare', 'Technology',
        ];

        $numInterests = rand(2, 5);
        $selectedInterests = array_rand(array_flip($interests), $numInterests);
        return is_array($selectedInterests) ? $selectedInterests : [$selectedInterests];
    }

    private function generateAgeGroup(): string
    {
        $ageGroups = ['18-25', '26-35', '36-45', '46-55', '56-65', '65+'];
        $weights = [20, 30, 25, 15, 8, 2]; // Weighted distribution

        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($ageGroups as $index => $ageGroup) {
            $cumulative += $weights[$index];
            if ($rand <= $cumulative) {
                return $ageGroup;
            }
        }

        return '26-35'; // Default
    }
}
