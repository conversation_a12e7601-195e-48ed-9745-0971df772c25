<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DefaultUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password123'),
                'phone' => '+60123456789',
                'role' => User::ROLE_ADMIN,
                'department' => 'Management',
                'is_active' => true,
            ]
        );

        // Create default manager user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Manager',
                'password' => Hash::make('password123'),
                'phone' => '+60123456790',
                'role' => User::ROLE_MANAGER,
                'department' => 'Sales',
                'is_active' => true,
            ]
        );

        // Create default user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sales Representative',
                'password' => Hash::make('password123'),
                'phone' => '+60123456791',
                'role' => User::ROLE_USER,
                'department' => 'Sales',
                'is_active' => true,
            ]
        );

        $this->command->info('Default users created successfully!');
    }
}
