<?php

/**
 * Temporary script to disable system-wide 2FA
 * Run this to allow login without 2FA so you can configure email settings
 * 
 * Usage: php disable_2fa_temp.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\SystemSetting;

try {
    // Disable system-wide 2FA temporarily
    SystemSetting::set('two_factor_auth_enabled', false, 'boolean', 'Enable or disable two-factor authentication system-wide');
    
    echo "✅ System-wide 2FA has been temporarily disabled.\n";
    echo "📧 You can now log in and configure email settings in Settings > Email.\n";
    echo "🔒 Remember to re-enable 2FA after configuring email settings.\n";
    echo "\nTo re-enable 2FA later, run: php enable_2fa.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
