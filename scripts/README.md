# KDT Auto-Rebuild System

This directory contains the auto-rebuild system for the KDT development environment. The system automatically detects code changes and rebuilds/restarts the appropriate containers to keep your development environment in sync with your code changes.

## Features

- **Automatic Container Rebuilds**: Detects changes in backend and frontend code and automatically rebuilds containers
- **Smart Triggering**: Different types of changes trigger different rebuild strategies
- **Development Mode**: Uses volume mounts for faster development with live code sync
- **Debounced Rebuilds**: Prevents excessive rebuilds by debouncing file change events
- **Comprehensive Logging**: Detailed logs with timestamps and color coding
- **Multiple Interfaces**: Shell scripts, Node.js watcher, and Makefile commands

## Quick Start

### Option 1: Using Makefile (Recommended)
```bash
# Install dependencies and start with auto-rebuild watcher
make dev-watch

# Or just start development environment
make dev

# Show all available commands
make help
```

### Option 2: Using Development Script
```bash
# Make scripts executable
chmod +x scripts/dev.sh

# Start with auto-rebuild watcher
./scripts/dev.sh watch

# Or start normally
./scripts/dev.sh start
```

### Option 3: Manual Setup
```bash
# Install watcher dependencies
cd scripts && npm install && cd ..

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Start file watcher (in separate terminal)
cd scripts && node watch-and-rebuild.js
```

## How It Works

### File Watching
The system watches for changes in:
- `backend/` - Backend PHP code
- `src/` - Frontend React/TypeScript code
- `public/` - Static assets

### Rebuild Triggers

#### Backend Rebuild Triggers
- `backend/app/**` - Application code changes
- `backend/config/**` - Configuration changes
- `backend/database/**` - Database migrations/seeders
- `backend/routes/**` - Route definitions

#### Frontend Rebuild Triggers
- `src/**` - React/TypeScript source code
- `public/**` - Static assets
- `index.html` - Main HTML file
- `package.json` - Dependencies
- `vite.config.ts` - Build configuration

#### Full Rebuild Triggers
- `backend/composer.json` - PHP dependencies
- `backend/Dockerfile` - Backend container definition
- `Dockerfile` - Frontend container definition
- `docker-compose.yml` - Production compose file
- `docker-compose.dev.yml` - Development compose file

### Development vs Production

#### Development Mode (`docker-compose.dev.yml`)
- Uses volume mounts for live code sync
- Faster rebuilds with cached dependencies
- Hot reload for frontend development
- Debug mode enabled

#### Production Mode (`docker-compose.yml`)
- Code is copied into containers during build
- Optimized for production performance
- No volume mounts for security

## Available Commands

### Makefile Commands
```bash
make help              # Show all available commands
make dev               # Install dependencies and start development
make dev-watch         # Start with auto-rebuild watcher
make start             # Start development environment
make stop              # Stop development environment
make restart           # Restart development environment
make rebuild           # Rebuild development environment
make status            # Show service status
make logs              # Show all logs
make logs-backend      # Show backend logs
make logs-frontend     # Show frontend logs
make backend-shell     # Open backend container shell
make frontend-shell    # Open frontend container shell
make backend-migrate   # Run database migrations
make db-shell          # Open database shell
make clean             # Clean up containers and volumes
```

### Development Script Commands
```bash
./scripts/dev.sh start           # Start development environment
./scripts/dev.sh stop            # Stop development environment
./scripts/dev.sh restart         # Restart development environment
./scripts/dev.sh rebuild         # Rebuild development environment
./scripts/dev.sh watch           # Start auto-rebuild file watcher
./scripts/dev.sh status          # Show development environment status
./scripts/dev.sh logs [service]  # Show logs
./scripts/dev.sh backend <cmd>   # Run command in backend container
./scripts/dev.sh frontend <cmd>  # Run command in frontend container
```

### File Watcher Commands
```bash
node watch-and-rebuild.js        # Start file watcher
node watch-and-rebuild.js --init # Initialize development environment
node watch-and-rebuild.js --stop # Stop development environment
```

## Configuration

### Watcher Configuration
Edit `scripts/watch-and-rebuild.js` to modify:
- Watch directories
- File patterns for different rebuild types
- Cooldown periods
- Docker Compose file location

### Docker Configuration
- `docker-compose.dev.yml` - Development environment configuration
- `docker-compose.yml` - Production environment configuration

## Troubleshooting

### Common Issues

#### File Watcher Not Starting
```bash
# Install Node.js dependencies
cd scripts && npm install

# Check Node.js version (requires >= 14.0.0)
node --version
```

#### Containers Not Rebuilding
```bash
# Check if containers are running
docker-compose -f docker-compose.dev.yml ps

# Check file watcher logs for errors
# Look for permission issues or file path problems
```

#### Permission Issues
```bash
# Make scripts executable
chmod +x scripts/dev.sh
chmod +x scripts/watch-and-rebuild.sh
chmod +x scripts/watch-and-rebuild.js
```

#### Port Conflicts
```bash
# Check if ports are already in use (Production ports)
lsof -i :4000  # Frontend (Production)
lsof -i :4001  # Backend (Production)
lsof -i :4002  # Database (Production)
lsof -i :4004  # Adminer (Production)

# Development ports (if using dev environment)
lsof -i :3000  # Frontend (Development)
lsof -i :8000  # Backend (Development)
lsof -i :5432  # Database (Development)
lsof -i :8001  # Adminer (Development)

# Stop conflicting services or change ports in docker-compose.dev.yml
```

### Debugging

#### Enable Verbose Logging
The file watcher provides detailed logs with timestamps. Watch the console output to see:
- Which files triggered rebuilds
- Rebuild progress and results
- Any errors during the rebuild process

#### Manual Container Management
```bash
# View container status
docker-compose -f docker-compose.dev.yml ps

# View container logs
docker-compose -f docker-compose.dev.yml logs -f [service_name]

# Restart specific service
docker-compose -f docker-compose.dev.yml restart [service_name]

# Rebuild specific service
docker-compose -f docker-compose.dev.yml build --no-cache [service_name]
```

## Performance Tips

1. **Use Development Mode**: Always use `docker-compose.dev.yml` for development
2. **Exclude Large Directories**: The watcher automatically excludes `node_modules`, `vendor`, etc.
3. **Debounced Rebuilds**: Multiple rapid changes are debounced to prevent excessive rebuilds
4. **Selective Rebuilds**: Only affected containers are rebuilt, not the entire stack

## Integration with IDEs

### VS Code
Add these tasks to `.vscode/tasks.json`:
```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Development with Watcher",
      "type": "shell",
      "command": "make dev-watch",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    }
  ]
}
```

### PhpStorm/WebStorm
Configure external tools to run the development commands directly from the IDE.

## Contributing

When adding new features that require container rebuilds:
1. Update the appropriate trigger patterns in `watch-and-rebuild.js`
2. Test the auto-rebuild functionality
3. Update this documentation if needed
