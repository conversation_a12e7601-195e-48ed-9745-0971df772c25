{"name": "kdt-auto-rebuild-watcher", "version": "1.0.0", "description": "Auto-rebuild watcher for KDT development environment", "main": "watch-and-rebuild.js", "scripts": {"start": "node watch-and-rebuild.js", "init": "node watch-and-rebuild.js --init", "stop": "node watch-and-rebuild.js --stop", "install-deps": "npm install"}, "dependencies": {"chokidar": "^3.5.3"}, "engines": {"node": ">=14.0.0"}, "author": "KDT Development Team", "license": "MIT"}