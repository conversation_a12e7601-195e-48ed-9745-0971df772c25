#!/usr/bin/env python3

"""
<PERSON>ript to remove console.log statements from React frontend code
This prepares the codebase for production deployment
"""

import os
import re
import sys
from pathlib import Path

def remove_console_logs_from_file(file_path):
    """Remove console.log statements from a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to match console.log statements
        # This handles various formats:
        # - console.log('message');
        # - console.log('message', variable);
        # - console.log(`template ${variable}`);
        # - Multi-line console.log statements
        
        # Remove standalone console.log lines (with optional whitespace)
        content = re.sub(r'^\s*console\.log\([^;]*\);\s*$', '', content, flags=re.MULTILINE)
        
        # Remove console.log statements that are part of other lines
        content = re.sub(r'\s*console\.log\([^)]*\);\s*', '', content)
        
        # Remove any remaining console.log calls (without semicolon)
        content = re.sub(r'\s*console\.log\([^)]*\)\s*', '', content)
        
        # Clean up multiple empty lines
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to process all TypeScript/React files"""
    src_dir = Path('src')
    
    if not src_dir.exists():
        print("Error: src directory not found")
        sys.exit(1)
    
    # Find all TypeScript and React files
    files_to_process = []
    for ext in ['*.ts', '*.tsx']:
        files_to_process.extend(src_dir.rglob(ext))
    
    print(f"Found {len(files_to_process)} files to process")
    
    modified_files = []
    
    for file_path in files_to_process:
        if remove_console_logs_from_file(file_path):
            modified_files.append(file_path)
            print(f"✓ Cleaned: {file_path}")
    
    print(f"\nProcessing complete!")
    print(f"Modified {len(modified_files)} files")
    
    if modified_files:
        print("\nModified files:")
        for file_path in modified_files:
            print(f"  - {file_path}")

if __name__ == "__main__":
    main()
