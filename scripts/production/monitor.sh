#!/bin/bash

# KDT Production Monitoring Script for Raspberry Pi
# This script monitors the health and performance of the KDT application

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.prod.yml"
LOG_FILE="$PROJECT_DIR/monitor.log"
ALERT_EMAIL=${ALERT_EMAIL:-""}

# Thresholds
CPU_THRESHOLD=${CPU_THRESHOLD:-80}
MEMORY_THRESHOLD=${MEMORY_THRESHOLD:-85}
DISK_THRESHOLD=${DISK_THRESHOLD:-90}
RESPONSE_TIME_THRESHOLD=${RESPONSE_TIME_THRESHOLD:-5}

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check system resources
check_system_resources() {
    log "Checking system resources..."
    
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [ "$cpu_usage" -gt "$CPU_THRESHOLD" ]; then
        warning "High CPU usage: ${cpu_usage}% (threshold: ${CPU_THRESHOLD}%)"
    else
        info "CPU usage: ${cpu_usage}%"
    fi
    
    # Memory usage
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [ "$memory_usage" -gt "$MEMORY_THRESHOLD" ]; then
        warning "High memory usage: ${memory_usage}% (threshold: ${MEMORY_THRESHOLD}%)"
    else
        info "Memory usage: ${memory_usage}%"
    fi
    
    # Disk usage
    local disk_usage=$(df -h "$PROJECT_DIR" | awk 'NR==2{print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
        warning "High disk usage: ${disk_usage}% (threshold: ${DISK_THRESHOLD}%)"
    else
        info "Disk usage: ${disk_usage}%"
    fi
    
    # Temperature (Raspberry Pi specific)
    if [ -f /sys/class/thermal/thermal_zone0/temp ]; then
        local temp=$(($(cat /sys/class/thermal/thermal_zone0/temp) / 1000))
        if [ "$temp" -gt 70 ]; then
            warning "High CPU temperature: ${temp}°C"
        else
            info "CPU temperature: ${temp}°C"
        fi
    fi
}

# Check Docker services
check_docker_services() {
    log "Checking Docker services..."
    
    local services=("kdt-postgres-prod" "kdt-redis-prod" "kdt-backend-prod" "kdt-frontend-prod")
    local failed_services=()
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$service.*Up"; then
            info "✓ $service is running"
        else
            error "✗ $service is not running"
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        warning "Failed services: ${failed_services[*]}"
        return 1
    fi
    
    return 0
}

# Check application health
check_application_health() {
    log "Checking application health..."
    
    # Check frontend
    local frontend_start_time=$(date +%s%N)
    if curl -f -s http://localhost:3000/health > /dev/null; then
        local frontend_end_time=$(date +%s%N)
        local frontend_response_time=$(( (frontend_end_time - frontend_start_time) / 1000000 ))
        
        if [ "$frontend_response_time" -gt "$((RESPONSE_TIME_THRESHOLD * 1000))" ]; then
            warning "Frontend slow response: ${frontend_response_time}ms"
        else
            info "✓ Frontend is healthy (${frontend_response_time}ms)"
        fi
    else
        error "✗ Frontend is not responding"
        return 1
    fi
    
    # Check backend
    local backend_start_time=$(date +%s%N)
    if curl -f -s http://localhost:8000/api/v1/health > /dev/null; then
        local backend_end_time=$(date +%s%N)
        local backend_response_time=$(( (backend_end_time - backend_start_time) / 1000000 ))
        
        if [ "$backend_response_time" -gt "$((RESPONSE_TIME_THRESHOLD * 1000))" ]; then
            warning "Backend slow response: ${backend_response_time}ms"
        else
            info "✓ Backend is healthy (${backend_response_time}ms)"
        fi
    else
        error "✗ Backend is not responding"
        return 1
    fi
    
    return 0
}

# Check database health
check_database_health() {
    log "Checking database health..."
    
    if docker-compose -f "$COMPOSE_FILE" exec -T kdt-postgres pg_isready -U kdt > /dev/null 2>&1; then
        info "✓ Database is healthy"
        
        # Check database connections
        local connections=$(docker-compose -f "$COMPOSE_FILE" exec -T kdt-postgres psql -U kdt -d kdt -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | xargs)
        info "Active database connections: $connections"
        
        return 0
    else
        error "✗ Database is not responding"
        return 1
    fi
}

# Check log files for errors
check_logs() {
    log "Checking for recent errors in logs..."
    
    local error_count=0
    
    # Check backend logs for errors in the last hour
    if docker-compose -f "$COMPOSE_FILE" logs --since=1h kdt-backend 2>/dev/null | grep -i "error\|exception\|fatal" > /dev/null; then
        local backend_errors=$(docker-compose -f "$COMPOSE_FILE" logs --since=1h kdt-backend 2>/dev/null | grep -i "error\|exception\|fatal" | wc -l)
        warning "Found $backend_errors error(s) in backend logs (last hour)"
        ((error_count += backend_errors))
    fi
    
    # Check system logs
    if journalctl --since="1 hour ago" | grep -i "error\|failed" > /dev/null 2>&1; then
        local system_errors=$(journalctl --since="1 hour ago" | grep -i "error\|failed" | wc -l)
        warning "Found $system_errors error(s) in system logs (last hour)"
        ((error_count += system_errors))
    fi
    
    if [ $error_count -eq 0 ]; then
        info "No recent errors found in logs"
    fi
    
    return $error_count
}

# Generate monitoring report
generate_report() {
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    local report_file="$PROJECT_DIR/monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
KDT Monitoring Report
====================
Date: $timestamp
Raspberry Pi: $(hostname)
Uptime: $(uptime -p)

System Resources:
- CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')
- Memory Usage: $(free -h | grep Mem | awk '{print $3"/"$2}')
- Disk Usage: $(df -h "$PROJECT_DIR" | awk 'NR==2{print $3"/"$2" ("$5")"}')
- Temperature: $([ -f /sys/class/thermal/thermal_zone0/temp ] && echo "$(($(cat /sys/class/thermal/thermal_zone0/temp) / 1000))°C" || echo "N/A")

Docker Services:
$(docker-compose -f "$COMPOSE_FILE" ps)

Application Health:
- Frontend: $(curl -f -s http://localhost:3000/health > /dev/null && echo "OK" || echo "FAILED")
- Backend: $(curl -f -s http://localhost:8000/api/v1/health > /dev/null && echo "OK" || echo "FAILED")
- Database: $(docker-compose -f "$COMPOSE_FILE" exec -T kdt-postgres pg_isready -U kdt > /dev/null 2>&1 && echo "OK" || echo "FAILED")

Network:
- External connectivity: $(ping -c 1 ******* > /dev/null 2>&1 && echo "OK" || echo "FAILED")

Storage:
- Available space: $(df -h "$PROJECT_DIR" | awk 'NR==2{print $4}')
- Backup directory: $([ -d "$PROJECT_DIR/backups" ] && du -sh "$PROJECT_DIR/backups" | cut -f1 || echo "N/A")

EOF
    
    echo "$report_file"
}

# Send alert (if email is configured)
send_alert() {
    local message="$1"
    
    if [ -n "$ALERT_EMAIL" ] && command -v mail > /dev/null; then
        echo "$message" | mail -s "KDT Alert - $(hostname)" "$ALERT_EMAIL"
        log "Alert sent to $ALERT_EMAIL"
    fi
}

# Main monitoring function
main() {
    log "Starting KDT monitoring check..."
    
    local issues=0
    
    # System checks
    check_system_resources
    
    # Service checks
    if ! check_docker_services; then
        ((issues++))
        send_alert "Docker services are not running properly on $(hostname)"
    fi
    
    if ! check_application_health; then
        ((issues++))
        send_alert "Application health check failed on $(hostname)"
    fi
    
    if ! check_database_health; then
        ((issues++))
        send_alert "Database health check failed on $(hostname)"
    fi
    
    # Log checks
    local log_errors=$(check_logs; echo $?)
    if [ $log_errors -gt 0 ]; then
        ((issues++))
        send_alert "Found $log_errors errors in logs on $(hostname)"
    fi
    
    # Summary
    if [ $issues -eq 0 ]; then
        log "✓ All checks passed - system is healthy"
    else
        warning "⚠ Found $issues issue(s) - check logs for details"
    fi
    
    log "Monitoring check completed"
}

# Show help
show_help() {
    cat << EOF
KDT Production Monitoring Script

Usage: $0 [OPTIONS]

Options:
    --system        Check only system resources
    --services      Check only Docker services
    --health        Check only application health
    --database      Check only database health
    --logs          Check only log files
    --report        Generate monitoring report
    --help          Show this help message

Environment Variables:
    CPU_THRESHOLD              CPU usage threshold (default: 80%)
    MEMORY_THRESHOLD           Memory usage threshold (default: 85%)
    DISK_THRESHOLD             Disk usage threshold (default: 90%)
    RESPONSE_TIME_THRESHOLD    Response time threshold in seconds (default: 5)
    ALERT_EMAIL                Email address for alerts

Examples:
    $0                  # Full monitoring check
    $0 --system         # Check system resources only
    $0 --report         # Generate monitoring report

EOF
}

# Parse command line arguments
case "${1:-full}" in
    --system)
        check_system_resources
        ;;
    --services)
        check_docker_services
        ;;
    --health)
        check_application_health
        ;;
    --database)
        check_database_health
        ;;
    --logs)
        check_logs
        ;;
    --report)
        report_file=$(generate_report)
        log "Monitoring report generated: $report_file"
        ;;
    --help)
        show_help
        ;;
    full|*)
        main
        ;;
esac
