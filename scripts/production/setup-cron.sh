#!/bin/bash

# Setup cron jobs for KDT production monitoring and backups

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Make scripts executable
chmod +x "$SCRIPT_DIR/backup.sh"
chmod +x "$SCRIPT_DIR/monitor.sh"

log "Setting up cron jobs for KDT production..."

# Create cron jobs
CRON_JOBS=$(cat << EOF
# KDT Production Cron Jobs
# Daily backup at 2:00 AM
0 2 * * * $SCRIPT_DIR/backup.sh >> $PROJECT_DIR/backup.log 2>&1

# Monitoring every 5 minutes
*/5 * * * * $SCRIPT_DIR/monitor.sh >> $PROJECT_DIR/monitor.log 2>&1

# Weekly cleanup of old logs (keep last 30 days)
0 3 * * 0 find $PROJECT_DIR -name "*.log" -type f -mtime +30 -delete

# Monthly system update check
0 4 1 * * apt list --upgradable >> $PROJECT_DIR/system-updates.log 2>&1
EOF
)

# Add cron jobs
echo "$CRON_JOBS" | crontab -

log "Cron jobs installed successfully!"

info "Installed cron jobs:"
echo "- Daily backup at 2:00 AM"
echo "- Monitoring every 5 minutes"
echo "- Weekly log cleanup"
echo "- Monthly system update check"

info "To view current cron jobs: crontab -l"
info "To edit cron jobs: crontab -e"

# Create log rotation configuration
log "Setting up log rotation..."

sudo tee /etc/logrotate.d/kdt << EOF > /dev/null
$PROJECT_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
}
EOF

log "Log rotation configured"

# Test cron jobs
log "Testing cron job setup..."

# Test backup script
info "Testing backup script..."
if $SCRIPT_DIR/backup.sh --help > /dev/null 2>&1; then
    info "✓ Backup script is working"
else
    echo "✗ Backup script test failed"
fi

# Test monitoring script
info "Testing monitoring script..."
if $SCRIPT_DIR/monitor.sh --help > /dev/null 2>&1; then
    info "✓ Monitoring script is working"
else
    echo "✗ Monitoring script test failed"
fi

log "Cron setup completed!"

cat << EOF

Next Steps:
1. Review the cron jobs: crontab -l
2. Check log files in: $PROJECT_DIR
3. Monitor the first backup: tail -f $PROJECT_DIR/backup.log
4. Monitor system health: tail -f $PROJECT_DIR/monitor.log

To manually run:
- Backup: $SCRIPT_DIR/backup.sh
- Monitor: $SCRIPT_DIR/monitor.sh

EOF
