#!/bin/bash

# KDT Production Backup Script for Raspberry Pi
# This script creates automated backups of the database and application files

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="$PROJECT_DIR/backups"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.prod.yml"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-7}

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log "Created backup directory: $BACKUP_DIR"
    fi
}

# Check if services are running
check_services() {
    if ! docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        error "KDT services are not running. Cannot create backup."
    fi
}

# Create database backup
backup_database() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/database_backup_$timestamp.sql"
    
    log "Creating database backup..."
    
    # Create database dump
    docker-compose -f "$COMPOSE_FILE" exec -T kdt-postgres pg_dump -U kdt kdt > "$backup_file"
    
    # Compress the backup
    gzip "$backup_file"
    
    local compressed_file="${backup_file}.gz"
    local file_size=$(du -h "$compressed_file" | cut -f1)
    
    log "Database backup created: $(basename "$compressed_file") (Size: $file_size)"
    echo "$compressed_file"
}

# Create application files backup
backup_application() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/application_backup_$timestamp.tar.gz"
    
    log "Creating application files backup..."
    
    # Create tar archive of important files
    tar -czf "$backup_file" \
        --exclude="node_modules" \
        --exclude="vendor" \
        --exclude="storage/logs" \
        --exclude="storage/framework/cache" \
        --exclude="storage/framework/sessions" \
        --exclude="storage/framework/views" \
        --exclude=".git" \
        --exclude="backups" \
        -C "$PROJECT_DIR" \
        backend/app \
        backend/config \
        backend/database \
        backend/routes \
        backend/resources \
        backend/public \
        backend/composer.json \
        backend/composer.lock \
        backend/.env.production.example \
        src \
        public \
        package.json \
        package-lock.json \
        docker-compose.prod.yml \
        deploy.sh \
        deploy \
        scripts/production
    
    local file_size=$(du -h "$backup_file" | cut -f1)
    log "Application backup created: $(basename "$backup_file") (Size: $file_size)"
    echo "$backup_file"
}

# Create storage backup
backup_storage() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/storage_backup_$timestamp.tar.gz"
    
    log "Creating storage backup..."
    
    # Backup storage volume
    docker run --rm \
        -v kdt_backend_storage:/source:ro \
        -v "$BACKUP_DIR:/backup" \
        alpine:latest \
        tar -czf "/backup/$(basename "$backup_file")" -C /source .
    
    local file_size=$(du -h "$backup_file" | cut -f1)
    log "Storage backup created: $(basename "$backup_file") (Size: $file_size)"
    echo "$backup_file"
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up backups older than $RETENTION_DAYS days..."
    
    local deleted_count=0
    
    # Clean database backups
    while IFS= read -r -d '' file; do
        rm "$file"
        ((deleted_count++))
        log "Deleted old backup: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "database_backup_*.sql.gz" -type f -mtime +$RETENTION_DAYS -print0)
    
    # Clean application backups
    while IFS= read -r -d '' file; do
        rm "$file"
        ((deleted_count++))
        log "Deleted old backup: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "application_backup_*.tar.gz" -type f -mtime +$RETENTION_DAYS -print0)
    
    # Clean storage backups
    while IFS= read -r -d '' file; do
        rm "$file"
        ((deleted_count++))
        log "Deleted old backup: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "storage_backup_*.tar.gz" -type f -mtime +$RETENTION_DAYS -print0)
    
    if [ $deleted_count -eq 0 ]; then
        log "No old backups to clean up"
    else
        log "Cleaned up $deleted_count old backup(s)"
    fi
}

# Create backup summary
create_backup_summary() {
    local db_backup="$1"
    local app_backup="$2"
    local storage_backup="$3"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    
    cat > "$BACKUP_DIR/backup_summary_$(date +%Y%m%d_%H%M%S).txt" << EOF
KDT Backup Summary
==================
Date: $timestamp
Raspberry Pi: $(hostname)

Database Backup: $(basename "$db_backup")
Application Backup: $(basename "$app_backup")
Storage Backup: $(basename "$storage_backup")

Database Size: $(du -h "$db_backup" | cut -f1)
Application Size: $(du -h "$app_backup" | cut -f1)
Storage Size: $(du -h "$storage_backup" | cut -f1)

Total Backup Size: $(du -sh "$BACKUP_DIR" | cut -f1)
Available Disk Space: $(df -h "$BACKUP_DIR" | awk 'NR==2{print $4}')

Backup Location: $BACKUP_DIR
Retention Policy: $RETENTION_DAYS days
EOF
}

# Main backup function
main() {
    log "Starting KDT backup process..."
    
    create_backup_dir
    check_services
    
    # Create backups
    local db_backup=$(backup_database)
    local app_backup=$(backup_application)
    local storage_backup=$(backup_storage)
    
    # Create summary
    create_backup_summary "$db_backup" "$app_backup" "$storage_backup"
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Show summary
    log "Backup process completed successfully!"
    log "Total backup size: $(du -sh "$BACKUP_DIR" | cut -f1)"
    log "Available disk space: $(df -h "$BACKUP_DIR" | awk 'NR==2{print $4}')"
}

# Show help
show_help() {
    cat << EOF
KDT Production Backup Script

Usage: $0 [OPTIONS]

Options:
    --database-only     Create only database backup
    --application-only  Create only application files backup
    --storage-only      Create only storage backup
    --cleanup-only      Only cleanup old backups
    --help             Show this help message

Environment Variables:
    BACKUP_RETENTION_DAYS    Number of days to keep backups (default: 7)

Examples:
    $0                      # Full backup
    $0 --database-only      # Database backup only
    $0 --cleanup-only       # Cleanup old backups only

EOF
}

# Parse command line arguments
case "${1:-full}" in
    --database-only)
        create_backup_dir
        check_services
        backup_database
        ;;
    --application-only)
        create_backup_dir
        backup_application
        ;;
    --storage-only)
        create_backup_dir
        check_services
        backup_storage
        ;;
    --cleanup-only)
        create_backup_dir
        cleanup_old_backups
        ;;
    --help)
        show_help
        ;;
    full|*)
        main
        ;;
esac
