#!/bin/bash

# <PERSON>ript to remove all console.log statements from React frontend code
# This prepares the codebase for production deployment

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
SRC_DIR="$PROJECT_DIR/src"

log "Starting console.log removal from React frontend code..."

# Check if src directory exists
if [ ! -d "$SRC_DIR" ]; then
    error "Source directory not found: $SRC_DIR"
fi

# Count initial console.log statements
INITIAL_COUNT=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "console\.log" | wc -l)
log "Found $INITIAL_COUNT console.log statements to remove"

# Create backup directory
BACKUP_DIR="$PROJECT_DIR/backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
log "Created backup directory: $BACKUP_DIR"

# Backup files that contain console.log
info "Creating backups of files with console.log statements..."
find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | while read -r file; do
    if grep -q "console\.log" "$file"; then
        relative_path="${file#$PROJECT_DIR/}"
        backup_file="$BACKUP_DIR/$relative_path"
        mkdir -p "$(dirname "$backup_file")"
        cp "$file" "$backup_file"
    fi
done

# Remove console.log statements
info "Removing console.log statements..."

# Method 1: Remove standalone console.log lines
find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | while read -r file; do
    # Remove lines that only contain console.log statements (with optional whitespace)
    sed -i.bak '/^[[:space:]]*console\.log.*$/d' "$file"
    
    # Remove console.log statements that are part of other lines
    sed -i.bak 's/console\.log([^;]*);[[:space:]]*//g' "$file"
    
    # Clean up any remaining console.log calls
    sed -i.bak 's/[[:space:]]*console\.log([^)]*);*[[:space:]]*//g' "$file"
    
    # Remove the backup file created by sed
    rm -f "$file.bak"
done

# Count remaining console.log statements
FINAL_COUNT=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "console\.log" | wc -l || echo "0")
REMOVED_COUNT=$((INITIAL_COUNT - FINAL_COUNT))

log "Removed $REMOVED_COUNT console.log statements"

if [ "$FINAL_COUNT" -gt 0 ]; then
    warning "$FINAL_COUNT console.log statements remain (may need manual review)"
    echo ""
    info "Remaining console.log statements:"
    find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "console\.log" || true
else
    log "All console.log statements successfully removed!"
fi

# Check for other debug statements
info "Checking for other debug statements..."
DEBUG_COUNT=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n -E "(console\.(warn|error|debug|info)|debugger)" | wc -l || echo "0")

if [ "$DEBUG_COUNT" -gt 0 ]; then
    warning "Found $DEBUG_COUNT other debug statements that may need review:"
    find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n -E "(console\.(warn|error|debug|info)|debugger)" || true
fi

log "Console.log removal completed!"
log "Backup created at: $BACKUP_DIR"
log "Please test the application to ensure functionality is preserved"
