#!/bin/bash

# Install Git Hooks for Security
# This script installs the pre-commit hook to prevent credential leaks

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Installing Git Security Hooks...${NC}"

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
GIT_HOOKS_DIR="$PROJECT_ROOT/.git/hooks"
CUSTOM_HOOKS_DIR="$PROJECT_ROOT/.githooks"

# Check if we're in a git repository
if [[ ! -d "$PROJECT_ROOT/.git" ]]; then
    echo -e "${RED}❌ Error: Not in a git repository${NC}"
    exit 1
fi

# Check if custom hooks directory exists
if [[ ! -d "$CUSTOM_HOOKS_DIR" ]]; then
    echo -e "${RED}❌ Error: Custom hooks directory not found: $CUSTOM_HOOKS_DIR${NC}"
    exit 1
fi

echo "Project root: $PROJECT_ROOT"
echo "Git hooks directory: $GIT_HOOKS_DIR"
echo "Custom hooks directory: $CUSTOM_HOOKS_DIR"
echo ""

# Install pre-commit hook
if [[ -f "$CUSTOM_HOOKS_DIR/pre-commit" ]]; then
    echo -e "${BLUE}📋 Installing pre-commit hook...${NC}"
    
    # Backup existing hook if it exists
    if [[ -f "$GIT_HOOKS_DIR/pre-commit" ]]; then
        echo -e "${YELLOW}⚠️  Backing up existing pre-commit hook${NC}"
        cp "$GIT_HOOKS_DIR/pre-commit" "$GIT_HOOKS_DIR/pre-commit.backup.$(date +%Y%m%d-%H%M%S)"
    fi
    
    # Copy and make executable
    cp "$CUSTOM_HOOKS_DIR/pre-commit" "$GIT_HOOKS_DIR/pre-commit"
    chmod +x "$GIT_HOOKS_DIR/pre-commit"
    
    echo -e "${GREEN}✅ Pre-commit hook installed successfully${NC}"
else
    echo -e "${RED}❌ Error: pre-commit hook not found in $CUSTOM_HOOKS_DIR${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Git security hooks installed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 What this does:${NC}"
echo "   • Scans commits for hardcoded credentials"
echo "   • Prevents API keys and passwords from being committed"
echo "   • Checks for debug statements in production code"
echo "   • Validates production readiness"
echo ""
echo -e "${YELLOW}💡 Usage:${NC}"
echo "   • Hooks run automatically on 'git commit'"
echo "   • Use 'git commit --no-verify' to bypass (not recommended)"
echo "   • Configure real credentials through Settings > Security"
echo ""
echo -e "${BLUE}🔍 Test the hook:${NC}"
echo "   Try committing a file with a test credential to see it in action!"
echo ""
