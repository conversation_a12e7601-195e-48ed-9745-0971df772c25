#!/usr/bin/env node

/**
 * KDT Auto-Rebuild Watcher
 * Watches for file changes and automatically rebuilds containers
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const chokidar = require('chokidar');

// Configuration
const config = {
  watchDirs: ['backend', 'src', 'public'],
  backendTriggers: ['backend/app/**', 'backend/config/**', 'backend/database/**', 'backend/routes/**'],
  frontendTriggers: ['src/**', 'public/**', 'index.html', 'package.json', 'vite.config.ts'],
  fullRebuildTriggers: ['backend/composer.json', 'backend/Dockerfile', 'Dockerfile', 'docker-compose.yml', 'docker-compose.dev.yml'],
  cooldownMs: 5000, // 5 seconds
  composeFile: 'docker-compose.dev.yml'
};

// State
let state = {
  backendNeedsRebuild: false,
  frontendNeedsRebuild: false,
  fullRebuildNeeded: false,
  lastRebuildTime: 0,
  isRebuilding: false
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Logging functions
const log = (message, color = colors.blue) => {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${color}[${timestamp}]${colors.reset} ${message}`);
};

const logSuccess = (message) => log(message, colors.green);
const logWarning = (message) => log(message, colors.yellow);
const logError = (message) => log(message, colors.red);

// Execute shell command
const execCommand = (command, options = {}) => {
  return new Promise((resolve, reject) => {
    exec(command, options, (error, stdout, stderr) => {
      if (error) {
        reject({ error, stdout, stderr });
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
};

// Check if containers are running
const checkContainersStatus = async () => {
  try {
    const { stdout } = await execCommand(`docker-compose -f ${config.composeFile} ps`);
    return stdout.includes('Up');
  } catch (error) {
    return false;
  }
};

// Start development environment
const startDevEnvironment = async () => {
  try {
    log('Starting development environment...');
    await execCommand(`docker-compose -f ${config.composeFile} up -d`);
    logSuccess('Development environment started');
  } catch (error) {
    logError('Failed to start development environment');
    console.error(error);
  }
};

// Rebuild backend container
const rebuildBackend = async () => {
  if (!state.backendNeedsRebuild || state.isRebuilding) return;
  
  try {
    state.isRebuilding = true;
    log('Rebuilding backend container...');
    
    await execCommand(`docker-compose -f ${config.composeFile} build kdt-backend --no-cache`);
    await execCommand(`docker-compose -f ${config.composeFile} restart kdt-backend`);
    
    logSuccess('Backend container rebuilt and restarted');
    state.backendNeedsRebuild = false;
  } catch (error) {
    logError('Failed to rebuild backend container');
    console.error(error);
  } finally {
    state.isRebuilding = false;
  }
};

// Rebuild frontend container
const rebuildFrontend = async () => {
  if (!state.frontendNeedsRebuild || state.isRebuilding) return;
  
  try {
    state.isRebuilding = true;
    log('Rebuilding frontend container...');
    
    await execCommand(`docker-compose -f ${config.composeFile} build kdt-frontend --no-cache`);
    await execCommand(`docker-compose -f ${config.composeFile} restart kdt-frontend`);
    
    logSuccess('Frontend container rebuilt and restarted');
    state.frontendNeedsRebuild = false;
  } catch (error) {
    logError('Failed to rebuild frontend container');
    console.error(error);
  } finally {
    state.isRebuilding = false;
  }
};

// Full rebuild
const fullRebuild = async () => {
  if (!state.fullRebuildNeeded || state.isRebuilding) return;
  
  try {
    state.isRebuilding = true;
    log('Performing full rebuild...');
    
    await execCommand(`docker-compose -f ${config.composeFile} down`);
    await execCommand(`docker-compose -f ${config.composeFile} build --no-cache`);
    await execCommand(`docker-compose -f ${config.composeFile} up -d`);
    
    logSuccess('Full rebuild completed');
    state.fullRebuildNeeded = false;
    state.backendNeedsRebuild = false;
    state.frontendNeedsRebuild = false;
  } catch (error) {
    logError('Failed to perform full rebuild');
    console.error(error);
  } finally {
    state.isRebuilding = false;
  }
};

// Check if file matches patterns
const matchesPatterns = (filePath, patterns) => {
  return patterns.some(pattern => {
    const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
    return regex.test(filePath);
  });
};

// Process file change
const processFileChange = (filePath) => {
  const currentTime = Date.now();
  
  // Skip if in cooldown period
  if (currentTime - state.lastRebuildTime < config.cooldownMs) {
    return;
  }
  
  log(`File changed: ${filePath}`);
  
  // Check if full rebuild is needed
  if (matchesPatterns(filePath, config.fullRebuildTriggers)) {
    logWarning(`Full rebuild triggered by: ${filePath}`);
    state.fullRebuildNeeded = true;
    return;
  }
  
  // Check backend changes
  if (matchesPatterns(filePath, config.backendTriggers)) {
    log(`Backend rebuild needed due to: ${filePath}`);
    state.backendNeedsRebuild = true;
  }
  
  // Check frontend changes
  if (matchesPatterns(filePath, config.frontendTriggers)) {
    log(`Frontend rebuild needed due to: ${filePath}`);
    state.frontendNeedsRebuild = true;
  }
};

// Perform rebuilds
const performRebuilds = async () => {
  if (state.fullRebuildNeeded) {
    await fullRebuild();
  } else {
    if (state.backendNeedsRebuild) {
      await rebuildBackend();
    }
    if (state.frontendNeedsRebuild) {
      await rebuildFrontend();
    }
  }
  
  state.lastRebuildTime = Date.now();
};

// Debounced rebuild function
let rebuildTimeout;
const debouncedRebuild = () => {
  clearTimeout(rebuildTimeout);
  rebuildTimeout = setTimeout(performRebuilds, 1000); // 1 second debounce
};

// Main function
const main = async () => {
  log('Starting KDT Auto-Rebuild Watcher...');
  log(`Watching directories: ${config.watchDirs.join(', ')}`);
  
  // Check if development environment is running
  const isRunning = await checkContainersStatus();
  if (!isRunning) {
    await startDevEnvironment();
  }
  
  // Setup file watcher
  const watcher = chokidar.watch(config.watchDirs, {
    ignored: [
      '**/node_modules/**',
      '**/vendor/**',
      '**/storage/**',
      '**/.git/**',
      '**/dist/**',
      '**/build/**'
    ],
    persistent: true,
    ignoreInitial: true
  });
  
  watcher.on('change', (filePath) => {
    processFileChange(filePath);
    debouncedRebuild();
  });
  
  watcher.on('add', (filePath) => {
    processFileChange(filePath);
    debouncedRebuild();
  });
  
  watcher.on('unlink', (filePath) => {
    processFileChange(filePath);
    debouncedRebuild();
  });
  
  logSuccess('File watcher started. Press Ctrl+C to stop.');
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log('Stopping file watcher...');
    watcher.close();
    process.exit(0);
  });
};

// CLI handling
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
KDT Auto-Rebuild Watcher

Usage: node watch-and-rebuild.js [options]

Options:
  --help, -h     Show this help message
  --init         Initialize development environment
  --stop         Stop development environment

This script watches for file changes and automatically rebuilds containers.
  `);
  process.exit(0);
}

if (args.includes('--init')) {
  (async () => {
    log('Initializing development environment...');
    try {
      await execCommand(`docker-compose -f ${config.composeFile} down`);
      await execCommand(`docker-compose -f ${config.composeFile} build --no-cache`);
      await execCommand(`docker-compose -f ${config.composeFile} up -d`);
      logSuccess('Development environment initialized');
    } catch (error) {
      logError('Failed to initialize development environment');
      console.error(error);
    }
  })();
} else if (args.includes('--stop')) {
  (async () => {
    log('Stopping development environment...');
    try {
      await execCommand(`docker-compose -f ${config.composeFile} down`);
      logSuccess('Development environment stopped');
    } catch (error) {
      logError('Failed to stop development environment');
      console.error(error);
    }
  })();
} else {
  main().catch(console.error);
}
