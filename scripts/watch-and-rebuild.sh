#!/bin/bash

# Auto-rebuild script for KDT development
# This script watches for file changes and automatically rebuilds containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WATCH_DIRS=("backend" "src" "public")
BACKEND_TRIGGER_FILES=("backend/app" "backend/config" "backend/database" "backend/routes")
FRONTEND_TRIGGER_FILES=("src" "public" "index.html" "package.json" "vite.config.ts")
FULL_REBUILD_TRIGGER_FILES=("backend/composer.json" "backend/Dockerfile" "Dockerfile" "docker-compose.yml" "docker-compose.dev.yml")

# Flags
BACKEND_NEEDS_REBUILD=false
FRONTEND_NEEDS_REBUILD=false
FULL_REBUILD_NEEDED=false
LAST_REBUILD_TIME=0
REBUILD_COOLDOWN=5  # seconds

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')]${NC} $1"
}

check_dependencies() {
    if ! command -v fswatch &> /dev/null; then
        log_error "fswatch is required but not installed."
        log "Installing fswatch..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install fswatch
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y fswatch
        else
            log_error "Please install fswatch manually for your system"
            exit 1
        fi
    fi
}

rebuild_backend() {
    if [ "$BACKEND_NEEDS_REBUILD" = true ]; then
        log "Rebuilding backend container..."
        docker-compose -f docker-compose.dev.yml build kdt-backend --no-cache
        docker-compose -f docker-compose.dev.yml restart kdt-backend
        log_success "Backend container rebuilt and restarted"
        BACKEND_NEEDS_REBUILD=false
    fi
}

rebuild_frontend() {
    if [ "$FRONTEND_NEEDS_REBUILD" = true ]; then
        log "Rebuilding frontend container..."
        docker-compose -f docker-compose.dev.yml build kdt-frontend --no-cache
        docker-compose -f docker-compose.dev.yml restart kdt-frontend
        log_success "Frontend container rebuilt and restarted"
        FRONTEND_NEEDS_REBUILD=false
    fi
}

full_rebuild() {
    if [ "$FULL_REBUILD_NEEDED" = true ]; then
        log "Performing full rebuild..."
        docker-compose -f docker-compose.dev.yml down
        docker-compose -f docker-compose.dev.yml build --no-cache
        docker-compose -f docker-compose.dev.yml up -d
        log_success "Full rebuild completed"
        FULL_REBUILD_NEEDED=false
        BACKEND_NEEDS_REBUILD=false
        FRONTEND_NEEDS_REBUILD=false
    fi
}

restart_services() {
    log "Restarting services to apply changes..."
    docker-compose -f docker-compose.dev.yml restart kdt-backend kdt-frontend
    log_success "Services restarted"
}

process_file_change() {
    local changed_file="$1"
    local current_time=$(date +%s)
    
    # Skip if in cooldown period
    if [ $((current_time - LAST_REBUILD_TIME)) -lt $REBUILD_COOLDOWN ]; then
        return
    fi
    
    log "File changed: $changed_file"
    
    # Check if full rebuild is needed
    for pattern in "${FULL_REBUILD_TRIGGER_FILES[@]}"; do
        if [[ "$changed_file" == *"$pattern"* ]]; then
            log_warning "Full rebuild triggered by: $changed_file"
            FULL_REBUILD_NEEDED=true
            return
        fi
    done
    
    # Check backend changes
    for pattern in "${BACKEND_TRIGGER_FILES[@]}"; do
        if [[ "$changed_file" == *"$pattern"* ]]; then
            log "Backend rebuild needed due to: $changed_file"
            BACKEND_NEEDS_REBUILD=true
            break
        fi
    done
    
    # Check frontend changes
    for pattern in "${FRONTEND_TRIGGER_FILES[@]}"; do
        if [[ "$changed_file" == *"$pattern"* ]]; then
            log "Frontend rebuild needed due to: $changed_file"
            FRONTEND_NEEDS_REBUILD=true
            break
        fi
    done
}

perform_rebuilds() {
    if [ "$FULL_REBUILD_NEEDED" = true ]; then
        full_rebuild
    elif [ "$BACKEND_NEEDS_REBUILD" = true ] || [ "$FRONTEND_NEEDS_REBUILD" = true ]; then
        rebuild_backend
        rebuild_frontend
    fi
    
    LAST_REBUILD_TIME=$(date +%s)
}

# Main execution
main() {
    log "Starting KDT Auto-Rebuild Watcher..."
    log "Watching directories: ${WATCH_DIRS[*]}"
    
    check_dependencies
    
    # Initial setup
    if ! docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        log "Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d
    fi
    
    # Watch for changes
    fswatch -r -e ".*" -i "\.(php|js|ts|tsx|jsx|vue|css|scss|json|yml|yaml|env)$" "${WATCH_DIRS[@]}" | while read file; do
        process_file_change "$file"
        perform_rebuilds
    done
}

# Handle script termination
cleanup() {
    log "Stopping file watcher..."
    exit 0
}

trap cleanup SIGINT SIGTERM

# Check if running in development mode
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "KDT Auto-Rebuild Watcher"
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --init         Initialize development environment"
    echo "  --stop         Stop development environment"
    echo ""
    echo "This script watches for file changes and automatically rebuilds containers."
    exit 0
fi

if [ "$1" = "--init" ]; then
    log "Initializing development environment..."
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.dev.yml build --no-cache
    docker-compose -f docker-compose.dev.yml up -d
    log_success "Development environment initialized"
    exit 0
fi

if [ "$1" = "--stop" ]; then
    log "Stopping development environment..."
    docker-compose -f docker-compose.dev.yml down
    log_success "Development environment stopped"
    exit 0
fi

main
