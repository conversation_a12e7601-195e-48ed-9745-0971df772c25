#!/bin/bash

# KDT Development Environment Manager
# Simplified script for managing development environment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.dev.yml" ]; then
    log_error "docker-compose.dev.yml not found. Please run this script from the project root."
    exit 1
fi

# Function to check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is required but not installed."
        log "Please install Node.js from https://nodejs.org/"
        exit 1
    fi
}

# Function to install watcher dependencies
install_watcher_deps() {
    if [ ! -d "scripts/node_modules" ]; then
        log "Installing watcher dependencies..."
        cd scripts
        npm install
        cd ..
        log_success "Watcher dependencies installed"
    fi
}

# Function to start development environment
start_dev() {
    log "Starting development environment..."
    docker-compose -f docker-compose.dev.yml up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be ready..."
    sleep 10
    
    # Check service status
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        log_success "Development environment started successfully"
        log "Frontend: http://localhost:3000"
        log "Backend API: http://localhost:8000"
        log "Adminer: http://localhost:8001"
    else
        log_error "Some services failed to start"
        docker-compose -f docker-compose.dev.yml ps
    fi
}

# Function to stop development environment
stop_dev() {
    log "Stopping development environment..."
    docker-compose -f docker-compose.dev.yml down
    log_success "Development environment stopped"
}

# Function to rebuild development environment
rebuild_dev() {
    log "Rebuilding development environment..."
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.dev.yml build --no-cache
    docker-compose -f docker-compose.dev.yml up -d
    log_success "Development environment rebuilt"
}

# Function to start file watcher
start_watcher() {
    check_node
    install_watcher_deps
    
    log "Starting auto-rebuild file watcher..."
    cd scripts
    node watch-and-rebuild.js
}

# Function to show logs
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose -f docker-compose.dev.yml logs -f "$service"
    fi
}

# Function to show status
show_status() {
    log "Development environment status:"
    docker-compose -f docker-compose.dev.yml ps
    
    echo ""
    log "Service URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8000"
    echo "  Adminer: http://localhost:8001"
}

# Function to run backend commands
backend_cmd() {
    docker-compose -f docker-compose.dev.yml exec kdt-backend "$@"
}

# Function to run frontend commands
frontend_cmd() {
    docker-compose -f docker-compose.dev.yml exec kdt-frontend "$@"
}

# Function to show help
show_help() {
    echo "KDT Development Environment Manager"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  start           Start development environment"
    echo "  stop            Stop development environment"
    echo "  restart         Restart development environment"
    echo "  rebuild         Rebuild and restart development environment"
    echo "  watch           Start auto-rebuild file watcher"
    echo "  status          Show development environment status"
    echo "  logs [service]  Show logs (optionally for specific service)"
    echo "  backend <cmd>   Run command in backend container"
    echo "  frontend <cmd>  Run command in frontend container"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start development environment"
    echo "  $0 watch                    # Start with auto-rebuild watcher"
    echo "  $0 logs kdt-backend         # Show backend logs"
    echo "  $0 backend php artisan migrate  # Run migration in backend"
    echo "  $0 frontend npm install     # Install frontend dependencies"
}

# Main command handling
case "$1" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        stop_dev
        start_dev
        ;;
    rebuild)
        rebuild_dev
        ;;
    watch)
        start_watcher
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    backend)
        shift
        backend_cmd "$@"
        ;;
    frontend)
        shift
        frontend_cmd "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
