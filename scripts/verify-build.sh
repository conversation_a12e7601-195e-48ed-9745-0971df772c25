#!/bin/bash

# Verify Build Configuration Script
# This script checks that the built frontend has the correct API URL configuration

echo "🔍 Verifying build configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if build directory exists
if [ ! -d "frontend/dist" ]; then
    echo -e "${RED}❌ Build directory 'frontend/dist' not found!${NC}"
    echo "Please run 'npm run build:pi' first."
    exit 1
fi

# Check if built JS files exist
JS_FILES=$(find frontend/dist/assets -name "index-*.js" 2>/dev/null)
if [ -z "$JS_FILES" ]; then
    echo -e "${RED}❌ No built JavaScript files found in frontend/dist/assets/${NC}"
    exit 1
fi

echo -e "${BLUE}📁 Found built files:${NC}"
echo "$JS_FILES"
echo ""

# Check for problematic localhost URLs
echo -e "${BLUE}🔍 Checking for problematic localhost URLs...${NC}"
LOCALHOST_FOUND=$(grep -o "localhost:[0-9]*" $JS_FILES 2>/dev/null || true)

if [ -n "$LOCALHOST_FOUND" ]; then
    echo -e "${RED}❌ Found problematic localhost URLs in built files:${NC}"
    echo "$LOCALHOST_FOUND"
    echo ""
    echo -e "${YELLOW}This will cause CORS errors in production deployment!${NC}"
    echo "The frontend should use relative URLs (/api/v1) or the same domain."
    exit 1
else
    echo -e "${GREEN}✅ No problematic localhost URLs found${NC}"
fi

# Check for correct API URL pattern
echo -e "${BLUE}🔍 Checking for correct API URL pattern...${NC}"
API_URLS=$(grep -o "/api/v1\|https://[^/]*/api/v1" $JS_FILES 2>/dev/null || true)

if [ -n "$API_URLS" ]; then
    echo -e "${GREEN}✅ Found correct API URL patterns:${NC}"
    echo "$API_URLS" | sort | uniq
else
    echo -e "${YELLOW}⚠️  No API URL patterns found - this might be an issue${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Build verification completed successfully!${NC}"
echo "The built frontend is ready for production deployment."
