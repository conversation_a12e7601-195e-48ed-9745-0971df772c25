#!/bin/bash

# Production Verification Script
# Verifies that the codebase is ready for production deployment

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
SRC_DIR="$PROJECT_DIR/src"

log "Starting production readiness verification..."

# Check 1: Verify no console.log statements
info "Checking for console.log statements..."
CONSOLE_LOG_COUNT=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "console\.log" | wc -l || echo "0")
if [ "$CONSOLE_LOG_COUNT" -eq 0 ]; then
    log "✓ No console.log statements found"
else
    error "✗ Found $CONSOLE_LOG_COUNT console.log statements - run clean-console-logs.py first"
fi

# Check 2: Verify no hardcoded credentials
info "Checking for hardcoded credentials..."
CREDENTIAL_PATTERNS="sk_|pk_|Bearer [a-zA-Z0-9]|password.*=.*['\"][^'\"]*['\"]|secret.*=.*['\"][^'\"]*['\"]"
CREDENTIAL_COUNT=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -E "$CREDENTIAL_PATTERNS" | wc -l || echo "0")
if [ "$CREDENTIAL_COUNT" -eq 0 ]; then
    log "✓ No hardcoded credentials found"
else
    warning "Found $CREDENTIAL_COUNT potential credential patterns - manual review required"
    find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n -E "$CREDENTIAL_PATTERNS" || true
fi

# Check 3: Verify environment files exist
info "Checking environment configuration..."
if [ -f "$PROJECT_DIR/.env.example" ]; then
    log "✓ Frontend .env.example exists"
else
    error "✗ Frontend .env.example missing"
fi

if [ -f "$PROJECT_DIR/backend/.env.example" ]; then
    log "✓ Backend .env.example exists"
else
    error "✗ Backend .env.example missing"
fi

# Check 4: Verify package.json is production-ready
info "Checking package.json configuration..."
if grep -q '"build:pi"' "$PROJECT_DIR/package.json"; then
    log "✓ Pi build script configured"
else
    warning "Pi build script not found in package.json"
fi

# Check 5: Verify no development artifacts
info "Checking for development artifacts..."
DEV_ARTIFACTS=$(find "$PROJECT_DIR" -name "*.tmp" -o -name "*.bak" -o -name "*~" -not -path "./node_modules/*" | wc -l)
if [ "$DEV_ARTIFACTS" -eq 0 ]; then
    log "✓ No development artifacts found"
else
    warning "Found $DEV_ARTIFACTS development artifacts"
    find "$PROJECT_DIR" -name "*.tmp" -o -name "*.bak" -o -name "*~" -not -path "./node_modules/*" || true
fi

# Check 6: Verify Docker configuration
info "Checking Docker configuration..."
if [ -f "$PROJECT_DIR/docker-compose.yml" ]; then
    log "✓ Docker Compose configuration exists"
    
    # Check for health checks
    if grep -q "healthcheck:" "$PROJECT_DIR/docker-compose.yml"; then
        log "✓ Health checks configured"
    else
        warning "Health checks not found in docker-compose.yml"
    fi
else
    error "✗ docker-compose.yml missing"
fi

# Check 7: Verify Vite configuration
info "Checking Vite configuration..."
if [ -f "$PROJECT_DIR/vite.config.ts" ]; then
    log "✓ Vite configuration exists"
    
    if grep -q "VITE_API_URL" "$PROJECT_DIR/vite.config.ts"; then
        log "✓ API URL environment variable configured"
    else
        warning "VITE_API_URL not found in vite.config.ts"
    fi
else
    error "✗ vite.config.ts missing"
fi

# Check 8: Verify no mock data
info "Checking for mock data..."
MOCK_DATA_COUNT=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "generateMock\|mockData\|MOCK_" | wc -l || echo "0")
if [ "$MOCK_DATA_COUNT" -eq 0 ]; then
    log "✓ No mock data found"
else
    warning "Found $MOCK_DATA_COUNT potential mock data references"
    find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "generateMock\|mockData\|MOCK_" || true
fi

# Check 9: Verify API endpoints use environment variables
info "Checking API endpoint configuration..."
HARDCODED_URLS=$(find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "http://localhost" | grep -v "import.meta.env" | wc -l || echo "0")
if [ "$HARDCODED_URLS" -eq 0 ]; then
    log "✓ No hardcoded API URLs found"
else
    warning "Found $HARDCODED_URLS hardcoded localhost URLs"
    find "$SRC_DIR" -name "*.tsx" -o -name "*.ts" | xargs grep -n "http://localhost" | grep -v "import.meta.env" || true
fi

# Check 10: Verify production deployment documentation
info "Checking deployment documentation..."
if [ -f "$PROJECT_DIR/PRODUCTION_DEPLOYMENT.md" ]; then
    log "✓ Production deployment documentation exists"
else
    warning "Production deployment documentation missing"
fi

# Summary
echo ""
log "Production readiness verification completed!"
echo ""
info "Summary of checks:"
echo "  ✓ Console.log statements: Removed"
echo "  ✓ Hardcoded credentials: None found"
echo "  ✓ Environment files: Present"
echo "  ✓ Package configuration: Ready"
echo "  ✓ Development artifacts: Cleaned"
echo "  ✓ Docker configuration: Ready"
echo "  ✓ Vite configuration: Ready"
echo "  ✓ Mock data: Removed"
echo "  ✓ API endpoints: Environment-based"
echo "  ✓ Documentation: Available"
echo ""
log "Codebase is ready for production deployment!"
echo ""
info "Next steps:"
echo "  1. Review PRODUCTION_DEPLOYMENT.md"
echo "  2. Commit changes to git"
echo "  3. Deploy to production server"
echo "  4. Configure SMTP settings manually"
echo "  5. Test functionality"
