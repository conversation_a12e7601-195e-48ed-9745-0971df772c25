# Payment Simulation Guide

This guide explains how to simulate the payment process for leads/prospects viewing invoices and quotations publicly.

## Overview

The KDT CRM system includes a complete public payment system that allows leads and prospects to view and pay invoices without requiring login credentials. This is essential for the customer journey from leads → deals → quotations → invoices → payments → clients.

## Public Payment System Components

### 1. Backend Components

- **PublicPaymentController**: Handles public invoice viewing and payment processing
- **PaymentService**: Manages payment recording and transaction creation
- **Public API Routes**: No authentication required for payment processing

### 2. Frontend Components

- **PublicInvoice.tsx**: Public invoice viewing and payment form
- **PaymentSuccess.tsx**: Payment confirmation page
- **Payment simulation**: 95% success rate for demo purposes

## How to Simulate Payments

### Step 1: Create an Invoice from a Deal

1. **Navigate to Deals page** (`/deals`)
2. **Move a deal to "Won" stage** by dragging it to the Won column
3. **Check "Create invoice"** in the confirmation modal
4. **Confirm the action** - this creates an invoice automatically

### Step 2: Get the Public Payment URL

The public payment URL format is:
```
/public/invoice/{invoiceId}
```

To find the invoice ID:
1. Go to **Invoices page** (`/invoices`)
2. Find the invoice created from your deal
3. Note the invoice ID from the URL or invoice details

### Step 3: Access Public Payment Page

1. **Open a new incognito/private browser window** (to simulate a lead/prospect)
2. **Navigate to**: `http://localhost:3000/public/invoice/{invoiceId}`
3. **You should see the public invoice page** with:
   - Invoice details
   - Payment form
   - Multiple payment methods

### Step 4: Simulate Payment

The system supports three payment methods:

#### Credit Card Payment
```json
{
  "payment_method": "credit_card",
  "payment_details": {
    "cardholder_name": "John Doe",
    "card_number": "****************",
    "expiry_month": 12,
    "expiry_year": 2025,
    "cvv": "123"
  }
}
```

#### Bank Transfer
```json
{
  "payment_method": "bank_transfer",
  "payment_details": {
    "bank_name": "Maybank",
    "account_number": "**********"
  }
}
```

#### E-Wallet
```json
{
  "payment_method": "ewallet",
  "payment_details": {
    "ewallet_type": "grabpay",
    "phone_number": "+***********"
  }
}
```

### Step 5: Payment Processing

1. **Fill in the payment form** with any of the above details
2. **Click "Process Payment"**
3. **The system simulates payment processing** (95% success rate)
4. **On success**: Redirected to payment success page
5. **On failure**: Error message displayed

### Step 6: Verify Payment

After successful payment:
1. **Check the Invoices page** - invoice status should be "Paid"
2. **Check the Transactions page** - new payment transaction created
3. **Check the Clients page** - lead converted to client with updated totals

## API Endpoints for Testing

### Get Invoice Details (Public)
```http
GET /api/v1/public/invoice/{invoiceId}
```

### Process Payment (Public)
```http
POST /api/v1/public/invoice/{invoiceId}/pay
Content-Type: application/json

{
  "payment_method": "credit_card",
  "payment_details": {
    "cardholder_name": "Test User",
    "card_number": "****************",
    "expiry_month": 12,
    "expiry_year": 2025,
    "cvv": "123"
  }
}
```

### Get Payment Success Details (Public)
```http
GET /api/v1/public/payment/{transactionId}/success
```

## Testing Scenarios

### Scenario 1: Complete Customer Journey
1. Create a lead manually
2. Create a deal from that lead
3. Move deal through pipeline stages
4. Create quotation at proposal stage
5. Move deal to won and create invoice
6. Simulate public payment
7. Verify lead conversion to client

### Scenario 2: Direct Invoice Payment
1. Create an invoice manually
2. Use the public payment URL
3. Test different payment methods
4. Verify payment processing

### Scenario 3: Payment Failure Simulation
The system has a 5% failure rate built-in for testing error handling.

## Important Notes

1. **No Authentication Required**: Public payment pages don't require login
2. **Automatic Lead Conversion**: Successful payments convert leads to clients
3. **Transaction Recording**: All payments create transaction records
4. **Client Updates**: Payment updates client's total spent and transaction count
5. **Security**: Sensitive payment details are not stored permanently

## Troubleshooting

### Common Issues

1. **Invoice Not Found**: Ensure invoice status is "sent" or "draft"
2. **Payment Failed**: Try again (5% failure rate is normal)
3. **Public URL Not Working**: Check if invoice ID is correct
4. **Lead Not Converting**: Verify invoice has lead_id associated

### Debug Steps

1. Check browser console for errors
2. Verify API responses in Network tab
3. Check backend logs for payment processing
4. Ensure database relationships are correct

## Next Steps

After successful payment simulation:
1. **Test the complete CRM workflow**
2. **Verify data integrity across all modules**
3. **Test with different payment amounts**
4. **Validate client segmentation updates**
