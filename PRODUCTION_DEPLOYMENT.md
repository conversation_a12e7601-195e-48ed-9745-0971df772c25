# Production Deployment Guide

## Overview
This document outlines the production deployment process for the Tarbiah Sentap CRM application. The codebase has been cleaned and prepared for secure production deployment on Raspberry Pi servers.

## Changes Made for Production Readiness

### 1. Debug Code Removal ✅
- **Console.log statements**: Removed all 119+ console.log statements from React frontend code
- **Mock data**: Removed generateMockProducts() function and fallback mock data
- **Development artifacts**: Cleaned up .DS_Store files and temporary files
- **Error logging**: Preserved console.error statements for production error monitoring

### 2. Security Hardening ✅
- **No hardcoded credentials**: Verified no API keys, passwords, or secrets in code
- **Environment variables**: All sensitive configuration uses environment variables
- **API endpoints**: Properly configured with VITE_API_URL environment variable
- **Authentication**: Laravel Sanctum tokens properly handled without hardcoding

### 3. Environment Configuration ✅
- **Frontend .env.example**: Contains all required environment variables with placeholders
- **Backend .env.example**: Properly configured for production with Zoho SMTP setup
- **Vite configuration**: Optimized for production builds with proper environment handling
- **Docker configuration**: Ready for containerized deployment

## Manual Configuration Required on Production

### 1. SMTP Email Configuration
The application requires manual SMTP configuration on the production server:

**Location**: Settings > Email Configuration (Admin Interface)
**Required Settings**:
- SMTP Host: smtp.zoho.com
- SMTP Port: 587
- SMTP Username: [<EMAIL>]
- SMTP Password: [your-zoho-password]
- Encryption: TLS
- From Address: <EMAIL>
- From Name: Tarbiah Sentap CRM

**Note**: The application gracefully handles missing SMTP configuration during initial deployment.

### 2. Database Configuration
**Environment Variables** (backend/.env):
```
DB_CONNECTION=pgsql
DB_HOST=kdt-postgres
DB_PORT=5432
DB_DATABASE=kdt
DB_USERNAME=kdt
DB_PASSWORD=[secure-password]
```

### 3. Application Keys
**Required for Laravel**:
```bash
php artisan key:generate
```

### 4. Redis Configuration
**Environment Variables**:
```
REDIS_HOST=kdt-redis
REDIS_PORT=6379
REDIS_PASSWORD=null
```

## Pi-Native Deployment Compatibility

### 1. Docker Compose Configuration ✅
- **Services**: postgres, redis, backend, frontend all configured
- **Health checks**: Implemented for all services
- **Networking**: kdt-network bridge configured
- **Volumes**: Persistent storage for database and uploads

### 2. Build Process ✅
- **Frontend build**: `npm run build:pi` creates optimized production build
- **Backend dependencies**: composer.json ready for production installation
- **Asset optimization**: Vite configured for Raspberry Pi deployment

### 3. Port Configuration ✅
- **Frontend**: Port 3000 (configurable)
- **Backend**: Port 8000 (configurable)
- **Database**: Port 5432 (internal)
- **Redis**: Port 6379 (internal)

## Deployment Steps

### Phase 1: Code Deployment
1. **Git Operations**:
   ```bash
   git add .
   git commit -m "feat: prepare codebase for production deployment
   
   - Remove all console.log statements from frontend
   - Remove mock data and development artifacts
   - Clean up temporary files and system files
   - Verify environment configuration
   - Ensure secure credential handling"
   git push origin main
   ```

2. **Pull on Production Server**:
   ```bash
   cd /home/<USER>/Apps/tarbiah-sentap-crm
   git pull origin main
   ```

### Phase 2: Environment Setup
1. **Copy environment files**:
   ```bash
   cp .env.example .env
   cp backend/.env.example backend/.env
   ```

2. **Configure environment variables** (edit .env files with production values)

3. **Generate application key**:
   ```bash
   cd backend
   php artisan key:generate
   ```

### Phase 3: Build and Deploy
1. **Install dependencies**:
   ```bash
   npm install
   cd backend && composer install --no-dev --optimize-autoloader
   ```

2. **Build frontend**:
   ```bash
   npm run build:pi
   ```

3. **Database setup**:
   ```bash
   cd backend
   php artisan migrate
   php artisan db:seed
   ```

4. **Start services**:
   ```bash
   docker-compose up -d
   ```

### Phase 4: Manual Configuration
1. **Access admin interface**: https://your-domain.com
2. **Login with admin credentials**
3. **Navigate to Settings > Email Configuration**
4. **Configure SMTP settings manually**
5. **Test email functionality**

## Security Considerations

### 1. Credential Management ✅
- **No hardcoded secrets**: All sensitive data uses environment variables
- **SMTP credentials**: Configured through admin interface, not in code
- **Database passwords**: Set via environment variables
- **API keys**: Properly externalized

### 2. Production Hardening
- **Debug mode**: Disabled in production (.env APP_DEBUG=false)
- **Error reporting**: Configured for production logging
- **HTTPS**: Handled by Cloudflare tunnel
- **CORS**: Properly configured for production domain

### 3. Data Protection
- **Soft deletes**: Implemented for data recovery
- **Audit logging**: Comprehensive activity tracking
- **Backup strategy**: Database backups with retention policy

## Monitoring and Maintenance

### 1. Health Checks
- **Docker health checks**: All services monitored
- **Application health**: Laravel health check endpoints
- **Database connectivity**: Automatic connection testing

### 2. Logging
- **Application logs**: Laravel log files in storage/logs
- **Error tracking**: Console.error statements preserved for monitoring
- **Audit trail**: User activity logging maintained

### 3. Performance
- **Asset optimization**: Vite build optimization for Pi hardware
- **Database indexing**: Proper indexes for performance
- **Caching**: Redis caching for improved performance

## Rollback Plan

### 1. Quick Rollback
```bash
git checkout [previous-commit-hash]
docker-compose restart
```

### 2. Database Rollback
```bash
php artisan migrate:rollback
```

### 3. Full System Restore
- Restore from backup
- Restart all services
- Verify functionality

## Support and Troubleshooting

### 1. Common Issues
- **SMTP not working**: Check Settings > Email Configuration
- **Database connection**: Verify environment variables
- **Permission issues**: Check file permissions and ownership

### 2. Logs Location
- **Laravel logs**: backend/storage/logs/
- **Docker logs**: `docker-compose logs [service-name]`
- **System logs**: /var/log/

### 3. Emergency Contacts
- **System Administrator**: [contact-info]
- **Developer**: [contact-info]
- **Hosting Provider**: [contact-info]

---

**Deployment Date**: [To be filled during deployment]
**Deployed By**: [To be filled during deployment]
**Version**: Production-ready v1.0
**Environment**: Raspberry Pi Production Server
