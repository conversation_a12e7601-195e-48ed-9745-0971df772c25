# KDT Production Deployment Guide for Raspberry Pi

This guide provides step-by-step instructions for deploying the KDT application on a Raspberry Pi server.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Raspberry Pi Setup](#raspberry-pi-setup)
3. [Application Deployment](#application-deployment)
4. [Configuration](#configuration)
5. [Monitoring and Maintenance](#monitoring-and-maintenance)
6. [Troubleshooting](#troubleshooting)
7. [Security Considerations](#security-considerations)

## Prerequisites

### Hardware Requirements

- **Raspberry Pi 4** (4GB RAM minimum, 8GB recommended)
- **MicroSD Card**: 32GB minimum (Class 10 or better)
- **Power Supply**: Official Raspberry Pi 4 power supply
- **Network**: Ethernet connection recommended for stability

### Software Requirements

- **Raspberry Pi OS**: 64-bit version (Bullseye or newer)
- **Docker**: Version 20.10 or newer
- **Docker Compose**: Version 2.0 or newer
- **Git**: For cloning the repository

## Raspberry Pi Setup

### 1. Install Raspberry Pi OS

1. Download Raspberry Pi Imager from [rpi.org](https://www.raspberrypi.org/software/)
2. Flash Raspberry Pi OS (64-bit) to your SD card
3. Enable SSH by creating an empty file named `ssh` in the boot partition
4. Boot your Raspberry Pi and connect via SSH

### 2. Initial System Configuration

```bash
# Update the system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y git curl wget vim htop

# Configure timezone
sudo timedatectl set-timezone UTC

# Increase swap space (recommended for 4GB RAM)
sudo dphys-swapfile swapoff
sudo sed -i 's/CONF_SWAPSIZE=100/CONF_SWAPSIZE=2048/' /etc/dphys-swapfile
sudo dphys-swapfile setup
sudo dphys-swapfile swapon

# Enable memory cgroup (required for Docker)
echo 'cgroup_enable=memory cgroup_memory=1' | sudo tee -a /boot/cmdline.txt

# Reboot to apply changes
sudo reboot
```

### 3. Install Docker and Docker Compose

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose
sudo apt install -y docker-compose

# Verify installation
docker --version
docker-compose --version

# Log out and back in for group changes to take effect
```

## Application Deployment

### 1. Clone the Repository

```bash
# Clone the KDT repository
git clone <your-repository-url> kdt
cd kdt

# Make deployment script executable
chmod +x deploy.sh
```

### 2. Configure Environment

```bash
# Copy and edit production environment file
cp .env.production.example .env.production
nano .env.production

# Update the following variables:
# - APP_KEY (generate new key)
# - DB_PASSWORD (secure password)
# - APP_URL (your Raspberry Pi IP)
# - VITE_API_URL (your Raspberry Pi IP)
```

### 3. Deploy the Application

```bash
# Run the deployment script
./deploy.sh deploy

# This will:
# - Check prerequisites
# - Create environment files
# - Build Docker images
# - Start all services
# - Run database migrations
# - Perform health checks
```

### 4. Verify Deployment

```bash
# Check service status
./deploy.sh status

# Check application health
./deploy.sh health

# View logs
./deploy.sh logs
```

## Configuration

### Environment Variables

Key environment variables to configure in `.env.production`:

```bash
# Application
APP_URL=http://*************:4000  # Your Pi's IP
VITE_API_URL=http://*************:4001/api/v1

# Security
APP_KEY=base64:your-generated-key-here
DB_PASSWORD=your-secure-database-password

# Performance (Raspberry Pi optimized)
PHP_MEMORY_LIMIT=256M
PHP_MAX_EXECUTION_TIME=300
```

### Resource Limits

The production Docker Compose file includes resource limits optimized for Raspberry Pi:

- **PostgreSQL**: 512MB RAM, 0.5 CPU
- **Redis**: 128MB RAM, 0.25 CPU
- **Backend**: 512MB RAM, 0.75 CPU
- **Frontend**: 256MB RAM, 0.5 CPU

### Network Configuration

By default, the application uses:
- **Port 4000**: Frontend (React application)
- **Port 4001**: Backend API (internal)
- **Port 4080/4443**: Reverse proxy (optional)

## Monitoring and Maintenance

### 1. Setup Automated Monitoring

```bash
# Setup cron jobs for monitoring and backups
./scripts/production/setup-cron.sh

# This creates:
# - Daily backups at 2:00 AM
# - Health monitoring every 5 minutes
# - Weekly log cleanup
# - Monthly system update checks
```

### 2. Manual Monitoring

```bash
# Check system health
./scripts/production/monitor.sh

# Create backup
./scripts/production/backup.sh

# View monitoring logs
tail -f monitor.log

# View backup logs
tail -f backup.log
```

### 3. Application Management

```bash
# Start application
./deploy.sh start

# Stop application
./deploy.sh stop

# Restart application
./deploy.sh restart

# Update application
./deploy.sh update

# View status
./deploy.sh status
```

### 4. Database Management

```bash
# Create database backup
./deploy.sh backup

# Access database shell
docker-compose -f docker-compose.prod.yml exec kdt-postgres psql -U kdt -d kdt

# View database logs
docker-compose -f docker-compose.prod.yml logs kdt-postgres
```

## Troubleshooting

### Common Issues

#### 1. Out of Memory Errors

```bash
# Check memory usage
free -h

# Check swap usage
swapon --show

# Increase swap if needed
sudo dphys-swapfile swapoff
sudo sed -i 's/CONF_SWAPSIZE=2048/CONF_SWAPSIZE=4096/' /etc/dphys-swapfile
sudo dphys-swapfile setup
sudo dphys-swapfile swapon
```

#### 2. Docker Build Failures

```bash
# Clean Docker cache
docker system prune -af

# Rebuild with no cache
docker-compose -f docker-compose.prod.yml build --no-cache

# Check available disk space
df -h
```

#### 3. Database Connection Issues

```bash
# Check database container
docker-compose -f docker-compose.prod.yml logs kdt-postgres

# Restart database
docker-compose -f docker-compose.prod.yml restart kdt-postgres

# Check database health
docker-compose -f docker-compose.prod.yml exec kdt-postgres pg_isready -U kdt
```

#### 4. High CPU Temperature

```bash
# Check temperature
vcgencmd measure_temp

# If temperature > 70°C:
# - Ensure proper ventilation
# - Consider adding a fan or heatsink
# - Reduce resource limits in docker-compose.prod.yml
```

### Log Files

Important log files to check:

- `deploy.log`: Deployment script logs
- `monitor.log`: Monitoring script logs
- `backup.log`: Backup script logs
- Docker logs: `docker-compose -f docker-compose.prod.yml logs [service]`

### Performance Optimization

#### For 4GB Raspberry Pi:

```bash
# Reduce resource limits in docker-compose.prod.yml
# PostgreSQL: 256MB RAM, 0.25 CPU
# Backend: 256MB RAM, 0.5 CPU
# Frontend: 128MB RAM, 0.25 CPU
```

#### For 8GB Raspberry Pi:

```bash
# Can use default resource limits
# Consider enabling additional features like Redis persistence
```

## Security Considerations

### 1. Firewall Configuration

```bash
# Install and configure UFW
sudo apt install -y ufw

# Allow SSH
sudo ufw allow ssh

# Allow application ports
sudo ufw allow 4000/tcp
sudo ufw allow 4080/tcp
sudo ufw allow 4443/tcp

# Enable firewall
sudo ufw enable
```

### 2. SSL/TLS Configuration

For production use, configure SSL certificates:

```bash
# Install Certbot for Let's Encrypt
sudo apt install -y certbot

# Generate certificates (replace with your domain)
sudo certbot certonly --standalone -d your-domain.com

# Update nginx configuration to use SSL
# Copy certificates to deploy/ssl/ directory
```

### 3. Regular Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker images
docker-compose -f docker-compose.prod.yml pull
./deploy.sh update

# Update application code
git pull
./deploy.sh update
```

### 4. Backup Strategy

- **Daily**: Automated database backups
- **Weekly**: Full application backup
- **Monthly**: System image backup
- **Offsite**: Copy backups to external storage

## Support and Maintenance

### Regular Maintenance Tasks

1. **Daily**: Check monitoring logs
2. **Weekly**: Review backup status
3. **Monthly**: Update system packages
4. **Quarterly**: Review security settings

### Getting Help

- Check logs: `./deploy.sh logs`
- Monitor health: `./scripts/production/monitor.sh`
- Create support bundle: Include logs and monitoring reports

---

For additional support or questions, refer to the project documentation or create an issue in the repository.
