#!/bin/bash

# KDT Production Deployment Script for Raspberry Pi
# Usage: ./deploy.sh [command] [options]

set -e

# Configuration
PROJECT_NAME="kdt"
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.production"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running on Raspberry Pi
check_raspberry_pi() {
    if [[ $(uname -m) == "aarch64" ]] || [[ $(uname -m) == "armv7l" ]]; then
        log "Detected ARM architecture - Raspberry Pi compatible"
    else
        warning "Not running on ARM architecture. This script is optimized for Raspberry Pi."
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check available memory
    AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$AVAILABLE_MEM" -lt 1024 ]; then
        warning "Available memory is less than 1GB. Consider adding swap space."
    fi
    
    # Check disk space
    AVAILABLE_DISK=$(df -h . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "${AVAILABLE_DISK%.*}" -lt 5 ]; then
        warning "Available disk space is less than 5GB."
    fi
    
    log "Prerequisites check completed"
}

# Create environment file
create_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        log "Creating production environment file..."
        cat > "$ENV_FILE" << EOF
# KDT Production Environment Configuration

# Application
APP_NAME=KDT
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:$(openssl rand -base64 32)
APP_URL=https://ts.crtvmkmn.space

# Database
DB_CONNECTION=pgsql
DB_HOST=kdt-postgres
DB_PORT=5432
DB_DATABASE=kdt
DB_USERNAME=kdt
DB_PASSWORD=$(openssl rand -base64 32)

# Redis
REDIS_HOST=kdt-redis
REDIS_PASSWORD=
REDIS_PORT=6379

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Frontend
VITE_API_URL=http://localhost:8000/api/v1
VITE_APP_NAME=KDT
VITE_APP_ENV=production
EOF
        log "Environment file created: $ENV_FILE"
        warning "Please review and update the environment variables in $ENV_FILE"
    else
        log "Environment file already exists: $ENV_FILE"
    fi
}

# Create backup directory
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log "Created backup directory: $BACKUP_DIR"
    fi
}

# Deploy application
deploy() {
    log "Starting KDT deployment..."
    
    check_raspberry_pi
    check_prerequisites
    create_env_file
    create_backup_dir
    
    # Load environment variables
    if [ -f "$ENV_FILE" ]; then
        export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    fi
    
    # Build and start services
    log "Building and starting services..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_health
    
    log "Deployment completed successfully!"

    # Show appropriate URLs based on configuration
    if [ -n "${APP_URL}" ] && [[ "${APP_URL}" == https://* ]]; then
        info "Application is available at: ${APP_URL}"
        info "API is available at: ${APP_URL}/api/v1"
        info "Local access: http://localhost:3000"
    else
        info "Application is available at: http://localhost:3000"
        info "API is available at: http://localhost:8000"
    fi
}

# Update application
update() {
    log "Updating KDT application..."
    
    # Create backup before update
    backup
    
    # Pull latest changes (if using git)
    if [ -d ".git" ]; then
        log "Pulling latest changes..."
        git pull
    fi
    
    # Rebuild and restart services
    log "Rebuilding services..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services
    sleep 30
    check_health
    
    log "Update completed successfully!"
}

# Stop application
stop() {
    log "Stopping KDT application..."
    docker-compose -f "$COMPOSE_FILE" down
    log "Application stopped"
}

# Start application
start() {
    log "Starting KDT application..."
    docker-compose -f "$COMPOSE_FILE" up -d
    sleep 30
    check_health
    log "Application started"
}

# Restart application
restart() {
    log "Restarting KDT application..."
    stop
    start
}

# Check service health
check_health() {
    log "Checking service health..."
    
    # Check frontend
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log "✓ Frontend is healthy"
    else
        error "✗ Frontend is not responding"
    fi
    
    # Check backend
    if curl -f http://localhost:8000/api/v1/health > /dev/null 2>&1; then
        log "✓ Backend is healthy"
    else
        error "✗ Backend is not responding"
    fi
    
    # Check database
    if docker-compose -f "$COMPOSE_FILE" exec -T kdt-postgres pg_isready -U kdt > /dev/null 2>&1; then
        log "✓ Database is healthy"
    else
        error "✗ Database is not responding"
    fi
}

# Create backup
backup() {
    log "Creating backup..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/kdt_backup_$TIMESTAMP.sql"
    
    # Database backup
    docker-compose -f "$COMPOSE_FILE" exec -T kdt-postgres pg_dump -U kdt kdt > "$BACKUP_FILE"
    
    # Compress backup
    gzip "$BACKUP_FILE"
    
    log "Backup created: ${BACKUP_FILE}.gz"
    
    # Keep only last 7 backups
    find "$BACKUP_DIR" -name "kdt_backup_*.sql.gz" -type f -mtime +7 -delete
}

# Show logs
logs() {
    SERVICE=${1:-""}
    if [ -n "$SERVICE" ]; then
        docker-compose -f "$COMPOSE_FILE" logs -f "$SERVICE"
    else
        docker-compose -f "$COMPOSE_FILE" logs -f
    fi
}

# Show status
status() {
    log "KDT Application Status:"
    docker-compose -f "$COMPOSE_FILE" ps
    echo ""
    check_health
}

# Show help
show_help() {
    cat << EOF
KDT Production Deployment Script for Raspberry Pi

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    deploy      Deploy the application (first time setup)
    update      Update the application
    start       Start the application
    stop        Stop the application
    restart     Restart the application
    status      Show application status
    health      Check service health
    backup      Create database backup
    logs        Show logs (optional: specify service name)
    help        Show this help message

Examples:
    $0 deploy           # Initial deployment
    $0 update           # Update application
    $0 logs backend     # Show backend logs
    $0 backup           # Create backup

Environment file: $ENV_FILE
Compose file: $COMPOSE_FILE
Log file: $LOG_FILE

EOF
}

# Main script logic
case "${1:-help}" in
    deploy)
        deploy
        ;;
    update)
        update
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    health)
        check_health
        ;;
    backup)
        backup
        ;;
    logs)
        logs "$2"
        ;;
    help|*)
        show_help
        ;;
esac
