#!/bin/bash

# Complete Email Configuration Fix and Deployment Script
# This script fixes both frontend defaults and backend database settings
# Run this from your development machine - it will deploy to production

set -euo pipefail

# Configuration
DEV_PROJECT_ROOT="$(pwd)"
PRODUCTION_SERVER="<EMAIL>"
PRODUCTION_PATH="/home/<USER>/Apps/ts-crm"
LOG_FILE="/tmp/email-fix-deployment-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        STEP)
            echo -e "${PURPLE}[STEP]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log INFO "🔍 Checking prerequisites"
    
    # Check if build exists
    if [[ ! -d "$DEV_PROJECT_ROOT/dist" ]]; then
        log ERROR "Frontend build not found. Please run 'npm run build' first"
        exit 1
    fi
    
    # Check SSH connection
    if ! ssh -o ConnectTimeout=10 "$PRODUCTION_SERVER" "echo 'SSH connection test'" &>/dev/null; then
        log ERROR "Cannot connect to production server: $PRODUCTION_SERVER"
        log ERROR "Please check your SSH configuration"
        exit 1
    fi
    
    # Check production path exists
    if ! ssh "$PRODUCTION_SERVER" "test -d $PRODUCTION_PATH"; then
        log ERROR "Production path not found: $PRODUCTION_PATH"
        exit 1
    fi
    
    log SUCCESS "Prerequisites check passed"
}

# Deploy frontend build
deploy_frontend() {
    log STEP "🚀 Deploying frontend to production"
    
    # Create backup of current frontend
    log INFO "Creating backup of current frontend"
    ssh "$PRODUCTION_SERVER" "
        if [[ -d $PRODUCTION_PATH/frontend/dist ]]; then
            cp -r $PRODUCTION_PATH/frontend/dist $PRODUCTION_PATH/frontend/dist.backup-$(date +%Y%m%d-%H%M%S)
        fi
    "
    
    # Deploy new frontend
    log INFO "Uploading new frontend build"
    rsync -avz --delete "$DEV_PROJECT_ROOT/dist/" "$PRODUCTION_SERVER:$PRODUCTION_PATH/frontend/dist/"
    
    # Set proper permissions
    ssh "$PRODUCTION_SERVER" "
        chown -R zulhelminasir:www-data $PRODUCTION_PATH/frontend/dist
        chmod -R 755 $PRODUCTION_PATH/frontend/dist
    "
    
    log SUCCESS "Frontend deployed successfully"
}

# Fix database settings on production
fix_database_settings() {
    log STEP "📝 Fixing database settings on production"
    
    # Upload the fix script
    scp "$DEV_PROJECT_ROOT/deploy/pi-native/scripts/fix-production-email-settings.sh" \
        "$PRODUCTION_SERVER:/tmp/fix-production-email-settings.sh"
    
    # Make it executable and run it
    ssh "$PRODUCTION_SERVER" "
        chmod +x /tmp/fix-production-email-settings.sh
        /tmp/fix-production-email-settings.sh
    "
    
    log SUCCESS "Database settings fixed"
}

# Test the complete flow
test_complete_flow() {
    log STEP "🧪 Testing complete email flow"
    
    # Test frontend accessibility
    log INFO "Testing frontend accessibility"
    local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/" || echo "000")
    
    if [[ "$frontend_status" == "200" ]]; then
        log SUCCESS "Frontend accessible (HTTP 200)"
    else
        log ERROR "Frontend not accessible (HTTP $frontend_status)"
        return 1
    fi
    
    # Test API connectivity
    log INFO "Testing API connectivity"
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/health" || echo "000")
    
    if [[ "$api_status" == "200" ]]; then
        log SUCCESS "API accessible (HTTP 200)"
    else
        log ERROR "API not accessible (HTTP $api_status)"
        return 1
    fi
    
    # Test email settings endpoint
    log INFO "Testing email settings endpoint"
    local email_settings_status=$(curl -s -o /dev/null -w "%{http_code}" "https://ts.crtvmkmn.space/api/v1/email-settings" || echo "000")
    
    if [[ "$email_settings_status" == "401" ]] || [[ "$email_settings_status" == "200" ]]; then
        log SUCCESS "Email settings endpoint responding (HTTP $email_settings_status)"
    else
        log WARNING "Email settings endpoint status: HTTP $email_settings_status"
    fi
    
    log SUCCESS "Complete flow test passed"
}

# Verify deployment
verify_deployment() {
    log STEP "✅ Verifying deployment"
    
    # Check if new frontend files are served
    log INFO "Checking frontend file integrity"
    
    # Check if the built files contain the correct API URL
    ssh "$PRODUCTION_SERVER" "
        if grep -q 'ts.crtvmkmn.space' $PRODUCTION_PATH/frontend/dist/assets/*.js 2>/dev/null; then
            echo 'Frontend contains correct API URL'
        else
            echo 'WARNING: API URL not found in frontend files'
            exit 1
        fi
    "
    
    # Check if CSS and JS files exist
    local css_count=$(ssh "$PRODUCTION_SERVER" "find $PRODUCTION_PATH/frontend/dist/assets -name '*.css' | wc -l")
    local js_count=$(ssh "$PRODUCTION_SERVER" "find $PRODUCTION_PATH/frontend/dist/assets -name '*.js' | wc -l")
    
    if [[ $css_count -gt 0 ]] && [[ $js_count -gt 0 ]]; then
        log SUCCESS "Build assets present ($css_count CSS, $js_count JS files)"
    else
        log ERROR "Missing build assets"
        return 1
    fi
    
    log SUCCESS "Deployment verification passed"
}

# Main deployment function
main() {
    echo "========================================"
    echo "  Email Configuration Fix & Deployment"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "🚀 Starting complete email configuration fix"
    log INFO "Development: $DEV_PROJECT_ROOT"
    log INFO "Production: $PRODUCTION_SERVER:$PRODUCTION_PATH"
    log INFO "Log file: $LOG_FILE"
    
    # Execute all steps
    check_prerequisites
    deploy_frontend
    fix_database_settings
    test_complete_flow
    verify_deployment
    
    echo ""
    echo -e "${GREEN}🎉 Email configuration fix completed successfully!${NC}"
    echo ""
    echo "What was fixed:"
    echo "✅ Frontend default values <NAME_EMAIL>"
    echo "✅ Database SMTP settings <NAME_EMAIL>"
    echo "✅ 2FA system disabled temporarily for testing"
    echo "✅ Debug mode disabled"
    echo "✅ Frontend deployed with new build"
    echo "✅ Services restarted"
    echo ""
    echo "Next steps:"
    echo "1. Go to: https://ts.crtvmkmn.space/settings"
    echo "2. Check that email <NAME_EMAIL> as SMTP username"
    echo "3. Test 2FA login process:"
    echo "   - Enable 2FA in Settings > Security"
    echo "   - Logout and login again"
    echo "   - Should receive 2FA <NAME_EMAIL>"
    echo "4. Check <NAME_EMAIL> for test email"
    echo ""
    echo "Log file: $LOG_FILE"
}

# Execute main function
main "$@"
