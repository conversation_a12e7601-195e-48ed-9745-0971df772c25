# KDT Cloudflare Tunnel Setup Guide

This guide explains how to deploy KDT with Cloudflare tunnel configuration for the domain `https://ts.crtvmkmn.space/`.

## Overview

Your setup:
- **Local Application**: Runs on `localhost:4000` (Raspberry Pi)
- **Public Access**: `https://ts.crtvmkmn.space/` (via Cloudflare tunnel)
- **Installation Directory**: `/home/<USER>/Apps/`

## Pre-Deployment Checklist

### ✅ Cloudflare Tunnel Requirements

1. **Cloudflare tunnel is already configured** to route `https://ts.crtvmkmn.space/` to your Raspberry Pi
2. **Tunnel points to**: `localhost:4000` on your Raspberry Pi
3. **HTTPS termination**: Handled by <PERSON>flare (SSL certificates managed by Cloudflare)

### ✅ Raspberry Pi Requirements

1. **Target directory exists**: `/home/<USER>/Apps/`
2. **Docker installed**: Will be handled by quick-setup script
3. **Cloudflare tunnel running**: Should be active and routing traffic

## Deployment Steps

### 1. Clone Repository to Target Directory

```bash
# SSH to your Raspberry Pi
ssh zulhelminasir@your-pi-ip

# Navigate to Apps directory
cd /home/<USER>/Apps/

# Clone the KDT repository
git clone <your-repository-url> kdt
cd kdt
```

### 2. Run Quick Setup

```bash
# Make setup script executable
chmod +x quick-setup.sh

# Run the automated setup
./quick-setup.sh
```

**During setup, when prompted:**
- **"Are you using Cloudflare tunnel?"**: Answer `y`
- **"Enter your domain"**: Enter `https://ts.crtvmkmn.space`

### 3. Deploy the Application

```bash
# Deploy the application
./deploy.sh deploy
```

## Configuration Details

### Environment Configuration

The setup will automatically create `.env.production` with:

```bash
# Application URLs
APP_URL=https://ts.crtvmkmn.space
VITE_API_URL=https://ts.crtvmkmn.space/api/v1

# Cloudflare Tunnel Settings
CLOUDFLARE_TUNNEL=true
TRUSTED_PROXIES=*
FORCE_HTTPS=true
SESSION_SECURE_COOKIE=true
```

### Docker Configuration

- **Frontend**: Binds to `0.0.0.0:4000` (accessible to Cloudflare tunnel)
- **Backend**: Binds to `127.0.0.1:4001` (internal only)
- **Database**: Binds to `127.0.0.1:4002` (internal only)

### Nginx Reverse Proxy

The internal nginx proxy will handle:
- Frontend requests: `/` → React app
- API requests: `/api/` → Laravel backend
- Health checks: `/health`

## Verification Steps

### 1. Check Local Services

```bash
# Check if services are running
./deploy.sh status

# Check health
./deploy.sh health

# Test local access
curl http://localhost:4000
curl http://localhost:4001/api/v1/health
```

### 2. Check Public Access

```bash
# Test public domain (from any device)
curl https://ts.crtvmkmn.space/
curl https://ts.crtvmkmn.space/api/v1/health
```

### 3. Verify in Browser

- **Frontend**: https://ts.crtvmkmn.space/
- **API Health**: https://ts.crtvmkmn.space/api/v1/health

## Cloudflare Tunnel Configuration

Your Cloudflare tunnel should be configured to:

```yaml
# Example cloudflared config.yml
tunnel: your-tunnel-id
credentials-file: /path/to/credentials.json

ingress:
  - hostname: ts.crtvmkmn.space
    service: http://localhost:4000
  - service: http_status:404
```

## Troubleshooting

### Issue: Public domain not accessible

**Check:**
1. Cloudflare tunnel is running: `sudo systemctl status cloudflared`
2. Local app is accessible: `curl http://localhost:4000`
3. Cloudflare tunnel logs: `sudo journalctl -u cloudflared -f`

### Issue: API calls failing

**Check:**
1. Backend is running: `./deploy.sh logs kdt-backend`
2. API health: `curl http://localhost:4001/api/v1/health`
3. Frontend environment: Check `VITE_API_URL` in `.env.production`

### Issue: HTTPS/SSL errors

**Solution:**
- Cloudflare handles SSL termination
- Ensure `FORCE_HTTPS=true` in environment
- Check `SESSION_SECURE_COOKIE=true` for proper session handling

## Management Commands

```bash
# Application management
./deploy.sh start|stop|restart|status|health

# View logs
./deploy.sh logs [service-name]

# Create backup
./deploy.sh backup

# Update application
./deploy.sh update

# Monitoring
./scripts/production/monitor.sh
```

## File Locations

```
/home/<USER>/Apps/kdt/
├── .env.production              # Main environment config
├── backend/.env.production      # Backend environment config
├── docker-compose.prod.yml      # Production Docker config
├── deploy.sh                    # Main deployment script
├── backups/                     # Automated backups
├── logs/                        # Application logs
└── scripts/production/          # Monitoring and backup scripts
```

## Security Notes

1. **Database and Redis**: Only accessible internally (127.0.0.1)
2. **Backend API**: Only accessible internally, exposed via nginx proxy
3. **Frontend**: Accessible to Cloudflare tunnel (0.0.0.0:4000)
4. **HTTPS**: Enforced by Cloudflare, secure cookies enabled
5. **Trusted Proxies**: Configured to trust Cloudflare IPs

## Next Steps After Deployment

1. **Setup Monitoring**: `./scripts/production/setup-cron.sh`
2. **Create Initial Backup**: `./scripts/production/backup.sh`
3. **Test All Features**: Verify CRM functionality through public domain
4. **Monitor Performance**: Check `./scripts/production/monitor.sh`

---

Your KDT application will be accessible at **https://ts.crtvmkmn.space/** once deployment is complete!
