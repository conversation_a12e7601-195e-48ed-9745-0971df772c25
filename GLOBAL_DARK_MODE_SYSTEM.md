# Global Dark Mode System Documentation

## 🌙 **Overview**

The KDT CRM application now features a comprehensive global dark mode styling system that eliminates the need for manual page-by-page auditing and ensures consistent dark mode appearance across all components.

## 🏗️ **System Architecture**

### **1. CSS Custom Properties Foundation**
The system uses CSS custom properties (CSS variables) to define semantic color tokens that automatically switch between light and dark modes:

```css
:root {
  --color-bg-primary: 255 255 255; /* white */
  --color-text-primary: 17 24 39; /* gray-900 */
  /* ... more light mode colors */
}

.dark {
  --color-bg-primary: 31 41 55; /* gray-800 */
  --color-text-primary: 255 255 255; /* white */
  /* ... more dark mode colors */
}
```

### **2. Global Base Styles**
Common HTML elements automatically inherit dark mode styling:

```css
body {
  @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-white;
}

input, textarea, select {
  @apply bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600;
}
```

### **3. Component Utility Classes**
Pre-built utility classes for common UI patterns:

```css
.card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}
```

## 🎯 **Usage Guide**

### **Basic Components**

#### **Cards**
```tsx
// Old way (manual dark mode)
<div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">

// New way (automatic)
<div className="card">
  <div className="card-header">Header content</div>
  <div className="card-body">Body content</div>
  <div className="card-footer">Footer content</div>
</div>
```

#### **Buttons**
```tsx
// Old way
<button className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded">

// New way
<button className="btn-primary">Primary Action</button>
<button className="btn-secondary">Secondary Action</button>
<button className="btn-outline">Outline Button</button>
```

#### **Forms**
```tsx
// Old way
<label className="text-gray-700 dark:text-gray-300">
<input className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">

// New way
<div className="form-group">
  <label className="form-label">Field Label</label>
  <input className="form-input" />
  <p className="form-help">Helper text</p>
</div>
```

#### **Badges**
```tsx
// Old way
<span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">

// New way
<span className="badge-success">Success</span>
<span className="badge-warning">Warning</span>
<span className="status-active">Active</span>
```

### **Layout Components**

#### **Page Structure**
```tsx
<div className="page-container">
  <div className="page-header">
    <h1 className="page-title">Page Title</h1>
    <p className="page-subtitle">Subtitle</p>
  </div>
  <div className="page-content">
    {/* Content */}
  </div>
</div>
```

#### **Sidebar**
```tsx
<div className="sidebar">
  <div className="sidebar-header">Header</div>
  <div className="sidebar-content">Content</div>
  <div className="sidebar-footer">Footer</div>
</div>
```

#### **Lists**
```tsx
<div className="list-container">
  <div className="list-header">List Header</div>
  <div className="list-item">
    <div className="list-item-content">
      <span className="list-item-primary">Primary text</span>
      <span className="list-item-secondary">Secondary text</span>
    </div>
  </div>
</div>
```

### **Semantic Color Classes**

Use semantic color classes for consistent theming:

```tsx
// Background colors
<div className="bg-surface-primary">   {/* Auto light/dark */}
<div className="bg-surface-secondary"> {/* Auto light/dark */}

// Text colors
<span className="text-content-primary">   {/* Auto light/dark */}
<span className="text-content-secondary"> {/* Auto light/dark */}

// Border colors
<div className="border-outline-primary"> {/* Auto light/dark */}
```

## 🔧 **Migration Guide**

### **Step 1: Replace Manual Dark Mode Classes**

**Before:**
```tsx
<div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
  <h2 className="text-gray-900 dark:text-white">Title</h2>
  <p className="text-gray-600 dark:text-gray-400">Description</p>
</div>
```

**After:**
```tsx
<div className="card">
  <h2 className="text-content-primary">Title</h2>
  <p className="text-content-secondary">Description</p>
</div>
```

### **Step 2: Use Component Classes**

**Before:**
```tsx
<button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600">
```

**After:**
```tsx
<button className="btn-secondary">
```

### **Step 3: Leverage Semantic Colors**

**Before:**
```tsx
<input className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
```

**After:**
```tsx
<input className="form-input">
```

## 🎨 **Customization**

### **Adding New Color Tokens**
Add new CSS custom properties in `src/index.css`:

```css
:root {
  --color-brand-primary: 59 130 246; /* blue-500 */
}

.dark {
  --color-brand-primary: 96 165 250; /* blue-400 */
}
```

### **Creating New Component Classes**
Add new component classes in the `@layer components` section:

```css
@layer components {
  .my-component {
    @apply bg-surface-primary text-content-primary border-outline-primary;
  }
}
```

## ✅ **Benefits**

1. **Automatic Dark Mode**: No need to manually add `dark:` prefixes
2. **Consistent Styling**: Unified design system across all components
3. **Maintainable Code**: Centralized color management
4. **Developer Experience**: Faster development with pre-built classes
5. **Future-Proof**: Easy to update colors globally
6. **Performance**: Reduced CSS bundle size through reusable classes

## 🚀 **Best Practices**

1. **Use semantic classes** instead of manual dark mode classes
2. **Leverage component classes** for common UI patterns
3. **Extend the system** by adding new utility classes when needed
4. **Test in both modes** during development
5. **Follow the established patterns** for consistency

## 📋 **Available Classes Reference**

### **Layout**
- `page-container`, `page-header`, `page-content`, `page-title`
- `sidebar`, `sidebar-header`, `sidebar-content`, `sidebar-footer`
- `layout-container`, `layout-section`, `layout-grid`

### **Components**
- `card`, `card-header`, `card-body`, `card-footer`
- `modal-overlay`, `modal-container`, `modal-header`, `modal-body`
- `list-container`, `list-header`, `list-item`

### **Forms**
- `form-group`, `form-label`, `form-input`, `form-textarea`, `form-select`
- `form-error`, `form-help`

### **Buttons**
- `btn`, `btn-primary`, `btn-secondary`, `btn-outline`, `btn-ghost`, `btn-danger`

### **Badges**
- `badge`, `badge-primary`, `badge-success`, `badge-warning`, `badge-danger`
- `status-active`, `status-pending`, `status-error`

### **Tables**
- `table`, `table-header`, `table-row`, `table-cell`, `table-header-cell`

This global dark mode system provides a robust foundation for consistent, maintainable, and scalable dark mode implementation across the entire KDT CRM application.
