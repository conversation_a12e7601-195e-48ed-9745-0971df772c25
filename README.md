# KDT Application

A complete containerized CRM application with React frontend and <PERSON><PERSON> backend.

## Architecture

- **Frontend**: React + TypeScript + Vite + Tailwind CSS (Port 4000)
- **Backend**: Laravel 10 API (Port 4001)
- **Database**: PostgreSQL 15 (Port 4002)
- **Cache**: Redis 7 (Port 4003)
- **Database Admin**: Adminer (Port 4004)

## Port Configuration

The application uses the following ports (optimized for Raspberry Pi deployment):

- **Frontend**: http://localhost:4000
- **Backend API**: http://localhost:4001
- **Database**: localhost:4002
- **Redis**: localhost:4003
- **Adminer**: http://localhost:4004

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Ports 4000, 4001, 4002, 4003, 4004 available

### Deploy the Application

1. **Clone and navigate to the project directory**:
   ```bash
   cd /Users/<USER>/Projects/Bolt/KDT
   ```

2. **Build and start all services**:
   ```bash
   docker-compose up --build -d
   ```

3. **Wait for all services to be healthy** (about 2-3 minutes):
   ```bash
   docker-compose ps
   ```

4. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000/api/health
   - Database Admin: http://localhost:8001

### Stopping the Application

```bash
docker-compose down
```

### Removing Everything (including data)

```bash
docker-compose down -v
docker rmi kdt-frontend kdt-backend
```

## Services

### Frontend (React)
- Built with Vite for fast development and optimized production builds
- Tailwind CSS for styling
- React Router for navigation
- Configured to communicate with Laravel backend

### Backend (Laravel)
- Laravel 10 with PHP 8.2
- PostgreSQL database with migrations and seeders
- Redis for caching
- RESTful API endpoints
- CORS configured for frontend communication

### Database
- PostgreSQL 15 with persistent storage
- Automatic migrations on startup
- Sample data seeded automatically
- Accessible via Adminer web interface

## API Endpoints

Base URL: `http://localhost:8000/api/v1`

### Clients
- `GET /clients` - List all clients
- `POST /clients` - Create new client
- `GET /clients/{id}` - Get client details
- `PUT /clients/{id}` - Update client
- `DELETE /clients/{id}` - Delete client

### Products
- `GET /products` - List all products
- `POST /products` - Create new product
- `GET /products/{id}` - Get product details
- `PUT /products/{id}` - Update product
- `DELETE /products/{id}` - Delete product

### Transactions
- `GET /transactions` - List all transactions
- `POST /transactions` - Create new transaction
- `GET /transactions/{id}` - Get transaction details
- `PUT /transactions/{id}` - Update transaction
- `DELETE /transactions/{id}` - Delete transaction

### Leads
- `GET /leads` - List all leads
- `POST /leads` - Create new lead
- `GET /leads/{id}` - Get lead details
- `PUT /leads/{id}` - Update lead
- `DELETE /leads/{id}` - Delete lead

### Dashboard
- `GET /dashboard/stats` - Get dashboard statistics

## Development

### Backend Development

```bash
# Access backend container
docker exec -it kdt-backend bash

# Run artisan commands
docker exec -it kdt-backend php artisan migrate
docker exec -it kdt-backend php artisan db:seed
```

### Frontend Development

```bash
# Access frontend container
docker exec -it kdt-frontend sh

# The frontend is built and served by Nginx
# For development, you can run locally:
npm install
npm run dev
```

### Database Access

**Via Adminer**: http://localhost:8001
- Server: kdt-postgres
- Username: kdt
- Password: kdt_password
- Database: kdt

**Via Command Line**:
```bash
docker exec -it kdt-postgres psql -U kdt -d kdt
```

## Troubleshooting

### Check Service Status
```bash
docker-compose ps
docker-compose logs [service-name]
```

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 5432, 6379, 8000, 8001 are available
2. **Database connection**: Wait for PostgreSQL to be fully ready before backend starts
3. **Permission issues**: Ensure Docker has proper permissions

### Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f kdt-backend
docker-compose logs -f kdt-frontend
```

## Environment Variables

### Frontend (.env)
- `VITE_API_URL`: Backend API URL
- `VITE_APP_NAME`: Application name
- Payment gateway configurations

### Backend (backend/.env)
- Database connection settings
- Redis configuration
- Application settings

## Data Persistence

- Database data: `kdt_postgres_data` volume
- Redis data: `kdt_redis_data` volume
- Backend storage: `kdt_backend_storage` volume

Data persists between container restarts unless volumes are explicitly removed.
