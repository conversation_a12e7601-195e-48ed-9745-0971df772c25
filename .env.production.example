# KDT Production Environment Configuration for Raspberry Pi with Cloudflare Tunnel
# Copy this file to .env.production and update the values
#
# Setup: Application runs on localhost:4000, Cloudflare tunnel exposes it as https://ts.crtvmkmn.space

# Application Settings
APP_NAME=KDT
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:GENERATE_NEW_KEY_HERE
APP_URL=https://ts.crtvmkmn.space

# Database Configuration
DB_CONNECTION=pgsql
DB_HOST=kdt-postgres
DB_PORT=4002
DB_DATABASE=kdt
DB_USERNAME=kdt
DB_PASSWORD=GENERATE_SECURE_PASSWORD_HERE

# Redis Configuration
REDIS_HOST=kdt-redis
REDIS_PASSWORD=
REDIS_PORT=4003

# Cache and Session Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120
QUEUE_CONNECTION=redis

# Frontend Configuration (Cloudflare Tunnel)
# Frontend runs on localhost:4000, exposed via Cloudflare tunnel
# Backend API accessible through the same domain
VITE_API_URL=https://ts.crtvmkmn.space/api/v1
VITE_APP_NAME=KDT
VITE_APP_ENV=production

# Cloudflare Tunnel Configuration
CLOUDFLARE_TUNNEL=true
TRUSTED_PROXIES=*
FORCE_HTTPS=true

# Mail Configuration (Optional)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-server
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Logging Configuration
LOG_CHANNEL=stack
LOG_LEVEL=error
LOG_DEPRECATIONS_CHANNEL=null

# Security Settings (Optional)
# Uncomment and configure if using HTTPS
# SESSION_SECURE_COOKIE=true
# SANCTUM_STATEFUL_DOMAINS=your-domain.com

# Backup Configuration
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Performance Settings for Raspberry Pi
PHP_MEMORY_LIMIT=256M
PHP_MAX_EXECUTION_TIME=300
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024

# Monitoring (Optional)
HEALTH_CHECK_INTERVAL=30
LOG_ROTATION_SIZE=100M
LOG_RETENTION_DAYS=30
