#!/bin/bash

# Pre-commit hook to prevent credential leaks
# This hook scans for potential security issues before allowing commits

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Security patterns to detect
declare -a SECURITY_PATTERNS=(
    # API Keys and Secrets
    "olmjxdaz-wx8z-1gl2-v8w8-xej3odua83j3"  # Example ToyyibPay key
    "Ari55@2025"                              # Hardcoded SMTP password
    "kdt_password"                            # Default database password
    "your-toyyibpay-secret-key-here"         # Placeholder that should be replaced
    
    # Generic patterns
    "password.*=.*['\"][^'\"]{8,}['\"]"      # Password assignments
    "secret.*=.*['\"][^'\"]{10,}['\"]"       # Secret assignments
    "api_key.*=.*['\"][^'\"]{10,}['\"]"      # API key assignments
    "token.*=.*['\"][^'\"]{20,}['\"]"        # Token assignments
    
    # Database credentials
    "DB_PASSWORD.*=.*['\"][^'\"]{5,}['\"]"   # Database password
    "REDIS_PASSWORD.*=.*['\"][^'\"]{5,}['\"]" # Redis password
    
    # Email credentials
    "MAIL_PASSWORD.*=.*['\"][^'\"]{5,}['\"]" # Email password
    "SMTP_PASSWORD.*=.*['\"][^'\"]{5,}['\"]" # SMTP password
)

# Files to exclude from scanning
declare -a EXCLUDE_PATTERNS=(
    "*.example"
    "*.md"
    "*.txt"
    ".git/*"
    "node_modules/*"
    "vendor/*"
    "dist/*"
    "build/*"
    ".githooks/*"
)

echo -e "${BLUE}🔍 Running security scan for credential leaks...${NC}"

# Get list of staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

if [ -z "$STAGED_FILES" ]; then
    echo -e "${GREEN}✅ No files to scan${NC}"
    exit 0
fi

SECURITY_ISSUES=0
TOTAL_FILES=0

# Function to check if file should be excluded
should_exclude() {
    local file=$1
    for pattern in "${EXCLUDE_PATTERNS[@]}"; do
        if [[ $file == $pattern ]]; then
            return 0
        fi
    done
    return 1
}

# Function to scan file for security issues
scan_file() {
    local file=$1
    local issues_found=0
    
    if should_exclude "$file"; then
        return 0
    fi
    
    if [[ ! -f "$file" ]]; then
        return 0
    fi
    
    ((TOTAL_FILES++))
    
    # Check for specific security patterns
    for pattern in "${SECURITY_PATTERNS[@]}"; do
        if grep -q "$pattern" "$file" 2>/dev/null; then
            if [[ $issues_found -eq 0 ]]; then
                echo -e "${RED}❌ Security issues found in: $file${NC}"
                issues_found=1
                ((SECURITY_ISSUES++))
            fi
            echo -e "   ${YELLOW}⚠️  Detected: $pattern${NC}"
        fi
    done
    
    # Check for hardcoded localhost URLs in production files
    if [[ "$file" == *.js ]] || [[ "$file" == *.ts ]] || [[ "$file" == *.jsx ]] || [[ "$file" == *.tsx ]]; then
        if grep -q "localhost:[0-9]" "$file" 2>/dev/null; then
            if [[ $issues_found -eq 0 ]]; then
                echo -e "${RED}❌ Security issues found in: $file${NC}"
                issues_found=1
                ((SECURITY_ISSUES++))
            fi
            echo -e "   ${YELLOW}⚠️  Hardcoded localhost URL detected${NC}"
        fi
    fi
    
    # Check for debug statements in production code
    if [[ "$file" == *.js ]] || [[ "$file" == *.ts ]] || [[ "$file" == *.jsx ]] || [[ "$file" == *.tsx ]] || [[ "$file" == *.php ]]; then
        if grep -E "(console\.log|var_dump|dd\(|dump\()" "$file" 2>/dev/null; then
            echo -e "${YELLOW}⚠️  Debug statements found in: $file${NC}"
            echo -e "   ${YELLOW}   Consider removing before production deployment${NC}"
        fi
    fi
    
    # Check for TODO/FIXME comments that might indicate security issues
    if grep -iE "(TODO.*security|FIXME.*security|TODO.*password|FIXME.*password)" "$file" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Security-related TODO/FIXME found in: $file${NC}"
    fi
    
    return $issues_found
}

# Scan all staged files
echo "Scanning $( echo "$STAGED_FILES" | wc -l ) staged files..."

while IFS= read -r file; do
    scan_file "$file"
done <<< "$STAGED_FILES"

echo ""
echo -e "${BLUE}📊 Scan Results:${NC}"
echo "   Files scanned: $TOTAL_FILES"
echo "   Security issues: $SECURITY_ISSUES"

if [[ $SECURITY_ISSUES -gt 0 ]]; then
    echo ""
    echo -e "${RED}🚫 COMMIT BLOCKED: Security issues detected!${NC}"
    echo ""
    echo -e "${YELLOW}🛡️  Security Recommendations:${NC}"
    echo "   1. Remove hardcoded credentials and API keys"
    echo "   2. Use environment variables for sensitive data"
    echo "   3. Use placeholder values in .example files"
    echo "   4. Never commit real production credentials"
    echo "   5. Use the Settings > Security page to configure credentials"
    echo ""
    echo -e "${BLUE}💡 To fix these issues:${NC}"
    echo "   1. Replace real credentials with placeholders"
    echo "   2. Move sensitive data to environment variables"
    echo "   3. Update .env.example files with safe placeholder values"
    echo "   4. Configure real credentials through the web interface"
    echo ""
    echo -e "${YELLOW}⚠️  If you're certain these changes are safe, you can bypass this hook with:${NC}"
    echo "   git commit --no-verify"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ Security scan passed - no credential leaks detected${NC}"
echo ""

# Additional checks for production readiness
echo -e "${BLUE}🔧 Additional Production Readiness Checks:${NC}"

# Check if .env files are properly ignored
if git ls-files | grep -E "\.env$|\.env\.production$" >/dev/null; then
    echo -e "${YELLOW}⚠️  .env files are tracked in git - ensure they don't contain real credentials${NC}"
fi

# Check for proper .gitignore
if [[ -f ".gitignore" ]]; then
    if ! grep -q "\.env" ".gitignore"; then
        echo -e "${YELLOW}⚠️  .env files should be in .gitignore${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  No .gitignore file found${NC}"
fi

echo -e "${GREEN}✅ Pre-commit security checks completed${NC}"
exit 0
