# Email Configuration Guide for Tarbiah Sentap CRM

## Overview

The Tarbiah Sentap CRM system uses email for various functionalities including:
- Two-Factor Authentication (2FA) verification codes
- Password reset emails
- System notifications
- Test emails for configuration verification

The system uses **Zoho Mail** as the email service provider for reliable email delivery with professional SMTP settings and secure authentication.

## Development Environment Setup

### 1. Mailpit Integration

For development, the system uses **Mailpit** - a local email testing tool that captures all outgoing emails.

#### Configuration:
- **SMTP Host**: `kdt-mailpit` (Docker service name)
- **SMTP Port**: `1025`
- **Web Interface**: `http://localhost:8025`
- **No Authentication Required**: Username and password are not needed

#### Docker Setup:
The `docker-compose.dev.yml` includes a Mailpit service:
```yaml
kdt-mailpit:
  image: axllent/mailpit:latest
  container_name: kdt-mailpit
  ports:
    - "8025:8025"  # Web interface
    - "1025:1025"  # SMTP server
  networks:
    - kdt-network
```

### 2. Environment Configuration

#### Development (.env):
```env
MAIL_MAILER=smtp
MAIL_HOST=kdt-mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Tarbiah Sentap CRM"
```

## Production Environment Setup

### Zoho Email Service Configuration

Zoho Mail offers excellent deliverability, detailed analytics, and easy setup with your own domain.

**Step 1: Set Up Zoho Mail Account**
1. Ensure you have a Zoho Mail account with your domain
2. Verify your account and domain are properly configured
3. Test that you can send and receive emails normally

**Step 2: Create App-Specific Password (Recommended)**
1. Go to Zoho Account Settings → Security
2. Navigate to **App Passwords** section
3. Create a new app-specific password for "Email Client"
4. Copy the generated password for use in CRM

**Step 3: Verify Domain Configuration**
1. Ensure your domain is properly configured with Zoho
2. Verify SPF and DKIM records are set up correctly
3. Test email delivery from your domain

**Step 4: Configure in CRM**
1. Go to Settings → Email in your CRM
2. Enter your Zoho email and password
3. Configure SMTP settings (pre-configured for Zoho)
4. Test the configuration

**Environment Configuration for Zoho:**
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.zoho.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-zoho-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Tarbiah Sentap CRM"
```

### Raspberry Pi Deployment

For Raspberry Pi deployment with native installation:

```env
# Use local SMTP or external service
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Tarbiah Sentap CRM"
```

## Email Configuration in CRM

### 1. Admin Settings

Administrators can configure email settings through the CRM interface:

1. Navigate to **Settings** → **Email** tab
2. Configure the following fields:
   - **From Name**: Display name for outgoing emails
   - **From Email**: Email address used as sender
   - **Domain**: Email domain (for reference)
   - **API Key**: If using external email service

### 2. Testing Email Configuration

#### Via CRM Interface:
1. Go to Settings → Email tab
2. Click "Send Test Email" button
3. Check Mailpit interface at `http://localhost:8025` (development)

#### Via API:
```bash
curl -X POST http://localhost:8000/api/v1/auth/settings/email/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"testEmail":"<EMAIL>"}'
```

## 2FA Email Configuration

### Current Setup:
- **Sender Email**: `<EMAIL>`
- **Admin Email**: `<EMAIL>`

### Important Notes:
1. **Sender vs Admin Email**: The sender email (`<EMAIL>`) is used for 2FA codes, while the admin email is for general administration
2. **Email Delivery**: Using different domains for sender and admin emails is acceptable and won't cause delivery issues
3. **Authentication**: 2FA emails are sent without user authentication, but the email address must exist in the system

## Troubleshooting

### Common Issues:

#### 1. Emails Not Being Sent
- **Check Mail Driver**: Ensure `MAIL_MAILER` is set to `smtp` (not `log`)
- **Verify SMTP Settings**: Confirm host, port, and credentials
- **Check Logs**: Review Laravel logs for email errors

#### 2. 2FA Codes Not Received
- **Verify Email Configuration**: Test with the "Send Test Email" feature
- **Check Mailpit**: In development, verify emails appear in Mailpit interface
- **Validate User Email**: Ensure the user's email address is correct

#### 3. 404 Error on Email Settings
- **Check Authentication**: User must be logged in with admin privileges
- **Verify Route**: Ensure frontend calls `/auth/settings/email` not `/settings/email`

#### 4. Zoho-Specific Issues
- **Password Invalid**: Ensure password is correct or use app-specific password
- **Sender Not Verified**: Verify your sender email address is from your Zoho domain
- **Rate Limits**: Check Zoho Mail dashboard for any rate limiting or quota issues
- **Domain Authentication**: For custom domains, complete SPF/DKIM setup in Zoho

### Debugging Commands:

```bash
# Test 2FA email sending
curl -X POST http://localhost:8000/api/v1/auth/2fa/send-code \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Check email settings
curl -X GET http://localhost:8000/api/v1/auth/settings/email \
  -H "Authorization: Bearer YOUR_TOKEN"

# Send test email
curl -X POST http://localhost:8000/api/v1/auth/settings/email/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"testEmail":"<EMAIL>"}'

# Validate API key
curl -X POST http://localhost:8000/api/v1/auth/settings/email/validate-key \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"apiKey":"xkeysib-your-api-key-here"}'
```

## Security Considerations

1. **Email Credentials**: Store SMTP credentials securely in environment variables
2. **API Key Protection**: Brevo API keys are encrypted in the database
3. **Rate Limiting**: 2FA email sending is rate-limited to prevent abuse
4. **Email Validation**: All email addresses are validated before sending
5. **Admin Access**: Email configuration requires admin privileges
6. **Secure Transmission**: All email communications use TLS encryption
7. **Key Rotation**: Regularly rotate API keys and passwords
8. **Domain Authentication**: Use SPF, DKIM, and DMARC records for better security

## Migration from Log Driver

If upgrading from log-based email (where emails were logged instead of sent):

1. Update `.env` file to use SMTP configuration
2. Restart Docker services: `docker-compose -f docker-compose.dev.yml restart`
3. Test email functionality through the CRM interface
4. Verify 2FA emails are being delivered properly

## Support

For additional support with email configuration:
- Check the Mailpit interface during development
- Review Laravel logs for detailed error messages
- Test with different email providers if delivery issues persist
