# 📧 Zoho Email Configuration Guide
## KDT/Tarbiah Sentap CRM - Consolidated Email Settings

This guide provides clear instructions for configuring Zoho email service in the KDT CRM system.

## 🎯 **Configuration Location**

**Single Configuration Point**: Settings → Email Tab
- ✅ **Centralized**: All email configuration is now in one location
- ❌ **Removed**: Duplicate SMTP configuration from Security tab
- 🔒 **Security**: Email configuration status still visible in Security tab for monitoring

## 📋 **Zoho Field Mapping**

### **Where to Find Each Value in Zoho Dashboard**

| CRM Field | Zoho Location | Example Format | Description |
|-----------|----------------|----------------|-------------|
| **Password** | Account Settings → Security | `your-password` | Your Zoho email password or app-specific password |
| **SMTP Host** | Fixed Value | `smtp.zoho.com` | <PERSON>oh<PERSON>'s SMTP server (pre-filled) |
| **SMTP Port** | Fixed Value | `587` (TLS) or `465` (SSL) | Recommended: 587 with TLS |
| **SMTP Username** | Your Email Address | `<EMAIL>` | Your full Zoho email address |
| **From Address** | Your Email Address | `<EMAIL>` | Any email from your Zoho domain |
| **Encryption** | Fixed Value | `TLS` | Recommended encryption method |

### **Critical Field Distinctions**

#### ⚠️ **SMTP Username vs From Address**
- **SMTP Username**: Use your full Zoho email address (e.g., `<EMAIL>`)
- **From Address**: Use any email from your Zoho domain (e.g., `<EMAIL>`)
- **Common Mistake**: Using partial email or incorrect domain

#### 🔑 **Password Security**
- **Password**: Use your Zoho email password or app-specific password
- **Recommendation**: Create app-specific password for better security
- **Location**: Zoho Account Settings → Security → App Passwords

## 🔧 **Step-by-Step Configuration**

### **Step 1: Prepare Your Zoho Email**
1. Login to your Zoho Mail account
2. Ensure your email account is active and accessible
3. Note your full email address (e.g., `<EMAIL>`)

### **Step 2: Create App-Specific Password (Recommended)**
1. Go to **Zoho Account Settings** → **Security**
2. Navigate to **App Passwords** section
3. Generate a new app-specific password for "Email Client"
4. Copy the generated password for use in CRM

### **Step 3: Verify Your Domain**
1. Ensure your domain is properly configured with Zoho
2. Verify you can send and receive emails normally
3. Use any email address from your verified domain

### **Step 4: Configure in CRM**
1. Go to **Settings** → **Email** tab
2. Fill in the fields using the mapping above
3. Click **Save Email Settings**
4. Click **Send Test Email** to verify

## ✅ **Validation Rules**

The system automatically validates:
- ✅ Password is provided and not empty
- ✅ SMTP Username is a valid email address
- ✅ From Address is valid email format
- ✅ All required fields are filled

## 🧪 **Testing Configuration**

### **Test Email Function**
- Sends test email to current user's email address
- Verifies complete email delivery chain
- Provides specific error messages for common issues

### **Common Error Messages**
| Error | Cause | Solution |
|-------|-------|----------|
| "Authentication failed" | Wrong password or SMTP username | Verify password and use full Zoho email as username |
| "Sender not verified" | From Address not from Zoho domain | Use email address from your verified Zoho domain |
| "Connection refused" | Network/firewall issue | Check internet connection and firewall settings |

## 🔒 **Security Features**

### **Credential Protection**
- ✅ Passwords encrypted in database
- ✅ Passwords masked in UI
- ✅ No hardcoded credentials in code
- ✅ Secure transmission over HTTPS

### **Access Control**
- 🔐 Admin-only access to email configuration
- 📊 Security status monitoring
- 📝 Audit logging for all changes

## 🚀 **Email Features Supported**

### **System Emails**
- ✅ Two-Factor Authentication codes
- ✅ Password reset emails
- ✅ User registration notifications
- ✅ System alerts and notifications

### **CRM Communications**
- ✅ Invoice delivery
- ✅ Quotation sending
- ✅ Client communications
- ✅ Campaign emails (future)

## 🔧 **Troubleshooting**

### **Authentication Issues**
1. Verify password is correct for your Zoho account
2. Ensure SMTP username is your full Zoho email address
3. Check that From Address is from your Zoho domain

### **Delivery Issues**
1. Test with Send Test Email function
2. Check Zoho Mail dashboard for sending statistics
3. Verify recipient email addresses are valid

### **Configuration Issues**
1. Use the field mapping guide above
2. Follow step-by-step configuration process
3. Validate all fields before saving

## 📞 **Support**

For additional help:
1. Check Zoho Mail documentation
2. Verify account status in Zoho dashboard
3. Contact system administrator for CRM-specific issues

---

**Last Updated**: Configuration updated for Zoho email service
**Version**: 3.0 - Zoho email integration with enhanced validation
