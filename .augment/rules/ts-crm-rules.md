---
type: "always_apply"
---

# Laravel CRM Development Rules

## Project Architecture & Environment
- Use Laravel 10.x backend with React 18.3.1 + TypeScript frontend
- Use PostgreSQL database with Redis for caching and sessions
- Deploy with Docker containerization using docker-compose.yml structure
- Use port 3000 for frontend, port 8000 for backend API
- Deploy to Raspberry Pi servers in `/home/<USER>/Apps/` directory
- Use Vite for frontend bundling with specific pi-optimized config
- Enable Docker health checks for all services (postgres, redis, backend, frontend)

## Database & Data Models
- Use SoftDeletes trait for all primary models (Lead, Deal, Client, etc.)
- Implement proper model relationships: Lead->Deal->Client workflow
- Use UUID fields where necessary (clients table has uuid field)
- Cast JSON fields as arrays: tags, documents, custom_fields, qualification_criteria
- Use decimal:2 casting for monetary fields: value, estimated_value, total_spent
- Maintain proper foreign key relationships: lead_id, client_id, assigned_to, created_by
- Use computed fields with auto-calculation: total_spent, transaction_count, ltv_segment
- Implement model scopes for common queries: active(), byStage(), overdue(), assignedTo()

## Authentication & Security
- Use Laravel Sanctum for API authentication
- Implement 3-tier role system: Super Admin/Staff/User
- Use User model with role field and proper fillable attributes
- Require admin approval for signups (admin_approved field)
- Use Zoho SMTP configuration for email services
- Never hardcode sensitive credentials - use environment variables
- Implement comprehensive audit logging with ActivityLog model

## Frontend React/TypeScript Structure
- Use React Router DOM v7.6.3 for routing
- Implement Context providers for state management: AuthProvider, NotificationProvider, ToastProvider, etc.
- Use proper TypeScript interfaces in /src/types directory
- Structure components in /src/components with proper naming: Layout.tsx, CustomDropdown.tsx, etc.
- Use service layer pattern in /src/services for API calls
- Implement proper error boundaries and loading states

## UI Component Standards
- Use CustomDropdown component instead of native HTML select elements
- Implement ToggleSwitch component for boolean settings with proper size variants (sm/md/lg)
- Use right sidebar panels for forms (400px-500px width with overlay background)
- Position toast notifications at bottom-right using ToastContainer component
- Implement standardized table layouts with leftmost checkboxes for bulk actions
- Use Badge component with semantic classes: badge-primary, badge-success, badge-warning, etc.
- Use lucide-react icons throughout the application
- Implement proper dark mode support with 'class' strategy in Tailwind

## Tailwind CSS & Styling Rules
- Use semantic color system with CSS custom properties in tailwind.config.js
- Implement dark mode classes: dark:bg-gray-800, dark:text-white, dark:border-gray-700
- Use component utility classes defined in index.css: .card, .btn, .badge, etc.
- Use proper Tailwind spacing: p-4 sm:p-6 for responsive padding
- Implement transition classes: transition-colors duration-300 for smooth animations
- Use proper border and shadow classes: border-gray-200 dark:border-gray-700
- Use scrollbar-hide utility class for hidden scrollbars

## Mobile Responsiveness Implementation
- Use mobile-first breakpoints: 320px-767px mobile, 768px-1199px tablet, 1200px+ desktop
- Implement responsive button sizing: w-full sm:w-auto for mobile-first buttons
- Use responsive flex classes: flex-col sm:flex-row for mobile stacking
- Implement minimum 44px touch targets for mobile interfaces
- Use responsive sidebar behavior with proper transform classes and overlays
- Implement collapsible sidebar with sidebarCollapsed state management

## Animation & Interaction Standards
- Use specific animation durations: 1500ms for scores, 1200ms for progress bars, 1000ms for circular progress
- Implement ease-out timing functions for all animations
- Use transform and transition classes for smooth hover effects
- Implement ESC key handling and click-outside functionality for modals/dropdowns
- Use proper focus management with focus:ring-2 focus:ring-blue-500 classes
- Implement loading states with proper skeleton components

## Form & Input Components
- Use controlled components with proper state management
- Implement PhoneInput component with country code selection and flag display
- Use DateRangePicker for date selections with proper date-fns integration
- Implement proper form validation with FormErrorMessage component
- Use minimum 14px font size for all form inputs
- Implement auto-save functionality for settings forms
- Use proper ARIA labels and accessibility attributes

## API & Service Layer Rules
- Use apiService class with proper error handling and caching
- Implement RESTful API endpoints following Laravel resource patterns
- Use proper HTTP status codes: 200, 201, 422, 404, 500
- Implement pagination for large datasets with proper meta information
- Use proper request validation in Laravel controllers
- Implement API rate limiting and throttling
- Use consistent JSON response structure with data/message/errors keys

## State Management Patterns
- Use React Context for global state: AuthContext, NotificationContext, etc.
- Implement proper loading states with useState hooks
- Use proper error handling with try-catch blocks in async functions
- Implement optimistic updates where appropriate
- Use proper dependency arrays in useEffect hooks
- Implement proper cleanup in useEffect return functions

## Deployment & DevOps Rules
- Use docker-compose.yml with health checks for all services
- Implement proper environment variable management: .env.example, .env.production.example
- Use deploy.sh script for Raspberry Pi deployment with proper logging
- Implement database backups with compression and retention policies
- Use proper service naming: kdt-postgres, kdt-redis, kdt-backend, kdt-frontend
- Implement proper container networking with kdt-network bridge

## Code Quality & Testing
- Remove all console.log statements before production deployment
- Implement proper error boundaries in React components
- Use proper TypeScript interfaces for all props and data structures
- Implement proper component prop validation
- Use meaningful component and variable names
- Implement proper code comments for complex business logic
- Use proper Git commit messages following conventional commits

## Business Logic Implementation
- Follow CRM workflow: Leads → Deals → Quotations → Invoices → Payments → Clients
- Maintain lead-deal associations permanently throughout status changes
- Implement proper deal pipeline stages with probability calculations
- Use proper status enums and constants in models
- Implement profile completion scoring with weighted field importance
- Use proper currency handling (MYR) throughout the application
- Implement proper LTV segment calculation based on total_spent values

## File Organization & Structure
- Organize components by feature: Layout.tsx, ClientModal.tsx, DealSidebar.tsx
- Use proper directory structure: /src/components, /src/contexts, /src/services, /src/types
- Implement proper model organization in backend: /app/Models with proper relationships
- Use proper controller organization with resource controllers
- Implement proper middleware for authentication and authorization
- Use proper migration files with proper foreign key constraints