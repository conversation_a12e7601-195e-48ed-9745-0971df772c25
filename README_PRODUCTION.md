# KDT Production Deployment for Raspberry Pi

A complete production-ready deployment package for the KDT CRM application optimized for Raspberry Pi servers.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone <your-repository-url> kdt
cd kdt

# Run the quick setup script
./quick-setup.sh

# Deploy the application
./deploy.sh deploy
```

### Option 2: Manual Setup

```bash
# 1. Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo apt install -y docker-compose

# 2. Configure environment
cp .env.production.example .env.production
nano .env.production  # Update with your settings

# 3. Deploy
./deploy.sh deploy
```

## 📋 What's Included

### Production-Optimized Configuration

- **ARM64 compatible Docker images**
- **Resource limits optimized for Raspberry Pi**
- **Production-grade security settings**
- **Automated SSL/TLS support**
- **Health monitoring and alerting**
- **Automated backup system**

### Services

- **Frontend**: React app with Nginx (Port 4000)
- **Backend**: Laravel API with PHP-FPM (Port 4001)
- **Database**: PostgreSQL 15 with persistent storage
- **Cache**: Redis with memory optimization
- **Proxy**: Nginx reverse proxy with SSL support
- **Monitoring**: Health checks and performance monitoring

### Scripts and Automation

- `deploy.sh` - Main deployment and management script
- `quick-setup.sh` - Automated initial setup
- `scripts/production/backup.sh` - Automated backup system
- `scripts/production/monitor.sh` - Health monitoring
- `scripts/production/setup-cron.sh` - Cron job configuration

## 🔧 Configuration Files

### Docker Configuration

- `docker-compose.prod.yml` - Production Docker Compose
- `Dockerfile.prod` - Production frontend Dockerfile
- `backend/Dockerfile.prod` - Production backend Dockerfile

### Nginx Configuration

- `deploy/nginx/nginx.conf` - Main reverse proxy config
- `deploy/nginx/frontend.conf` - Frontend server config
- `deploy/nginx/backend.conf` - Backend server config

### Environment Configuration

- `.env.production.example` - Production environment template
- `backend/.env.production.example` - Backend environment template

## 📊 System Requirements

### Minimum Requirements

- **Raspberry Pi 4** with 4GB RAM
- **32GB MicroSD Card** (Class 10 or better)
- **Stable internet connection**
- **Raspberry Pi OS 64-bit** (Bullseye or newer)

### Recommended Requirements

- **Raspberry Pi 4** with 8GB RAM
- **64GB MicroSD Card** or USB SSD
- **Ethernet connection**
- **Active cooling** (fan or heatsink)

## 🛠️ Management Commands

### Deployment Management

```bash
# Deploy application (first time)
./deploy.sh deploy

# Start services
./deploy.sh start

# Stop services
./deploy.sh stop

# Restart services
./deploy.sh restart

# Update application
./deploy.sh update

# Check status
./deploy.sh status

# Check health
./deploy.sh health

# View logs
./deploy.sh logs [service-name]

# Create backup
./deploy.sh backup
```

### Monitoring and Maintenance

```bash
# Run health check
./scripts/production/monitor.sh

# Create manual backup
./scripts/production/backup.sh

# Setup automated monitoring
./scripts/production/setup-cron.sh

# Generate monitoring report
./scripts/production/monitor.sh --report
```

## 📈 Monitoring and Alerts

### Automated Monitoring

- **Health checks every 5 minutes**
- **Daily automated backups**
- **Weekly log cleanup**
- **Monthly system update checks**

### Monitored Metrics

- **System resources** (CPU, memory, disk, temperature)
- **Service health** (frontend, backend, database)
- **Application performance** (response times)
- **Error rates** (application and system logs)

### Alert Thresholds

- **CPU usage**: > 80%
- **Memory usage**: > 85%
- **Disk usage**: > 90%
- **Response time**: > 5 seconds
- **Temperature**: > 70°C

## 🔒 Security Features

### Built-in Security

- **Non-root containers**
- **Resource limits and quotas**
- **Network isolation**
- **Secure default configurations**
- **Regular security updates**

### SSL/TLS Support

```bash
# Install SSL certificates
sudo apt install -y certbot
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates to SSL directory
sudo cp /etc/letsencrypt/live/your-domain.com/* deploy/ssl/

# Update nginx configuration for HTTPS
# Edit deploy/nginx/nginx.conf
```

## 💾 Backup and Recovery

### Automated Backups

- **Database**: Daily PostgreSQL dumps
- **Application files**: Weekly application backup
- **Storage volumes**: Weekly storage backup
- **Retention**: 7 days (configurable)

### Manual Backup

```bash
# Full backup
./scripts/production/backup.sh

# Database only
./scripts/production/backup.sh --database-only

# Application files only
./scripts/production/backup.sh --application-only
```

### Recovery

```bash
# Restore database from backup
docker-compose -f docker-compose.prod.yml exec -T kdt-postgres psql -U kdt -d kdt < backups/database_backup_YYYYMMDD_HHMMSS.sql

# Restore application files
tar -xzf backups/application_backup_YYYYMMDD_HHMMSS.tar.gz

# Restart services
./deploy.sh restart
```

## 🔧 Troubleshooting

### Common Issues

#### High Memory Usage

```bash
# Check memory usage
free -h

# Increase swap space
sudo dphys-swapfile swapoff
sudo sed -i 's/CONF_SWAPSIZE=2048/CONF_SWAPSIZE=4096/' /etc/dphys-swapfile
sudo dphys-swapfile setup
sudo dphys-swapfile swapon
```

#### Service Not Starting

```bash
# Check service logs
./deploy.sh logs [service-name]

# Check Docker status
docker ps -a

# Restart specific service
docker-compose -f docker-compose.prod.yml restart [service-name]
```

#### Database Connection Issues

```bash
# Check database health
docker-compose -f docker-compose.prod.yml exec kdt-postgres pg_isready -U kdt

# Reset database connection
docker-compose -f docker-compose.prod.yml restart kdt-postgres
```

### Performance Optimization

#### For 4GB Raspberry Pi

Edit `docker-compose.prod.yml` to reduce resource limits:

```yaml
deploy:
  resources:
    limits:
      memory: 256M
      cpus: '0.25'
```

#### For 8GB Raspberry Pi

Use default resource limits or increase for better performance.

## 📚 Documentation

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - Comprehensive deployment guide
- **[README.md](README.md)** - Development documentation
- **Production logs** - Available in project directory

## 🆘 Support

### Log Files

- `deploy.log` - Deployment script logs
- `monitor.log` - Monitoring logs
- `backup.log` - Backup logs
- Docker logs: `./deploy.sh logs [service]`

### Getting Help

1. Check the logs for error messages
2. Run health checks: `./deploy.sh health`
3. Review the troubleshooting section
4. Create an issue with logs and system information

## 📄 License

This deployment package is part of the KDT project. See the main project license for details.

---

**Ready to deploy?** Start with `./quick-setup.sh` and follow the prompts!
