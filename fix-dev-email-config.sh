#!/bin/bash

# Development Email Configuration Fix Script
# Updates development environment <NAME_EMAIL> as system sender
# Phase 1: Fix Development Environment

set -euo pipefail

# Configuration
PROJECT_ROOT="$(pwd)"
LOG_FILE="/tmp/dev-email-fix-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        STEP)
            echo -e "${PURPLE}[STEP]${NC} $message" | tee -a "$LOG_FILE"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Check if Docker environment is running
check_docker_environment() {
    log INFO "🔍 Checking Docker development environment"
    
    if ! docker-compose ps | grep -q "kdt-backend.*Up"; then
        log ERROR "Docker backend container is not running"
        log ERROR "Please start the development environment: docker-compose up -d"
        exit 1
    fi

    if ! docker-compose ps | grep -q "kdt-postgres.*Up"; then
        log ERROR "Docker postgres container is not running"
        exit 1
    fi
    
    log SUCCESS "Docker environment is running"
}

# Update database settings
update_database_settings() {
    log STEP "📝 Updating database email settings"
    
    # Create PHP script to update development database settings
    cat > /tmp/update_dev_email_settings.php << 'EOF'
<?php
require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\SystemSetting;
use Illuminate\Support\Facades\DB;

echo "🔧 Updating Development Email Settings...\n\n";

try {
    DB::beginTransaction();

    // Development email <NAME_EMAIL>
    $emailSettings = [
        // For development, we use Mailpit (no real SMTP needed)
        'smtp_host' => 'kdt-mailpit',
        'smtp_port' => 1025,
        'smtp_username' => '<EMAIL>',
        'smtp_password' => '', // No password needed for Mailpit
        'smtp_encryption' => null, // No encryption for Mailpit
        
        // Email Sender Settings - this is what matters for FROM address
        'email_from_address' => '<EMAIL>',
        'email_from_name' => 'Tarbiah Sentap CRM',
        'email_reply_to' => '<EMAIL>',
        
        // System Settings
        'two_factor_auth_enabled' => true, // Enable for testing
        'app_debug' => true, // Development mode
    ];

    echo "📝 Updating system settings...\n";
    
    foreach ($emailSettings as $key => $value) {
        $type = 'string';
        $description = '';
        
        // Set appropriate types and descriptions
        switch ($key) {
            case 'smtp_port':
                $type = 'integer';
                $description = 'SMTP port number (1025 for Mailpit development)';
                break;
            case 'two_factor_auth_enabled':
                $type = 'boolean';
                $description = 'Enable or disable two-factor authentication system-wide';
                break;
            case 'app_debug':
                $type = 'boolean';
                $description = 'Application debug mode';
                break;
            case 'smtp_host':
                $description = 'SMTP server hostname (kdt-mailpit for development)';
                break;
            case 'smtp_username':
                $description = 'SMTP username (system sender email address)';
                break;
            case 'smtp_password':
                $description = 'SMTP password (empty for Mailpit)';
                break;
            case 'smtp_encryption':
                $description = 'SMTP encryption method (null for Mailpit)';
                break;
            case 'email_from_address':
                $description = 'Default sender email address for all system emails';
                break;
            case 'email_from_name':
                $description = 'Default sender name for all system emails';
                break;
            case 'email_reply_to':
                $description = 'Default reply-to email address';
                break;
        }
        
        SystemSetting::set($key, $value, $type, $description);
        echo "  ✅ {$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : ($value ?: 'null')) . "\n";
    }

    DB::commit();
    
    echo "\n🎉 Development email settings updated successfully!\n\n";
    
    // Verify settings
    echo "📋 Current email configuration:\n";
    echo "  SMTP Host: " . SystemSetting::get('smtp_host') . "\n";
    echo "  SMTP Port: " . SystemSetting::get('smtp_port') . "\n";
    echo "  SMTP Username: " . SystemSetting::get('smtp_username') . "\n";
    echo "  From Address: " . SystemSetting::get('email_from_address') . "\n";
    echo "  From Name: " . SystemSetting::get('email_from_name') . "\n";
    echo "  Reply To: " . SystemSetting::get('email_reply_to') . "\n";
    echo "  2FA Enabled: " . (SystemSetting::get('two_factor_auth_enabled') ? 'true' : 'false') . "\n";
    echo "  Debug Mode: " . (SystemSetting::get('app_debug') ? 'true' : 'false') . "\n";
    
    echo "\n✅ Configuration complete!\n\n";

} catch (Exception $e) {
    DB::rollBack();
    echo "❌ Error updating settings: " . $e->getMessage() . "\n";
    exit(1);
}
EOF

    # Copy script to backend container and run it
    docker cp /tmp/update_dev_email_settings.php kdt-backend:/tmp/update_dev_email_settings.php
    
    if docker-compose exec kdt-backend php /tmp/update_dev_email_settings.php; then
        log SUCCESS "Database settings updated successfully"
        docker-compose exec kdt-backend rm -f /tmp/update_dev_email_settings.php
    else
        log ERROR "Failed to update database settings"
        exit 1
    fi
    
    rm -f /tmp/update_dev_email_settings.php
}

# Clear Laravel caches
clear_caches() {
    log STEP "🧹 Clearing Laravel caches"
    
    docker-compose exec kdt-backend php artisan config:clear
    docker-compose exec kdt-backend php artisan cache:clear
    docker-compose exec kdt-backend php artisan route:clear
    docker-compose exec kdt-backend php artisan view:clear
    
    log SUCCESS "Caches cleared"
}

# Test email configuration
test_email_config() {
    log STEP "📧 Testing email configuration"
    
    # Create test email script
    cat > /tmp/test_dev_email.php << 'EOF'
<?php
require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Mail;
use App\Models\SystemSetting;

echo "🧪 Testing development email configuration...\n";

try {
    // Get current settings
    $fromEmail = SystemSetting::get('email_from_address', '<EMAIL>');
    $fromName = SystemSetting::get('email_from_name', 'Tarbiah Sentap CRM');
    
    echo "From: {$fromName} <{$fromEmail}>\n";
    echo "To: <EMAIL>\n";
    echo "Subject: Development Email Test\n\n";
    
    // Send test email
    Mail::raw('Development email configuration test successful - ' . date('Y-m-d H:i:s'), function($message) use ($fromEmail, $fromName) {
        $message->to('<EMAIL>')
                ->subject('Development Email Test - <EMAIL>')
                ->from($fromEmail, $fromName);
    });
    
    echo "✅ Test email sent successfully!\n";
    echo "Check Mailpit at http://localhost:8025 to see the email.\n";
    echo "The email should show FROM: <EMAIL>\n";
    
} catch (Exception $e) {
    echo "❌ Email test failed: " . $e->getMessage() . "\n";
    exit(1);
}
EOF

    # Copy script to backend container and run it
    docker cp /tmp/test_dev_email.php kdt-backend:/tmp/test_dev_email.php
    
    if docker-compose exec kdt-backend php /tmp/test_dev_email.php; then
        log SUCCESS "Email test completed"
        docker-compose exec kdt-backend rm -f /tmp/test_dev_email.php
    else
        log ERROR "Email test failed"
        docker-compose exec kdt-backend rm -f /tmp/test_dev_email.php
        return 1
    fi
    
    rm -f /tmp/test_dev_email.php
}

# Rebuild frontend with updated defaults
rebuild_frontend() {
    log STEP "🔨 Rebuilding frontend with updated defaults"
    
    # The frontend defaults were already updated in previous commits
    # Just rebuild to ensure they take effect
    if npm run build; then
        log SUCCESS "Frontend rebuilt successfully"
    else
        log ERROR "Frontend build failed"
        return 1
    fi
}

# Main function
main() {
    echo "========================================"
    echo "  Development Email Configuration Fix"
    echo "  Phase 1: Fix Development Environment"
    echo "  $(date)"
    echo "========================================"
    
    log INFO "🚀 Starting development email configuration fix"
    log INFO "Project: $PROJECT_ROOT"
    log INFO "Log file: $LOG_FILE"
    
    # Execute fix steps
    check_docker_environment
    update_database_settings
    clear_caches
    rebuild_frontend
    test_email_config
    
    echo ""
    echo -e "${GREEN}🎉 Development email configuration fix completed!${NC}"
    echo ""
    echo "What was fixed:"
    echo "✅ Database settings updated <NAME_EMAIL> as system sender"
    echo "✅ Frontend defaults already <NAME_EMAIL>"
    echo "✅ 2FA enabled for testing"
    echo "✅ Mailpit configured for email testing"
    echo "✅ Laravel caches cleared"
    echo ""
    echo "Next steps for testing:"
    echo "1. Go to: http://localhost:3000/settings"
    echo "2. Check that email <NAME_EMAIL> as default"
    echo "3. Test 2FA login process:"
    echo "   - Logout and login again"
    echo "   - Should receive 2FA <NAME_EMAIL>"
    echo "4. Check Mailpit at http://localhost:8025 to see all emails"
    echo "5. Test password reset emails"
    echo "6. Verify all system emails <NAME_EMAIL>"
    echo ""
    echo "Email testing interface: http://localhost:8025"
    echo "Application: http://localhost:3000"
    echo "Log file: $LOG_FILE"
}

# Execute main function
main "$@"
