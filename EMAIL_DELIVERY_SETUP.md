# Email Delivery Setup Guide

## Current Status

The KDT CRM system is currently configured for **development mode** using Mailpit, which captures all emails locally for testing purposes. This is why emails appear in the Mailpit inbox (http://localhost:8025) but are not delivered to actual email addresses.

## Email Configuration Options

### Option 1: Development Mode (Current Setup)
- **Purpose**: Local testing and development
- **Behavior**: Emails are captured in Mailpit but not sent externally
- **Configuration**: Already set up in `backend/.env`

```env
MAIL_MAILER=smtp
MAIL_HOST=kdt-mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
```

### Option 2: Production Mode with Gmail SMTP
- **Purpose**: Real email delivery to external addresses
- **Requirements**: Gmail account with App Password enabled

#### Setup Steps:

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"

3. **Update backend/.env**:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-character-app-password
MAIL_ENCRYPTION=tls
```

### Option 3: Production Mode with Other SMTP Services

#### Mailgun:
```env
MAIL_MAILER=mailgun
MAILGUN_DOMAIN=your-domain.com
MAILGUN_SECRET=your-mailgun-secret
```

#### Amazon SES:
```env
MAIL_MAILER=ses
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
```

## Quick Switch for Testing

To quickly test real email delivery without permanently changing the configuration:

1. **Stop the development environment**:
   ```bash
   ./scripts/dev.sh stop
   ```

2. **Temporarily update backend/.env** (comment out Mailpit, uncomment Gmail):
   ```env
   # Development (Mailpit)
   # MAIL_MAILER=smtp
   # MAIL_HOST=kdt-mailpit
   # MAIL_PORT=1025
   
   # Production SMTP (Gmail)
   MAIL_MAILER=smtp
   MAIL_HOST=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-app-password
   MAIL_ENCRYPTION=tls
   ```

3. **Restart the environment**:
   ```bash
   ./scripts/dev.sh start
   ```

4. **Test email delivery** through the CRM interface

5. **Revert changes** when done testing

## Testing Email Delivery

1. **Through CRM Interface**:
   - Go to Settings → Email tab
   - Use "Test Email Configuration" feature
   - Enter your email address to receive a test email

2. **Through 2FA**:
   - Try logging in with 2FA enabled
   - Check if verification codes are delivered

3. **Through Password Reset**:
   - Use "Forgot Password" feature
   - Check if reset emails are delivered

## Troubleshooting

### Common Issues:

1. **Gmail Authentication Errors**:
   - Ensure 2FA is enabled
   - Use App Password, not regular password
   - Check if "Less secure app access" is disabled (should be)

2. **Port/Firewall Issues**:
   - Ensure port 587 is not blocked
   - Try port 465 with SSL encryption

3. **Rate Limiting**:
   - Gmail has sending limits for new accounts
   - Consider using dedicated email service for production

### Verification Steps:

1. Check Laravel logs: `backend/storage/logs/laravel.log`
2. Check email queue status (if using queues)
3. Verify SMTP credentials are correct
4. Test with simple mail client first

## Recommendations

- **Development**: Keep Mailpit configuration for local testing
- **Production**: Use dedicated email service (Mailgun, SES, etc.)
- **Testing**: Temporarily switch to Gmail SMTP when needed
- **Security**: Never commit real SMTP credentials to version control
