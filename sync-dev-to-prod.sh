#!/bin/bash

# Database Synchronization Script: Development to Production
# This script safely transfers clean client data from dev to production

set -e  # Exit on any error

# Configuration
DEV_DB_HOST="localhost"
DEV_DB_PORT="5432"
DEV_DB_NAME="kdt"
DEV_DB_USER="kdt"
DEV_DB_PASSWORD=""

PROD_DB_HOST="localhost"
PROD_DB_PORT="5432"
PROD_DB_NAME="kdt"
PROD_DB_USER="kdt"
PROD_DB_PASSWORD=""

BACKUP_DIR="/tmp/db-sync-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="$BACKUP_DIR/sync.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create backup directory
mkdir -p "$BACKUP_DIR"
log "Created backup directory: $BACKUP_DIR"

# Step 1: Export clean data from development
log "Step 1: Exporting clean client data from development database..."

# Check if development database is accessible
if ! docker-compose -f docker-compose.dev.yml exec -T kdt-postgres pg_isready -U "$DEV_DB_USER" -d "$DEV_DB_NAME" > /dev/null 2>&1; then
    error "Development database is not accessible. Make sure Docker containers are running."
fi

# Export clients data from development
docker-compose -f docker-compose.dev.yml exec -T kdt-postgres pg_dump \
    -U "$DEV_DB_USER" \
    -d "$DEV_DB_NAME" \
    --table=clients \
    --data-only \
    --column-inserts \
    --no-owner \
    --no-privileges > "$BACKUP_DIR/dev_clients_data.sql"

if [ $? -eq 0 ]; then
    success "Development data exported successfully"
    DEV_RECORD_COUNT=$(docker-compose -f docker-compose.dev.yml exec -T kdt-postgres psql -U "$DEV_DB_USER" -d "$DEV_DB_NAME" -t -c "SELECT COUNT(*) FROM clients WHERE deleted_at IS NULL;" | tr -d ' ')
    log "Development database contains $DEV_RECORD_COUNT active client records"
else
    error "Failed to export development data"
fi

# Step 2: Create production backup
log "Step 2: Creating production database backup..."

# Note: This section would need to be run on the production server
cat > "$BACKUP_DIR/production_backup_commands.sh" << 'EOF'
#!/bin/bash
# Run these commands on the production server

PROD_BACKUP_FILE="/tmp/prod_clients_backup_$(date +%Y%m%d_%H%M%S).sql"

# Backup current production data
pg_dump -U kdt -d kdt --table=clients --data-only --column-inserts > "$PROD_BACKUP_FILE"

echo "Production backup created: $PROD_BACKUP_FILE"

# Show current production record count
PROD_COUNT=$(psql -U kdt -d kdt -t -c "SELECT COUNT(*) FROM clients WHERE deleted_at IS NULL;" | tr -d ' ')
echo "Production database currently contains $PROD_COUNT active client records"
EOF

chmod +x "$BACKUP_DIR/production_backup_commands.sh"

# Step 3: Create cleanup and import script for production
log "Step 3: Creating production cleanup and import script..."

cat > "$BACKUP_DIR/production_import_script.sql" << 'EOF'
-- Production Database Cleanup and Import Script
-- Run this on the production server after backing up

BEGIN;

-- Step 1: Backup existing data
CREATE TABLE IF NOT EXISTS clients_backup_sync AS 
SELECT * FROM clients WHERE deleted_at IS NULL;

-- Step 2: Soft delete all existing client records
UPDATE clients 
SET deleted_at = NOW(), 
    updated_at = NOW() 
WHERE deleted_at IS NULL;

-- Step 3: Reset the sequence to avoid ID conflicts
SELECT setval('clients_id_seq', 1, false);

-- Step 4: Import will be done by loading the dev_clients_data.sql file
-- This should be done after this script completes

-- Verify the cleanup
SELECT 
    'Active Records After Cleanup' as status,
    COUNT(*) as count
FROM clients 
WHERE deleted_at IS NULL;

SELECT 
    'Backed Up Records' as status,
    COUNT(*) as count
FROM clients_backup_sync;

COMMIT;

-- After importing dev_clients_data.sql, run this verification:
-- SELECT COUNT(*) as imported_records FROM clients WHERE deleted_at IS NULL;
EOF

# Step 4: Create verification script
cat > "$BACKUP_DIR/verify_sync.sql" << 'EOF'
-- Verification Script - Run after import
-- This verifies the sync was successful

SELECT 
    'Total Active Records' as metric,
    COUNT(*) as count
FROM clients 
WHERE deleted_at IS NULL

UNION ALL

SELECT 
    'Records with Email' as metric,
    COUNT(*) as count
FROM clients 
WHERE email IS NOT NULL 
    AND email != '' 
    AND deleted_at IS NULL

UNION ALL

SELECT 
    'Records without Email' as metric,
    COUNT(*) as count
FROM clients 
WHERE (email IS NULL OR email = '') 
    AND deleted_at IS NULL

UNION ALL

SELECT 
    'Duplicate Emails (should be 0)' as metric,
    COUNT(*) as count
FROM (
    SELECT email, COUNT(*) as cnt
    FROM clients 
    WHERE email IS NOT NULL 
        AND email != '' 
        AND deleted_at IS NULL
    GROUP BY email 
    HAVING COUNT(*) > 1
) duplicates;

-- Show sample records
SELECT 
    id,
    name,
    email,
    phone,
    created_at
FROM clients 
WHERE deleted_at IS NULL
ORDER BY id
LIMIT 5;
EOF

# Step 5: Create complete sync instructions
cat > "$BACKUP_DIR/SYNC_INSTRUCTIONS.md" << 'EOF'
# Database Synchronization Instructions

## Files Created:
1. `dev_clients_data.sql` - Clean client data from development
2. `production_backup_commands.sh` - Commands to backup production
3. `production_import_script.sql` - Cleanup script for production
4. `verify_sync.sql` - Verification script

## Steps to Execute on Production Server:

### 1. Backup Production Data
```bash
./production_backup_commands.sh
```

### 2. Stop Import Processes
```bash
sudo pkill -f "csv-import"
sudo systemctl restart php8.2-fpm
```

### 3. Run Cleanup Script
```bash
psql -U kdt -d kdt -f production_import_script.sql
```

### 4. Import Development Data
```bash
psql -U kdt -d kdt -f dev_clients_data.sql
```

### 5. Verify Import
```bash
psql -U kdt -d kdt -f verify_sync.sql
```

### 6. Clear Laravel Caches
```bash
cd /home/<USER>/Apps/ts-crm/backend
php artisan cache:clear
php artisan config:clear
```

## Expected Result:
- Exactly 26,711 active client records
- No duplicate emails
- Clean, consistent data from development environment
EOF

success "Database sync scripts created successfully!"
log "Backup directory: $BACKUP_DIR"
log "Development records to sync: $DEV_RECORD_COUNT"

echo ""
echo -e "${GREEN}Next Steps:${NC}"
echo "1. Copy the backup directory to your production server:"
echo "   scp -r $BACKUP_DIR <EMAIL>:/tmp/"
echo ""
echo "2. Follow the instructions in SYNC_INSTRUCTIONS.md"
echo ""
echo "3. This will replace all production client data with clean development data"
echo ""
warning "This operation will replace ALL client data in production!"
warning "Make sure you have a backup before proceeding!"
