import { useState, useCallback, useMemo } from 'react';
import { ValidationError, ValidationResult, validateClient, validateLead, getFieldError, hasFieldError } from '../utils/validation';

export interface UseFormValidationOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

export interface UseFormValidationReturn {
  errors: ValidationError[];
  isValid: boolean;
  isFormValid: boolean;
  validateField: (fieldName: string, value: any, formData: any) => void;
  validateForm: (formData: any, type: 'client' | 'lead') => ValidationResult;
  clearErrors: () => void;
  clearFieldError: (fieldName: string) => void;
  getFieldError: (fieldName: string) => string | null;
  hasFieldError: (fieldName: string) => boolean;
  setErrors: (errors: ValidationError[]) => void;
}

export const useFormValidation = (options: UseFormValidationOptions = {}): UseFormValidationReturn => {
  const { validateOnChange = true, validateOnBlur = true } = options;
  const [errors, setErrors] = useState<ValidationError[]>([]);

  const validateForm = useCallback((formData: any, type: 'client' | 'lead'): ValidationResult => {
    const result = type === 'client' ? validateClient(formData) : validateLead(formData);
    setErrors(result.errors);
    return result;
  }, []);

  const validateField = useCallback((fieldName: string, value: any, formData: any) => {
    // Create a temporary form data object with the updated field
    const tempFormData = { ...formData, [fieldName]: value };
    
    // Validate the entire form to get field-specific errors
    const clientResult = validateClient(tempFormData);
    const leadResult = validateLead(tempFormData);
    
    // Combine errors and filter for the specific field
    const allErrors = [...clientResult.errors, ...leadResult.errors];
    const fieldErrors = allErrors.filter(error => error.field === fieldName);
    
    // Update errors state by removing old errors for this field and adding new ones
    setErrors(prevErrors => {
      const otherErrors = prevErrors.filter(error => error.field !== fieldName);
      return [...otherErrors, ...fieldErrors];
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const clearFieldError = useCallback((fieldName: string) => {
    setErrors(prevErrors => prevErrors.filter(error => error.field !== fieldName));
  }, []);

  const getFieldErrorMessage = useCallback((fieldName: string): string | null => {
    return getFieldError(errors, fieldName);
  }, [errors]);

  const hasFieldErrorFlag = useCallback((fieldName: string): boolean => {
    return hasFieldError(errors, fieldName);
  }, [errors]);

  const isValid = useMemo(() => errors.length === 0, [errors]);
  const isFormValid = useMemo(() => errors.length === 0, [errors]);

  return {
    errors,
    isValid,
    isFormValid,
    validateField,
    validateForm,
    clearErrors,
    clearFieldError,
    getFieldError: getFieldErrorMessage,
    hasFieldError: hasFieldErrorFlag,
    setErrors,
  };
};

// Validation event handlers for form inputs
export const createValidationHandlers = (
  validation: UseFormValidationReturn,
  formData: any,
  setFormData: (data: any) => void,
  options: UseFormValidationOptions = {}
) => {
  const { validateOnChange = true, validateOnBlur = true } = options;

  const handleInputChange = (fieldName: string) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const value = event.target.value;
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);

    if (validateOnChange) {
      validation.validateField(fieldName, value, newFormData);
    }
  };

  const handleInputBlur = (fieldName: string) => (
    event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    if (validateOnBlur) {
      const value = event.target.value;
      validation.validateField(fieldName, value, formData);
    }
  };

  const handleSelectChange = (fieldName: string) => (value: string) => {
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);

    if (validateOnChange) {
      validation.validateField(fieldName, value, newFormData);
    }
  };

  const handleArrayChange = (fieldName: string) => (value: any[]) => {
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);

    if (validateOnChange) {
      validation.validateField(fieldName, value, newFormData);
    }
  };

  const handleDateChange = (fieldName: string) => (value: Date | null) => {
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);

    if (validateOnChange) {
      validation.validateField(fieldName, value, newFormData);
    }
  };

  const handleNumberChange = (fieldName: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = parseFloat(event.target.value) || 0;
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);

    if (validateOnChange) {
      validation.validateField(fieldName, value, newFormData);
    }
  };

  return {
    handleInputChange,
    handleInputBlur,
    handleSelectChange,
    handleArrayChange,
    handleDateChange,
    handleNumberChange,
  };
};

// Utility to get input props with validation
export const getValidatedInputProps = (
  fieldName: string,
  validation: UseFormValidationReturn,
  formData: any,
  handlers: ReturnType<typeof createValidationHandlers>
) => {
  return {
    value: formData[fieldName] || '',
    onChange: handlers.handleInputChange(fieldName),
    onBlur: handlers.handleInputBlur(fieldName),
    className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
      validation.hasFieldError(fieldName)
        ? 'border-red-500 bg-red-50'
        : 'border-gray-200'
    }`,
    'aria-invalid': validation.hasFieldError(fieldName),
    'aria-describedby': validation.hasFieldError(fieldName) ? `${fieldName}-error` : undefined,
  };
};

// Error message component props
export const getErrorMessageProps = (fieldName: string, validation: UseFormValidationReturn) => {
  const error = validation.getFieldError(fieldName);
  return {
    show: !!error,
    message: error || '',
    id: `${fieldName}-error`,
    className: 'text-red-600 text-sm mt-1',
  };
};
