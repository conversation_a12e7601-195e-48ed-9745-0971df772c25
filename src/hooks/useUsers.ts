import { useState, useEffect } from 'react';
import { apiService } from '../services/api';

export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'staff' | 'manager' | 'user';
  department?: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

interface UseUsersReturn {
  users: User[];
  loading: boolean;
  error: string | null;
  fetchUsers: () => Promise<void>;
  refreshUsers: () => Promise<void>;
}

export const useUsers = (): UseUsersReturn => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      // Remove is_active filter to show ALL users (active and inactive)
      const response = await apiService.get('/users?per_page=100');

      // Ensure we create a new array reference to trigger React re-render
      const newUsers = Array.isArray(response.data) ? [...response.data] : [];
      setUsers(newUsers);

    } catch (err: any) {
      console.error('Failed to fetch users:', err);
      setError(err.message || 'Failed to fetch users');
      setUsers([]); // Reset to empty array on error
    } finally {
      setLoading(false);
    }
  };

  const refreshUsers = async () => {

    await fetchUsers();
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return {
    users,
    loading,
    error,
    fetchUsers,
    refreshUsers,
  };
};
