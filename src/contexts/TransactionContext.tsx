import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api';
import { useAuth } from './AuthContext';

export interface Transaction {
  id: string;
  type: 'Purchase' | 'Donation' | 'Invoice';
  clientName: string;
  clientId: string;
  amount: number;
  item: string;
  date: Date;
  status: 'Completed' | 'Pending' | 'Failed';
  paymentMethod: string;
  utmSource: string;
  description?: string;
}

interface TransactionContextType {
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
  addTransaction: (transaction: Omit<Transaction, 'id' | 'date'>) => Promise<void>;
  updateTransaction: (id: string, updates: Partial<Transaction>) => Promise<void>;
  deleteTransaction: (id: string) => Promise<void>;
  getTransactionById: (id: string) => Transaction | undefined;
  refreshTransactions: () => Promise<void>;
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

// Helper function to map API transaction to frontend transaction
const mapApiTransactionToTransaction = (apiTransaction: any): Transaction => {
  // Map backend transaction types to frontend types
  const mapTransactionType = (backendType: string): 'Purchase' | 'Donation' | 'Invoice' => {
    switch (backendType) {
      case 'payment':
        return 'Invoice';
      case 'invoice':
        return 'Invoice';
      case 'donation':
        return 'Donation';
      case 'purchase':
      case 'order':
      default:
        return 'Purchase';
    }
  };

  return {
    id: apiTransaction.id.toString(),
    type: mapTransactionType(apiTransaction.type || 'purchase'),
    clientName: apiTransaction.client?.name || 'Unknown Client',
    clientId: apiTransaction.client_id?.toString() || '',
    amount: parseFloat(apiTransaction.total_amount || apiTransaction.amount) || 0,
    item: apiTransaction.product?.name || apiTransaction.notes || 'Payment Transaction',
    date: new Date(apiTransaction.created_at),
    status: apiTransaction.status === 'completed' ? 'Completed' :
            apiTransaction.status === 'pending' ? 'Pending' : 'Failed',
    paymentMethod: apiTransaction.payment_method || 'Unknown',
    utmSource: apiTransaction.utm_source || 'direct',
    description: apiTransaction.notes || apiTransaction.description || ''
  };
};

export const TransactionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load transactions from API when authenticated - with cleanup
  useEffect(() => {
    let isMounted = true;

    const loadTransactions = async () => {
      if (isMounted && isAuthenticated && !authLoading) {
        await refreshTransactions();
      }
    };

    loadTransactions();

    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, authLoading]); // Depend on authentication state

  const refreshTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getTransactions();
      const mappedTransactions = response.data.map(mapApiTransactionToTransaction);
      setTransactions(mappedTransactions);
    } catch (err: any) {
      setError(err.message || 'Failed to load transactions');
      console.error('Failed to load transactions:', err);
    } finally {
      setLoading(false);
    }
  };

  const addTransaction = async (transaction: Omit<Transaction, 'id' | 'date'>) => {
    try {
      setLoading(true);
      setError(null);
      // Note: This would need proper API mapping for creating transactions
      const newTransaction: Transaction = {
        ...transaction,
        id: `tx-${Date.now()}`,
        date: new Date()
      };
      setTransactions(prev => [newTransaction, ...prev]);
    } catch (err: any) {
      setError(err.message || 'Failed to create transaction');
      console.error('Failed to create transaction:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateTransaction = async (id: string, updates: Partial<Transaction>) => {
    try {
      setLoading(true);
      setError(null);
      // Note: This would need proper API mapping for updating transactions
      setTransactions(prev =>
        prev.map(transaction =>
          transaction.id === id ? { ...transaction, ...updates } : transaction
        )
      );
    } catch (err: any) {
      setError(err.message || 'Failed to update transaction');
      console.error('Failed to update transaction:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteTransaction = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      await apiService.deleteTransaction(id);
      setTransactions(prev => prev.filter(transaction => transaction.id !== id));
    } catch (err: any) {
      setError(err.message || 'Failed to delete transaction');
      console.error('Failed to delete transaction:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getTransactionById = (id: string) => {
    return transactions.find(transaction => transaction.id === id);
  };

  return (
    <TransactionContext.Provider value={{
      transactions,
      loading,
      error,
      addTransaction,
      updateTransaction,
      deleteTransaction,
      getTransactionById,
      refreshTransactions
    }}>
      {children}
    </TransactionContext.Provider>
  );
};

export const useTransactions = () => {
  const context = useContext(TransactionContext);
  if (!context) {
    throw new Error('useTransactions must be used within a TransactionProvider');
  }
  return context;
};