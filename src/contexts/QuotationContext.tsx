import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api';
import { useAuth } from './AuthContext';
import logger from '../utils/logger';

export interface QuotationItem {
  id?: string;
  product_id?: string;
  item_name: string;
  description?: string;
  sku?: string;
  unit: string;
  quantity: number;
  unit_price: number;
  discount_rate: number;
  discount_amount: number;
  line_total: number;
  sort_order: number;
}

export interface Quotation {
  id: string;
  quotation_number: string;
  client_id?: string;
  lead_id?: string;
  deal_id?: string;
  created_by?: string;
  assigned_to?: string;
  title: string;
  description?: string;
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  subtotal: number | string;
  tax_rate: number | string;
  tax_amount: number | string;
  discount_rate: number | string;
  discount_amount: number | string;
  total_amount: number | string;
  currency: string;
  terms_conditions?: string;
  notes?: string;
  internal_notes?: string;
  valid_until?: string;
  expected_close_date?: string;
  validity_days: number;
  sent_at?: string;
  viewed_at?: string;
  accepted_at?: string;
  rejected_at?: string;
  view_count: number;
  pdf_path?: string;
  secure_token?: string;
  is_public: boolean;
  pdf_generated_at?: string;
  converted_to_invoice_id?: string;
  converted_at?: string;
  converted_by?: string;
  custom_fields?: any;
  attachments?: any;
  template_used?: string;
  created_at: string;
  updated_at: string;
  client?: any;
  lead?: any;
  deal?: any;
  created_by_user?: any;
  assigned_to_user?: any;
  items: QuotationItem[];
}

interface QuotationContextType {
  quotations: Quotation[];
  loading: boolean;
  error: string | null;
  fetchQuotations: () => Promise<void>;
  createQuotation: (quotation: Partial<Quotation>) => Promise<Quotation>;
  updateQuotation: (id: string, quotation: Partial<Quotation>) => Promise<Quotation>;
  deleteQuotation: (id: string) => Promise<void>;
  sendQuotation: (id: string) => Promise<void>;
  acceptQuotation: (id: string) => Promise<void>;
  rejectQuotation: (id: string) => Promise<void>;
  duplicateQuotation: (id: string) => Promise<Quotation>;
  getQuotationStats: () => Promise<any>;
}

const QuotationContext = createContext<QuotationContextType | undefined>(undefined);

export const useQuotations = () => {
  const context = useContext(QuotationContext);
  if (!context) {
    throw new Error('useQuotations must be used within a QuotationProvider');
  }
  return context;
};

interface QuotationProviderProps {
  children: ReactNode;
}

// Helper function to transform API data to ensure proper types
const transformQuotationData = (data: any): Quotation => {
  return {
    ...data,
    subtotal: parseFloat(data.subtotal) || 0,
    tax_amount: parseFloat(data.tax_amount) || 0,
    discount_amount: parseFloat(data.discount_amount) || 0,
    total_amount: parseFloat(data.total_amount) || 0,
    tax_rate: parseFloat(data.tax_rate) || 0,
    validity_days: parseInt(data.validity_days) || 30,
    items: (data.items || []).map((item: any) => ({
      ...item,
      quantity: parseFloat(item.quantity) || 0,
      unit_price: parseFloat(item.unit_price) || 0,
      discount_rate: parseFloat(item.discount_rate) || 0,
      discount_amount: parseFloat(item.discount_amount) || 0,
      line_total: parseFloat(item.line_total) || 0,
      sort_order: parseInt(item.sort_order) || 1,
    }))
  };
};

export const QuotationProvider: React.FC<QuotationProviderProps> = ({ children }) => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchQuotations = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.get('/quotations');
      const transformedQuotations = (response.data || []).map(transformQuotationData);
      setQuotations(transformedQuotations);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch quotations');
      console.error('Error fetching quotations:', err);
    } finally {
      setLoading(false);
    }
  };

  const createQuotation = async (quotationData: Partial<Quotation>): Promise<Quotation> => {
    try {
      setError(null);
      const response = await apiService.post('/quotations', quotationData);
      const newQuotation = transformQuotationData(response);
      setQuotations(prev => [newQuotation, ...prev]);

      // Log the creation
      logger.logQuotationCreated(
        newQuotation.quotation_number,
        newQuotation.client?.name
      );

      return newQuotation;
    } catch (err: any) {
      setError(err.message || 'Failed to create quotation');
      throw err;
    }
  };

  const updateQuotation = async (id: string, quotationData: Partial<Quotation>): Promise<Quotation> => {
    try {
      setError(null);
      const response = await apiService.put(`/quotations/${id}`, quotationData);
      const updatedQuotation = transformQuotationData(response);
      setQuotations(prev => prev.map(q => q.id === id ? updatedQuotation : q));

      // Log the update
      logger.logQuotationUpdated(updatedQuotation.quotation_number);

      return updatedQuotation;
    } catch (err: any) {
      setError(err.message || 'Failed to update quotation');
      throw err;
    }
  };

  const deleteQuotation = async (id: string): Promise<void> => {
    try {
      setError(null);
      await apiService.delete(`/quotations/${id}`);
      setQuotations(prev => prev.filter(q => q.id !== id));
    } catch (err: any) {
      setError(err.message || 'Failed to delete quotation');
      throw err;
    }
  };

  const sendQuotation = async (id: string): Promise<void> => {
    try {
      setError(null);
      const response = await apiService.post(`/quotations/${id}/send`);
      const updatedQuotation = response.quotation;
      setQuotations(prev => prev.map(q => q.id === id ? updatedQuotation : q));
    } catch (err: any) {
      setError(err.message || 'Failed to send quotation');
      throw err;
    }
  };

  const acceptQuotation = async (id: string): Promise<void> => {
    try {
      setError(null);
      const response = await apiService.post(`/quotations/${id}/accept`);
      const updatedQuotation = response.quotation;
      setQuotations(prev => prev.map(q => q.id === id ? updatedQuotation : q));
    } catch (err: any) {
      setError(err.message || 'Failed to accept quotation');
      throw err;
    }
  };

  const rejectQuotation = async (id: string): Promise<void> => {
    try {
      setError(null);
      const response = await apiService.post(`/quotations/${id}/reject`);
      const updatedQuotation = response.quotation;
      setQuotations(prev => prev.map(q => q.id === id ? updatedQuotation : q));
    } catch (err: any) {
      setError(err.message || 'Failed to reject quotation');
      throw err;
    }
  };

  const duplicateQuotation = async (id: string): Promise<Quotation> => {
    try {
      setError(null);
      const response = await apiService.post(`/quotations/${id}/duplicate`);
      const newQuotation = transformQuotationData(response);
      setQuotations(prev => [newQuotation, ...prev]);
      return newQuotation;
    } catch (err: any) {
      setError(err.message || 'Failed to duplicate quotation');
      throw err;
    }
  };

  const getQuotationStats = async () => {
    try {
      setError(null);
      const response = await apiService.get('/quotations-statistics');
      return response;
    } catch (err: any) {
      setError(err.message || 'Failed to fetch quotation statistics');
      throw err;
    }
  };

  // Load quotations from API when authenticated - with cleanup
  useEffect(() => {
    let isMounted = true;

    const loadQuotations = async () => {
      if (isMounted && isAuthenticated && !authLoading) {
        await fetchQuotations();
      }
    };

    loadQuotations();

    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, authLoading]); // Depend on authentication state

  const value: QuotationContextType = {
    quotations,
    loading,
    error,
    fetchQuotations,
    createQuotation,
    updateQuotation,
    deleteQuotation,
    sendQuotation,
    acceptQuotation,
    rejectQuotation,
    duplicateQuotation,
    getQuotationStats,
  };

  return (
    <QuotationContext.Provider value={value}>
      {children}
    </QuotationContext.Provider>
  );
};
