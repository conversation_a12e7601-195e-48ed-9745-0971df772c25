import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api';

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'staff' | 'manager' | 'user';
  department?: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ requires2FA?: boolean; email?: string } | void>;
  completeTwoFactorLogin: (user: User, token: string) => void;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isStaff: boolean;
  isManager: boolean;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
  department?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const TOKEN_KEY = 'auth_token';
const USER_KEY = 'auth_user';

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const logout = (): void => {
    setUser(null);
    setToken(null);

    // Clear localStorage
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);

    // Clear token from API service
    apiService.setAuthToken(null);

    // Call logout endpoint if token exists
    if (token) {
      apiService.post('/auth/logout').catch(() => {
        // Ignore errors on logout
      });
    }
  };

  // Set up unauthorized handler
  useEffect(() => {
    apiService.setUnauthorizedHandler(() => {

      // Only logout if we actually have a token (avoid logout loops)
      if (token) {

        logout();
      } else {

      }
    });
  }, [token]);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedToken = localStorage.getItem(TOKEN_KEY);
        const storedUser = localStorage.getItem(USER_KEY);

        if (storedToken && storedUser) {

          // Parse stored user data
          let parsedUser;
          try {
            parsedUser = JSON.parse(storedUser);
          } catch (parseError) {
            console.error('Failed to parse stored user data:', parseError);
            // Clear invalid data
            localStorage.removeItem(TOKEN_KEY);
            localStorage.removeItem(USER_KEY);
            setLoading(false);
            return;
          }

          // Set token in API service first
          apiService.setAuthToken(storedToken);

          // Try to verify token, but don't fail immediately if it doesn't work
          try {

            const response = await apiService.get('/auth/me');

            // Update user data from server response
            setToken(storedToken);
            setUser(response.data || response);
          } catch (error) {

            // Instead of immediately clearing, try to use stored data temporarily
            // This allows the app to work even if the verification endpoint is temporarily unavailable

            setToken(storedToken);
            setUser(parsedUser);

            // Set up a delayed verification that will clear auth if it continues to fail
            setTimeout(async () => {
              try {
                await apiService.get('/auth/me');

              } catch (delayedError) {

                localStorage.removeItem(TOKEN_KEY);
                localStorage.removeItem(USER_KEY);
                apiService.setAuthToken(null);
                setToken(null);
                setUser(null);
              }
            }, 5000); // Wait 5 seconds before trying again
          }
        } else {

        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Only clear on critical errors
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
        apiService.setAuthToken(null);
        setToken(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string): Promise<{ requires2FA?: boolean; email?: string } | void> => {
    try {
      setLoading(true);

      const response = await apiService.post('/auth/login', { email, password });

      // Check if 2FA is required
      if (response.requires_2fa) {

        return { requires2FA: true, email: response.email };
      }

      // The API response has user and token at the root level, not in a data property
      const { user: userData, token: authToken } = response as any;

      setUser(userData);
      setToken(authToken);

      // Store in localStorage
      localStorage.setItem(TOKEN_KEY, authToken);
      localStorage.setItem(USER_KEY, JSON.stringify(userData));

      // Set token in API service
      apiService.setAuthToken(authToken);

    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const completeTwoFactorLogin = (userData: User, authToken: string): void => {
    setUser(userData);
    setToken(authToken);

    // Store in localStorage
    localStorage.setItem(TOKEN_KEY, authToken);
    localStorage.setItem(USER_KEY, JSON.stringify(userData));

    // Set token in API service
    apiService.setAuthToken(authToken);

  };

  const register = async (data: RegisterData): Promise<void> => {
    try {
      setLoading(true);
      await apiService.post('/auth/register', data);
    } catch (error: any) {
      // The API service throws ApiError with message directly
      throw new Error(error.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const isAuthenticated = !!user && !!token;
  const isAdmin = user?.role === 'admin';
  const isStaff = user?.role === 'staff';
  const isManager = user?.role === 'manager';

  const value: AuthContextType = {
    user,
    token,
    loading,
    login,
    completeTwoFactorLogin,
    register,
    logout,
    isAuthenticated,
    isAdmin,
    isStaff,
    isManager,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
