import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import {
  Deal,
  DealFilters,
  DealStats,
  CreateDealRequest,
  UpdateDealRequest,
  MoveStageRequest,
  PipelineStage,
  User
} from '../types/deal';
import { dealApiService } from '../services/dealApi';
import { ApiError, apiService } from '../services/api';
import { useAuth } from './AuthContext';

interface DealContextType {
  deals: Deal[];
  users: User[];
  stats: DealStats | null;
  loading: boolean;
  error: string | null;
  filters: DealFilters;
  
  // Actions
  fetchDeals: (filters?: DealFilters) => Promise<void>;
  fetchDealStats: () => Promise<void>;
  fetchUsers: () => Promise<void>;
  createDeal: (deal: CreateDealRequest) => Promise<Deal>;
  updateDeal: (id: string, updates: UpdateDealRequest) => Promise<Deal>;
  deleteDeal: (id: string) => Promise<void>;
  moveDealStage: (id: string, request: MoveStageRequest) => Promise<Deal>;
  convertLeadToDeal: (leadId: string, dealData: CreateDealRequest) => Promise<Deal>;
  setFilters: (filters: DealFilters) => void;
  getDealById: (id: string) => Deal | undefined;
  getDealsByStage: (stage: PipelineStage) => Deal[];
}

const DealContext = createContext<DealContextType | undefined>(undefined);

export const useDeal = () => {
  const context = useContext(DealContext);
  if (context === undefined) {
    throw new Error('useDeal must be used within a DealProvider');
  }
  return context;
};

// No mock data generation - use real data from API only

// Users are fetched from API only

export const DealProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [deals, setDeals] = useState<Deal[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<DealStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<DealFilters>({});

  // Track active requests to prevent race conditions
  const [activeRequestId, setActiveRequestId] = useState<string | null>(null);

  // Initialize with API data when authenticated
  useEffect(() => {
    let isMounted = true;

    const initializeData = async () => {
      if (isMounted && isAuthenticated && !authLoading) {
        await fetchDeals();
        await fetchUsers();
        await fetchDealStats();
      }
    };

    initializeData();

    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, authLoading]); // Depend on authentication state

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [
    filters.stage,
    filters.assignedTo,
    filters.clientId,
    filters.dealType,
    filters.priority,
    filters.minValue,
    filters.maxValue,
    filters.closeDateFrom?.toISOString(),
    filters.closeDateTo?.toISOString(),
    filters.search,
    filters.activeOnly,
    filters.overdueOnly
  ]);

  // Refresh deals when filters change - with debouncing
  useEffect(() => {
    if (Object.keys(memoizedFilters).length === 0) return; // Skip if no filters

    let isMounted = true;
    const timeoutId = setTimeout(async () => {
      if (isMounted) {
        await fetchDeals(memoizedFilters);
        await fetchDealStats();
      }
    }, 300); // 300ms debounce

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [memoizedFilters]); // Use memoized filters to prevent infinite loops

  const calculateStats = useCallback((dealList: Deal[]) => {
    const totalDeals = dealList.length;
    const totalValue = dealList.reduce((sum, deal) => sum + deal.value, 0);
    const wonDeals = dealList.filter(deal => deal.pipelineStage === 'won').length;
    const wonValue = dealList.filter(deal => deal.pipelineStage === 'won').reduce((sum, deal) => sum + (deal.actualRevenue || 0), 0);
    const lostDeals = dealList.filter(deal => deal.pipelineStage === 'lost').length;
    const activeDeals = dealList.filter(deal => !['won', 'lost', 'on_hold'].includes(deal.pipelineStage)).length;
    const activeValue = dealList.filter(deal => !['won', 'lost', 'on_hold'].includes(deal.pipelineStage)).reduce((sum, deal) => sum + deal.value, 0);
    const overdueDeals = dealList.filter(deal => 
      deal.expectedCloseDate && 
      deal.expectedCloseDate < new Date() && 
      !['won', 'lost'].includes(deal.pipelineStage)
    ).length;

    const stages = dealList.reduce((acc, deal) => {
      if (!acc[deal.pipelineStage]) {
        acc[deal.pipelineStage] = { count: 0, totalValue: 0 };
      }
      acc[deal.pipelineStage].count++;
      acc[deal.pipelineStage].totalValue += deal.value;
      return acc;
    }, {} as Record<string, { count: number; totalValue: number }>);

    setStats({
      totalDeals,
      totalValue,
      wonDeals,
      wonValue,
      lostDeals,
      activeDeals,
      activeValue,
      overdueDeals,
      avgDealValue: totalDeals > 0 ? totalValue / totalDeals : 0,
      conversionRate: (wonDeals + lostDeals) > 0 ? (wonDeals / (wonDeals + lostDeals)) * 100 : 0,
      stages: stages as Record<PipelineStage, { count: number; totalValue: number }>
    });
  }, []);

  const fetchDeals = useCallback(async (newFilters?: DealFilters) => {
    // Generate unique request ID to prevent race conditions
    const requestId = Date.now().toString();
    setActiveRequestId(requestId);

    setLoading(true);
    setError(null);

    try {
      const filtersToUse = newFilters || filters;
      const response = await dealApiService.getDeals(filtersToUse);

      // Check if this request is still active by comparing with current state
      setActiveRequestId(current => {
        if (current === requestId) {
          // This is still the active request, update the deals
          setDeals(response.deals);
          calculateStats(response.deals);
          setLoading(false); // Set loading to false here
          return null; // Clear the active request ID
        }
        return current; // Keep the current active request ID
      });
    } catch (err) {
      console.error('[DealContext] Error fetching deals:', err);
      // Check if this request is still active before handling error
      setActiveRequestId(current => {
        if (current === requestId) {
          if (err instanceof ApiError) {
            setError(`Failed to fetch deals: ${err.message}`);
          } else {
            setError('Failed to fetch deals. Please check your connection.');
          }
          setLoading(false); // Set loading to false here
          return null; // Clear the active request ID
        }
        return current; // Keep the current active request ID
      });
    }
  }, [filters, calculateStats]);

  const fetchDealStats = async () => {
    // Always use calculated stats from current deals to ensure consistency
    // This prevents showing stale data from the stats endpoint
    calculateStats(deals);
  };

  const fetchUsers = async () => {
    try {
      // Use the proper users API endpoint - fetch all users for assignment purposes
      const response = await apiService.get('/users?per_page=100');
      const usersData = response.data || [];

      // Transform to match Deal User interface
      const transformedUsers = usersData.map((user: any) => ({
        id: user.id.toString(),
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        department: user.department,
        isActive: user.is_active,
        lastLoginAt: user.last_login_at ? new Date(user.last_login_at) : undefined
      }));

      setUsers(transformedUsers);
    } catch (err) {
      console.error('Failed to fetch users:', err);
      // Only fallback to empty array, don't use mock data
      setUsers([]);
    }
  };

  const createDeal = async (dealData: CreateDealRequest): Promise<Deal> => {
    try {
      const newDeal = await dealApiService.createDeal(dealData);
      setDeals(prev => [newDeal, ...prev]);

      // Refresh stats
      fetchDealStats();

      return newDeal;
    } catch (err) {
      console.error('Failed to create deal:', err);
      if (err instanceof ApiError) {
        throw new Error(`Failed to create deal: ${err.message}`);
      } else {
        throw new Error('Failed to create deal. Please try again.');
      }
    }
  };

  const updateDeal = async (id: string, updates: UpdateDealRequest): Promise<Deal> => {
    try {
      // Get the original deal to preserve leadId
      const originalDeal = deals.find(deal => deal.id === id);

      const updatedDeal = await dealApiService.updateDeal(id, updates, originalDeal);
      setDeals(prev => prev.map(deal => deal.id === id ? updatedDeal : deal));

      // Refresh stats
      fetchDealStats();

      return updatedDeal;
    } catch (err) {
      console.error('Failed to update deal:', err);
      if (err instanceof ApiError) {
        throw new Error(`Failed to update deal: ${err.message}`);
      } else {
        throw new Error('Failed to update deal. Please try again.');
      }
    }
  };

  const deleteDeal = async (id: string): Promise<void> => {
    try {
      await dealApiService.deleteDeal(id);
      setDeals(prev => prev.filter(deal => deal.id !== id));

      // Refresh stats
      fetchDealStats();
    } catch (err) {
      console.error('Failed to delete deal:', err);
      if (err instanceof ApiError) {
        throw new Error(`Failed to delete deal: ${err.message}`);
      } else {
        throw new Error('Failed to delete deal. Please try again.');
      }
    }
  };

  const moveDealStage = async (id: string, request: MoveStageRequest): Promise<Deal> => {
    try {
      // Get the original deal to preserve leadId
      const originalDeal = deals.find(deal => deal.id === id);

      const updatedDeal = await dealApiService.moveDealStage(id, request, originalDeal);

      // Double-check leadId preservation (fallback)
      if (originalDeal?.leadId && !updatedDeal.leadId) {
        updatedDeal.leadId = originalDeal.leadId;
      }

      setDeals(prev => prev.map(deal => deal.id === id ? updatedDeal : deal));

      // Refresh stats
      fetchDealStats();

      return updatedDeal;
    } catch (err) {
      console.error('Failed to move deal stage:', err);
      if (err instanceof ApiError) {
        throw new Error(`Failed to move deal stage: ${err.message}`);
      } else {
        throw new Error('Failed to move deal stage. Please try again.');
      }
    }
  };

  const convertLeadToDeal = async (leadId: string, dealData: CreateDealRequest): Promise<Deal> => {
    try {
      const newDeal = await dealApiService.convertLeadToDeal(leadId, dealData);
      setDeals(prev => [newDeal, ...prev]);

      // Refresh stats
      fetchDealStats();

      return newDeal;
    } catch (err) {
      console.error('Failed to convert lead to deal:', err);
      if (err instanceof ApiError) {
        throw new Error(`Failed to convert lead to deal: ${err.message}`);
      } else {
        throw new Error('Failed to convert lead to deal. Please try again.');
      }
    }
  };

  const getDealById = (id: string) => {
    return deals.find(deal => deal.id === id);
  };

  const getDealsByStage = (stage: PipelineStage) => {
    return deals.filter(deal => deal.pipelineStage === stage);
  };

  const value: DealContextType = {
    deals,
    users,
    stats,
    loading,
    error,
    filters,
    fetchDeals,
    fetchDealStats,
    fetchUsers,
    createDeal,
    updateDeal,
    deleteDeal,
    moveDealStage,
    convertLeadToDeal,
    setFilters,
    getDealById,
    getDealsByStage
  };

  return (
    <DealContext.Provider value={value}>
      {children}
    </DealContext.Provider>
  );
};
