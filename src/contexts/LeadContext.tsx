import React, { createContext, useContext, useState, ReactNode } from 'react';
import { STORAGE_KEYS } from '../utils/storageKeys';
import { logger } from '../utils/logger';

export interface Lead {
  id: string;

  // Lead capture fields
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  address?: string;

  // UTM and campaign tracking
  utmSource?: string;
  utmCampaign?: string;
  utmMedium?: string;
  utmContent?: string;
  utmTerm?: string;
  channel?: string;

  // Lead classification
  leadType: 'capture' | 'opportunity';
  tags: string[];

  // Sales opportunity fields
  title?: string;
  description?: string;
  status: 'new' | 'contacted' | 'engaged' | 'qualified' | 'converted' | 'disqualified';
  opportunityStatus?: 'contacted' | 'info_sent' | 'negotiation' | 'waiting_payment' | 'closed_won' | 'closed_lost';
  priority: 'High' | 'Medium' | 'Low';
  engagementLevel: 'Hot' | 'Warm' | 'Cold' | 'Frozen';
  ltvSegment?: 'Silver' | 'Gold' | 'Gold+' | 'Platinum';

  // Additional fields for data consistency
  totalSpent?: number;
  transactionCount?: number;
  customFields?: Record<string, any>;

  // Sales tracking
  source?: string;
  assignedTo?: string;
  estimatedValue?: number;
  probability?: number;
  expectedCloseDate?: Date;

  // Activity and lifecycle tracking
  notes?: string;
  internalRemarks?: string;
  suggestedAction?: string;
  lastActivity?: Date;
  lastContacted?: Date;
  lifecycleStage: number; // 0-100 progress bar

  // Document uploads
  documents?: string[];

  // Recycling and conversion tracking
  isRecycled: boolean;
  recycledAt?: Date;
  convertedAt?: Date;
  convertedToClientId?: string;

  // Relationships
  clientId?: string;
  client?: any;
  convertedToClient?: any;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

interface LeadContextType {
  leads: Lead[];
  addLead: (lead: Omit<Lead, 'id' | 'createdAt'>) => void;
  updateLead: (id: string, updates: Partial<Lead>) => void;
  deleteLead: (id: string) => void;
  getLeadById: (id: string) => Lead | undefined;
  resetLeadsData: () => void;
}

const LeadContext = createContext<LeadContextType | undefined>(undefined);

// No mock data generation - use real data from API or localStorage only

// Use storage key from data manager
const LEADS_STORAGE_KEY = STORAGE_KEYS.LEADS;

// Load leads from localStorage or return empty array
const getPersistedLeads = (): Lead[] => {
  try {
    const stored = localStorage.getItem(LEADS_STORAGE_KEY);
    if (stored) {
      const parsedLeads = JSON.parse(stored);
      // Convert date strings back to Date objects
      return parsedLeads.map((lead: any) => ({
        ...lead,
        createdAt: new Date(lead.createdAt),
        updatedAt: new Date(lead.updatedAt),
        lastActivity: lead.lastActivity ? new Date(lead.lastActivity) : undefined,
        lastContacted: lead.lastContacted ? new Date(lead.lastContacted) : undefined,
        recycledAt: lead.recycledAt ? new Date(lead.recycledAt) : undefined,
        convertedAt: lead.convertedAt ? new Date(lead.convertedAt) : undefined,
        expectedCloseDate: lead.expectedCloseDate ? new Date(lead.expectedCloseDate) : undefined,
      }));
    }
  } catch (error) {
    console.warn('Failed to load leads from localStorage:', error);
  }

  // Return empty array if no stored data - don't generate dummy data automatically
  return [];
};

// Save leads to localStorage
const persistLeads = (leads: Lead[]) => {
  try {
    localStorage.setItem(LEADS_STORAGE_KEY, JSON.stringify(leads));
  } catch (error) {
    console.warn('Failed to save leads to localStorage:', error);
  }
};

export const LeadProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [leads, setLeads] = useState<Lead[]>(getPersistedLeads());

  const addLead = (lead: Omit<Lead, 'id' | 'createdAt'>) => {
    const newLead: Lead = {
      ...lead,
      id: `lead-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    const updatedLeads = [newLead, ...leads];
    setLeads(updatedLeads);
    persistLeads(updatedLeads);

    // Log the creation
    logger.log(
      'Created',
      'Lead',
      `Created new lead: ${newLead.name || 'Unnamed'} (${newLead.email || 'No email'})`,
      'Admin',
      newLead.utmSource || 'direct'
    );
  };

  const updateLead = (id: string, updates: Partial<Lead>) => {
    const originalLead = leads.find(lead => lead.id === id);
    const updatedLeads = leads.map(lead =>
      lead.id === id ? { ...lead, ...updates, updatedAt: new Date() } : lead
    );
    setLeads(updatedLeads);
    persistLeads(updatedLeads);

    // Log the update
    if (originalLead) {
      const changedFields = Object.keys(updates).filter(key =>
        updates[key as keyof Lead] !== originalLead[key as keyof Lead]
      );
      logger.log(
        'Updated',
        'Lead',
        `Updated lead: ${originalLead.name || 'Unnamed'} - Changed: ${changedFields.join(', ')}`,
        'Admin',
        originalLead.utmSource || 'direct'
      );
    }
  };

  const deleteLead = (id: string) => {
    const leadToDelete = leads.find(lead => lead.id === id);
    const updatedLeads = leads.filter(lead => lead.id !== id);
    setLeads(updatedLeads);
    persistLeads(updatedLeads);

    // Log the deletion
    if (leadToDelete) {
      logger.log(
        'Deleted',
        'Lead',
        `Deleted lead: ${leadToDelete.name || 'Unnamed'} (${leadToDelete.email || 'No email'})`,
        'Admin',
        leadToDelete.utmSource || 'direct'
      );
    }
  };

  const getLeadById = (id: string) => {
    return leads.find(lead => lead.id === id);
  };

  const resetLeadsData = () => {
    // Clear all leads data
    setLeads([]);
    persistLeads([]);
  };

  return (
    <LeadContext.Provider value={{
      leads,
      addLead,
      updateLead,
      deleteLead,
      getLeadById,
      resetLeadsData,
    }}>
      {children}
    </LeadContext.Provider>
  );
};

export const useLeads = () => {
  const context = useContext(LeadContext);
  if (!context) {
    throw new Error('useLeads must be used within a LeadProvider');
  }
  return context;
};