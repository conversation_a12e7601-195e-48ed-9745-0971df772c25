import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ToastProps } from '../components/Toast';
import ToastContainer from '../components/ToastContainer';

interface ToastContextType {
  showToast: (message: string, type?: 'success' | 'error' | 'warning' | 'info') => void;
  showToastAdvanced: (toast: Omit<ToastProps, 'id' | 'onClose'>) => void;
  showSuccess: (title: string, message: string) => void;
  showError: (title: string, message: string) => void;
  showWarning: (title: string, message: string) => void;
  showInfo: (title: string, message: string) => void;
  removeToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);
  const [recentMessages, setRecentMessages] = useState<Map<string, number>>(new Map());

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const showToastAdvanced = useCallback((toast: Omit<ToastProps, 'id' | 'onClose'>) => {
    const messageKey = `${toast.type}-${toast.title}-${toast.message}`;
    const now = Date.now();
    const DEDUPLICATION_WINDOW = 3000; // 3 seconds

    // Check for recent duplicate messages
    const lastShown = recentMessages.get(messageKey);
    if (lastShown && (now - lastShown) < DEDUPLICATION_WINDOW) {

      return;
    }

    // Update recent messages map
    setRecentMessages(prev => {
      const newMap = new Map(prev);
      newMap.set(messageKey, now);

      // Clean up old entries (older than deduplication window)
      for (const [key, timestamp] of newMap.entries()) {
        if (now - timestamp > DEDUPLICATION_WINDOW) {
          newMap.delete(key);
        }
      }

      return newMap;
    });

    // Check for currently visible duplicate messages
    setToasts(prev => {
      const isDuplicate = prev.some(existingToast =>
        existingToast.type === toast.type &&
        existingToast.title === toast.title &&
        existingToast.message === toast.message
      );

      // If duplicate found in current toasts, don't add new toast
      if (isDuplicate) {

        return prev;
      }

      const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
      const newToast: ToastProps = {
        ...toast,
        id,
        onClose: removeToast,
      };

      return [...prev, newToast];
    });
  }, [removeToast, recentMessages]);

  const showToast = useCallback((message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    const title = type.charAt(0).toUpperCase() + type.slice(1);
    showToastAdvanced({ type, title, message });
  }, [showToastAdvanced]);

  const showSuccess = useCallback((title: string, message: string) => {
    showToastAdvanced({ type: 'success', title, message });
  }, [showToastAdvanced]);

  const showError = useCallback((title: string, message: string) => {
    showToastAdvanced({ type: 'error', title, message });
  }, [showToastAdvanced]);

  const showWarning = useCallback((title: string, message: string) => {
    showToastAdvanced({ type: 'warning', title, message });
  }, [showToastAdvanced]);

  const showInfo = useCallback((title: string, message: string) => {
    showToastAdvanced({ type: 'info', title, message });
  }, [showToastAdvanced]);

  const value: ToastContextType = {
    showToast,
    showToastAdvanced,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeToast,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
