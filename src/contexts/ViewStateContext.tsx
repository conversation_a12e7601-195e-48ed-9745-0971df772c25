import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ViewStateContextType {
  clientsViewMode: 'table' | 'card';
  setClientsViewMode: (mode: 'table' | 'card') => void;
  rememberViewMode: (mode: 'table' | 'card') => void;
  getRememberedViewMode: () => 'table' | 'card';
}

const ViewStateContext = createContext<ViewStateContextType | undefined>(undefined);

const VIEW_STATE_STORAGE_KEY = 'crm_view_states';

interface ViewStateProviderProps {
  children: ReactNode;
}

export const ViewStateProvider: React.FC<ViewStateProviderProps> = ({ children }) => {
  const [clientsViewMode, setClientsViewMode] = useState<'table' | 'card'>('table');

  // Load view state from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(VIEW_STATE_STORAGE_KEY);
      if (stored) {
        const viewStates = JSON.parse(stored);
        if (viewStates.clientsViewMode) {
          setClientsViewMode(viewStates.clientsViewMode);
        }
      }
    } catch (error) {
      console.warn('Failed to load view states from localStorage:', error);
    }
  }, []);

  // Persist view state to localStorage whenever it changes
  const persistViewState = (newViewMode: 'table' | 'card') => {
    try {
      const currentStates = JSON.parse(localStorage.getItem(VIEW_STATE_STORAGE_KEY) || '{}');
      const updatedStates = {
        ...currentStates,
        clientsViewMode: newViewMode
      };
      localStorage.setItem(VIEW_STATE_STORAGE_KEY, JSON.stringify(updatedStates));
    } catch (error) {
      console.warn('Failed to persist view state to localStorage:', error);
    }
  };

  const rememberViewMode = (mode: 'table' | 'card') => {
    setClientsViewMode(mode);
    persistViewState(mode);
  };

  const getRememberedViewMode = (): 'table' | 'card' => {
    try {
      const stored = localStorage.getItem(VIEW_STATE_STORAGE_KEY);
      if (stored) {
        const viewStates = JSON.parse(stored);
        return viewStates.clientsViewMode || 'table';
      }
    } catch (error) {
      console.warn('Failed to get remembered view mode:', error);
    }
    return 'table';
  };

  return (
    <ViewStateContext.Provider value={{
      clientsViewMode,
      setClientsViewMode: rememberViewMode,
      rememberViewMode,
      getRememberedViewMode
    }}>
      {children}
    </ViewStateContext.Provider>
  );
};

export const useViewState = (): ViewStateContextType => {
  const context = useContext(ViewStateContext);
  if (!context) {
    throw new Error('useViewState must be used within a ViewStateProvider');
  }
  return context;
};
