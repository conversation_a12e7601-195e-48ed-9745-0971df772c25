import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { STORAGE_KEYS } from '../utils/storageKeys';
import { logger } from '../utils/logger';
import { apiService } from '../services/api';
import { ApiClient } from '../types/api';
import { useAuth } from './AuthContext';

export interface ClientPhoneNumber {
  id: string;
  phoneNumber: string;
  isPrimary: boolean;
  phoneVerified: boolean;
  phoneScore: number;
  phoneCarrier?: string;
}

export interface ClientEmail {
  id: string;
  emailAddress: string;
  isPrimary: boolean;
  emailVerified: boolean;
  emailScore: number;
  emailDeliverability?: string;
}

export interface Client {
  id: string;
  uuid?: string;
  name: string;
  email: string; // Keep for backward compatibility, will be primary email
  phone: string; // Keep for backward compatibility, will be primary phone
  address: string; // Keep for backward compatibility
  status: 'active' | 'inactive' | 'prospect';
  utmSource: string;
  tags: string[];
  category: 'First Timer' | 'Retainer' | 'Loyal' | 'Advocator';
  ltvSegment: 'Silver' | 'Gold' | 'Gold+' | 'Platinum';
  engagementLevel: 'Hot' | 'Warm' | 'Cold' | 'Frozen';
  priority: 'High' | 'Medium' | 'Low';
  notes: string;
  suggestedAction: string;
  createdAt: Date;
  lastActivity: Date;
  totalSpent: number;
  transactionCount: number;
  customFields: Record<string, any>;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  // New scoring fields
  nameScore?: number;
  emailScore?: number;
  phoneScore?: number;
  overallScore?: number;
  // New validation fields
  emailDeliverability?: string;
  phoneValidity?: boolean;
  phoneCarrier?: string;
  // New categorization fields
  dataQuality?: 'Poor' | 'Fair' | 'Good' | 'Excellent';
  customerCategory?: string;
  notesRemarks?: string;
  suggestedNextAction?: string;
  // Personal information fields
  icNumber?: string;
  birthday?: Date;
  gender?: 'Male' | 'Female';
  religion?: 'Muslim' | 'Non-Muslim';
  // Financial information
  income?: number;
  incomeCategory?: 'Low' | 'Medium' | 'High';
  // Enhanced address fields
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  postcode?: string;
  state?: string;
  // Behavioral data
  behaviour?: string;
  interest?: string;
  // Multi-contact relationships
  phoneNumbers?: ClientPhoneNumber[];
  emails?: ClientEmail[];
}

interface PaginationInfo {
  currentPage: number;
  lastPage: number;
  perPage: number;
  total: number;
  from: number;
  to: number;
}

interface ClientFilters {
  search?: string;
  status?: string;
  category?: string;
  ltv_segment?: string;
  engagement_level?: string;
  priority?: string;
  utm_source?: string;
  data_quality?: string;
  phone_carrier?: string;
  overall_score_min?: number;
  overall_score_max?: number;
}

interface ClientContextType {
  clients: Client[];
  allClients: Client[]; // All clients for dashboard analytics
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo | null;
  filters: ClientFilters;
  addClient: (client: Omit<Client, 'id' | 'createdAt'>) => Promise<void>;
  updateClient: (id: string, updates: Partial<Client>) => Promise<void>;
  deleteClient: (id: string) => Promise<void>;
  getClientById: (id: string) => Client | undefined;
  refreshClients: () => Promise<void>;
  loadClients: (page?: number, perPage?: number, filters?: ClientFilters) => Promise<void>;
  loadAllClients: () => Promise<void>; // New method to load all clients
  setFilters: (filters: ClientFilters) => void;
  clearClientCache: () => Promise<void>; // Clear cache and refresh data
  refreshStatistics: () => Promise<void>; // Refresh statistics across the app
}

const ClientContext = createContext<ClientContextType | undefined>(undefined);

// Transform API client data to our Client interface
const transformApiClient = (apiClient: ApiClient): Client => {
  return {
    id: apiClient.id.toString(),
    uuid: apiClient.uuid,
    name: apiClient.name,
    email: apiClient.email,
    phone: apiClient.phone || '',
    address: apiClient.address || '',
    status: apiClient.status,
    utmSource: apiClient.utm_source || '',
    tags: apiClient.tags || [],
    category: apiClient.category,
    ltvSegment: apiClient.ltv_segment,
    engagementLevel: apiClient.engagement_level,
    priority: apiClient.priority,
    notes: apiClient.notes || '',
    suggestedAction: apiClient.suggested_action || '',
    createdAt: new Date(apiClient.created_at),
    lastActivity: apiClient.last_activity ? new Date(apiClient.last_activity) : new Date(),
    totalSpent: parseFloat(apiClient.total_spent),
    transactionCount: apiClient.transaction_count,
    customFields: apiClient.custom_fields || {},
    emailVerified: apiClient.email_verified,
    phoneVerified: apiClient.phone_verified,
    // New scoring fields
    nameScore: apiClient.name_score || 0,
    emailScore: apiClient.email_score || 0,
    phoneScore: apiClient.phone_score || 0,
    overallScore: apiClient.overall_score || 0,
    // New validation fields
    emailDeliverability: apiClient.email_deliverability,
    phoneValidity: apiClient.phone_validity,
    phoneCarrier: apiClient.phone_carrier,
    // New categorization fields
    dataQuality: apiClient.data_quality || 'Poor',
    customerCategory: apiClient.customer_category,
    notesRemarks: apiClient.notes_remarks,
    suggestedNextAction: apiClient.suggested_next_action,
    // Personal information fields
    icNumber: apiClient.ic_number,
    birthday: apiClient.birthday ? new Date(apiClient.birthday) : undefined,
    gender: apiClient.gender,
    religion: apiClient.religion,
    // Financial information
    income: apiClient.income ? parseFloat(apiClient.income) : undefined,
    incomeCategory: apiClient.income_category,
    // Enhanced address fields
    addressLine1: apiClient.address_line_1,
    addressLine2: apiClient.address_line_2,
    city: apiClient.city,
    postcode: apiClient.postcode,
    state: apiClient.state,
    // Behavioral data
    behaviour: apiClient.behaviour,
    interest: apiClient.interest,
    // Multi-contact relationships
    phoneNumbers: apiClient.phone_numbers?.map(phone => ({
      id: phone.id.toString(),
      phoneNumber: phone.phone_number,
      isPrimary: phone.is_primary,
      phoneVerified: phone.phone_verified,
      phoneScore: phone.phone_score,
      phoneCarrier: phone.phone_carrier,
    })) || [],
    emails: apiClient.emails?.map(email => ({
      id: email.id.toString(),
      emailAddress: email.email_address,
      isPrimary: email.is_primary,
      emailVerified: email.email_verified,
      emailScore: email.email_score,
      emailDeliverability: email.email_deliverability,
    })) || [],
  };
};

// Transform our Client interface to API format
const transformClientToApi = (client: Omit<Client, 'id' | 'createdAt'>): any => {
  // Extract primary email and phone from multi-contact arrays if available
  const primaryEmailObj = client.emails?.find(email => email.isPrimary);
  const primaryPhoneObj = client.phoneNumbers?.find(phone => phone.isPrimary);

  const primaryEmail = primaryEmailObj?.emailAddress || client.email;
  const primaryPhone = primaryPhoneObj?.phoneNumber || client.phone;

  // Extract verification status from primary contacts or fallback to client-level fields
  const emailVerified = primaryEmailObj?.emailVerified ?? client.emailVerified ?? false;
  const phoneVerified = primaryPhoneObj?.phoneVerified ?? client.phoneVerified ?? false;

  return {
    uuid: client.uuid,
    name: client.name,
    email: primaryEmail,
    phone: primaryPhone,
    address: client.address,
    status: client.status,
    utm_source: client.utmSource,
    tags: client.tags,
    category: client.category,
    ltv_segment: client.ltvSegment,
    engagement_level: client.engagementLevel,
    priority: client.priority,
    notes: client.notes,
    suggested_action: client.suggestedAction,
    last_activity: client.lastActivity.toISOString(),
    total_spent: client.totalSpent,
    transaction_count: client.transactionCount,
    custom_fields: client.customFields,
    email_verified: emailVerified,
    phone_verified: phoneVerified,
    // New scoring fields
    name_score: client.nameScore,
    email_score: client.emailScore,
    phone_score: client.phoneScore,
    overall_score: client.overallScore,
    // New validation fields
    email_deliverability: client.emailDeliverability,
    phone_validity: client.phoneValidity,
    phone_carrier: client.phoneCarrier,
    // New categorization fields
    data_quality: client.dataQuality,
    customer_category: client.customerCategory,
    notes_remarks: client.notesRemarks,
    suggested_next_action: client.suggestedNextAction,
    // Personal information fields
    ic_number: client.icNumber,
    birthday: client.birthday ? (
      client.birthday instanceof Date
        ? client.birthday.toISOString().split('T')[0]
        : client.birthday
    ) : null,
    gender: client.gender,
    religion: client.religion,
    // Financial information
    income: client.income,
    income_category: client.incomeCategory,
    // Enhanced address fields
    address_line_1: client.addressLine1,
    address_line_2: client.addressLine2,
    city: client.city,
    postcode: client.postcode,
    state: client.state,
    // Behavioral data
    behaviour: client.behaviour,
    interest: client.interest,
    // Multi-contact relationships
    emails: client.emails?.map(email => ({
      id: email.id.startsWith('new-') ? undefined : email.id, // Don't send temp IDs for new emails
      email_address: email.emailAddress,
      is_primary: email.isPrimary,
      email_verified: email.emailVerified,
      email_score: email.emailScore,
      email_deliverability: email.emailDeliverability,
    })) || [],
    phone_numbers: client.phoneNumbers?.map(phone => ({
      id: phone.id.startsWith('new-') ? undefined : phone.id, // Don't send temp IDs for new phones
      phone_number: phone.phoneNumber,
      is_primary: phone.isPrimary,
      phone_verified: phone.phoneVerified,
      phone_score: phone.phoneScore,
      phone_carrier: phone.phoneCarrier,
    })) || [],
  };
};

// Fallback: Load clients from localStorage (for migration purposes)
const getPersistedClients = (): Client[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.CLIENTS);
    if (stored) {
      const parsedClients = JSON.parse(stored);
      return parsedClients.map((client: any) => ({
        ...client,
        status: client.status || 'active', // Default status for legacy data
        createdAt: new Date(client.createdAt),
        lastActivity: new Date(client.lastActivity),
      }));
    }
  } catch (error) {
    console.warn('Failed to load clients from localStorage:', error);
  }
  return [];
};

export const ClientProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [allClients, setAllClients] = useState<Client[]>([]); // All clients for dashboard
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [filters, setFiltersState] = useState<ClientFilters>({});
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Load clients with pagination and filters
  const loadClients = async (page = 1, perPage = 50, newFilters?: ClientFilters) => {
    // Don't attempt API calls if not authenticated
    if (!isAuthenticated) {
      const localClients = getPersistedClients();
      setClients(localClients);
      setError('Please log in to sync with server');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const filtersToUse = newFilters || filters;
      const params = {
        page,
        per_page: perPage,
        ...filtersToUse
      };

      const response = await apiService.getClients(params);

      const apiClients = response.data.map(transformApiClient);
      setClients(apiClients);

      // Set pagination info
      setPagination({
        currentPage: response.current_page,
        lastPage: response.last_page,
        perPage: response.per_page,
        total: response.total,
        from: response.from,
        to: response.to
      });

    } catch (error) {
      console.error('Failed to fetch clients from API:', error);
      setError('Failed to load clients from server');

      // Fallback to localStorage if API fails
      const localClients = getPersistedClients();
      setClients(localClients);
    } finally {
      setLoading(false);
    }
  };

  // Load dashboard analytics (efficient approach using backend calculations)
  const loadDashboardAnalytics = async () => {
    if (!isAuthenticated) {
      const localClients = getPersistedClients();
      setAllClients(localClients);
      return;
    }

    try {
      const analytics = await apiService.getClientDashboardAnalytics();

      // Create a mock client array with the correct count for compatibility
      const mockClients = Array.from({ length: analytics.totalClients }, (_, index) => ({
        id: `mock-${index}`,
        name: `Client ${index + 1}`,
        email: `client${index + 1}@example.com`,
        phone: '',
        status: 'active' as const,
        createdAt: new Date(),
        lastActivity: new Date(),
        totalSpent: 0,
        transactionCount: 0,
        overallScore: 75,
        // Add other required fields with default values
        address: '',
        company: '',
        utmSource: '',
        tags: [],
        category: 'First Timer',
        ltvSegment: 'Silver',
        engagementLevel: 'Cold',
        priority: 'Low',
        notes: '',
        customFields: {},
        emailVerified: false,
        phoneVerified: false,
        nameScore: 0,
        emailScore: 0,
        phoneScore: 0,
        emailDeliverability: '',
        phoneValidity: false,
        phoneCarrier: '',
        dataQuality: 'Fair' as const,
        customerCategory: '',
        notesRemarks: '',
        suggestedNextAction: '',
      }));

      setAllClients(mockClients);

      // Store the analytics data for direct use
      (window as any).dashboardAnalytics = analytics;

    } catch (error) {
      console.error('Failed to fetch dashboard analytics from API:', error);

      // Fallback to localStorage if API fails
      const localClients = getPersistedClients();
      setAllClients(localClients);
    }
  };

  // Keep the old function name for compatibility
  const loadAllClients = loadDashboardAnalytics;

  // Legacy method for backward compatibility
  const refreshClients = async () => {
    await loadClients(1, 50, filters);
  };

  // Refresh statistics across the app
  const refreshStatistics = async () => {
    // Trigger a custom event that components can listen to
    window.dispatchEvent(new CustomEvent('refreshStatistics'));

    // Also refresh dashboard analytics
    await loadDashboardAnalytics();
  };

  // Clear client cache and refresh data
  const clearClientCache = async () => {
    // Clear local state
    setClients([]);
    setAllClients([]);

    // Clear localStorage
    localStorage.removeItem(STORAGE_KEYS.CLIENTS);

    // Clear global window analytics data
    if ((window as any).dashboardAnalytics) {
      delete (window as any).dashboardAnalytics;
    }

    // Refresh data from API
    await Promise.all([
      loadClients(1, 50, filters),
      loadDashboardAnalytics()
    ]);

    // Trigger statistics refresh across the app
    await refreshStatistics();
  };

  // Set filters and reload clients
  const setFilters = (newFilters: ClientFilters) => {
    setFiltersState(newFilters);
    loadClients(1, pagination?.perPage || 50, newFilters);
  };

  // Load clients when authentication is resolved
  useEffect(() => {
    if (!authLoading) {
      loadClients(1, 50);
      loadAllClients(); // Also load all clients for dashboard
    }
  }, [isAuthenticated, authLoading]);

  // Generate UUID for client
  const generateClientUUID = (): string => {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    // Fallback UUID generation for older browsers
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  };

  const addClient = async (clientData: Omit<Client, 'id' | 'createdAt'>) => {
    // Ensure UUID is generated if not provided
    const clientWithUUID = {
      ...clientData,
      uuid: clientData.uuid || generateClientUUID(),
    };

    if (!isAuthenticated) {
      // Fallback to localStorage for unauthenticated users
      const newClient: Client = {
        ...clientWithUUID,
        id: `client-${Date.now()}`,
        createdAt: new Date(),
      };
      const updatedClients = [newClient, ...clients];
      setClients(updatedClients);
      localStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(updatedClients));

      logger.log(
        'Created',
        'Client',
        `Created new client locally: ${newClient.name} (${newClient.email || 'No email'}) with UUID: ${newClient.uuid}`,
        'Admin',
        newClient.utmSource || 'direct'
      );
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const apiData = transformClientToApi(clientWithUUID);

      const response = await apiService.createClient(apiData);

      const newClient = transformApiClient(response);

      const updatedClients = [newClient, ...clients];
      setClients(updatedClients);

      // Update localStorage
      localStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(updatedClients));

      // Log the creation
      logger.log(
        'Created',
        'Client',
        `Created new client: ${newClient.name} (${newClient.email || 'No email'})`,
        'Admin',
        newClient.utmSource || 'direct'
      );
    } catch (error) {
      console.error('Failed to create client:', error);
      setError('Failed to create client');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateClient = async (id: string, updates: Partial<Client>) => {
    const originalClient = clients.find(client => client.id === id);
    if (!originalClient) {
      throw new Error('Client not found');
    }

    if (!isAuthenticated) {
      // Fallback to localStorage for unauthenticated users
      const updatedClients = clients.map(client =>
        client.id === id ? { ...client, ...updates } : client
      );
      setClients(updatedClients);
      localStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(updatedClients));

      const changedFields = Object.keys(updates).filter(key =>
        updates[key as keyof Client] !== originalClient[key as keyof Client]
      );
      logger.log(
        'Updated',
        'Client',
        `Updated client locally: ${originalClient.name} - Changed: ${changedFields.join(', ')}`,
        'Admin',
        originalClient.utmSource || 'direct'
      );
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const updatedClientData = { ...originalClient, ...updates };
      const apiData = transformClientToApi(updatedClientData);

      const response = await apiService.updateClient(id, apiData);
      const updatedClient = transformApiClient(response);

      const updatedClients = clients.map(client =>
        client.id === id ? updatedClient : client
      );
      setClients(updatedClients);

      // Update localStorage
      localStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(updatedClients));

      // Log the update
      const changedFields = Object.keys(updates).filter(key =>
        updates[key as keyof Client] !== originalClient[key as keyof Client]
      );
      logger.log(
        'Updated',
        'Client',
        `Updated client: ${originalClient.name} - Changed: ${changedFields.join(', ')}`,
        'Admin',
        originalClient.utmSource || 'direct'
      );
    } catch (error) {
      console.error('Failed to update client:', error);
      setError('Failed to update client');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteClient = async (id: string) => {
    const clientToDelete = clients.find(client => client.id === id);
    if (!clientToDelete) {
      throw new Error('Client not found');
    }

    if (!isAuthenticated) {
      // Fallback to localStorage for unauthenticated users
      const updatedClients = clients.filter(client => client.id !== id);
      setClients(updatedClients);
      localStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(updatedClients));

      logger.log(
        'Deleted',
        'Client',
        `Deleted client locally: ${clientToDelete.name} (${clientToDelete.email || 'No email'})`,
        'Admin',
        clientToDelete.utmSource || 'direct'
      );
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await apiService.deleteClient(id);

      const updatedClients = clients.filter(client => client.id !== id);
      setClients(updatedClients);

      // Update localStorage
      localStorage.setItem(STORAGE_KEYS.CLIENTS, JSON.stringify(updatedClients));

      // Log the deletion
      logger.log(
        'Deleted',
        'Client',
        `Deleted client: ${clientToDelete.name} (${clientToDelete.email || 'No email'})`,
        'Admin',
        clientToDelete.utmSource || 'direct'
      );
    } catch (error) {
      console.error('Failed to delete client:', error);
      setError('Failed to delete client');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getClientById = (id: string) => {
    return clients.find(client => client.id === id);
  };

  return (
    <ClientContext.Provider value={{
      clients,
      allClients,
      loading,
      error,
      pagination,
      filters,
      addClient,
      updateClient,
      deleteClient,
      getClientById,
      refreshClients,
      loadClients,
      loadAllClients,
      setFilters,
      clearClientCache,
      refreshStatistics,
    }}>
      {children}
    </ClientContext.Provider>
  );
};

export const useClients = () => {
  const context = useContext(ClientContext);
  if (!context) {
    throw new Error('useClients must be used within a ClientProvider');
  }
  return context;
};