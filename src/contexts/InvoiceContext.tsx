import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api';
import { useAuth } from './AuthContext';
import logger from '../utils/logger';

export interface InvoiceItem {
  id?: string;
  product_id?: string;
  item_name: string;
  description?: string;
  sku?: string;
  unit: string;
  quantity: number;
  unit_price: number;
  discount_rate: number;
  discount_amount: number;
  line_total: number;
  sort_order: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  client_id?: string;
  quotation_id?: string;
  deal_id?: string;
  created_by?: string;
  assigned_to?: string;
  title: string;
  description?: string;
  status: 'draft' | 'sent' | 'viewed' | 'paid' | 'overdue' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  subtotal: number | string;
  tax_rate: number | string;
  tax_amount: number | string;
  discount_rate: number | string;
  discount_amount: number | string;
  total_amount: number | string;
  currency: string;
  terms_conditions?: string;
  notes?: string;
  internal_notes?: string;
  issue_date?: string;
  due_date?: string;
  sent_at?: string;
  viewed_at?: string;
  paid_at?: string;
  view_count: number;
  pdf_path?: string;
  secure_token?: string;
  is_public: boolean;
  pdf_generated_at?: string;
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded';
  paid_amount?: number;
  payment_method?: string;
  payment_notes?: string;
  custom_fields?: any;
  attachments?: any;
  template_used?: string;
  created_at: string;
  updated_at: string;
  client?: any;
  quotation?: any;
  deal?: any;
  created_by_user?: any;
  assigned_to_user?: any;
  items: InvoiceItem[];
}

interface InvoiceContextType {
  invoices: Invoice[];
  loading: boolean;
  error: string | null;
  fetchInvoices: () => Promise<void>;
  createInvoice: (invoice: Partial<Invoice>) => Promise<Invoice>;
  updateInvoice: (id: string, invoice: Partial<Invoice>) => Promise<Invoice>;
  deleteInvoice: (id: string) => Promise<void>;
  sendInvoice: (id: string) => Promise<void>;
  markInvoiceAsPaid: (id: string, data?: any) => Promise<void>;
  createInvoiceFromQuotation: (quotationId: string) => Promise<Invoice>;
  createInvoiceFromDeal: (dealId: string, invoiceData: Partial<Invoice>) => Promise<Invoice>;
}

const InvoiceContext = createContext<InvoiceContextType | undefined>(undefined);

export const useInvoices = () => {
  const context = useContext(InvoiceContext);
  if (!context) {
    throw new Error('useInvoices must be used within an InvoiceProvider');
  }
  return context;
};

interface InvoiceProviderProps {
  children: ReactNode;
  refreshTransactions?: () => Promise<void>;
}

// Helper function to transform API data to ensure proper types
const transformInvoiceData = (data: any): Invoice => {
  return {
    ...data,
    subtotal: parseFloat(data.subtotal) || 0,
    tax_amount: parseFloat(data.tax_amount) || 0,
    discount_amount: parseFloat(data.discount_amount) || 0,
    total_amount: parseFloat(data.total_amount) || 0,
    tax_rate: parseFloat(data.tax_rate) || 0,
    paid_amount: parseFloat(data.paid_amount) || 0,
    items: (data.items || []).map((item: any) => ({
      ...item,
      quantity: parseFloat(item.quantity) || 0,
      unit_price: parseFloat(item.unit_price) || 0,
      discount_rate: parseFloat(item.discount_rate) || 0,
      discount_amount: parseFloat(item.discount_amount) || 0,
      line_total: parseFloat(item.line_total) || 0,
      sort_order: parseInt(item.sort_order) || 1,
    }))
  };
};

export const InvoiceProvider: React.FC<InvoiceProviderProps> = ({ children, refreshTransactions }) => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getInvoices();
      const transformedInvoices = (response.data || []).map(transformInvoiceData);
      setInvoices(transformedInvoices);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch invoices');
      console.error('Error fetching invoices:', err);
    } finally {
      setLoading(false);
    }
  };

  const createInvoice = async (invoiceData: Partial<Invoice>): Promise<Invoice> => {
    try {
      setError(null);
      const response = await apiService.createInvoice(invoiceData);
      const newInvoice = transformInvoiceData(response);
      setInvoices(prev => [newInvoice, ...prev]);

      // Log the creation
      logger.logInvoiceCreated(
        newInvoice.invoice_number,
        newInvoice.client?.name
      );

      return newInvoice;
    } catch (err: any) {
      setError(err.message || 'Failed to create invoice');
      throw err;
    }
  };

  const updateInvoice = async (id: string, invoiceData: Partial<Invoice>): Promise<Invoice> => {
    try {
      setError(null);
      const response = await apiService.updateInvoice(parseInt(id), invoiceData);
      const updatedInvoice = transformInvoiceData(response);
      setInvoices(prev => prev.map(i => i.id === id ? updatedInvoice : i));

      // Log the update
      logger.logInvoiceUpdated(updatedInvoice.invoice_number);

      return updatedInvoice;
    } catch (err: any) {
      setError(err.message || 'Failed to update invoice');
      throw err;
    }
  };

  const deleteInvoice = async (id: string): Promise<void> => {
    try {
      setError(null);
      await apiService.deleteInvoice(parseInt(id));
      setInvoices(prev => prev.filter(i => i.id !== id));
    } catch (err: any) {
      setError(err.message || 'Failed to delete invoice');
      throw err;
    }
  };

  const sendInvoice = async (id: string): Promise<void> => {
    try {
      setError(null);
      const response = await apiService.sendInvoice(parseInt(id));
      const updatedInvoice = response.invoice;
      setInvoices(prev => prev.map(i => i.id === id ? updatedInvoice : i));
    } catch (err: any) {
      setError(err.message || 'Failed to send invoice');
      throw err;
    }
  };

  const markInvoiceAsPaid = async (id: string, data?: any): Promise<void> => {
    try {
      setError(null);
      const response = await apiService.markInvoiceAsPaid(parseInt(id), data);
      const updatedInvoice = response.invoice;
      setInvoices(prev => prev.map(i => i.id === id ? updatedInvoice : i));

      // Refresh transactions since a new transaction was created
      if (refreshTransactions) {
        await refreshTransactions();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to mark invoice as paid');
      throw err;
    }
  };

  const createInvoiceFromQuotation = async (quotationId: string): Promise<Invoice> => {
    try {
      setError(null);
      const response = await apiService.createInvoiceFromQuotation(parseInt(quotationId));
      const newInvoice = transformInvoiceData(response.invoice);
      setInvoices(prev => [newInvoice, ...prev]);
      return newInvoice;
    } catch (err: any) {
      setError(err.message || 'Failed to create invoice from quotation');
      throw err;
    }
  };

  const createInvoiceFromDeal = async (dealId: string, invoiceData: Partial<Invoice>): Promise<Invoice> => {
    try {
      setError(null);
      const dataWithDeal = {
        ...invoiceData,
        deal_id: dealId,
      };
      const response = await apiService.createInvoice(dataWithDeal);
      const newInvoice = transformInvoiceData(response);
      setInvoices(prev => [newInvoice, ...prev]);

      // Log the creation
      logger.logInvoiceCreated(
        newInvoice.invoice_number,
        newInvoice.client?.name || 'Unknown Client'
      );

      return newInvoice;
    } catch (err: any) {
      setError(err.message || 'Failed to create invoice from deal');
      throw err;
    }
  };

  // Load invoices from API when authenticated
  useEffect(() => {
    let isMounted = true;

    const loadInvoices = async () => {
      if (isMounted && isAuthenticated && !authLoading) {
        await fetchInvoices();
      }
    };

    loadInvoices();

    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, authLoading]);

  const value: InvoiceContextType = {
    invoices,
    loading,
    error,
    fetchInvoices,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    sendInvoice,
    markInvoiceAsPaid,
    createInvoiceFromQuotation,
    createInvoiceFromDeal,
  };

  return (
    <InvoiceContext.Provider value={value}>
      {children}
    </InvoiceContext.Provider>
  );
};
