import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api';
import { useAuth } from './AuthContext';

export interface Product {
  id: string;
  name: string;
  category: 'Books' | 'Merchandise' | 'Programs' | 'Donations';
  price: number;
  stock: number;
  sold: number;
  image: string;
  description: string;
  status: 'Active' | 'Inactive' | 'Discontinued';
  createdAt: Date;
  updatedAt: Date;
}

interface ProductContextType {
  products: Product[];
  loading: boolean;
  error: string | null;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProductById: (id: string) => Product | undefined;
  refreshProducts: (filters?: { search?: string; category?: string; status?: string }) => Promise<void>;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);



// Helper function to map API product to frontend product
const mapApiProductToProduct = (apiProduct: any): Product => {
  // Map API status to frontend status
  const mapStatus = (apiStatus: string): 'Active' | 'Inactive' | 'Discontinued' => {
    switch (apiStatus?.toLowerCase()) {
      case 'active': return 'Active';
      case 'inactive': return 'Inactive';
      case 'discontinued': return 'Discontinued';
      default: return 'Active';
    }
  };

  // Map API category to frontend category
  const mapCategory = (apiCategory: string): 'Books' | 'Merchandise' | 'Programs' | 'Donations' => {
    switch (apiCategory) {
      case 'Books': return 'Books';
      case 'Prayer Items':
      case 'Electronics':
      case 'Clothing': return 'Merchandise';
      case 'Programs': return 'Programs';
      case 'Donations': return 'Donations';
      default: return 'Books';
    }
  };

  return {
    id: apiProduct.id.toString(),
    name: apiProduct.name || 'Unknown Product',
    category: mapCategory(apiProduct.category || 'Books'),
    price: parseFloat(apiProduct.price) || 0,
    stock: parseInt(apiProduct.stock_quantity) || 0, // Fix: use stock_quantity from API
    sold: parseInt(apiProduct.sold) || 0, // This field might not exist in API, defaulting to 0
    image: apiProduct.image || 'https://via.placeholder.com/300',
    description: apiProduct.description || '',
    status: mapStatus(apiProduct.status),
    createdAt: new Date(apiProduct.created_at),
    updatedAt: new Date(apiProduct.updated_at)
  };
};

export const ProductProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load products from API when authenticated - with cleanup
  useEffect(() => {
    let isMounted = true;

    const loadProducts = async () => {
      if (isMounted && isAuthenticated && !authLoading) {
        await refreshProducts();
      }
    };

    loadProducts();

    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, authLoading]); // Depend on authentication state

  const refreshProducts = async (filters?: { search?: string; category?: string; status?: string }) => {
    try {
      setLoading(true);
      setError(null);

      // Map frontend status to API status
      const apiFilters = filters ? {
        ...filters,
        status: filters.status ? filters.status.toLowerCase() : undefined
      } : undefined;

      const response = await apiService.getProducts(apiFilters);
      const mappedProducts = response.data.map(mapApiProductToProduct);
      setProducts(mappedProducts);
    } catch (err: any) {
      setError(err.message || 'Failed to load products');
      console.error('Failed to load products:', err);

      // Set empty array if API fails
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const addProduct = async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      // Note: This would need proper API mapping for creating products
      const newProduct: Product = {
        ...product,
        id: `product-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setProducts(prev => [newProduct, ...prev]);
    } catch (err: any) {
      setError(err.message || 'Failed to create product');
      console.error('Failed to create product:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (id: string, updates: Partial<Product>) => {
    try {
      setLoading(true);
      setError(null);
      // Note: This would need proper API mapping for updating products
      setProducts(prev => prev.map(product =>
        product.id === id ? { ...product, ...updates, updatedAt: new Date() } : product
      ));
    } catch (err: any) {
      setError(err.message || 'Failed to update product');
      console.error('Failed to update product:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      await apiService.deleteProduct(id);
      setProducts(prev => prev.filter(product => product.id !== id));
    } catch (err: any) {
      setError(err.message || 'Failed to delete product');
      console.error('Failed to delete product:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getProductById = (id: string) => {
    return products.find(product => product.id === id);
  };

  return (
    <ProductContext.Provider value={{
      products,
      loading,
      error,
      addProduct,
      updateProduct,
      deleteProduct,
      getProductById,
      refreshProducts
    }}>
      {children}
    </ProductContext.Provider>
  );
};

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};