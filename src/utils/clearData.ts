/**
 * Utility functions to clear all data from localStorage and backend
 */

import { STORAGE_KEYS } from './storageKeys';

/**
 * Clear all frontend localStorage data
 */
export const clearLocalStorageData = (): void => {
  try {
    // Clear all defined storage keys
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear any legacy or additional keys that might exist
    const keysToRemove = [
      'kdt_leads',
      'kdt_clients',
      'kdt_deals',
      'kdt_logs_data',
      'app_logs',
      'auth_token',
      'auth_user'
    ];

    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
      }
    });

    // Clear any remaining application-related items
    Object.keys(localStorage).forEach(key => {
      if (key.toLowerCase().includes('kdt') ||
          key.toLowerCase().includes('tarbiah') ||
          key.toLowerCase().includes('lead') ||
          key.toLowerCase().includes('client')) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    // Silent error handling in production
  }
};

/**
 * Clear all data and reload the page
 */
export const clearAllDataAndReload = (): void => {
  clearLocalStorageData();
  
  // Reload the page to ensure all contexts are reset
  setTimeout(() => {
    window.location.reload();
  }, 500);
};

