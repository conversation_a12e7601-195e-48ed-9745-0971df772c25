import { Client } from '../contexts/ClientContext';

export interface ProfileCompletionScore {
  score: number;
  maxScore: number;
  percentage: number;
  breakdown: {
    [category: string]: {
      score: number;
      maxScore: number;
      fields: {
        [fieldName: string]: {
          score: number;
          maxScore: number;
          completed: boolean;
        };
      };
    };
  };
}

/**
 * Calculate profile completion score based on the exact field scores specified:
 * - Core fields (4 points each): Name, Email, Phone, Transactions, Total Spent, Transaction Count
 * - Business fields (4 points each): utm_source, tags, customer_category, ltv_segment, engagement_level, priority
 * - Action fields (4 points each): suggested_next_action, phone verified, email verified
 * - Personal fields (4 points each): IC Number, Birthday, Gender, Religion, Income, Income category
 * - Address fields: Address line 1 (2), Address line 2 (1), city (1), postcode (2), state (2)
 * - Behavioral fields (4 points each): behaviour, interest
 * Total possible score: 100 points
 */
export const calculateProfileCompletion = (client: Client): ProfileCompletionScore => {
  const breakdown: ProfileCompletionScore['breakdown'] = {
    core: {
      score: 0,
      maxScore: 24, // 6 fields × 4 points
      fields: {
        name: { score: 0, maxScore: 4, completed: false },
        email: { score: 0, maxScore: 4, completed: false },
        phone: { score: 0, maxScore: 4, completed: false },
        transactions: { score: 0, maxScore: 4, completed: false },
        totalSpent: { score: 0, maxScore: 4, completed: false },
        transactionCount: { score: 0, maxScore: 4, completed: false },
      }
    },
    business: {
      score: 0,
      maxScore: 24, // 6 fields × 4 points
      fields: {
        utmSource: { score: 0, maxScore: 4, completed: false },
        tags: { score: 0, maxScore: 4, completed: false },
        customerCategory: { score: 0, maxScore: 4, completed: false },
        ltvSegment: { score: 0, maxScore: 4, completed: false },
        engagementLevel: { score: 0, maxScore: 4, completed: false },
        priority: { score: 0, maxScore: 4, completed: false },
      }
    },
    actions: {
      score: 0,
      maxScore: 12, // 3 fields × 4 points
      fields: {
        suggestedNextAction: { score: 0, maxScore: 4, completed: false },
        phoneVerified: { score: 0, maxScore: 4, completed: false },
        emailVerified: { score: 0, maxScore: 4, completed: false },
      }
    },
    personal: {
      score: 0,
      maxScore: 24, // 6 fields × 4 points
      fields: {
        icNumber: { score: 0, maxScore: 4, completed: false },
        birthday: { score: 0, maxScore: 4, completed: false },
        gender: { score: 0, maxScore: 4, completed: false },
        religion: { score: 0, maxScore: 4, completed: false },
        income: { score: 0, maxScore: 4, completed: false },
        incomeCategory: { score: 0, maxScore: 4, completed: false },
      }
    },
    address: {
      score: 0,
      maxScore: 8, // Address line 1 (2) + Address line 2 (1) + city (1) + postcode (2) + state (2)
      fields: {
        addressLine1: { score: 0, maxScore: 2, completed: false },
        addressLine2: { score: 0, maxScore: 1, completed: false },
        city: { score: 0, maxScore: 1, completed: false },
        postcode: { score: 0, maxScore: 2, completed: false },
        state: { score: 0, maxScore: 2, completed: false },
      }
    },
    behavioral: {
      score: 0,
      maxScore: 8, // 2 fields × 4 points
      fields: {
        behaviour: { score: 0, maxScore: 4, completed: false },
        interest: { score: 0, maxScore: 4, completed: false },
      }
    }
  };

  // Helper function to check if a value is considered "filled"
  const isFilled = (value: any): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (typeof value === 'number') return value > 0;
    if (typeof value === 'boolean') return value;
    if (Array.isArray(value)) return value.length > 0;
    if (value instanceof Date) return true;
    return false;
  };

  // Core fields
  if (isFilled(client.name)) {
    breakdown.core.fields.name.score = 4;
    breakdown.core.fields.name.completed = true;
  }
  if (isFilled(client.email)) {
    breakdown.core.fields.email.score = 4;
    breakdown.core.fields.email.completed = true;
  }
  if (isFilled(client.phone)) {
    breakdown.core.fields.phone.score = 4;
    breakdown.core.fields.phone.completed = true;
  }
  if (isFilled(client.transactionCount) && client.transactionCount > 0) {
    breakdown.core.fields.transactions.score = 4;
    breakdown.core.fields.transactions.completed = true;
  }
  if (isFilled(client.totalSpent)) {
    breakdown.core.fields.totalSpent.score = 4;
    breakdown.core.fields.totalSpent.completed = true;
  }
  if (isFilled(client.transactionCount)) {
    breakdown.core.fields.transactionCount.score = 4;
    breakdown.core.fields.transactionCount.completed = true;
  }

  // Business fields
  if (isFilled(client.utmSource)) {
    breakdown.business.fields.utmSource.score = 4;
    breakdown.business.fields.utmSource.completed = true;
  }
  if (isFilled(client.tags)) {
    breakdown.business.fields.tags.score = 4;
    breakdown.business.fields.tags.completed = true;
  }
  if (isFilled(client.category)) {
    breakdown.business.fields.customerCategory.score = 4;
    breakdown.business.fields.customerCategory.completed = true;
  }
  if (isFilled(client.ltvSegment)) {
    breakdown.business.fields.ltvSegment.score = 4;
    breakdown.business.fields.ltvSegment.completed = true;
  }
  if (isFilled(client.engagementLevel)) {
    breakdown.business.fields.engagementLevel.score = 4;
    breakdown.business.fields.engagementLevel.completed = true;
  }
  if (isFilled(client.priority)) {
    breakdown.business.fields.priority.score = 4;
    breakdown.business.fields.priority.completed = true;
  }

  // Action fields
  if (isFilled(client.suggestedAction)) {
    breakdown.actions.fields.suggestedNextAction.score = 4;
    breakdown.actions.fields.suggestedNextAction.completed = true;
  }
  if (client.phoneVerified === true) {
    breakdown.actions.fields.phoneVerified.score = 4;
    breakdown.actions.fields.phoneVerified.completed = true;
  }
  if (client.emailVerified === true) {
    breakdown.actions.fields.emailVerified.score = 4;
    breakdown.actions.fields.emailVerified.completed = true;
  }

  // Personal fields
  if (isFilled(client.icNumber)) {
    breakdown.personal.fields.icNumber.score = 4;
    breakdown.personal.fields.icNumber.completed = true;
  }
  if (isFilled(client.birthday)) {
    breakdown.personal.fields.birthday.score = 4;
    breakdown.personal.fields.birthday.completed = true;
  }
  if (isFilled(client.gender)) {
    breakdown.personal.fields.gender.score = 4;
    breakdown.personal.fields.gender.completed = true;
  }
  if (isFilled(client.religion)) {
    breakdown.personal.fields.religion.score = 4;
    breakdown.personal.fields.religion.completed = true;
  }
  if (isFilled(client.income)) {
    breakdown.personal.fields.income.score = 4;
    breakdown.personal.fields.income.completed = true;
  }
  if (isFilled(client.incomeCategory)) {
    breakdown.personal.fields.incomeCategory.score = 4;
    breakdown.personal.fields.incomeCategory.completed = true;
  }

  // Address fields
  if (isFilled(client.addressLine1)) {
    breakdown.address.fields.addressLine1.score = 2;
    breakdown.address.fields.addressLine1.completed = true;
  }
  if (isFilled(client.addressLine2)) {
    breakdown.address.fields.addressLine2.score = 1;
    breakdown.address.fields.addressLine2.completed = true;
  }
  if (isFilled(client.city)) {
    breakdown.address.fields.city.score = 1;
    breakdown.address.fields.city.completed = true;
  }
  if (isFilled(client.postcode)) {
    breakdown.address.fields.postcode.score = 2;
    breakdown.address.fields.postcode.completed = true;
  }
  if (isFilled(client.state)) {
    breakdown.address.fields.state.score = 2;
    breakdown.address.fields.state.completed = true;
  }

  // Behavioral fields
  if (isFilled(client.behaviour)) {
    breakdown.behavioral.fields.behaviour.score = 4;
    breakdown.behavioral.fields.behaviour.completed = true;
  }
  if (isFilled(client.interest)) {
    breakdown.behavioral.fields.interest.score = 4;
    breakdown.behavioral.fields.interest.completed = true;
  }

  // Calculate category scores
  Object.keys(breakdown).forEach(category => {
    const categoryData = breakdown[category];
    categoryData.score = Object.values(categoryData.fields).reduce((sum, field) => sum + field.score, 0);
  });

  // Calculate total score
  const totalScore = Object.values(breakdown).reduce((sum, category) => sum + category.score, 0);
  const maxScore = 100; // Total possible score
  const percentage = Math.round((totalScore / maxScore) * 100);

  return {
    score: totalScore,
    maxScore,
    percentage,
    breakdown
  };
};
