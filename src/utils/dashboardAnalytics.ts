import { Client } from '../contexts/ClientContext';
import {
  getCarrierColor,
  getUTMColor,
  getEngagementColor,
  getCategoryColor,
  getLTVColor,
  getQualityColor,
  getPrimaryColor
} from './dashboardColors';

export interface DashboardAnalytics {
  totalClients: number;
  totalTransactions: number;
  totalTransactionValue: number;
  averageOverallScore: number;
  dataQualityDistribution: {
    excellent: number;
    good: number;
    fair: number;
    poor: number;
  };
  highValueClients: number;
  verificationStatus: {
    emailVerified: number;
    phoneVerified: number;
    bothVerified: number;
  };
  phoneScoreDistribution: {
    range: string;
    count: number;
    percentage: number;
  }[];
  emailScoreDistribution: {
    range: string;
    count: number;
    percentage: number;
  }[];
  phoneCarrierDistribution: {
    carrier: string;
    count: number;
    percentage: number;
    color: string;
  }[];
  utmSourceDistribution: {
    source: string;
    count: number;
    percentage: number;
    color: string;
  }[];
  engagementLevelDistribution: {
    level: string;
    count: number;
    percentage: number;
    color: string;
  }[];
  categoryDistribution: {
    category: string;
    count: number;
    percentage: number;
    color: string;
  }[];
  scoreDistribution: {
    range: string;
    count: number;
    percentage: number;
  }[];
  ltvSegmentData: {
    name: string;
    value: number;
    percentage: number;
    color: string;
  }[];
  dataQualityCorrelation: {
    dataQuality: string;
    engagementLevel: string;
    count: number;
    x: number;
    y: number;
  }[];
  acquisitionTrends: {
    month: string;
    total: number;
    facebook: number;
    google: number;
    tiktok: number;
    instagram: number;
    direct: number;
    referral: number;
    other: number;
  }[];
  topPerformingClients: Client[];
  clientsNeedingAttention: Client[];
}

export const calculateDashboardAnalytics = (clients: Client[]): DashboardAnalytics => {
  const totalClients = clients.length;

  // Calculate transaction metrics
  const totalTransactions = clients.reduce((sum, client) => sum + (client.transactionCount || 0), 0);
  const totalTransactionValue = clients.reduce((sum, client) => sum + (client.totalSpent || 0), 0);

  // Calculate average overall score
  const averageOverallScore = totalClients > 0
    ? Math.round(clients.reduce((sum, client) => sum + (client.overallScore || 0), 0) / totalClients)
    : 0;

  // Data quality distribution
  const dataQualityDistribution = {
    excellent: clients.filter(c => c.dataQuality === 'Excellent').length,
    good: clients.filter(c => c.dataQuality === 'Good').length,
    fair: clients.filter(c => c.dataQuality === 'Fair').length,
    poor: clients.filter(c => c.dataQuality === 'Poor' || !c.dataQuality).length,
  };

  // High-value clients (Gold+ and Platinum)
  const highValueClients = clients.filter(c => 
    c.ltvSegment === 'Gold+' || c.ltvSegment === 'Platinum'
  ).length;

  // Verification status
  const verificationStatus = {
    emailVerified: clients.filter(c => c.emailVerified).length,
    phoneVerified: clients.filter(c => c.phoneVerified).length,
    bothVerified: clients.filter(c => c.emailVerified && c.phoneVerified).length,
  };

  // Score distribution (histogram)
  const scoreRanges = [
    { range: '90-100', min: 90, max: 100 },
    { range: '80-89', min: 80, max: 89 },
    { range: '70-79', min: 70, max: 79 },
    { range: '60-69', min: 60, max: 69 },
    { range: '50-59', min: 50, max: 59 },
    { range: '0-49', min: 0, max: 49 },
  ];

  const scoreDistribution = scoreRanges.map(range => {
    const count = clients.filter(c => {
      const score = c.overallScore || 0;
      return score >= range.min && score <= range.max;
    }).length;
    
    return {
      range: range.range,
      count,
      percentage: totalClients > 0 ? (count / totalClients) * 100 : 0,
    };
  });

  // LTV Segment data
  const ltvSegments = ['Silver', 'Gold', 'Gold+', 'Platinum'];
  const ltvColors = ['#9ca3af', '#f59e0b', '#eab308', '#8b5cf6'];
  
  const ltvSegmentData = ltvSegments.map((segment, index) => {
    const count = clients.filter(c => c.ltvSegment === segment).length;
    return {
      name: segment,
      value: count,
      percentage: totalClients > 0 ? (count / totalClients) * 100 : 0,
      color: ltvColors[index],
    };
  });

  // Data Quality vs Engagement correlation
  const dataQualities = ['Poor', 'Fair', 'Good', 'Excellent'];
  const correlationEngagementLevels = ['Frozen', 'Cold', 'Warm', 'Hot'];

  const dataQualityCorrelation: any[] = [];
  dataQualities.forEach((quality, qIndex) => {
    correlationEngagementLevels.forEach((engagement, eIndex) => {
      const count = clients.filter(c => 
        (c.dataQuality || 'Poor') === quality && c.engagementLevel === engagement
      ).length;
      
      if (count > 0) {
        dataQualityCorrelation.push({
          dataQuality: quality,
          engagementLevel: engagement,
          count,
          x: qIndex + 1,
          y: eIndex + 1,
        });
      }
    });
  });

  // Mock acquisition trends (in real app, this would come from historical data)
  const acquisitionTrends = generateMockAcquisitionTrends(clients);

  // Top performing clients (highest overall scores)
  const topPerformingClients = [...clients]
    .sort((a, b) => (b.overallScore || 0) - (a.overallScore || 0))
    .slice(0, 10);

  // Phone Score Distribution
  const phoneScoreRanges = [
    { range: '90-100', min: 90, max: 100 },
    { range: '80-89', min: 80, max: 89 },
    { range: '70-79', min: 70, max: 79 },
    { range: '60-69', min: 60, max: 69 },
    { range: '50-59', min: 50, max: 59 },
    { range: '0-49', min: 0, max: 49 }
  ];

  const phoneScoreDistribution = phoneScoreRanges.map(range => {
    const count = clients.filter(c => {
      const score = c.phoneScore || 0;
      return score >= range.min && score <= range.max;
    }).length;

    return {
      range: range.range,
      count,
      percentage: totalClients > 0 ? Math.round((count / totalClients) * 100) : 0,
    };
  });

  // Email Score Distribution
  const emailScoreDistribution = phoneScoreRanges.map(range => {
    const count = clients.filter(c => {
      const score = c.emailScore || 0;
      return score >= range.min && score <= range.max;
    }).length;

    return {
      range: range.range,
      count,
      percentage: totalClients > 0 ? Math.round((count / totalClients) * 100) : 0,
    };
  });

  // Phone Carrier Distribution
  const carrierCounts: { [key: string]: number } = {};
  clients.forEach(client => {
    const carrier = client.phoneCarrier || 'Unknown';
    carrierCounts[carrier] = (carrierCounts[carrier] || 0) + 1;
  });

  const phoneCarrierDistribution = Object.entries(carrierCounts)
    .map(([carrier, count]) => ({
      carrier,
      count,
      percentage: totalClients > 0 ? Math.round((count / totalClients) * 100) : 0,
      color: getCarrierColor(carrier)
    }))
    .sort((a, b) => b.count - a.count);

  // UTM Source Distribution
  const utmCounts: { [key: string]: number } = {};
  clients.forEach(client => {
    const source = client.utmSource || 'Direct';
    utmCounts[source] = (utmCounts[source] || 0) + 1;
  });

  const utmSourceDistribution = Object.entries(utmCounts)
    .map(([source, count]) => ({
      source,
      count,
      percentage: totalClients > 0 ? Math.round((count / totalClients) * 100) : 0,
      color: getUTMColor(source)
    }))
    .sort((a, b) => b.count - a.count);

  // Engagement Level Distribution
  const engagementLevels = ['Hot', 'Warm', 'Cold', 'Inactive'];

  const engagementLevelDistribution = engagementLevels.map((level) => {
    const count = clients.filter(c => c.engagementLevel === level).length;
    return {
      level,
      count,
      percentage: totalClients > 0 ? Math.round((count / totalClients) * 100) : 0,
      color: getEngagementColor(level)
    };
  });

  // Category Distribution
  const categoryCounts: { [key: string]: number } = {};
  clients.forEach(client => {
    const category = client.category || 'Uncategorized';
    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
  });

  const categoryDistribution = Object.entries(categoryCounts)
    .map(([category, count]) => ({
      category,
      count,
      percentage: totalClients > 0 ? Math.round((count / totalClients) * 100) : 0,
      color: getCategoryColor(category)
    }))
    .sort((a, b) => b.count - a.count);

  // Clients needing attention (low scores, poor data quality, inactive)
  const clientsNeedingAttention = clients
    .filter(c => {
      const score = c.overallScore || 0;
      const dataQuality = c.dataQuality || 'Poor';
      const engagement = c.engagementLevel;

      return score < 60 ||
             dataQuality === 'Poor' ||
             engagement === 'Cold' ||
             engagement === 'Frozen' ||
             (!c.emailVerified && !c.phoneVerified);
    })
    .sort((a, b) => (a.overallScore || 0) - (b.overallScore || 0))
    .slice(0, 10);

  return {
    totalClients,
    totalTransactions,
    totalTransactionValue,
    averageOverallScore,
    dataQualityDistribution,
    highValueClients,
    verificationStatus,
    scoreDistribution,
    ltvSegmentData,
    dataQualityCorrelation,
    acquisitionTrends,
    topPerformingClients,
    clientsNeedingAttention,
    phoneScoreDistribution,
    emailScoreDistribution,
    phoneCarrierDistribution,
    utmSourceDistribution,
    engagementLevelDistribution,
    categoryDistribution,
  };
};

const generateMockAcquisitionTrends = (clients: Client[]) => {
  // Group clients by month and UTM source
  const monthlyData: { [key: string]: any } = {};
  
  clients.forEach(client => {
    const month = client.createdAt.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short' 
    });
    
    if (!monthlyData[month]) {
      monthlyData[month] = {
        month,
        total: 0,
        facebook: 0,
        google: 0,
        tiktok: 0,
        instagram: 0,
        direct: 0,
        referral: 0,
        other: 0,
      };
    }
    
    monthlyData[month].total++;
    
    const source = client.utmSource?.toLowerCase() || 'direct';
    if (source.includes('facebook') || source.includes('fb')) {
      monthlyData[month].facebook++;
    } else if (source.includes('google')) {
      monthlyData[month].google++;
    } else if (source.includes('tiktok')) {
      monthlyData[month].tiktok++;
    } else if (source.includes('instagram') || source.includes('ig')) {
      monthlyData[month].instagram++;
    } else if (source.includes('referral') || source.includes('ref')) {
      monthlyData[month].referral++;
    } else if (source === 'direct' || source === '') {
      monthlyData[month].direct++;
    } else {
      monthlyData[month].other++;
    }
  });
  
  return Object.values(monthlyData).slice(-6); // Last 6 months
};
