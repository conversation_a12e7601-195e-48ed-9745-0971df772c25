/**
 * Field mapping utilities to handle camelCase (frontend) to snake_case (backend) conversions
 * and ensure data consistency across the application
 */

// Lead field mappings
export const leadFieldMap = {
  // Frontend -> Backend
  leadType: 'lead_type',
  utmSource: 'utm_source',
  utmCampaign: 'utm_campaign',
  utmMedium: 'utm_medium',
  utmContent: 'utm_content',
  utmTerm: 'utm_term',
  opportunityStatus: 'opportunity_status',
  engagementLevel: 'engagement_level',
  ltvSegment: 'ltv_segment',
  assignedTo: 'assigned_to',
  estimatedValue: 'estimated_value',
  expectedCloseDate: 'expected_close_date',
  internalRemarks: 'internal_remarks',
  suggestedAction: 'suggested_action',
  lastActivity: 'last_activity',
  lastContacted: 'last_contacted',
  lifecycleStage: 'lifecycle_stage',
  isRecycled: 'is_recycled',
  recycledAt: 'recycled_at',
  convertedAt: 'converted_at',
  convertedToClientId: 'converted_to_client_id',
  clientId: 'client_id',
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  totalSpent: 'total_spent',
  transactionCount: 'transaction_count',
  customFields: 'custom_fields'
};

// Deal field mappings
export const dealFieldMap = {
  leadId: 'lead_id',
  clientId: 'client_id',
  assignedTo: 'assigned_to',
  createdBy: 'created_by',
  dealNumber: 'deal_number',
  expectedRevenue: 'expected_revenue',
  actualRevenue: 'actual_revenue',
  pipelineStage: 'pipeline_stage',
  stageOrder: 'stage_order',
  stageChangedAt: 'stage_changed_at',
  stageChangedBy: 'stage_changed_by',
  dealSize: 'deal_size',
  expectedCloseDate: 'expected_close_date',
  actualCloseDate: 'actual_close_date',
  daysInPipeline: 'days_in_pipeline',
  daysInCurrentStage: 'days_in_current_stage',
  utmSource: 'utm_source',
  utmCampaign: 'utm_campaign',
  utmMedium: 'utm_medium',
  dealType: 'deal_type',
  competitiveAdvantage: 'competitive_advantage',
  winProbabilityReason: 'win_probability_reason',
  internalNotes: 'internal_notes',
  lastActivity: 'last_activity',
  nextFollowUp: 'next_follow_up',
  nextAction: 'next_action',
  automationTriggers: 'automation_triggers',
  autoFollowUpEnabled: 'auto_follow_up_enabled',
  followUpFrequencyDays: 'follow_up_frequency_days',
  proposalDocuments: 'proposal_documents',
  contractDocuments: 'contract_documents',
  lossReason: 'loss_reason',
  lossDetails: 'loss_details',
  competitorWon: 'competitor_won',
  externalId: 'external_id',
  customFields: 'custom_fields',
  createdAt: 'created_at',
  updatedAt: 'updated_at'
};

// Client field mappings
export const clientFieldMap = {
  utmSource: 'utm_source',
  utmCampaign: 'utm_campaign',
  utmMedium: 'utm_medium',
  ltvSegment: 'ltv_segment',
  engagementLevel: 'engagement_level',
  suggestedAction: 'suggested_action',
  lastActivity: 'last_activity',
  totalSpent: 'total_spent',
  transactionCount: 'transaction_count',
  customFields: 'custom_fields',
  createdAt: 'created_at',
  updatedAt: 'updated_at'
};

/**
 * Convert frontend object to backend format
 */
export function toBackendFormat<T extends Record<string, any>>(
  data: T,
  fieldMap: Record<string, string>
): Record<string, any> {
  const result: Record<string, any> = {};
  
  Object.entries(data).forEach(([key, value]) => {
    const backendKey = fieldMap[key] || key;
    result[backendKey] = value;
  });
  
  return result;
}

/**
 * Convert backend object to frontend format
 */
export function toFrontendFormat<T extends Record<string, any>>(
  data: T,
  fieldMap: Record<string, string>
): Record<string, any> {
  const result: Record<string, any> = {};
  const reverseMap = Object.fromEntries(
    Object.entries(fieldMap).map(([frontend, backend]) => [backend, frontend])
  );
  
  Object.entries(data).forEach(([key, value]) => {
    const frontendKey = reverseMap[key] || key;
    result[frontendKey] = value;
  });
  
  return result;
}

/**
 * Convert date strings to Date objects for frontend
 */
export function convertDates(data: Record<string, any>, dateFields: string[]): Record<string, any> {
  const result = { ...data };
  
  dateFields.forEach(field => {
    if (result[field] && typeof result[field] === 'string') {
      result[field] = new Date(result[field]);
    }
  });
  
  return result;
}

/**
 * Convert Date objects to ISO strings for backend
 */
export function convertDatesToStrings(data: Record<string, any>, dateFields: string[]): Record<string, any> {
  const result = { ...data };
  
  dateFields.forEach(field => {
    if (result[field] && result[field] instanceof Date) {
      result[field] = result[field].toISOString();
    }
  });
  
  return result;
}
