interface LogEntry {
  action: string;
  target: string;
  details: string;
  actor: string;
  timestamp: string;
  utmSource?: string;
  metadata?: any;
}

class Logger {
  private static instance: Logger;
  private logs: LogEntry[] = [];

  private constructor() {
    // Load existing logs from localStorage
    this.loadLogs();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private loadLogs(): void {
    try {
      const storedLogs = localStorage.getItem('app_logs');
      if (storedLogs) {
        this.logs = JSON.parse(storedLogs);
      }
    } catch (error) {
      this.logs = [];
    }
  }

  private saveLogs(): void {
    try {
      // Keep only the last 1000 logs to prevent localStorage from growing too large
      const logsToSave = this.logs.slice(-1000);
      localStorage.setItem('app_logs', JSON.stringify(logsToSave));
    } catch (error) {
      // Silent error handling in production
    }
  }

  public log(
    action: string,
    target: string,
    details: string,
    actor: string = 'Admin',
    utmSource: string = 'Direct',
    metadata?: any
  ): void {
    const logEntry: LogEntry = {
      action,
      target,
      details,
      actor,
      timestamp: new Date().toISOString(),
      utmSource,
      metadata,
    };

    this.logs.push(logEntry);
    this.saveLogs();
  }

  public getLogs(): LogEntry[] {
    return [...this.logs].reverse(); // Return newest first
  }

  public clearLogs(): void {
    this.logs = [];
    this.saveLogs();
  }

  // Specific logging methods for different actions
  public logQuotationCreated(quotationNumber: string, clientName?: string): void {
    this.log(
      'Created',
      'Quotation',
      `Created quotation ${quotationNumber}${clientName ? ` for ${clientName}` : ''}`,
      'Admin',
      'CRM'
    );
  }

  public logQuotationUpdated(quotationNumber: string, changes?: string[]): void {
    const changeDetails = changes && changes.length > 0 
      ? ` (Updated: ${changes.join(', ')})` 
      : '';
    this.log(
      'Updated',
      'Quotation',
      `Updated quotation ${quotationNumber}${changeDetails}`,
      'Admin',
      'CRM'
    );
  }

  public logQuotationDeleted(quotationNumber: string): void {
    this.log(
      'Deleted',
      'Quotation',
      `Deleted quotation ${quotationNumber}`,
      'Admin',
      'CRM'
    );
  }

  public logQuotationDuplicated(originalNumber: string, newNumber: string): void {
    this.log(
      'Duplicated',
      'Quotation',
      `Duplicated quotation ${originalNumber} to ${newNumber}`,
      'Admin',
      'CRM'
    );
  }

  public logQuotationSent(quotationNumber: string, clientEmail?: string): void {
    this.log(
      'Sent',
      'Quotation',
      `Sent quotation ${quotationNumber}${clientEmail ? ` to ${clientEmail}` : ''}`,
      'Admin',
      'Email'
    );
  }

  public logQuotationAccepted(quotationNumber: string): void {
    this.log(
      'Accepted',
      'Quotation',
      `Quotation ${quotationNumber} was accepted`,
      'Admin',
      'CRM'
    );
  }

  public logQuotationRejected(quotationNumber: string): void {
    this.log(
      'Rejected',
      'Quotation',
      `Quotation ${quotationNumber} was rejected`,
      'Admin',
      'CRM'
    );
  }

  public logQuotationConverted(quotationNumber: string, invoiceNumber: string): void {
    this.log(
      'Converted',
      'Quotation',
      `Converted quotation ${quotationNumber} to invoice ${invoiceNumber}`,
      'Admin',
      'CRM'
    );
  }

  // Invoice logging methods
  public logInvoiceCreated(invoiceNumber: string, clientName?: string): void {
    this.log(
      'Created',
      'Invoice',
      `Created invoice ${invoiceNumber}${clientName ? ` for ${clientName}` : ''}`,
      'Admin',
      'CRM'
    );
  }

  public logInvoiceUpdated(invoiceNumber: string, changes?: string[]): void {
    const changeDetails = changes && changes.length > 0
      ? ` (Updated: ${changes.join(', ')})`
      : '';
    this.log(
      'Updated',
      'Invoice',
      `Updated invoice ${invoiceNumber}${changeDetails}`,
      'Admin',
      'CRM'
    );
  }

  public logInvoiceDeleted(invoiceNumber: string): void {
    this.log(
      'Deleted',
      'Invoice',
      `Deleted invoice ${invoiceNumber}`,
      'Admin',
      'CRM'
    );
  }

  public logInvoiceSent(invoiceNumber: string, clientEmail?: string): void {
    this.log(
      'Sent',
      'Invoice',
      `Sent invoice ${invoiceNumber}${clientEmail ? ` to ${clientEmail}` : ''}`,
      'Admin',
      'Email'
    );
  }

  public logInvoicePaid(invoiceNumber: string, amount?: number): void {
    this.log(
      'Paid',
      'Invoice',
      `Invoice ${invoiceNumber} was paid${amount ? ` (RM ${amount})` : ''}`,
      'Admin',
      'Payment'
    );
  }
}

export const logger = Logger.getInstance();
export default logger;
