/**
 * Comprehensive validation utilities for forms
 * Matches backend validation rules to prevent API errors
 */

// Backend enum values (must match <PERSON><PERSON> validation rules)
export const VALIDATION_ENUMS = {
  CLIENT_STATUS: ['active', 'inactive', 'prospect'] as const,
  CLIENT_CATEGORY: ['First Timer', 'Retainer', 'Loyal', 'Advocator'] as const,
  LTV_SEGMENT: ['Silver', 'Gold', 'Gold+', 'Platinum'] as const,
  ENGAGEMENT_LEVEL: ['Hot', 'Warm', 'Cold', 'Frozen'] as const,
  PRIORITY: ['High', 'Medium', 'Low'] as const,
  LEAD_STATUS: ['new', 'contacted', 'engaged', 'qualified', 'converted', 'disqualified'] as const,
  LEAD_TYPE: ['capture', 'opportunity'] as const,
  OPPORTUNITY_STATUS: ['contacted', 'info_sent', 'negotiation', 'waiting_payment', 'closed_won', 'closed_lost'] as const,
} as const;

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Phone validation (basic international format)
export const validatePhone = (phone: string): boolean => {
  if (!phone.trim()) return true; // Phone is optional
  const phoneRegex = /^[\+]?[1-9][\d\s\-\(\)]{7,15}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// Required field validation
export const validateRequired = (value: any, fieldName: string): ValidationError | null => {
  if (value === null || value === undefined || value === '') {
    return { field: fieldName, message: `${fieldName} is required` };
  }
  if (typeof value === 'string' && value.trim() === '') {
    return { field: fieldName, message: `${fieldName} is required` };
  }
  return null;
};

// Enum validation
export const validateEnum = <T extends readonly string[]>(
  value: string,
  allowedValues: T,
  fieldName: string
): ValidationError | null => {
  if (!allowedValues.includes(value as T[number])) {
    return {
      field: fieldName,
      message: `${fieldName} must be one of: ${allowedValues.join(', ')}`
    };
  }
  return null;
};

// String length validation
export const validateLength = (
  value: string,
  maxLength: number,
  fieldName: string,
  minLength: number = 0
): ValidationError | null => {
  if (value.length < minLength) {
    return { field: fieldName, message: `${fieldName} must be at least ${minLength} characters` };
  }
  if (value.length > maxLength) {
    return { field: fieldName, message: `${fieldName} must not exceed ${maxLength} characters` };
  }
  return null;
};

// Client validation
export const validateClient = (data: any): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields
  const nameError = validateRequired(data.name, 'Name');
  if (nameError) errors.push(nameError);

  const statusError = validateRequired(data.status, 'Status');
  if (statusError) errors.push(statusError);

  // Require at least email or phone (check both legacy fields and multi-contact arrays)
  const hasEmail = data.email || (data.emails && data.emails.length > 0 && data.emails.some((e: any) => e.emailAddress));
  const hasPhone = data.phone || (data.phoneNumbers && data.phoneNumbers.length > 0 && data.phoneNumbers.some((p: any) => p.phoneNumber));

  if (!hasEmail && !hasPhone) {
    errors.push({ field: 'contact', message: 'At least one contact method (email or phone) is required' });
  }

  // Field format validation
  if (data.email && !validateEmail(data.email)) {
    errors.push({ field: 'email', message: 'Please enter a valid email address' });
  }

  if (data.phone && !validatePhone(data.phone)) {
    errors.push({ field: 'phone', message: 'Please enter a valid phone number' });
  }

  // Validate multi-contact arrays
  if (data.emails && Array.isArray(data.emails)) {
    data.emails.forEach((email: any, index: number) => {
      if (email.emailAddress && !validateEmail(email.emailAddress)) {
        errors.push({ field: `emails.${index}.emailAddress`, message: `Email ${index + 1}: Please enter a valid email address` });
      }
    });
  }

  if (data.phoneNumbers && Array.isArray(data.phoneNumbers)) {
    data.phoneNumbers.forEach((phone: any, index: number) => {
      if (phone.phoneNumber && !validatePhone(phone.phoneNumber)) {
        errors.push({ field: `phoneNumbers.${index}.phoneNumber`, message: `Phone ${index + 1}: Please enter a valid phone number` });
      }
    });
  }

  // Length validation
  if (data.name) {
    const lengthError = validateLength(data.name, 255, 'Name', 1);
    if (lengthError) errors.push(lengthError);
  }

  if (data.phone) {
    const lengthError = validateLength(data.phone, 20, 'Phone');
    if (lengthError) errors.push(lengthError);
  }

  if (data.company) {
    const lengthError = validateLength(data.company, 255, 'Company');
    if (lengthError) errors.push(lengthError);
  }

  if (data.suggestedAction) {
    const lengthError = validateLength(data.suggestedAction, 255, 'Suggested Action');
    if (lengthError) errors.push(lengthError);
  }

  // Enum validation
  if (data.status) {
    const enumError = validateEnum(data.status, VALIDATION_ENUMS.CLIENT_STATUS, 'Status');
    if (enumError) errors.push(enumError);
  }

  if (data.category) {
    const enumError = validateEnum(data.category, VALIDATION_ENUMS.CLIENT_CATEGORY, 'Category');
    if (enumError) errors.push(enumError);
  }

  if (data.ltvSegment) {
    const enumError = validateEnum(data.ltvSegment, VALIDATION_ENUMS.LTV_SEGMENT, 'LTV Segment');
    if (enumError) errors.push(enumError);
  }

  if (data.engagementLevel) {
    const enumError = validateEnum(data.engagementLevel, VALIDATION_ENUMS.ENGAGEMENT_LEVEL, 'Engagement Level');
    if (enumError) errors.push(enumError);
  }

  if (data.priority) {
    const enumError = validateEnum(data.priority, VALIDATION_ENUMS.PRIORITY, 'Priority');
    if (enumError) errors.push(enumError);
  }

  // Numeric validation
  if (data.totalSpent !== undefined && (isNaN(data.totalSpent) || data.totalSpent < 0)) {
    errors.push({ field: 'totalSpent', message: 'Total spent must be a positive number' });
  }

  if (data.transactionCount !== undefined && (isNaN(data.transactionCount) || data.transactionCount < 0)) {
    errors.push({ field: 'transactionCount', message: 'Transaction count must be a positive number' });
  }

  // Tags validation
  if (data.tags && Array.isArray(data.tags)) {
    data.tags.forEach((tag: string, index: number) => {
      if (typeof tag !== 'string') {
        errors.push({ field: `tags[${index}]`, message: 'Tags must be text values' });
      } else if (tag.length > 50) {
        errors.push({ field: `tags[${index}]`, message: 'Each tag must not exceed 50 characters' });
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Lead validation
export const validateLead = (data: any): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields for leads
  const nameError = validateRequired(data.name, 'Name');
  if (nameError) errors.push(nameError);

  const emailError = validateRequired(data.email, 'Email');
  if (emailError) errors.push(emailError);

  const statusError = validateRequired(data.status, 'Status');
  if (statusError) errors.push(statusError);

  const leadTypeError = validateRequired(data.leadType, 'Lead Type');
  if (leadTypeError) errors.push(leadTypeError);

  // Field format validation
  if (data.email && !validateEmail(data.email)) {
    errors.push({ field: 'email', message: 'Please enter a valid email address' });
  }

  if (data.phone && !validatePhone(data.phone)) {
    errors.push({ field: 'phone', message: 'Please enter a valid phone number' });
  }

  // Length validation
  if (data.name) {
    const lengthError = validateLength(data.name, 255, 'Name', 1);
    if (lengthError) errors.push(lengthError);
  }

  // Enum validation
  if (data.status) {
    const enumError = validateEnum(data.status, VALIDATION_ENUMS.LEAD_STATUS, 'Status');
    if (enumError) errors.push(enumError);
  }

  if (data.leadType) {
    const enumError = validateEnum(data.leadType, VALIDATION_ENUMS.LEAD_TYPE, 'Lead Type');
    if (enumError) errors.push(enumError);
  }

  if (data.opportunityStatus) {
    const enumError = validateEnum(data.opportunityStatus, VALIDATION_ENUMS.OPPORTUNITY_STATUS, 'Opportunity Status');
    if (enumError) errors.push(enumError);
  }

  if (data.engagementLevel) {
    const enumError = validateEnum(data.engagementLevel, VALIDATION_ENUMS.ENGAGEMENT_LEVEL, 'Engagement Level');
    if (enumError) errors.push(enumError);
  }

  if (data.priority) {
    const enumError = validateEnum(data.priority, VALIDATION_ENUMS.PRIORITY, 'Priority');
    if (enumError) errors.push(enumError);
  }

  if (data.ltvSegment) {
    const enumError = validateEnum(data.ltvSegment, VALIDATION_ENUMS.LTV_SEGMENT, 'LTV Segment');
    if (enumError) errors.push(enumError);
  }

  // Numeric validation
  if (data.estimatedValue !== undefined && (isNaN(data.estimatedValue) || data.estimatedValue < 0)) {
    errors.push({ field: 'estimatedValue', message: 'Estimated value must be a positive number' });
  }

  if (data.probability !== undefined && (isNaN(data.probability) || data.probability < 0 || data.probability > 100)) {
    errors.push({ field: 'probability', message: 'Probability must be between 0 and 100' });
  }

  if (data.lifecycleStage !== undefined && (isNaN(data.lifecycleStage) || data.lifecycleStage < 0 || data.lifecycleStage > 100)) {
    errors.push({ field: 'lifecycleStage', message: 'Lifecycle stage must be between 0 and 100' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Utility to get error message for a specific field
export const getFieldError = (errors: ValidationError[], fieldName: string): string | null => {
  const error = errors.find(err => err.field === fieldName);
  return error ? error.message : null;
};

// Utility to check if a specific field has errors
export const hasFieldError = (errors: ValidationError[], fieldName: string): boolean => {
  return errors.some(err => err.field === fieldName);
};
