/**
 * Unified client scoring system
 * Ensures consistency across all views (card, details, metrics)
 */

import { Client } from '../contexts/ClientContext';

export interface ClientScoreBreakdown {
  overall: number;
  engagement: number;
  ltv: number;
  category: number;
  priority: number;
  activity: number;
  dataQuality: number;
}

/**
 * Get the overall score for a client, prioritizing database value over calculation
 * This ensures consistency across all views
 * @param client - The client object
 * @param overrides - Optional overrides for manual score fields (for real-time updates)
 */
export const getClientOverallScore = (
  client: Client,
  overrides?: { nameScore?: number; emailScore?: number; phoneScore?: number }
): number => {
  if (!client) return 0;

  // If overrides are provided, create a temporary client object with the overrides
  if (overrides) {
    const tempClient = {
      ...client,
      nameScore: overrides.nameScore !== undefined ? overrides.nameScore : client.nameScore,
      emailScore: overrides.emailScore !== undefined ? overrides.emailScore : client.emailScore,
      phoneScore: overrides.phoneScore !== undefined ? overrides.phoneScore : client.phoneScore,
    };
    const scores = calculateClientScore(tempClient);
    return scores.overall;
  }

  // Check if manual scores are present - if so, always calculate instead of using database value
  const hasManualScores = (client.nameScore && client.nameScore > 0) ||
                          (client.emailScore && client.emailScore > 0) ||
                          (client.phoneScore && client.phoneScore > 0);

  if (hasManualScores) {
    // Always calculate when manual scores are present to ensure they're incorporated
    const scores = calculateClientScore(client);
    return scores.overall;
  }

  // Use database overall score if available and no manual scores are present
  if (client.overallScore !== undefined && client.overallScore !== null) {
    return client.overallScore;
  }

  // Fallback to calculation
  const scores = calculateClientScore(client);
  return scores.overall;
};

/**
 * Calculate comprehensive client score with breakdown
 */
export const calculateClientScore = (client: Client): ClientScoreBreakdown => {
  if (!client) {
    return {
      overall: 0,
      engagement: 0,
      ltv: 0,
      category: 0,
      priority: 0,
      activity: 0,
      dataQuality: 0
    };
  }

  // Engagement level scoring (25 points max)
  const engagementScores = {
    'Hot': 25,
    'Warm': 20,
    'Cold': 10,
    'Frozen': 5
  };
  const engagement = engagementScores[client.engagementLevel] || 10;

  // LTV segment scoring (25 points max)
  const ltvScores = {
    'Platinum': 25,
    'Gold+': 20,
    'Gold': 15,
    'Silver': 10
  };
  const ltv = ltvScores[client.ltvSegment] || 10;

  // Category scoring (25 points max)
  const categoryScores = {
    'Advocator': 25,
    'Loyal': 20,
    'Retainer': 15,
    'First Timer': 10
  };
  const category = categoryScores[client.category] || 10;

  // Priority scoring (15 points max)
  const priorityScores = {
    'High': 15,
    'Medium': 10,
    'Low': 5
  };
  const priority = priorityScores[client.priority] || 10;

  // Activity recency scoring (10 points max)
  const daysSinceActivity = Math.floor((Date.now() - client.lastActivity.getTime()) / (1000 * 60 * 60 * 24));
  let activity = 0;
  if (daysSinceActivity <= 7) activity = 10;
  else if (daysSinceActivity <= 30) activity = 7;
  else if (daysSinceActivity <= 90) activity = 3;

  // Manual scoring fields (if provided, use these instead of calculated values)
  // These are editable fields that users can manually adjust
  const manualNameScore = client.nameScore || 0;
  const manualEmailScore = client.emailScore || 0;
  const manualPhoneScore = client.phoneScore || 0;

  // If manual scores are provided, incorporate them into the overall score
  // Otherwise use the traditional calculation
  let overall;
  if (manualNameScore > 0 || manualEmailScore > 0 || manualPhoneScore > 0) {
    // Use manual scores as part of the calculation
    // Adjust the traditional scoring to accommodate manual scores
    const manualScoreTotal = manualNameScore + manualEmailScore + manualPhoneScore; // Max 100 (10+50+40)
    const traditionalScoreWeight = 0.4; // 40% weight for traditional factors
    const manualScoreWeight = 0.6; // 60% weight for manual scores

    const traditionalScore = Math.min(engagement + ltv + category + priority + activity, 100);
    overall = Math.min(
      (traditionalScore * traditionalScoreWeight) + (manualScoreTotal * manualScoreWeight),
      100
    );
  } else {
    // Use traditional calculation when no manual scores are provided
    overall = Math.min(engagement + ltv + category + priority + activity, 100);
  }

  // Data quality scoring (bonus points, not part of main 100)
  let dataQuality = 0;
  if (client.email) dataQuality += 2;
  if (client.phone) dataQuality += 2;
  if (client.address) dataQuality += 2;
  if (client.emailVerified) dataQuality += 2;
  if (client.phoneVerified) dataQuality += 2;

  return {
    overall,
    engagement,
    ltv,
    category,
    priority,
    activity,
    dataQuality
  };
};

/**
 * Get color class based on score
 * Excellent (80+): Green, Good (60-79): Blue, Fair (40-59): Orange, Poor (<40): Red
 */
export const getScoreColor = (score: number): string => {
  if (score >= 80) return 'text-green-600';  // Excellent
  if (score >= 60) return 'text-blue-600';   // Good
  if (score >= 40) return 'text-orange-600'; // Fair
  return 'text-red-600';                     // Poor
};

/**
 * Get background color class based on score
 * Excellent (80+): Green, Good (60-79): Blue, Fair (40-59): Orange, Poor (<40): Red
 */
export const getScoreBackgroundColor = (score: number): string => {
  if (score >= 80) return 'bg-green-500';  // Excellent
  if (score >= 60) return 'bg-blue-500';   // Good
  if (score >= 40) return 'bg-orange-500'; // Fair
  return 'bg-red-500';                     // Poor
};

/**
 * Get score description
 */
export const getScoreDescription = (score: number): string => {
  if (score >= 80) return 'Excellent';
  if (score >= 60) return 'Good';
  if (score >= 40) return 'Fair';
  return 'Poor';
};

/**
 * Calculate engagement score for specific display
 */
export const calculateEngagementScore = (client: Client): number => {
  const engagementScores = {
    'Hot': 90,
    'Warm': 70,
    'Cold': 40,
    'Frozen': 15
  };
  return engagementScores[client.engagementLevel] || 40;
};

/**
 * Calculate LTV progress percentage
 */
export const calculateLTVProgress = (client: Client): number => {
  const baseProgress = {
    'Silver': 25,
    'Gold': 50,
    'Gold+': 75,
    'Platinum': 100
  };
  return baseProgress[client.ltvSegment] || 25;
};

/**
 * Calculate retention rate
 */
export const calculateRetentionRate = (client: Client): number => {
  const categoryBase = {
    'Advocator': 85,
    'Loyal': 75,
    'Retainer': 60,
    'First Timer': 30
  };
  
  const base = categoryBase[client.category] || 30;
  const transactionBonus = Math.min(client.transactionCount * 2, 15);
  return Math.min(base + transactionBonus, 100);
};

/**
 * Calculate data quality score
 */
export const calculateDataQuality = (client: Client): number => {
  let quality = 0;
  if (client.email) quality += 20;
  if (client.phone) quality += 20;
  if (client.address) quality += 15;
  if (client.utmSource) quality += 10;
  if (client.tags.length > 0) quality += 10;
  if (client.notes) quality += 10;
  if (client.emailVerified) quality += 10;
  if (client.phoneVerified) quality += 5;
  return Math.min(quality, 100);
};
