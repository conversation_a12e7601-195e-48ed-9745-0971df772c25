// Utility functions to map between API types and frontend types

import { ApiClient, ApiLead, ApiProduct, ApiTransaction } from '../types/api';
import { Client } from '../contexts/ClientContext';
import { Lead } from '../contexts/LeadContext';
import { toFrontendFormat, toBackendFormat, convertDates, convertDatesToStrings, clientFieldMap, leadFieldMap } from './fieldMappers';

// Map API Client to Frontend Client
export function mapApiClientToClient(apiClient: ApiClient): Client {
  return {
    id: apiClient.id.toString(),
    name: apiClient.name,
    email: apiClient.email,
    phone: apiClient.phone || '',
    address: apiClient.address || '',
    utmSource: apiClient.utm_source || 'direct',
    tags: apiClient.tags || [],
    category: apiClient.category,
    ltvSegment: apiClient.ltv_segment,
    engagementLevel: apiClient.engagement_level,
    priority: apiClient.priority,
    notes: apiClient.notes || '',
    suggestedAction: apiClient.suggested_action || '',
    createdAt: new Date(apiClient.created_at),
    lastActivity: apiClient.last_activity ? new Date(apiClient.last_activity) : new Date(apiClient.updated_at),
    totalSpent: parseFloat(apiClient.total_spent),
    transactionCount: apiClient.transaction_count,
    customFields: {
      company: apiClient.company,
      city: apiClient.city,
      state: apiClient.state,
      country: apiClient.country,
      postal_code: apiClient.postal_code,
      status: apiClient.status,
      ...(apiClient.custom_fields || {}),
    },
  };
}

// Map Frontend Client to API Client format for creation/updates
export function mapClientToApiClient(client: Partial<Client>): any {
  return {
    name: client.name,
    email: client.email,
    phone: client.phone || null,
    company: client.customFields?.company || null,
    address: client.address || null,
    city: client.customFields?.city || null,
    state: client.customFields?.state || null,
    country: client.customFields?.country || null,
    postal_code: client.customFields?.postal_code || null,
    status: client.customFields?.status || 'prospect',
    notes: client.notes || null,
    utm_source: client.utmSource || null,
    tags: client.tags || null,
    category: client.category || 'First Timer',
    ltv_segment: client.ltvSegment || 'Silver',
    engagement_level: client.engagementLevel || 'Cold',
    priority: client.priority || 'Medium',
    suggested_action: client.suggestedAction || null,
    last_activity: client.lastActivity ? client.lastActivity.toISOString() : null,
    total_spent: client.totalSpent || 0,
    transaction_count: client.transactionCount || 0,
    custom_fields: client.customFields || null,
  };
}

// Map API Lead to Frontend Lead
export function mapApiLeadToLead(apiLead: ApiLead): Lead {
  // Map priority
  let priority: Lead['priority'] = 'Medium';
  if (apiLead.priority === 'high' || apiLead.priority === 'urgent') priority = 'High';
  else if (apiLead.priority === 'low') priority = 'Low';

  // Map engagement level based on status
  let engagementLevel: Lead['engagementLevel'] = 'Cold';
  if (apiLead.status === 'contacted' || apiLead.status === 'qualified') engagementLevel = 'Warm';
  else if (apiLead.status === 'proposal' || apiLead.status === 'negotiation') engagementLevel = 'Hot';

  return {
    id: apiLead.id.toString(),
    name: apiLead.title,
    email: apiLead.client?.email || '',
    phone: apiLead.client?.phone || '',
    address: apiLead.client?.address || '',
    utmSource: apiLead.source || 'direct',
    tags: [],
    leadType: 'opportunity',
    ltvSegment: 'Silver', // Default for leads
    engagementLevel,
    priority,
    notes: apiLead.notes || '',
    suggestedAction: '', // Could be enhanced
    createdAt: new Date(apiLead.created_at),
    lastActivity: new Date(apiLead.updated_at),
    totalSpent: 0, // Leads haven't spent yet
    transactionCount: 0,
    customFields: {
      title: apiLead.title,
      description: apiLead.description,
      status: apiLead.status,
      estimated_value: apiLead.estimated_value,
      probability: apiLead.probability,
      expected_close_date: apiLead.expected_close_date,
      assigned_to: apiLead.assigned_to,
      client_id: apiLead.client_id,
    },
  };
}

// Map Frontend Lead to API Lead format
export function mapLeadToApiLead(lead: Partial<Lead>): any {
  // Map priority
  let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';
  if (lead.priority === 'High') priority = 'high';
  else if (lead.priority === 'Low') priority = 'low';

  return {
    client_id: lead.customFields?.client_id || null,
    title: lead.name || lead.customFields?.title,
    description: lead.customFields?.description || null,
    status: lead.customFields?.status || 'new',
    priority,
    source: lead.utmSource || null,
    assigned_to: lead.customFields?.assigned_to || null,
    estimated_value: lead.customFields?.estimated_value || null,
    probability: lead.customFields?.probability || null,
    expected_close_date: lead.customFields?.expected_close_date || null,
    notes: lead.notes || null,
  };
}

// Helper function to handle API errors
export function handleApiError(error: any): string {
  if (error.errors) {
    // Laravel validation errors
    const messages = Object.values(error.errors).flat();
    return messages.join(', ');
  }
  return error.message || 'An unexpected error occurred';
}
