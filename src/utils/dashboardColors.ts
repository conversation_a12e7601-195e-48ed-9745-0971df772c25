// Dashboard Color Scheme - Consistent colors for charts and components
// These colors are designed to work well in both light and dark modes

export const DASHBOARD_COLORS = {
  // Primary color palette for charts
  primary: [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f59e0b', // amber-500
    '#ef4444', // red-500
    '#8b5cf6', // violet-500
    '#06b6d4', // cyan-500
    '#84cc16', // lime-500
    '#f97316', // orange-500
    '#ec4899', // pink-500
    '#6366f1', // indigo-500
  ],

  // Score-based colors (consistent across all score components)
  score: {
    excellent: '#10b981', // emerald-500 (90-100)
    good: '#3b82f6',      // blue-500 (70-89)
    fair: '#f59e0b',      // amber-500 (50-69)
    poor: '#ef4444',      // red-500 (0-49)
  },

  // Status colors
  status: {
    active: '#10b981',    // emerald-500
    inactive: '#6b7280',  // gray-500
    pending: '#f59e0b',   // amber-500
    error: '#ef4444',     // red-500
    success: '#10b981',   // emerald-500
    warning: '#f59e0b',   // amber-500
    info: '#3b82f6',      // blue-500
  },

  // Data quality colors
  quality: {
    excellent: '#10b981', // emerald-500
    good: '#3b82f6',      // blue-500
    fair: '#f59e0b',      // amber-500
    poor: '#ef4444',      // red-500
  },

  // Engagement level colors
  engagement: {
    hot: '#ef4444',       // red-500
    warm: '#f59e0b',      // amber-500
    cold: '#3b82f6',      // blue-500
    inactive: '#6b7280',  // gray-500
  },

  // LTV segment colors
  ltv: {
    high: '#10b981',      // emerald-500
    medium: '#3b82f6',    // blue-500
    low: '#f59e0b',       // amber-500
    none: '#6b7280',      // gray-500
  },

  // Category colors (for client categories)
  category: {
    premium: '#8b5cf6',   // violet-500
    standard: '#3b82f6',  // blue-500
    basic: '#f59e0b',     // amber-500
    trial: '#6b7280',     // gray-500
  },

  // UTM source colors
  utm: {
    facebook: '#1877f2',  // Facebook blue
    instagram: '#e4405f', // Instagram pink
    google: '#4285f4',    // Google blue
    youtube: '#ff0000',   // YouTube red
    tiktok: '#000000',    // TikTok black
    whatsapp: '#25d366',  // WhatsApp green
    referral: '#8b5cf6',  // violet-500
    direct: '#6b7280',    // gray-500
    email: '#f59e0b',     // amber-500
    organic: '#10b981',   // emerald-500
  },

  // Carrier colors
  carrier: {
    maxis: '#e60012',     // Maxis red
    celcom: '#0066cc',    // Celcom blue
    digi: '#ffcc00',      // Digi yellow
    umobile: '#ff6600',   // U Mobile orange
    yes: '#00a651',       // Yes green
    tune: '#8b5cf6',      // Tune purple
    other: '#6b7280',     // gray-500
  },
};

// Helper functions for getting colors
export const getScoreColor = (score: number): string => {
  if (score >= 90) return DASHBOARD_COLORS.score.excellent;
  if (score >= 70) return DASHBOARD_COLORS.score.good;
  if (score >= 50) return DASHBOARD_COLORS.score.fair;
  return DASHBOARD_COLORS.score.poor;
};

export const getQualityColor = (quality: string): string => {
  const qualityLower = quality.toLowerCase();
  return DASHBOARD_COLORS.quality[qualityLower as keyof typeof DASHBOARD_COLORS.quality] || DASHBOARD_COLORS.quality.poor;
};

export const getEngagementColor = (level: string): string => {
  const levelLower = level.toLowerCase();
  return DASHBOARD_COLORS.engagement[levelLower as keyof typeof DASHBOARD_COLORS.engagement] || DASHBOARD_COLORS.engagement.inactive;
};

export const getLTVColor = (segment: string): string => {
  const segmentLower = segment.toLowerCase();
  return DASHBOARD_COLORS.ltv[segmentLower as keyof typeof DASHBOARD_COLORS.ltv] || DASHBOARD_COLORS.ltv.none;
};

export const getCategoryColor = (category: string): string => {
  const categoryLower = category.toLowerCase();
  return DASHBOARD_COLORS.category[categoryLower as keyof typeof DASHBOARD_COLORS.category] || DASHBOARD_COLORS.primary[0];
};

export const getUTMColor = (source: string): string => {
  const sourceLower = source.toLowerCase();
  return DASHBOARD_COLORS.utm[sourceLower as keyof typeof DASHBOARD_COLORS.utm] || DASHBOARD_COLORS.primary[0];
};

export const getCarrierColor = (carrier: string): string => {
  const carrierLower = carrier.toLowerCase();
  return DASHBOARD_COLORS.carrier[carrierLower as keyof typeof DASHBOARD_COLORS.carrier] || DASHBOARD_COLORS.carrier.other;
};

// Get a color from the primary palette by index
export const getPrimaryColor = (index: number): string => {
  return DASHBOARD_COLORS.primary[index % DASHBOARD_COLORS.primary.length];
};

// Generate a color palette for a given number of items
export const generateColorPalette = (count: number): string[] => {
  const colors: string[] = [];
  for (let i = 0; i < count; i++) {
    colors.push(getPrimaryColor(i));
  }
  return colors;
};

export default DASHBOARD_COLORS;
