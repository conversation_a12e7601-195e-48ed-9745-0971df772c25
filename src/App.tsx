import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { ToastProvider } from './contexts/ToastContext';
import { ConfirmationProvider } from './contexts/ConfirmationContext';
import { ViewStateProvider } from './contexts/ViewStateContext';

import { ClientProvider } from './contexts/ClientContext';
import { LeadProvider } from './contexts/LeadContext';
import { TransactionProvider, useTransactions } from './contexts/TransactionContext';
import { ProductProvider } from './contexts/ProductContext';
import { QuotationProvider } from './contexts/QuotationContext';
import { InvoiceProvider } from './contexts/InvoiceContext';
import { DealProvider } from './contexts/DealContext';
import ProtectedRoute from './components/ProtectedRoute';

// Import development utilities
import './utils/clearData';
import { apiService } from './services/api';
import Layout from './components/Layout';

// Expose apiService globally for debugging
if (typeof window !== 'undefined') {
  (window as any).apiService = apiService;

  // Add helper function to manually refresh dashboard
  (window as any).refreshDashboard = () => {
    apiService.clearCache();
    window.dispatchEvent(new CustomEvent('refreshStatistics'));
  };
}
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import PublicInvoice from './pages/PublicInvoice';
import PaymentSuccess from './pages/PaymentSuccess';
import Dashboard from './pages/Dashboard';
import Clients from './pages/Clients';
import ClientDetail from './pages/ClientDetail';
import Leads from './pages/Leads';
import Deals from './pages/Deals';
import Quotations from './pages/Quotations';
import Invoices from './pages/Invoices';
import Transactions from './pages/Transactions';
import Products from './pages/Products';
import Communications from './pages/Communications';
import Logs from './pages/Logs';
import Settings from './pages/Settings';
import Forms from './pages/Forms';

import MyProfile from './pages/MyProfile';

import './index.css';

// Wrapper component to connect InvoiceProvider with TransactionProvider
const InvoiceProviderWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { refreshTransactions } = useTransactions();

  return (
    <InvoiceProvider refreshTransactions={refreshTransactions}>
      {children}
    </InvoiceProvider>
  );
};

function App() {
  return (
    <AuthProvider>
      <NotificationProvider>
        <ToastProvider>
          <ConfirmationProvider>
            <ViewStateProvider>
              <ClientProvider>
                <LeadProvider>
                  <TransactionProvider>
                    <ProductProvider>
                      <QuotationProvider>
                        <InvoiceProviderWrapper>
                          <DealProvider>
                          <Router>
                          <Routes>
                            {/* Public routes */}
                            <Route path="/login" element={<Login />} />
                            <Route path="/register" element={<Register />} />
                            <Route path="/forgot-password" element={<ForgotPassword />} />
                            <Route path="/reset-password" element={<ResetPassword />} />
                            <Route path="/public/invoice/:invoiceId" element={<PublicInvoice />} />
                            <Route path="/public/payment/:transactionId/success" element={<PaymentSuccess />} />

                            {/* Protected routes - FIXED: Removed nested Routes structure */}
                            <Route path="/" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Dashboard />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/clients" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Clients />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/clients/:id" element={
                              <ProtectedRoute>
                                <Layout>
                                  <ClientDetail />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/leads" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Leads />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/deals" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Deals />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/quotations" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Quotations />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/invoices" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Invoices />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/transactions" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Transactions />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/products" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Products />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/engagements" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Communications />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/logs" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Logs />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/settings" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Settings />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/my-profile" element={
                              <ProtectedRoute>
                                <Layout>
                                  <MyProfile />
                                </Layout>
                              </ProtectedRoute>
                            } />
                            <Route path="/forms" element={
                              <ProtectedRoute>
                                <Layout>
                                  <Forms />
                                </Layout>
                              </ProtectedRoute>
                            } />
                          </Routes>
                        </Router>
                          </DealProvider>
                        </InvoiceProviderWrapper>
                      </QuotationProvider>
                    </ProductProvider>
                  </TransactionProvider>
                </LeadProvider>
              </ClientProvider>
            </ViewStateProvider>
          </ConfirmationProvider>
        </ToastProvider>
      </NotificationProvider>
    </AuthProvider>
  );
}

export default App;