import React from 'react';

interface ChartLoadingSkeletonProps {
  height?: number;
  type?: 'bar' | 'pie' | 'line';
  title?: string;
}

const ChartLoadingSkeleton: React.FC<ChartLoadingSkeletonProps> = ({
  height = 240,
  type = 'bar',
  title
}) => {
  return (
    <div className="w-full">
      {title && (
        <div className="mb-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mt-2 animate-pulse"></div>
        </div>
      )}
      
      <div 
        className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 flex items-center justify-center"
        style={{ height: `${height}px` }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-3"></div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Loading chart...</p>
        </div>
      </div>
    </div>
  );
};

export default ChartLoadingSkeleton;
