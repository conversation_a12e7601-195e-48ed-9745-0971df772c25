import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ShieldCheck, Shield } from 'lucide-react';

interface EmailInputWithVerificationProps {
  value: string;
  verified: boolean;
  onChange: (value: string) => void;
  onVerificationChange: (verified: boolean) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

const EmailInputWithVerification: React.FC<EmailInputWithVerificationProps> = ({
  value,
  verified,
  onChange,
  onVerificationChange,
  placeholder = "Enter email address",
  className = "",
  disabled = false,
  required = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleVerificationSelect = (isVerified: boolean) => {
    onVerificationChange(isVerified);
    setIsOpen(false);
  };

  const verificationOptions = [
    { value: false, label: 'Unverified', icon: Shield, color: 'text-gray-500' },
    { value: true, label: 'Verified', icon: ShieldCheck, color: 'text-blue-600' }
  ];

  const currentOption = verificationOptions.find(opt => opt.value === verified) || verificationOptions[0];

  return (
    <div className={`relative ${className}`}>
      <div className={`flex rounded-md transition-all ${isFocused ? 'ring-2 ring-blue-500' : ''}`}>
        {/* Email Input */}
        <input
          type="email"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`flex-1 px-3 py-2 border border-r-0 rounded-l-md text-sm focus:outline-none transition-colors h-10 leading-normal ${
            isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
          } ${
            disabled ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed' : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
          }`}
        />

        {/* Verification Selector */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => !disabled && setIsOpen(!isOpen)}
            disabled={disabled}
            className={`flex items-center space-x-2 px-3 py-2 border border-l-0 rounded-r-md text-sm focus:outline-none transition-colors min-w-[100px] h-10 leading-normal ${
              isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
            } ${
              disabled
                ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <currentOption.icon className={`w-4 h-4 ${currentOption.color}`} />
            <span className="text-gray-700 dark:text-gray-300 text-xs">{currentOption.label}</span>
            <ChevronDown className={`w-3 h-3 text-gray-400 dark:text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>

          {isOpen && (
            <div className="absolute z-[9999] w-32 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg right-0">
              <div className="py-1">
                {verificationOptions.map((option) => (
                  <button
                    key={option.label}
                    type="button"
                    onClick={() => handleVerificationSelect(option.value)}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 flex items-center space-x-2 ${
                      verified === option.value ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                    }`}
                  >
                    <option.icon className={`w-4 h-4 ${option.color}`} />
                    <span className="text-xs">{option.label}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailInputWithVerification;
