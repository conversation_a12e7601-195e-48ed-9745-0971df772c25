import React, { useState } from 'react';
import { Save } from 'lucide-react';
import Modal from './Modal';
import CustomDropdown from './CustomDropdown';
import { useToast } from '../contexts/ToastContext';

interface CampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'email' | 'whatsapp';
}

const CampaignModal: React.FC<CampaignModalProps> = ({ isOpen, onClose, type }) => {
  const { showSuccess } = useToast();
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    recipients: 'all',
    scheduleType: 'now',
    scheduleDate: '',
  });

  const emailTemplates = [
    { value: 'welcome', label: 'Welcome New Client', content: 'Welcome to Islamic Books Community! We\'re excited to have you join us.' },
    { value: 'donation-thank', label: 'Donation Thank You', content: 'Thank you for your generous donation to our Save Gaza campaign.' },
    { value: 'product-launch', label: 'New Product Launch', content: 'Exciting news! We\'ve just launched our new Islamic merchandise collection.' },
    { value: 'seminar-invite', label: 'Seminar Invitation', content: 'You\'re invited to our upcoming Islamic Youth Seminar.' },
  ];

  const whatsappTemplates = [
    { value: 'follow-up', label: 'Purchase Follow Up', content: 'Assalamualaikum! How did you like your recent purchase from Islamic Books?' },
    { value: 'reminder', label: 'Seminar Reminder', content: 'Reminder: Your Islamic seminar is tomorrow at 2 PM. See you there!' },
    { value: 'donation-update', label: 'Donation Update', content: 'Alhamdulillah! Thanks to your support, we\'ve reached 75% of our Save Gaza goal.' },
    { value: 'new-arrival', label: 'New Arrival', content: 'New Islamic books just arrived! Check out our latest collection.' },
  ];

  const templates = type === 'email' ? emailTemplates : whatsappTemplates;

  const handleTemplateChange = (templateValue: string) => {
    setSelectedTemplate(templateValue);
    const template = templates.find(t => t.value === templateValue);
    if (template) {
      setFormData({
        ...formData,
        subject: template.label,
        content: template.content,
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    showSuccess('Campaign Created', `${type === 'email' ? 'Email' : 'WhatsApp'} campaign created successfully!`);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`New ${type === 'email' ? 'Email' : 'WhatsApp'} Campaign`}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Campaign Name
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Template
          </label>
          <CustomDropdown
            options={[
              { value: '', label: 'Select a template' },
              ...templates.map(t => ({ value: t.value, label: t.label }))
            ]}
            value={selectedTemplate}
            onChange={handleTemplateChange}
            className="w-full"
          />
        </div>

        {type === 'email' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject
            </label>
            <input
              type="text"
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message Content
          </label>
          <textarea
            value={formData.content}
            onChange={(e) => setFormData({ ...formData, content: e.target.value })}
            rows={6}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Recipients
            </label>
            <CustomDropdown
              options={[
                { value: 'all', label: 'All Clients' },
                { value: 'hot', label: 'Hot Leads' },
                { value: 'warm', label: 'Warm Leads' },
                { value: 'platinum', label: 'Platinum Clients' },
                { value: 'gold', label: 'Gold Clients' },
              ]}
              value={formData.recipients}
              onChange={(value) => setFormData({ ...formData, recipients: value })}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Schedule
            </label>
            <CustomDropdown
              options={[
                { value: 'now', label: 'Send Now' },
                { value: 'schedule', label: 'Schedule for Later' },
              ]}
              value={formData.scheduleType}
              onChange={(value) => setFormData({ ...formData, scheduleType: value })}
              className="w-full"
            />
          </div>
        </div>

        {formData.scheduleType === 'schedule' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Schedule Date & Time
            </label>
            <input
              type="datetime-local"
              value={formData.scheduleDate}
              onChange={(e) => setFormData({ ...formData, scheduleDate: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
        )}

        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2">
            <button
              type="button"
              onClick={onClose}
              className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{formData.scheduleType === 'now' ? 'Send Campaign' : 'Schedule Campaign'}</span>
            </button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default CampaignModal;