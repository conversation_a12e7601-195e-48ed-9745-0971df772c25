import React, { useState, useRef, useEffect } from 'react';
import { Eye, EyeOff } from 'lucide-react';

interface FloatingLabelInputProps {
  id: string;
  name: string;
  type?: 'text' | 'email' | 'tel' | 'password';
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  showPasswordToggle?: boolean;
}

const FloatingLabelInput: React.FC<FloatingLabelInputProps> = ({
  id,
  name,
  type = 'text',
  value,
  onChange,
  label,
  required = false,
  disabled = false,
  className = '',
  showPasswordToggle = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Determine if label should be floating
  const isFloating = isFocused || value.length > 0;

  // Determine input type based on password toggle
  const inputType = showPasswordToggle && showPassword ? 'text' : type;

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleLabelClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Input Field */}
      <input
        ref={inputRef}
        id={id}
        name={name}
        type={inputType}
        value={value}
        onChange={onChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        required={required}
        className={`
          w-full px-3 pt-6 pb-2 border rounded-lg transition-all duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
          ${disabled ? 'bg-gray-50 dark:bg-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-700'}
          ${isFocused ? 'border-blue-500' : 'border-gray-300 dark:border-gray-600'}
          text-gray-900 dark:text-white
          ${showPasswordToggle ? 'pr-10' : 'pr-3'}
        `}
        placeholder=""
      />

      {/* Floating Label */}
      <label
        htmlFor={id}
        onClick={handleLabelClick}
        className={`
          absolute left-3 transition-all duration-200 ease-in-out cursor-text
          ${isFloating 
            ? 'top-1 text-xs text-blue-600 dark:text-blue-400' 
            : 'top-1/2 -translate-y-1/2 text-sm text-gray-500 dark:text-gray-400'
          }
          ${disabled ? 'cursor-not-allowed' : ''}
          pointer-events-none
        `}
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {/* Password Toggle Button */}
      {showPasswordToggle && (
        <button
          type="button"
          onClick={togglePasswordVisibility}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
          tabIndex={-1}
        >
          {showPassword ? (
            <EyeOff className="w-5 h-5" />
          ) : (
            <Eye className="w-5 h-5" />
          )}
        </button>
      )}
    </div>
  );
};

export default FloatingLabelInput;
