import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home, Users, CreditCard, Package, MessageSquare,
  FileText, Settings, Bell, Menu, X, FormInput,
  BookOpen, User, ChevronDown, Zap, Target, Briefcase, Receipt, LogOut, FileCheck, Upload,
  UserCircle, Cog
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../contexts/NotificationContext';
import { useToast } from '../contexts/ToastContext';
import NotificationDropdown from './NotificationDropdown';
import NotificationsSidebar from './NotificationsSidebar';
import QuickActionDropdown from './QuickActionDropdown';

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  // Initialize sidebar state based on screen size
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // Check if we're on the client side and screen width
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 768; // Close on mobile (< 768px), open on desktop
    }
    return true; // Default to open for SSR
  });
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [notificationOpen, setNotificationOpen] = useState(false);
  const [notificationsSidebarOpen, setNotificationsSidebarOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);

  const [quickActionOpen, setQuickActionOpen] = useState(false);
  const { unreadCount } = useNotifications();
  const { user, logout } = useAuth();
  const { showToast } = useToast();

  // Handle window resize to adjust sidebar state for mobile/desktop
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      // Only auto-close on mobile, don't auto-open on desktop if user manually closed it
      if (isMobile && sidebarOpen) {
        setSidebarOpen(false);
      }
    };

    // Set initial state on mount
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []); // Empty dependency array to run only on mount

  const handleLogout = () => {
    logout();
    showToast('Logged out successfully', 'success');
  };

  const navigation = [
    { name: 'Dashboard', href: '/', icon: Home },
    { name: 'Leads', href: '/leads', icon: Target },
    { name: 'Deals', href: '/deals', icon: Briefcase },
    { name: 'Quotations', href: '/quotations', icon: Receipt },
    { name: 'Invoices', href: '/invoices', icon: FileCheck },
    { name: 'Clients', href: '/clients', icon: Users },
    { name: 'Transactions', href: '/transactions', icon: CreditCard },
    { name: 'Products', href: '/products', icon: Package },
    { name: 'Forms', href: '/forms', icon: FormInput },
    { name: 'Engagements', href: '/engagements', icon: MessageSquare },
    { name: 'Logs', href: '/logs', icon: FileText },
  ];

  return (
    <div className="min-h-screen transition-colors duration-300 bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 shadow-lg transform transition-all duration-300 ease-in-out bg-white dark:bg-gray-800 ${
        sidebarCollapsed ? 'w-16' : 'w-80'
      } ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      }`}>
        <div className={`flex items-center h-16 border-b border-gray-200 dark:border-gray-700 ${sidebarCollapsed ? 'justify-center px-4' : 'px-6'}`}>
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-600 dark:bg-blue-500 rounded-md">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
              sidebarCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
            }`}>
              <div className="whitespace-nowrap">
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">Tarbiah Sentap</h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">CRM & E-commerce</p>
              </div>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md ml-auto text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <nav className={`mt-8 space-y-1 ${sidebarCollapsed ? 'px-2' : 'px-4'}`}>
          {navigation.map((item, index) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.href;
            const showComingSoonBadge = (index >= 2 && index <= 4) || (index >= 6 && index <= 10); // Items: Deals, Quotations, Invoices, Transactions, Products, Forms, Engagements, Logs (excluding Clients)
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center rounded-md text-sm font-medium transition-colors relative ${
                  sidebarCollapsed ? 'justify-center p-3' : 'space-x-3 px-3 py-2'
                } ${
                  isActive
                    ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-r-2 border-blue-600 dark:border-blue-400'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <Icon className="w-5 h-5" />
                {!sidebarCollapsed && (
                  <div className="flex items-center justify-between w-full">
                    <span>{item.name}</span>
                    {showComingSoonBadge && (
                      <span className="px-2 py-1 text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded animate-pulse">
                        Coming Soon
                      </span>
                    )}
                  </div>
                )}
                {sidebarCollapsed && showComingSoonBadge && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded animate-pulse"></span>
                )}
              </Link>
            );
          })}
        </nav>

      </div>

      {/* Main content */}
      <div className={`transition-all duration-300 ${sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-80'} ml-0`}>
        {/* Top navigation */}
        <header className="shadow-sm border-b transition-colors duration-300 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4">
            <div className="flex items-center space-x-4">
              {/* Sidebar toggle - always visible */}
              <button
                onClick={() => {
                  if (window.innerWidth >= 1024) {
                    setSidebarCollapsed(!sidebarCollapsed);
                  } else {
                    setSidebarOpen(true);
                  }
                }}
                className="p-2 rounded-md transition-colors text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                aria-label="Toggle sidebar"
              >
                <Menu className="w-5 h-5" />
              </button>
            </div>

            <div className="flex items-center space-x-2">
              {/* Quick Actions - Hidden on small screens */}
              <div className="relative hidden sm:block">
                <button
                  onClick={() => setQuickActionOpen(!quickActionOpen)}
                  className="p-2 rounded-md relative transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center text-gray-400 hover:text-gray-600"
                >
                  <Zap className="w-5 h-5" />
                </button>

                {quickActionOpen && (
                  <QuickActionDropdown onClose={() => setQuickActionOpen(false)} />
                )}
              </div>

              <div className="relative">
                <button
                  onClick={() => setNotificationOpen(!notificationOpen)}
                  className="p-2 rounded-md relative transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center text-gray-400 hover:text-gray-600"
                >
                  <Bell className="w-5 h-5" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {unreadCount}
                    </span>
                  )}
                </button>

                {notificationOpen && (
                  <NotificationDropdown
                    onClose={() => setNotificationOpen(false)}
                    onViewAll={() => setNotificationsSidebarOpen(true)}
                  />
                )}
              </div>

              <div className="relative">
                <button
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                  className="flex items-center space-x-2 sm:space-x-3 p-2 rounded-md transition-colors min-h-[44px] hover:bg-gray-50 dark:hover:bg-gray-700"
                  aria-label="User profile menu"
                >
                  <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center">
                    <UserCircle className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-sm font-medium hidden sm:block text-gray-700 dark:text-gray-300">{user?.name || 'User'}</span>
                  <ChevronDown className="w-4 h-4 hidden sm:block text-gray-400 dark:text-gray-500" />
                </button>
                
                {userDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg border z-50 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                    <div className="py-1">
                      <div className="px-4 py-2 text-xs text-gray-500 dark:text-gray-400">
                        <div className="font-medium text-gray-700 dark:text-gray-300">{user?.name}</div>
                        <div>{user?.email}</div>
                        <div className="capitalize">{user?.role}</div>
                      </div>
                      <div className="border-t border-gray-100 dark:border-gray-600"></div>
                      <Link
                        to="/my-profile"
                        className="flex items-center space-x-2 px-4 py-2 text-sm transition-colors text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => setUserDropdownOpen(false)}
                        aria-label="Go to my profile"
                      >
                        <User className="w-4 h-4" />
                        <span>My Profile</span>
                      </Link>
                      <Link
                        to="/settings"
                        className="flex items-center space-x-2 px-4 py-2 text-sm transition-colors text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => setUserDropdownOpen(false)}
                        aria-label="Go to settings"
                      >
                        <Cog className="w-4 h-4" />
                        <span>Settings</span>
                      </Link>
                      <div className="border-t border-gray-100 dark:border-gray-600"></div>
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm transition-colors flex items-center space-x-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                        aria-label="Logout from account"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>Sign Out</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-4 sm:p-6 transition-colors duration-300">
          {children}
        </main>
      </div>

      {/* Notifications Sidebar */}
      <NotificationsSidebar
        isOpen={notificationsSidebarOpen}
        onClose={() => setNotificationsSidebarOpen(false)}
      />
    </div>
  );
};

export default Layout;