import React from 'react';
import { LucideIcon } from 'lucide-react';

export type BadgeVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'danger' 
  | 'info';

export type BadgeSize = 'sm' | 'md' | 'lg';

interface BadgeProps {
  children: React.ReactNode;
  variant?: BadgeVariant;
  size?: BadgeSize;
  icon?: LucideIcon;
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'secondary',
  size = 'md',
  icon: Icon,
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded transition-colors';
  
  const variantClasses = {
    primary: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300',
    secondary: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300',
    success: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300',
    warning: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300',
    danger: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300',
    info: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}>
      {Icon && (
        <Icon className={`${iconSizeClasses[size]} ${children ? 'mr-1.5' : ''}`} />
      )}
      {children}
    </span>
  );
};

// Status Badge Component
interface StatusBadgeProps {
  status: string;
  icon?: LucideIcon;
  className?: string;
  size?: BadgeSize;
  statusMap?: Record<string, BadgeVariant>;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  icon,
  className,
  size = 'md',
  statusMap
}) => {
  const getVariant = (status: string): BadgeVariant => {
    const normalizedStatus = status.toLowerCase();

    // Use custom status map if provided
    if (statusMap && statusMap[normalizedStatus]) {
      return statusMap[normalizedStatus];
    }

    if (['active', 'paid', 'completed', 'approved', 'verified', 'delivered'].includes(normalizedStatus)) {
      return 'success';
    }

    if (['pending', 'processing', 'sent', 'viewed', 'in_progress'].includes(normalizedStatus)) {
      return 'warning';
    }

    if (['inactive', 'cancelled', 'failed', 'rejected', 'overdue', 'expired'].includes(normalizedStatus)) {
      return 'danger';
    }

    if (['draft', 'new', 'created'].includes(normalizedStatus)) {
      return 'info';
    }

    return 'secondary';
  };

  return (
    <Badge variant={getVariant(status)} icon={icon} className={className} size={size}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

// Priority Badge Component
interface PriorityBadgeProps {
  priority: string;
  className?: string;
}

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority, className }) => {
  const getVariant = (priority: string): BadgeVariant => {
    const normalizedPriority = priority.toLowerCase();
    
    switch (normalizedPriority) {
      case 'low':
        return 'success';
      case 'medium':
        return 'warning';
      case 'high':
      case 'urgent':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  return (
    <Badge variant={getVariant(priority)} className={className}>
      {priority.charAt(0).toUpperCase() + priority.slice(1)}
    </Badge>
  );
};

export default Badge;
