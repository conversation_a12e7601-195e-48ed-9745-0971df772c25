import React from 'react';
import { AlertCircle, CheckCircle, Info, HelpCircle } from 'lucide-react';

interface ImportExplanationProps {
  totalRows: number;
  processed: number;
  created: number;
  updated: number;
  skipped: number;
  errors: number;
}

const ImportExplanation: React.FC<ImportExplanationProps> = ({
  totalRows,
  processed,
  created,
  updated,
  skipped,
  errors
}) => {
  const dataRows = totalRows - 1; // Exclude header
  const successRate = dataRows > 0 ? (processed / dataRows) * 100 : 0;
  const unprocessed = Math.max(0, dataRows - processed);

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h4 className="font-semibold text-blue-900 mb-4 flex items-center">
        <Info className="w-5 h-5 mr-2" />
        Understanding Your Import Results
      </h4>
      
      <div className="space-y-4 text-sm">
        {/* Total Rows Explanation */}
        <div className="flex items-start space-x-3">
          <HelpCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium text-blue-900">Total Rows ({totalRows.toLocaleString()})</p>
            <p className="text-blue-700">
              This includes the header row. Your CSV file contained {dataRows.toLocaleString()} data rows that were eligible for processing.
            </p>
          </div>
        </div>

        {/* Processed Records */}
        <div className="flex items-start space-x-3">
          <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium text-blue-900">Successfully Processed ({processed.toLocaleString()})</p>
            <p className="text-blue-700">
              These records were successfully imported into the database. This includes both newly created records ({created.toLocaleString()}) 
              and updated existing records ({updated.toLocaleString()}).
            </p>
          </div>
        </div>

        {/* Skipped Records */}
        {skipped > 0 && (
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium text-blue-900">Skipped Records ({skipped.toLocaleString()})</p>
              <p className="text-blue-700">
                These records were skipped because they were identified as duplicates of existing records in your database. 
                This prevents duplicate entries and maintains data integrity.
              </p>
            </div>
          </div>
        )}

        {/* Unprocessed Records */}
        {unprocessed > 0 && (
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium text-blue-900">Unprocessed Records ({unprocessed.toLocaleString()})</p>
              <p className="text-blue-700">
                These records were not processed due to validation errors or missing required fields. 
                Common issues include missing names, invalid email formats, or data type mismatches.
              </p>
            </div>
          </div>
        )}

        {/* Success Rate */}
        <div className="bg-white rounded-lg p-4 border border-blue-200">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium text-blue-900">Import Success Rate</span>
            <span className={`font-bold text-lg ${
              successRate > 95 ? 'text-green-600' : 
              successRate > 85 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {successRate.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-1000 ${
                successRate > 95 ? 'bg-green-500' : 
                successRate > 85 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${successRate}%` }}
            ></div>
          </div>
          <p className="text-xs text-blue-600 mt-2">
            {successRate > 95 ? 'Excellent data quality! Most records were successfully imported.' :
             successRate > 85 ? 'Good data quality with some issues that may need attention.' :
             'Data quality needs review. Consider checking for formatting issues or missing required fields.'}
          </p>
        </div>

        {/* Tips for Better Imports */}
        <div className="bg-blue-100 rounded-lg p-4">
          <h5 className="font-medium text-blue-900 mb-2">💡 Tips for Better Import Results</h5>
          <ul className="text-blue-700 text-xs space-y-1 list-disc list-inside">
            <li>Ensure all required fields (Name) are filled in your CSV</li>
            <li>Use valid email formats (e.g., <EMAIL>)</li>
            <li>Clean phone numbers should contain only digits and common separators</li>
            <li>Remove duplicate rows from your CSV before importing</li>
            <li>Use the "Update Existing" option to modify existing records instead of skipping them</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ImportExplanation;
