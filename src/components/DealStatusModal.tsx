import React, { useState } from 'react';
import { X, CheckCircle, XCircle, AlertTriangle, FileText } from 'lucide-react';
import { Deal, PipelineStage } from '../types/deal';
import CustomDropdown from './CustomDropdown';

interface DealStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  deal: Deal | null;
  targetStage: PipelineStage;
  showQuotationOption?: boolean;
  onConfirm: (data: { createInvoice?: boolean; createQuotation?: boolean; notes?: string; lossReason?: string; lossDetails?: string }) => void;
}

const DealStatusModal: React.FC<DealStatusModalProps> = ({
  isOpen,
  onClose,
  deal,
  targetStage,
  showQuotationOption = false,
  onConfirm
}) => {
  const [notes, setNotes] = useState('');
  const [createInvoice, setCreateInvoice] = useState(false);
  const [createQuotation, setCreateQuotation] = useState(false);
  const [lossReason, setLossReason] = useState('');
  const [lossDetails, setLossDetails] = useState('');

  if (!isOpen || !deal) return null;

  const handleConfirm = () => {
    onConfirm({
      createInvoice: targetStage === 'won' ? createInvoice : undefined,
      createQuotation: showQuotationOption ? createQuotation : undefined,
      notes,
      lossReason: targetStage === 'lost' ? lossReason : undefined,
      lossDetails: targetStage === 'lost' ? lossDetails : undefined,
    });
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getModalIcon = () => {
    switch (targetStage) {
      case 'won':
        return <CheckCircle className="w-8 h-8 text-green-500" />;
      case 'lost':
        return <XCircle className="w-8 h-8 text-red-500" />;
      default:
        return <AlertTriangle className="w-8 h-8 text-yellow-500" />;
    }
  };

  const getModalTitle = () => {
    switch (targetStage) {
      case 'won':
        return 'Mark Deal as Won';
      case 'lost':
        return 'Mark Deal as Lost';
      default:
        return 'Change Deal Status';
    }
  };

  const getModalMessage = () => {
    if (showQuotationOption) {
      return `Deal "${deal.title}" is moving to Proposal stage. Would you like to create a quotation?`;
    }

    switch (targetStage) {
      case 'won':
        return `Congratulations! Deal "${deal.title}" will be marked as WON.`;
      case 'lost':
        return `Deal "${deal.title}" will be marked as LOST. Please provide a reason.`;
      default:
        return `Deal "${deal.title}" status will be changed.`;
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
      onClick={handleOverlayClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            {getModalIcon()}
            <h3 className="text-lg font-semibold text-gray-900">
              {getModalTitle()}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <p className="text-gray-600">
            {getModalMessage()}
          </p>

          {targetStage === 'won' && (
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200">
                <input
                  type="checkbox"
                  id="createInvoice"
                  checked={createInvoice}
                  onChange={(e) => setCreateInvoice(e.target.checked)}
                  className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                />
                <label htmlFor="createInvoice" className="flex items-center space-x-2 text-sm font-medium text-green-800">
                  <FileText className="w-4 h-4" />
                  <span>Create invoice for this deal</span>
                </label>
              </div>
            </div>
          )}

          {showQuotationOption && (
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <input
                  type="checkbox"
                  id="createQuotation"
                  checked={createQuotation}
                  onChange={(e) => setCreateQuotation(e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="createQuotation" className="flex items-center space-x-2 text-sm font-medium text-blue-800">
                  <FileText className="w-4 h-4" />
                  <span>Create quotation for this deal</span>
                </label>
              </div>
            </div>
          )}

          {targetStage === 'lost' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Loss Reason *
                </label>
                <CustomDropdown
                  options={[
                    { value: '', label: 'Select a reason' },
                    { value: 'Price Too High', label: 'Price Too High' },
                    { value: 'Competitor Won', label: 'Competitor Won' },
                    { value: 'No Budget', label: 'No Budget' },
                    { value: 'No Decision', label: 'No Decision' },
                    { value: 'Product Mismatch', label: 'Product Mismatch' },
                    { value: 'Timing Issues', label: 'Timing Issues' },
                    { value: 'Lost Contact', label: 'Lost Contact' },
                    { value: 'Other', label: 'Other' }
                  ]}
                  value={lossReason}
                  onChange={setLossReason}
                  placeholder="Select a reason"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Details
                </label>
                <textarea
                  value={lossDetails}
                  onChange={(e) => setLossDetails(e.target.value)}
                  placeholder="Provide additional details about why this deal was lost..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  rows={3}
                />
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any additional notes about this status change..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
            />
          </div>
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={targetStage === 'lost' && !lossReason}
            className={`px-4 py-2 text-white rounded-lg transition-colors ${
              targetStage === 'won'
                ? 'bg-green-600 hover:bg-green-700'
                : targetStage === 'lost'
                ? 'bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {targetStage === 'won' ? 'Mark as Won' : targetStage === 'lost' ? 'Mark as Lost' : 'Confirm'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DealStatusModal;
