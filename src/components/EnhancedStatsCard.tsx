import React from 'react';
import { LucideIcon } from 'lucide-react';
import { AnimatedScoreDisplay, CircularProgress } from './AnimatedComponents';

interface EnhancedStatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: LucideIcon;
  iconColor?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  score?: number; // For animated score display
  scoreLabel?: string;
  distribution?: {
    label: string;
    value: number;
    color: string;
  }[];
  className?: string;
  children?: React.ReactNode;
}

const EnhancedStatsCard: React.FC<EnhancedStatsCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  iconColor = 'text-blue-500',
  trend,
  score,
  scoreLabel,
  distribution,
  className = '',
  children
}) => {
  // Check if this is a dark background card (like the blue gradient)
  const isDarkCard = className.includes('from-blue-5') || className.includes('text-white');

  return (
    <div className={`rounded-lg shadow-sm p-6 ${className.includes('bg-') ? className : `bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}`}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <p className={`text-sm font-medium mb-1 ${isDarkCard ? 'text-blue-100' : 'text-gray-600 dark:text-gray-400'}`}>{title}</p>
          <p className={`text-2xl font-bold ${isDarkCard ? 'text-white' : 'text-gray-900 dark:text-white'}`}>{value}</p>
          {subtitle && (
            <p className={`text-sm mt-1 ${isDarkCard ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>{subtitle}</p>
          )}
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${
              isDarkCard
                ? (trend.isPositive ? 'text-green-200' : 'text-red-200')
                : (trend.isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400')
            }`}>
              <span className={`inline-flex items-center ${
                isDarkCard
                  ? (trend.isPositive ? 'text-green-200' : 'text-red-200')
                  : (trend.isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400')
              }`}>
                {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%
              </span>
              <span className={`ml-1 ${isDarkCard ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>this month</span>
            </div>
          )}
        </div>

        {/* Icon or Score Display */}
        <div className="flex-shrink-0">
          {score !== undefined && scoreLabel ? (
            <div className="text-center">
              <AnimatedScoreDisplay
                score={score}
                label={scoreLabel}
                colorClass={
                  isDarkCard
                    ? 'text-white'
                    : (score >= 80 ? 'text-green-600 dark:text-green-400' : score >= 60 ? 'text-blue-600 dark:text-blue-400' : score >= 40 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400')
                }
                labelColorClass={isDarkCard ? 'text-white' : 'text-gray-600'}
                size="sm"
              />
            </div>
          ) : Icon ? (
            <div className={`p-3 rounded-full ${isDarkCard ? 'bg-blue-400/20' : 'bg-gray-50 dark:bg-gray-700'} ${iconColor}`}>
              <Icon className="h-6 w-6" />
            </div>
          ) : null}
        </div>
      </div>

      {/* Distribution bars */}
      {distribution && (
        <div className="space-y-2">
          <p className={`text-xs font-medium mb-3 ${isDarkCard ? 'text-blue-100' : 'text-gray-600 dark:text-gray-400'}`}>Distribution</p>
          {distribution.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-2 flex-1">
                <div
                  className="w-3 h-3 rounded-sm"
                  style={{ backgroundColor: item.color }}
                />
                <span className={`text-xs ${isDarkCard ? 'text-blue-100' : 'text-gray-600 dark:text-gray-400'}`}>{item.label}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-16 rounded-full h-1.5 ${isDarkCard ? 'bg-blue-400/20' : 'bg-gray-200 dark:bg-gray-600'}`}>
                  <div
                    className="h-1.5 rounded-full transition-all duration-1500 ease-out"
                    style={{
                      width: `${item.value}%`,
                      backgroundColor: item.color
                    }}
                  />
                </div>
                <span className={`text-xs font-medium w-8 text-right ${isDarkCard ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                  {item.value}%
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Custom children content */}
      {children && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          {children}
        </div>
      )}
    </div>
  );
};

export default EnhancedStatsCard;
