import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Save, Calculator } from 'lucide-react';
import { useQuotations, Quotation, QuotationItem } from '../contexts/QuotationContext';
import { useClients } from '../contexts/ClientContext';
import { useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';
import CustomDropdown from './CustomDropdown';

interface QuotationModalProps {
  isOpen: boolean;
  onClose: () => void;
  quotation?: Quotation;
  mode: 'create' | 'edit' | 'view';
}

const QuotationModal: React.FC<QuotationModalProps> = ({ isOpen, onClose, quotation, mode }) => {
  const { createQuotation, updateQuotation } = useQuotations();
  const { clients } = useClients();
  const { products } = useProducts();
  const { showSuccess, showError } = useToast();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    priority: 'medium' as const,
    tax_rate: 6,
    discount_amount: 0,
    currency: 'MYR',
    validity_days: 30,
    terms_conditions: '',
    notes: '',
    internal_notes: '',
    expected_close_date: '',
  });

  const [items, setItems] = useState<QuotationItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (quotation && (mode === 'edit' || mode === 'view')) {
      setFormData({
        title: quotation.title,
        description: quotation.description || '',
        client_id: quotation.client_id || '',
        priority: quotation.priority,
        tax_rate: quotation.tax_rate,
        discount_amount: quotation.discount_amount,
        currency: quotation.currency,
        validity_days: quotation.validity_days,
        terms_conditions: quotation.terms_conditions || '',
        notes: quotation.notes || '',
        internal_notes: quotation.internal_notes || '',
        expected_close_date: quotation.expected_close_date ? quotation.expected_close_date.split('T')[0] : '',
      });
      setItems(quotation.items || []);
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        client_id: '',
        priority: 'medium',
        tax_rate: 6,
        discount_amount: 0,
        currency: 'MYR',
        validity_days: 30,
        terms_conditions: 'Payment terms: 50% advance, 50% upon completion\nAll services will be conducted according to Islamic principles\nDelivery timeline as specified in project schedule',
        notes: '',
        internal_notes: '',
        expected_close_date: '',
      });
      setItems([{
        item_name: '',
        description: '',
        unit: 'pcs',
        quantity: 1,
        unit_price: 0,
        discount_rate: 0,
        discount_amount: 0,
        line_total: 0,
        sort_order: 1,
      }]);
    }
  }, [quotation, mode]);

  const addItem = () => {
    const newItem: QuotationItem = {
      item_name: '',
      description: '',
      unit: 'pcs',
      quantity: 1,
      unit_price: 0,
      discount_rate: 0,
      discount_amount: 0,
      line_total: 0,
      sort_order: items.length + 1,
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof QuotationItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate line total when relevant fields change
    if (['quantity', 'unit_price', 'discount_rate', 'discount_amount'].includes(field)) {
      const item = updatedItems[index];
      const subtotal = item.quantity * item.unit_price;
      const discount = item.discount_rate > 0 
        ? subtotal * (item.discount_rate / 100)
        : item.discount_amount;
      updatedItems[index].line_total = subtotal - discount;
    }
    
    setItems(updatedItems);
  };

  const addProductToItems = (productId: string) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      const newItem: QuotationItem = {
        product_id: product.id,
        item_name: product.name,
        description: product.description || '',
        sku: product.sku || '',
        unit: 'pcs',
        quantity: 1,
        unit_price: product.price,
        discount_rate: 0,
        discount_amount: 0,
        line_total: product.price,
        sort_order: items.length + 1,
      };
      setItems([...items, newItem]);
    }
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = subtotal * (formData.tax_rate / 100);
    const total = subtotal + taxAmount - formData.discount_amount;
    
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === 'view') return;

    setLoading(true);
    try {
      // Handle client ID: filter out frontend client IDs (string IDs starting with 'client-')
      let clientId = formData.client_id;
      if (clientId && typeof clientId === 'string' && clientId.startsWith('client-')) {

        clientId = null; // Don't send frontend client IDs to backend
      } else if (clientId === '') {
        clientId = null; // Convert empty string to null
      }

      const quotationData = {
        ...formData,
        client_id: clientId,
        items: items.map((item, index) => ({
          ...item,
          sort_order: index + 1,
        })),
      };

      if (mode === 'create') {
        await createQuotation(quotationData);
        showSuccess('Success', 'Quotation created successfully');
      } else if (mode === 'edit' && quotation) {
        await updateQuotation(quotation.id, quotationData);
        showSuccess('Success', 'Quotation updated successfully');
      }

      onClose();
    } catch (error) {
      console.error('Error saving quotation:', error);
      showError('Error', `Failed to ${mode === 'create' ? 'create' : 'update'} quotation. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const { subtotal, taxAmount, total } = calculateTotals();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {mode === 'create' ? 'Create New Quotation' :
             mode === 'edit' ? 'Edit Quotation' : 'View Quotation'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  required
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Client
                </label>
                <CustomDropdown
                  options={[
                    { value: '', label: 'Select Client' },
                    ...clients.map(client => ({ value: client.id, label: client.name }))
                  ]}
                  value={formData.client_id}
                  onChange={(value) => setFormData({ ...formData, client_id: value })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <CustomDropdown
                  options={[
                    { value: 'low', label: 'Low' },
                    { value: 'medium', label: 'Medium' },
                    { value: 'high', label: 'High' },
                    { value: 'urgent', label: 'Urgent' },
                  ]}
                  value={formData.priority}
                  onChange={(value) => setFormData({ ...formData, priority: value as any })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <CustomDropdown
                  options={[
                    { value: 'MYR', label: 'MYR - Malaysian Ringgit' },
                    { value: 'USD', label: 'USD - US Dollar' },
                    { value: 'SGD', label: 'SGD - Singapore Dollar' },
                  ]}
                  value={formData.currency}
                  onChange={(value) => setFormData({ ...formData, currency: value })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Validity (Days)
                </label>
                <input
                  type="number"
                  min="1"
                  max="365"
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.validity_days}
                  onChange={(e) => setFormData({ ...formData, validity_days: parseInt(e.target.value) || 30 })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Expected Close Date
                </label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.expected_close_date}
                  onChange={(e) => setFormData({ ...formData, expected_close_date: e.target.value })}
                  disabled={mode === 'view'}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                rows={3}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                disabled={mode === 'view'}
              />
            </div>

            {/* Items Section */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Items</h3>
                {mode !== 'view' && (
                  <div className="flex items-center space-x-2">
                    <CustomDropdown
                      options={[
                        { value: '', label: 'Add Product' },
                        ...products.map(product => ({
                          value: product.id,
                          label: `${product.name} - ${product.price}`
                        }))
                      ]}
                      value=""
                      onChange={(value) => value && addProductToItems(value)}
                      placeholder="Add Product"
                    />
                    <button
                      type="button"
                      onClick={addItem}
                      className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-1"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Add Item</span>
                    </button>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {items.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Item Name *
                        </label>
                        <input
                          type="text"
                          required
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={item.item_name}
                          onChange={(e) => updateItem(index, 'item_name', e.target.value)}
                          disabled={mode === 'view'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Unit
                        </label>
                        <CustomDropdown
                          options={[
                            { value: 'pcs', label: 'Pieces' },
                            { value: 'hours', label: 'Hours' },
                            { value: 'sessions', label: 'Sessions' },
                            { value: 'months', label: 'Months' },
                            { value: 'kg', label: 'Kilograms' },
                          ]}
                          value={item.unit}
                          onChange={(value) => updateItem(index, 'unit', value)}
                          disabled={mode === 'view'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Quantity *
                        </label>
                        <input
                          type="number"
                          required
                          min="0.01"
                          step="0.01"
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          disabled={mode === 'view'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Unit Price *
                        </label>
                        <input
                          type="number"
                          required
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={item.unit_price}
                          onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          disabled={mode === 'view'}
                        />
                      </div>

                      <div className="flex items-end">
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Line Total
                          </label>
                          <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm font-medium">
                            {new Intl.NumberFormat('en-MY', {
                              style: 'currency',
                              currency: formData.currency,
                            }).format(item.line_total)}
                          </div>
                        </div>
                        {mode !== 'view' && items.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            className="ml-2 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={item.description || ''}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        disabled={mode === 'view'}
                      />
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Discount Rate (%)
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={item.discount_rate}
                          onChange={(e) => updateItem(index, 'discount_rate', parseFloat(e.target.value) || 0)}
                          disabled={mode === 'view'}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Discount Amount
                        </label>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={item.discount_amount}
                          onChange={(e) => updateItem(index, 'discount_amount', parseFloat(e.target.value) || 0)}
                          disabled={mode === 'view'}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pricing and Totals */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tax Rate (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.tax_rate}
                  onChange={(e) => setFormData({ ...formData, tax_rate: parseFloat(e.target.value) || 0 })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Discount Amount
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.discount_amount}
                  onChange={(e) => setFormData({ ...formData, discount_amount: parseFloat(e.target.value) || 0 })}
                  disabled={mode === 'view'}
                />
              </div>
            </div>

            {/* Totals Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <Calculator className="w-5 h-5 text-gray-600" />
                <h4 className="text-lg font-medium text-gray-900">Totals</h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat('en-MY', {
                      style: 'currency',
                      currency: formData.currency,
                    }).format(subtotal)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax ({formData.tax_rate}%):</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat('en-MY', {
                      style: 'currency',
                      currency: formData.currency,
                    }).format(taxAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Discount:</span>
                  <span className="font-medium text-red-600">
                    -{new Intl.NumberFormat('en-MY', {
                      style: 'currency',
                      currency: formData.currency,
                    }).format(formData.discount_amount)}
                  </span>
                </div>
                <div className="border-t pt-2 flex justify-between">
                  <span className="text-lg font-semibold text-gray-900">Total:</span>
                  <span className="text-lg font-bold text-blue-600">
                    {new Intl.NumberFormat('en-MY', {
                      style: 'currency',
                      currency: formData.currency,
                    }).format(total)}
                  </span>
                </div>
              </div>
            </div>

            {/* Terms and Notes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Terms & Conditions
                </label>
                <textarea
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.terms_conditions}
                  onChange={(e) => setFormData({ ...formData, terms_conditions: e.target.value })}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  disabled={mode === 'view'}
                />
              </div>
            </div>

            {mode !== 'view' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Internal Notes
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={formData.internal_notes}
                  onChange={(e) => setFormData({ ...formData, internal_notes: e.target.value })}
                  placeholder="Internal notes (not visible to client)"
                />
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {mode === 'view' ? 'Close' : 'Cancel'}
          </button>
          {mode !== 'view' && (
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>{mode === 'create' ? 'Create Quotation' : 'Update Quotation'}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuotationModal;
