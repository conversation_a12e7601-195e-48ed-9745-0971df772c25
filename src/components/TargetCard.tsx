import React from 'react';

interface TargetCardProps {
  title: string;
  completionPercentage: number;
  currentValue: number;
  targetValue: number;
  subtitle?: string;
  className?: string;
}

const TargetCard: React.FC<TargetCardProps> = ({
  title,
  completionPercentage,
  currentValue,
  targetValue,
  subtitle,
  className = ''
}) => {
  const radius = 45;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (completionPercentage / 100) * circumference;

  return (
    <div className={`bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-lg text-white p-6 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold mb-2">{title}</h3>
          <p className="text-blue-100 text-sm mb-4">
            {subtitle || `You have completed ${completionPercentage}% of the given target.`}
          </p>
          <p className="text-blue-100 text-sm mb-4">
            You also need to check your status.
          </p>
          <button className="text-white text-sm underline hover:no-underline">
            Click here
          </button>
        </div>
        
        <div className="flex-shrink-0 ml-6">
          <div className="relative w-24 h-24">
            <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              {/* Background circle */}
              <circle
                cx="50"
                cy="50"
                r={radius}
                stroke="rgba(255,255,255,0.2)"
                strokeWidth="8"
                fill="transparent"
              />
              {/* Progress circle */}
              <circle
                cx="50"
                cy="50"
                r={radius}
                stroke="white"
                strokeWidth="8"
                fill="transparent"
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                className="transition-all duration-300 ease-in-out"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl font-bold">{Math.round(completionPercentage)}%</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-blue-400">
        <div className="flex justify-between items-center text-sm">
          <span className="text-blue-100">Current: RM{currentValue.toLocaleString()}</span>
          <span className="text-blue-100">Target: RM{targetValue.toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
};

export default TargetCard;
