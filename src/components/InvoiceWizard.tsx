import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Save, Plus, Trash2 } from 'lucide-react';
import { useInvoices, Invoice, InvoiceItem } from '../contexts/InvoiceContext';
import { useClients } from '../contexts/ClientContext';
import { useLeads } from '../contexts/LeadContext';
import { useToast } from '../contexts/ToastContext';
import { apiService } from '../services/api';
import CustomDropdown from './CustomDropdown';

interface InvoiceWizardProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: Invoice;
  mode: 'create' | 'edit' | 'view';
}

const InvoiceWizard: React.FC<InvoiceWizardProps> = ({ isOpen, onClose, invoice, mode }) => {
  const { createInvoice, updateInvoice } = useInvoices();
  const { clients: frontendClients } = useClients();
  const { leads: frontendLeads } = useLeads();
  const { showSuccess, showError } = useToast();

  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  // Removed backend API calls - using only frontend context data for consistency

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    lead_id: '',
    priority: 'medium' as const,
    tax_rate: 6,
    discount_amount: 0,
    currency: 'MYR',
    terms_conditions: '',
    notes: '',
    internal_notes: '',
    issue_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  });

  const [items, setItems] = useState<InvoiceItem[]>([
    // Start with one default item to improve UX
    {
      item_name: '',
      description: '',
      unit: 'pcs',
      quantity: 1,
      unit_price: 0,
      discount_rate: 0,
      discount_amount: 0,
      line_total: 0,
      sort_order: 1,
    }
  ]);

  const steps = [
    { id: 1, title: 'Basic Information', description: 'Invoice details and client' },
    { id: 2, title: 'Items & Pricing', description: 'Add products and services' },
    { id: 3, title: 'Terms & Notes', description: 'Terms, conditions and notes' },
    { id: 4, title: 'Review & Submit', description: 'Review all details' },
  ];

  // Removed backend API calls - using only frontend context data for consistency

  // Use only frontend clients and leads for consistency
  const allClientsAndLeads = [
    { value: '', label: 'Select a client or lead' },
    ...frontendClients.map(client => ({
      value: `client-${client.id}`,
      label: `${client.name} (Client) ${client.email ? `- ${client.email}` : ''}`
    })),
    ...frontendLeads.map(lead => ({
      value: `lead-${lead.id}`,
      label: `${lead.name || 'Unnamed'} (Lead) ${lead.email ? `- ${lead.email}` : ''} ${lead.company ? `- ${lead.company}` : ''}`
    }))
  ];

  // Check if we have any entities available
  const hasEntities = frontendClients.length > 0 || frontendLeads.length > 0;

  // Removed debug logging and backend API calls

  useEffect(() => {
    if (invoice && (mode === 'edit' || mode === 'view')) {
      // Determine the selected value for the dropdown
      let selectedValue = '';
      if (invoice.client_id) {
        selectedValue = `client-${invoice.client_id}`;
      } else if (invoice.lead_id) {
        selectedValue = `lead-${invoice.lead_id}`;
      }

      setFormData({
        title: invoice.title,
        description: invoice.description || '',
        client_id: selectedValue,
        lead_id: '',
        priority: invoice.priority,
        tax_rate: Number(invoice.tax_rate) || 6,
        discount_amount: Number(invoice.discount_amount) || 0,
        currency: invoice.currency || 'MYR',
        terms_conditions: invoice.terms_conditions || '',
        notes: invoice.notes || '',
        internal_notes: invoice.internal_notes || '',
        issue_date: invoice.issue_date || new Date().toISOString().split('T')[0],
        due_date: invoice.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      });
      setItems(invoice.items || []);
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        client_id: '',
        lead_id: '',
        priority: 'medium',
        tax_rate: 6,
        discount_amount: 0,
        currency: 'MYR',
        terms_conditions: '',
        notes: '',
        internal_notes: '',
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      });
      setItems([]);
      setCurrentStep(1);
    }
  }, [invoice, mode, isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;
      
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addItem = () => {
    const newItem: InvoiceItem = {
      item_name: '',
      description: '',
      unit: 'pcs',
      quantity: 1,
      unit_price: 0,
      discount_rate: 0,
      discount_amount: 0,
      line_total: 0,
      sort_order: items.length + 1,
    };
    setItems([...items, newItem]);
  };

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate line total
    const item = updatedItems[index];
    const subtotal = item.quantity * item.unit_price;
    const discountAmount = (item.discount_rate / 100) * subtotal;
    item.discount_amount = discountAmount;
    item.line_total = subtotal - discountAmount;
    
    setItems(updatedItems);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = subtotal * (formData.tax_rate / 100);
    const total = subtotal + taxAmount - formData.discount_amount;
    
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async () => {
    if (!formData.title.trim()) {
      showError('Validation Error', 'Invoice title is required');
      setCurrentStep(1);
      return;
    }

    if (!formData.client_id) {
      showError('Validation Error', 'Please select a client or lead');
      setCurrentStep(1);
      return;
    }

    if (items.length === 0) {
      showError('Validation Error', 'Please add at least one item');
      setCurrentStep(2);
      return;
    }

    // Check if all items have valid names
    const invalidItems = items.filter(item => !item.item_name || item.item_name.trim() === '');
    if (invalidItems.length > 0) {
      showError('Validation Error', 'Please provide names for all items');
      setCurrentStep(2);
      return;
    }

    setLoading(true);
    try {
      const { subtotal, taxAmount, total } = calculateTotals();

      // Parse the selected value and handle frontend/backend entities
      let clientId = null;
      let leadId = null;

      if (formData.client_id) {
        if (formData.client_id.startsWith('backend-client-')) {
          // Backend client - use directly
          clientId = parseInt(formData.client_id.replace('backend-client-', ''));

        } else if (formData.client_id.startsWith('backend-lead-')) {
          // Backend lead - use directly
          leadId = parseInt(formData.client_id.replace('backend-lead-', ''));

        } else if (formData.client_id.startsWith('client-')) {
          // Frontend client - need to create in backend first
          const frontendClientId = formData.client_id.replace('client-', '');
          const frontendClient = frontendClients.find(c => c.id === frontendClientId);

          if (frontendClient) {

            // Map frontend client data to backend format with proper validation
            const clientData = {
              name: frontendClient.name,
              email: frontendClient.email,
              phone: frontendClient.phone || null,
              address: frontendClient.address || null,
              utm_source: frontendClient.utmSource || 'direct',
              tags: frontendClient.tags || [],
              // Ensure category is one of the valid backend values
              category: ['First Timer', 'Retainer', 'Loyal', 'Advocator'].includes(frontendClient.category)
                ? frontendClient.category
                : 'First Timer',
              // Ensure ltv_segment is valid
              ltv_segment: ['Silver', 'Gold', 'Gold+', 'Platinum'].includes(frontendClient.ltvSegment)
                ? frontendClient.ltvSegment
                : 'Silver',
              // Ensure engagement_level is valid
              engagement_level: ['Hot', 'Warm', 'Cold', 'Frozen'].includes(frontendClient.engagementLevel)
                ? frontendClient.engagementLevel
                : 'Cold',
              // Ensure priority is valid
              priority: ['High', 'Medium', 'Low'].includes(frontendClient.priority)
                ? frontendClient.priority
                : 'Medium',
              status: 'prospect',
              notes: frontendClient.notes || null,
              suggested_action: frontendClient.suggestedAction || null,
              total_spent: frontendClient.totalSpent || 0,
              transaction_count: frontendClient.transactionCount || 0,
            };

            try {
              const backendClient = await apiService.createClient(clientData);
              clientId = backendClient.id;

            } catch (error) {
              console.error('🔍 [DEBUG] Failed to create backend client:', error);
              throw new Error('Failed to create client in backend. Please try again.');
            }
          }
        } else if (formData.client_id.startsWith('lead-')) {
          // Frontend lead - need to create in backend first
          const frontendLeadId = formData.client_id.replace('lead-', '');
          const frontendLead = frontendLeads.find(l => l.id === frontendLeadId);

          if (frontendLead) {

            const leadData = {
              title: frontendLead.name || 'Converted from frontend',
              description: frontendLead.notes || 'Direct conversion from leads page',
              status: 'new',
              priority: 'medium',
              source: frontendLead.utmSource || 'direct',
              estimated_value: frontendLead.estimatedValue || 0,
              probability: frontendLead.probability || 50,
              notes: frontendLead.notes || null
            };

            try {
              const backendLead = await apiService.createLead(leadData);
              leadId = backendLead.id;

            } catch (error) {
              console.error('Failed to create backend lead:', error);
              throw new Error('Failed to create lead in backend. Please try again.');
            }
          }
        }
      }

      const invoiceData = {
        title: formData.title,
        description: formData.description,
        client_id: clientId,
        lead_id: leadId,
        priority: formData.priority,
        tax_rate: formData.tax_rate,
        discount_amount: formData.discount_amount,
        currency: formData.currency,
        terms_conditions: formData.terms_conditions,
        notes: formData.notes,
        internal_notes: formData.internal_notes,
        issue_date: formData.issue_date,
        due_date: formData.due_date,
        subtotal,
        tax_amount: taxAmount,
        total_amount: total,
        items,
        status: 'draft' as const,
        payment_status: 'pending' as const,
      };

      if (mode === 'edit' && invoice) {
        await updateInvoice(invoice.id, invoiceData);
        showSuccess('Success', 'Invoice updated successfully');
      } else {
        await createInvoice(invoiceData);
        showSuccess('Success', 'Invoice created successfully');
      }
      
      onClose();
    } catch (error: any) {
      showError('Error', error.message || 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-4">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative bg-white rounded-xl shadow-xl w-full max-w-5xl max-h-[95vh] flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {mode === 'create' ? 'Create New Invoice' : mode === 'edit' ? 'Edit Invoice' : 'View Invoice'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {steps[currentStep - 1]?.description}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Step Navigation */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => goToStep(step.id)}
                    className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                      currentStep === step.id
                        ? 'bg-blue-600 text-white'
                        : currentStep > step.id
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                    }`}
                  >
                    {step.id}
                  </button>
                  <span className={`ml-2 text-sm font-medium ${
                    currentStep === step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-green-600' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Invoice Title *
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter invoice title"
                      disabled={mode === 'view'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Client or Lead *
                    </label>
                    <CustomDropdown
                      options={allClientsAndLeads}
                      value={formData.client_id}
                      onChange={(value) => handleInputChange('client_id', value)}
                      disabled={mode === 'view'}
                      placeholder="Select a client or lead"
                    />
                    {!hasEntities ? (
                      <p className="mt-1 text-xs text-red-500">
                        No clients or leads available. Please create clients or leads first.
                      </p>
                    ) : (
                      <p className="mt-1 text-xs text-gray-500">
                        Select a client or lead. Frontend entities will be synced to the backend automatically.
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <CustomDropdown
                      options={[
                        { value: 'low', label: 'Low' },
                        { value: 'medium', label: 'Medium' },
                        { value: 'high', label: 'High' },
                        { value: 'urgent', label: 'Urgent' },
                      ]}
                      value={formData.priority}
                      onChange={(value) => handleInputChange('priority', value)}
                      disabled={mode === 'view'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency
                    </label>
                    <CustomDropdown
                      options={[
                        { value: 'MYR', label: 'MYR - Malaysian Ringgit' },
                        { value: 'USD', label: 'USD - US Dollar' },
                        { value: 'SGD', label: 'SGD - Singapore Dollar' },
                      ]}
                      value={formData.currency}
                      onChange={(value) => handleInputChange('currency', value)}
                      disabled={mode === 'view'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Issue Date
                    </label>
                    <input
                      type="date"
                      value={formData.issue_date}
                      onChange={(e) => handleInputChange('issue_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={mode === 'view'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Due Date
                    </label>
                    <input
                      type="date"
                      value={formData.due_date}
                      onChange={(e) => handleInputChange('due_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={mode === 'view'}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter invoice description"
                    disabled={mode === 'view'}
                  />
                </div>
              </div>
            )}

            {/* Step 2: Items & Pricing */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Invoice Items</h3>
                  {mode !== 'view' && (
                    <button
                      onClick={addItem}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Add Item</span>
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {items.map((item, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Item Name *
                          </label>
                          <input
                            type="text"
                            value={item.item_name}
                            onChange={(e) => updateItem(index, 'item_name', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Enter item name"
                            disabled={mode === 'view'}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Unit
                          </label>
                          <input
                            type="text"
                            value={item.unit}
                            onChange={(e) => updateItem(index, 'unit', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="pcs"
                            disabled={mode === 'view'}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Quantity
                          </label>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            min="0"
                            step="0.01"
                            disabled={mode === 'view'}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Unit Price
                          </label>
                          <input
                            type="number"
                            value={item.unit_price}
                            onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            min="0"
                            step="0.01"
                            disabled={mode === 'view'}
                          />
                        </div>

                        <div className="flex items-end">
                          <div className="flex-1">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Total
                            </label>
                            <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-900">
                              {formData.currency} {item.line_total.toFixed(2)}
                            </div>
                          </div>
                          {mode !== 'view' && (
                            <button
                              onClick={() => removeItem(index)}
                              className="ml-2 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          value={item.description || ''}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Item description (optional)"
                          disabled={mode === 'view'}
                        />
                      </div>
                    </div>
                  ))}

                  {items.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No items added yet. Click "Add Item" to get started.
                    </div>
                  )}
                </div>

                {/* Totals */}
                <div className="border-t pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tax Rate (%)
                      </label>
                      <input
                        type="number"
                        value={formData.tax_rate}
                        onChange={(e) => handleInputChange('tax_rate', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        max="100"
                        step="0.01"
                        disabled={mode === 'view'}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Discount Amount
                      </label>
                      <input
                        type="number"
                        value={formData.discount_amount}
                        onChange={(e) => handleInputChange('discount_amount', parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        step="0.01"
                        disabled={mode === 'view'}
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Subtotal:</span>
                        <span>{formData.currency} {calculateTotals().subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Tax:</span>
                        <span>{formData.currency} {calculateTotals().taxAmount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Discount:</span>
                        <span>-{formData.currency} {formData.discount_amount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-semibold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>{formData.currency} {calculateTotals().total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Terms & Notes */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Terms & Conditions
                  </label>
                  <textarea
                    value={formData.terms_conditions}
                    onChange={(e) => handleInputChange('terms_conditions', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter terms and conditions"
                    disabled={mode === 'view'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Visible to Client)
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter notes for the client"
                    disabled={mode === 'view'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Internal Notes (Private)
                  </label>
                  <textarea
                    value={formData.internal_notes}
                    onChange={(e) => handleInputChange('internal_notes', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter internal notes (not visible to client)"
                    disabled={mode === 'view'}
                  />
                </div>
              </div>
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Review Invoice</h3>

                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Basic Information</h4>
                      <div className="space-y-1 text-sm">
                        <div><span className="font-medium">Title:</span> {formData.title}</div>
                        <div><span className="font-medium">Client/Lead:</span> {
                          (() => {
                            if (!formData.client_id) return 'Not selected';

                            if (formData.client_id.startsWith('backend-client-')) {
                              const clientId = formData.client_id.replace('backend-client-', '');
                              const client = backendClients.find(c => c.id.toString() === clientId);
                              return client ? `${client.name} (Client - DB)` : 'Not found';
                            } else if (formData.client_id.startsWith('backend-lead-')) {
                              const leadId = formData.client_id.replace('backend-lead-', '');
                              const lead = backendLeads.find(l => l.id.toString() === leadId);
                              return lead ? `${lead.name} (Lead - DB)` : 'Not found';
                            } else if (formData.client_id.startsWith('client-')) {
                              const clientId = formData.client_id.replace('client-', '');
                              const client = frontendClients.find(c => c.id === clientId);
                              return client ? `${client.name} (Client)` : 'Not found';
                            } else if (formData.client_id.startsWith('lead-')) {
                              const leadId = formData.client_id.replace('lead-', '');
                              const lead = frontendLeads.find(l => l.id === leadId);
                              return lead ? `${lead.name} (Lead)` : 'Not found';
                            }
                            return 'Not selected';
                          })()
                        }</div>
                        <div><span className="font-medium">Priority:</span> {formData.priority}</div>
                        <div><span className="font-medium">Currency:</span> {formData.currency}</div>
                        <div><span className="font-medium">Issue Date:</span> {formData.issue_date}</div>
                        <div><span className="font-medium">Due Date:</span> {formData.due_date}</div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Financial Summary</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Subtotal:</span>
                          <span>{formData.currency} {calculateTotals().subtotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax ({formData.tax_rate}%):</span>
                          <span>{formData.currency} {calculateTotals().taxAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Discount:</span>
                          <span>-{formData.currency} {formData.discount_amount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between font-semibold border-t pt-1">
                          <span>Total:</span>
                          <span>{formData.currency} {calculateTotals().total.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h4 className="font-medium text-gray-900 mb-2">Items ({items.length})</h4>
                    <div className="space-y-2">
                      {items.map((item, index) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span>{item.item_name} ({item.quantity} {item.unit})</span>
                          <span>{formData.currency} {item.line_total.toFixed(2)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              {currentStep > 1 && (
                <button
                  onClick={prevStep}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                  <span>Previous</span>
                </button>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>

              {currentStep < steps.length ? (
                <button
                  onClick={nextStep}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <span>Next</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              ) : (
                mode !== 'view' && (
                  <button
                    onClick={handleSubmit}
                    disabled={loading}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    <span>{loading ? 'Saving...' : mode === 'edit' ? 'Update Invoice' : 'Create Invoice'}</span>
                  </button>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceWizard;
