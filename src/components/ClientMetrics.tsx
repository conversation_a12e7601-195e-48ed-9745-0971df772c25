import React from 'react';
import { Client } from '../contexts/ClientContext';
import {
  AnimatedScoreDisplay,
  CircularProgress,
  AnimatedProgressBar,
  DataQualityBadge,
  useReducedMotion
} from './AnimatedComponents';
import {
  calculateClientScore,
  getClientOverallScore,
  calculateEngagementScore,
  calculateLTVProgress,
  calculateRetentionRate,
  calculateDataQuality,
  getScoreColor,
  getScoreBackgroundColor
} from '../utils/clientScoring';
import { ShieldCheck, TrendingUp, Heart, Star } from 'lucide-react';

interface ClientMetricsProps {
  client: Client;
}

const ClientMetrics: React.FC<ClientMetricsProps> = ({ client }) => {
  const prefersReducedMotion = useReducedMotion();

  // Calculate client metrics using unified system
  const clientScores = calculateClientScore(client);

  // Use centralized function for consistent overall score across all views
  const clientScore = getClientOverallScore(client);
  const ltvProgress = calculateLTVProgress(client);
  const engagementScore = calculateEngagementScore(client);
  const dataQuality = calculateDataQuality(client);
  const retentionRate = calculateRetentionRate(client);

  const getProgressColor = (score: number) => {
    return getScoreBackgroundColor(score);
  };

  const getCircularColor = (score: number) => {
    return getScoreColor(score).replace('text-', 'text-');
  };

  return (
    <div className="space-y-6">

      {/* Progress Bars Section */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Performance Metrics</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <AnimatedProgressBar
              percentage={ltvProgress}
              color={getProgressColor(ltvProgress)}
              label="LTV Progress"
              delay={0}
            />
            <AnimatedProgressBar
              percentage={engagementScore}
              color={getProgressColor(engagementScore)}
              label="Engagement Level"
              delay={300}
            />
          </div>
          <div>
            <AnimatedProgressBar
              percentage={dataQuality}
              color={getProgressColor(dataQuality)}
              label="Data Quality Score"
              delay={600}
            />
            <AnimatedProgressBar
              percentage={retentionRate}
              color={getProgressColor(retentionRate)}
              label="Retention Rate"
              delay={900}
            />
          </div>
        </div>
      </div>

    </div>
  );
};

export default ClientMetrics;
