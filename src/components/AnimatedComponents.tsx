import React, { useEffect, useState } from 'react';

// Custom hook for animated percentage counting
export const useAnimatedCounter = (
  targetValue: number, 
  duration: number, 
  delay: number = 0, 
  resetKey?: string
) => {
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    setCurrentValue(0); // Reset to 0 when target changes

    const startTime = Date.now() + delay;
    const incrementInterval = 50; // Update every 50ms for smooth animation

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;

      if (elapsed < 0) return; // Wait for delay

      if (elapsed >= duration) {
        setCurrentValue(targetValue);
        clearInterval(timer);
        return;
      }

      const progress = elapsed / duration;
      // Use ease-out curve for smooth deceleration
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const newValue = targetValue * easedProgress;

      setCurrentValue(Math.min(newValue, targetValue));
    }, incrementInterval);

    return () => clearInterval(timer);
  }, [targetValue, duration, delay, resetKey]);

  return Math.round(currentValue);
};

// Animated Score Display Component
interface AnimatedScoreDisplayProps {
  score: number;
  label: string;
  colorClass: string;
  labelColorClass?: string;
  triggerKey?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const AnimatedScoreDisplay: React.FC<AnimatedScoreDisplayProps> = ({
  score,
  label,
  colorClass,
  labelColorClass = 'text-gray-600',
  triggerKey = 'default',
  size = 'md'
}) => {
  const prefersReducedMotion = useReducedMotion();
  const animatedScore = useAnimatedCounter(
    score,
    prefersReducedMotion ? 0 : 2250,
    0,
    triggerKey
  );

  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl'
  };

  return (
    <div className="text-center" role="img" aria-label={`${label}: ${score}%`}>
      <div
        className={`font-bold ${colorClass} ${sizeClasses[size]} ${
          prefersReducedMotion ? '' : 'transition-all duration-2250 ease-out'
        }`}
        aria-live="polite"
      >
        {animatedScore}%
      </div>
      <div className={`text-sm mt-1 ${labelColorClass}`}>{label}</div>
    </div>
  );
};

// Circular Progress Ring Component
interface CircularProgressProps {
  percentage: number;
  size: number;
  strokeWidth: number;
  color: string;
  children: React.ReactNode;
  delay?: number;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  size,
  strokeWidth,
  color,
  children,
  delay = 0
}) => {
  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage);
    }, prefersReducedMotion ? 0 : delay);

    return () => clearTimeout(timer);
  }, [percentage, delay, prefersReducedMotion]);

  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (animatedPercentage / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center" role="img" aria-label={`Progress: ${percentage}%`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
        aria-hidden="true"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-200"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className={`${color} ${prefersReducedMotion ? '' : 'transition-all duration-1800 ease-out'}`}
          strokeLinecap="round"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        {children}
      </div>
    </div>
  );
};

// Animated Progress Bar Component
interface AnimatedProgressBarProps {
  percentage: number;
  color: string;
  label: string;
  delay?: number;
}

export const AnimatedProgressBar: React.FC<AnimatedProgressBarProps> = ({
  percentage,
  color,
  label,
  delay = 0
}) => {
  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const prefersReducedMotion = useReducedMotion();
  const animatedCount = useAnimatedCounter(
    percentage,
    prefersReducedMotion ? 0 : 1500,
    prefersReducedMotion ? 0 : delay + 300
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage);
    }, prefersReducedMotion ? 0 : delay + 300);

    return () => clearTimeout(timer);
  }, [percentage, delay, prefersReducedMotion]);

  return (
    <div className="mb-4" role="progressbar" aria-valuenow={percentage} aria-valuemin={0} aria-valuemax={100} aria-label={label}>
      <div className="flex justify-between text-sm text-gray-700 dark:text-gray-300 mb-2">
        <span className="font-medium">{label}</span>
        <span className="font-semibold" aria-live="polite">{animatedCount}%</span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3">
        <div
          className={`${color} h-3 rounded-full ${
            prefersReducedMotion ? '' : 'transition-all duration-1500 ease-out'
          }`}
          style={{ width: `${animatedPercentage}%` }}
          aria-hidden="true"
        ></div>
      </div>
    </div>
  );
};

// Data Quality Badge Component
interface DataQualityBadgeProps {
  score: number;
  verified: boolean;
  label: string;
}

export const DataQualityBadge: React.FC<DataQualityBadgeProps> = ({
  score,
  verified,
  label
}) => {
  const animatedScore = useAnimatedCounter(score, 1500, 300); // 1500ms duration, 300ms delay

  const getColorClass = (score: number) => {
    if (score >= 80) return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800';
    if (score >= 60) return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800';
    return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800';
  };

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${getColorClass(score)}`}>
      <span className="text-sm font-medium">{label}</span>
      <span className="text-sm font-bold">{animatedScore}%</span>
      {verified && (
        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  );
};

// Motion preference support
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};
