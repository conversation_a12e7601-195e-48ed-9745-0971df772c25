import React, { useState } from 'react';
import { Plus, Trash2, Star, <PERSON>Off } from 'lucide-react';
import EmailInputWithVerification from './EmailInputWithVerification';
import { ClientEmail } from '../contexts/ClientContext';

interface MultiEmailManagerProps {
  emails: ClientEmail[];
  onChange: (emails: ClientEmail[]) => void;
  disabled?: boolean;
  maxEmails?: number;
}

const MultiEmailManager: React.FC<MultiEmailManagerProps> = ({
  emails,
  onChange,
  disabled = false,
  maxEmails = 5
}) => {
  const [nextId, setNextId] = useState(emails.length + 1);

  const addEmail = () => {
    if (emails.length >= maxEmails) return;

    const newEmail: ClientEmail = {
      id: `new-${nextId}`,
      emailAddress: '',
      isPrimary: emails.length === 0, // First email is primary by default
      emailVerified: true,
      emailScore: 0,
      emailDeliverability: undefined
    };

    onChange([...emails, newEmail]);
    setNextId(nextId + 1);
  };

  const removeEmail = (id: string) => {
    const updatedEmails = emails.filter(email => email.id !== id);
    
    // If we removed the primary email and there are still emails left, make the first one primary
    if (updatedEmails.length > 0 && !updatedEmails.some(email => email.isPrimary)) {
      updatedEmails[0].isPrimary = true;
    }
    
    onChange(updatedEmails);
  };

  const updateEmail = (id: string, updates: Partial<ClientEmail>) => {
    const updatedEmails = emails.map(email => {
      if (email.id === id) {
        return { ...email, ...updates };
      }
      return email;
    });
    
    onChange(updatedEmails);
  };

  const setPrimaryEmail = (id: string) => {
    const updatedEmails = emails.map(email => ({
      ...email,
      isPrimary: email.id === id
    }));
    
    onChange(updatedEmails);
  };

  const getDeliverabilityBadge = (deliverability?: string) => {
    if (!deliverability) return null;

    let badgeClass = 'px-2 py-1 text-xs rounded';
    
    if (deliverability.toLowerCase().includes('deliverable')) {
      badgeClass += ' bg-green-100 text-green-700';
    } else if (deliverability.toLowerCase().includes('undeliverable')) {
      badgeClass += ' bg-red-100 text-red-700';
    } else {
      badgeClass += ' bg-gray-100 text-gray-700';
    }

    return (
      <span className={badgeClass}>
        {deliverability}
      </span>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Email Addresses
        </label>
        {!disabled && emails.length < maxEmails && (
          <button
            type="button"
            onClick={addEmail}
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Email
          </button>
        )}
      </div>

      <div className="space-y-3">
        {emails.map((email, index) => (
          <div key={email.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">
                  Email {index + 1}
                </span>
                {email.isPrimary && (
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                    Primary
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                {!email.isPrimary && emails.length > 1 && !disabled && (
                  <button
                    type="button"
                    onClick={() => setPrimaryEmail(email.id)}
                    className="text-gray-400 hover:text-yellow-500"
                    title="Set as primary"
                  >
                    <StarOff className="w-4 h-4" />
                  </button>
                )}
                
                {email.isPrimary && (
                  <Star className="w-4 h-4 text-yellow-500" title="Primary email" />
                )}
                
                {emails.length > 1 && !disabled && (
                  <button
                    type="button"
                    onClick={() => removeEmail(email.id)}
                    className="text-gray-400 hover:text-red-500"
                    title="Remove email"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            <EmailInputWithVerification
              value={email.emailAddress}
              verified={email.emailVerified}
              onChange={(value) => updateEmail(email.id, { emailAddress: value })}
              onVerificationChange={(verified) => updateEmail(email.id, { emailVerified: verified })}
              disabled={disabled}
              placeholder="Enter email address"
            />
            
            {email.emailDeliverability && (
              <div className="mt-2 flex items-center gap-2">
                <span className="text-xs text-gray-500">Deliverability:</span>
                {getDeliverabilityBadge(email.emailDeliverability)}
              </div>
            )}
          </div>
        ))}

        {emails.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p className="text-sm">No email addresses added yet.</p>
            {!disabled && (
              <button
                type="button"
                onClick={addEmail}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm"
              >
                Add your first email address
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MultiEmailManager;
