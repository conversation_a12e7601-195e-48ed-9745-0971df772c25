import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Save, Calculator, Eye, Edit, Copy } from 'lucide-react';
import { useQuotations, Quotation, QuotationItem } from '../contexts/QuotationContext';
import { useClients } from '../contexts/ClientContext';
import { useLeads } from '../contexts/LeadContext';
import { useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';
import { useDeal } from '../contexts/DealContext';
import { apiService } from '../services/api';
import CustomDropdown from './CustomDropdown';

interface QuotationSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  quotation?: Quotation;
  mode: 'create' | 'edit' | 'view';
}

const QuotationSidebar: React.FC<QuotationSidebarProps> = ({ isOpen, onClose, quotation, mode }) => {
  const { createQuotation, updateQuotation, deleteQuotation, duplicateQuotation } = useQuotations();
  const { clients: frontendClients } = useClients();
  const { leads: frontendLeads } = useLeads();
  const { products } = useProducts();
  const { deals } = useDeal();
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !quotation);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Removed backend API calls - using only frontend context data for consistency

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    lead_id: '',
    deal_id: '',
    priority: 'medium' as const,
    tax_rate: 6,
    discount_amount: 0,
    currency: 'MYR',
    validity_days: 30,
    terms_conditions: '',
    notes: '',
    internal_notes: '',
    expected_close_date: '',
  });

  const [items, setItems] = useState<QuotationItem[]>([]);

  // Removed backend API calls - using only frontend context data for consistency

  useEffect(() => {
    if (quotation && (mode === 'edit' || mode === 'view')) {
      setFormData({
        title: quotation.title,
        description: quotation.description || '',
        client_id: quotation.client_id || '',
        lead_id: quotation.lead_id || '',
        deal_id: quotation.deal_id || '',
        priority: quotation.priority,
        tax_rate: quotation.tax_rate,
        discount_amount: quotation.discount_amount,
        currency: quotation.currency,
        validity_days: quotation.validity_days,
        terms_conditions: quotation.terms_conditions || '',
        notes: quotation.notes || '',
        internal_notes: quotation.internal_notes || '',
        expected_close_date: quotation.expected_close_date ? quotation.expected_close_date.split('T')[0] : '',
      });
      setItems(quotation.items || []);
      setIsEditing(mode === 'edit');
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        client_id: '',
        lead_id: '',
        deal_id: '',
        priority: 'medium',
        tax_rate: 6,
        discount_amount: 0,
        currency: 'MYR',
        validity_days: 30,
        terms_conditions: 'Payment terms: 50% advance, 50% upon completion\nAll services will be conducted according to Islamic principles\nDelivery timeline as specified in project schedule',
        notes: '',
        internal_notes: '',
        expected_close_date: '',
      });

      // Initialize with one empty item
      setItems([{
        item_name: '',
        description: '',
        unit: 'pcs',
        quantity: 1,
        unit_price: 0,
        discount_rate: 0,
        discount_amount: 0,
        line_total: 0,
        sort_order: 1,
      }]);
      setIsEditing(true);
    }
    setError(null);
  }, [quotation, mode]);

  // Use only frontend clients for consistency
  const allClients = frontendClients.map(client => ({
    id: client.id,
    name: client.name,
    email: client.email,
    source: 'frontend'
  }));

  // Use only frontend leads for consistency
  const allLeads = frontendLeads.map(lead => ({
    id: lead.id,
    name: lead.name || 'Unnamed',
    email: lead.email,
    company: lead.company,
    source: 'frontend'
  }));

  // Combine clients and leads for dropdown
  const allClientsAndLeads = [
    { value: '', label: 'Select a client or lead' },
    // Clients section
    ...allClients.map(client => ({
      value: client.id,
      label: `${client.name} (Client) ${client.email ? `- ${client.email}` : ''}`
    })),
    // Leads section
    ...allLeads.map(lead => ({
      value: `lead-${lead.id}`,
      label: `${lead.name} (Lead) ${lead.email ? `- ${lead.email}` : ''} ${lead.company ? `- ${lead.company}` : ''}`
    }))
  ];

  const addItem = () => {
    const newItem: QuotationItem = {
      item_name: '',
      description: '',
      unit: 'pcs',
      quantity: 1,
      unit_price: 0,
      discount_rate: 0,
      discount_amount: 0,
      line_total: 0,
      sort_order: items.length + 1,
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof QuotationItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate line total when quantity, unit_price, or discount changes
    if (['quantity', 'unit_price', 'discount_rate', 'discount_amount'].includes(field)) {
      const item = updatedItems[index];
      const subtotal = item.quantity * item.unit_price;
      const discount = item.discount_rate > 0 
        ? subtotal * (item.discount_rate / 100)
        : item.discount_amount;
      updatedItems[index].line_total = subtotal - discount;
    }
    
    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const taxAmount = subtotal * (formData.tax_rate / 100);
    const total = subtotal + taxAmount - formData.discount_amount;
    
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;

    setLoading(true);
    setError(null);

    try {
      // Handle client/lead selection
      let clientId = null;
      let leadId = null;

      if (formData.client_id) {
        if (typeof formData.client_id === 'string' && formData.client_id.startsWith('lead-')) {
          // Lead selected
          leadId = formData.client_id.replace('lead-', '');

        } else if (typeof formData.client_id === 'string' && formData.client_id.startsWith('client-')) {
          // Frontend client selected - don't send to backend

        } else {
          // Backend client selected
          clientId = formData.client_id;

        }
      }

      const quotationData = {
        ...formData,
        client_id: clientId,
        lead_id: leadId,
        items: items.map((item, index) => ({
          ...item,
          sort_order: index + 1,
        })),
      };

      if (mode === 'create') {
        await createQuotation(quotationData);
        showSuccess('Success', 'Quotation created successfully');
      } else if (mode === 'edit' && quotation) {
        await updateQuotation(quotation.id, quotationData);
        showSuccess('Success', 'Quotation updated successfully');
      }

      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Error', `Failed to ${mode === 'create' ? 'create' : 'update'} quotation. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDelete = async () => {
    if (!quotation) return;
    
    setLoading(true);
    try {
      await deleteQuotation(quotation.id);
      showSuccess('Quotation Deleted', 'Quotation has been successfully deleted');
      onClose();
    } catch (error) {
      console.error('Error deleting quotation:', error);
      const errorMessage = 'Failed to delete quotation. Please try again.';
      setError(errorMessage);
      showError('Delete Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async () => {
    if (!quotation) return;
    
    setLoading(true);
    try {
      await duplicateQuotation(quotation.id);
      showSuccess('Quotation Duplicated', 'Quotation has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating quotation:', error);
      const errorMessage = 'Failed to duplicate quotation. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const { subtotal, taxAmount, total } = calculateTotals();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {quotation ? (isEditing ? 'Edit Quotation' : 'Quotation Details') : 'Create New Quotation'}
            </h2>
            {quotation && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{quotation.quotation_number}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {quotation && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-500 dark:text-gray-400"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!isEditing}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Client or Lead
                  </label>
                  <CustomDropdown
                    options={allClientsAndLeads}
                    value={formData.client_id}
                    onChange={(value) => handleInputChange('client_id', value)}
                    disabled={!isEditing}
                    placeholder="Select a client or lead"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Select a client or lead for this quotation.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Associated Deal (Optional)
                  </label>
                  <CustomDropdown
                    options={[
                      { value: '', label: 'No deal association' },
                      ...deals.map(deal => ({
                        value: deal.id,
                        label: `${deal.title} - ${deal.value ? `RM ${deal.value}` : 'No value'} (${deal.status})`
                      }))
                    ]}
                    value={formData.deal_id}
                    onChange={(value) => handleInputChange('deal_id', value)}
                    disabled={!isEditing}
                    placeholder="Select a deal (optional)"
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Optionally associate this quotation with a deal.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Priority
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'low', label: 'Low' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'high', label: 'High' },
                      { value: 'urgent', label: 'Urgent' },
                    ]}
                    value={formData.priority}
                    onChange={(value) => handleInputChange('priority', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Currency
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'MYR', label: 'MYR (Malaysian Ringgit)' },
                      { value: 'USD', label: 'USD (US Dollar)' },
                      { value: 'SGD', label: 'SGD (Singapore Dollar)' },
                    ]}
                    value={formData.currency}
                    onChange={(value) => handleInputChange('currency', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Validity (Days)
                  </label>
                  <input
                    type="number"
                    value={formData.validity_days}
                    onChange={(e) => handleInputChange('validity_days', parseInt(e.target.value) || 30)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="1"
                    max="365"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Items Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Items</h3>
                {isEditing && (
                  <button
                    type="button"
                    onClick={addItem}
                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add Item
                  </button>
                )}
              </div>

              <div className="space-y-4">
                {items.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">Item {index + 1}</h4>
                      {isEditing && items.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeItem(index)}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Item Name *
                        </label>
                        <input
                          type="text"
                          value={item.item_name}
                          onChange={(e) => updateItem(index, 'item_name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                          disabled={!isEditing}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Description
                        </label>
                        <textarea
                          value={item.description || ''}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Unit
                        </label>
                        <input
                          type="text"
                          value={item.unit}
                          onChange={(e) => updateItem(index, 'unit', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quantity *
                        </label>
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0.01"
                          step="0.01"
                          required
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Unit Price ({formData.currency})
                        </label>
                        <input
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          step="0.01"
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Discount (%)
                        </label>
                        <input
                          type="number"
                          value={item.discount_rate || 0}
                          onChange={(e) => updateItem(index, 'discount_rate', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          max="100"
                          step="0.01"
                          disabled={!isEditing}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Line Total
                        </label>
                        <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900 font-medium">
                          {formData.currency} {(item.line_total || 0).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Totals Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Totals</h3>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tax Rate (%)
                    </label>
                    <input
                      type="number"
                      value={formData.tax_rate}
                      onChange={(e) => handleInputChange('tax_rate', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      max="100"
                      step="0.01"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Amount ({formData.currency})
                    </label>
                    <input
                      type="number"
                      value={formData.discount_amount}
                      onChange={(e) => handleInputChange('discount_amount', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.01"
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Subtotal:</span>
                    <span className="text-sm font-medium">{formData.currency} {subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tax ({formData.tax_rate}%):</span>
                    <span className="text-sm font-medium">{formData.currency} {taxAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Discount:</span>
                    <span className="text-sm font-medium">-{formData.currency} {formData.discount_amount.toFixed(2)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between">
                    <span className="font-medium">Total:</span>
                    <span className="font-bold text-lg">{formData.currency} {total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Terms and Notes */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Terms & Notes</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Terms & Conditions
                </label>
                <textarea
                  value={formData.terms_conditions}
                  onChange={(e) => handleInputChange('terms_conditions', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Internal Notes
                </label>
                <textarea
                  value={formData.internal_notes}
                  onChange={(e) => handleInputChange('internal_notes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-800">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-4">
              <div className="flex flex-col sm:flex-row gap-2">
                {quotation && (
                  <>
                    <button
                      type="button"
                      onClick={handleDuplicate}
                      disabled={loading}
                      className="w-full sm:w-auto px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      <Copy className="w-4 h-4" />
                      Duplicate
                    </button>
                    <button
                      type="button"
                      onClick={handleDelete}
                      disabled={loading}
                      className="w-full sm:w-auto px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? 'Saving...' : mode === 'create' ? 'Create Quotation' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuotationSidebar;
