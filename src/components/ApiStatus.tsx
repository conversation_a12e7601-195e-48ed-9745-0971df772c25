import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { CheckCircle, XCircle, Clock } from 'lucide-react';

interface ApiStatusProps {
  className?: string;
}

const ApiStatus: React.FC<ApiStatusProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkApiHealth = async () => {
    setStatus('checking');
    try {
      // Try to fetch a small amount of data to test the connection
      await apiService.get('/deals?per_page=1');
      setStatus('connected');
      setLastChecked(new Date());
    } catch (error) {
      console.error('API health check failed:', error);
      setStatus('disconnected');
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    // Only check once on mount, no automatic polling
    checkApiHealth();

    // Remove automatic polling to prevent excessive API calls
    // const interval = setInterval(checkApiHealth, 30000);
    // return () => clearInterval(interval);
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case 'checking':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'checking':
        return 'Checking API...';
      case 'connected':
        return 'API Connected';
      case 'disconnected':
        return 'API Disconnected';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'checking':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'connected':
        return 'text-green-600 dark:text-green-400';
      case 'disconnected':
        return 'text-red-600 dark:text-red-400';
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {getStatusIcon()}
      <span className={`text-sm font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </span>
      {lastChecked && (
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {lastChecked.toLocaleTimeString()}
        </span>
      )}
      {status === 'disconnected' && (
        <button
          onClick={checkApiHealth}
          className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline ml-2"
        >
          Retry
        </button>
      )}
    </div>
  );
};

export default ApiStatus;
