import React, { useState, useEffect } from 'react';
import { X, Save, Eye, Edit, Trash2, Copy } from 'lucide-react';
import CustomDropdown from './CustomDropdown';
import EmailInputWithVerification from './EmailInputWithVerification';
import PhoneInputWithVerification from './PhoneInputWithVerification';
import MultiEmailManager from './MultiEmailManager';
import MultiPhoneManager from './MultiPhoneManager';
import FormErrorMessage from './FormErrorMessage';
import { Client, ClientEmail, ClientPhoneNumber, useClients } from '../contexts/ClientContext';
import { useToast } from '../contexts/ToastContext';
import { useFormValidation, createValidationHandlers, getValidatedInputProps, getErrorMessageProps } from '../hooks/useFormValidation';
import { VALIDATION_ENUMS } from '../utils/validation';

interface ClientSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  client?: Client;
  mode: 'create' | 'edit' | 'view';
  onScoreChange?: (scores: { nameScore: number; emailScore: number; phoneScore: number }) => void;
}

const ClientSidebar: React.FC<ClientSidebarProps> = ({ isOpen, onClose, client, mode, onScoreChange }) => {
  const { addClient, updateClient, deleteClient } = useClients();
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !client);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form validation
  const validation = useFormValidation({ validateOnChange: true, validateOnBlur: true });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    status: 'active' as Client['status'],
    utmSource: 'facebook',
    tags: [] as string[],
    category: 'First Timer' as Client['category'],
    ltvSegment: 'Silver' as Client['ltvSegment'],
    engagementLevel: 'Cold' as Client['engagementLevel'],
    priority: 'Medium' as Client['priority'],
    notes: '',
    suggestedAction: '',
    lastActivity: new Date(),
    totalSpent: 0,
    transactionCount: 0,
    customFields: {},
    emailVerified: false,
    phoneVerified: false,
    // Scoring fields
    nameScore: 0,
    emailScore: 0,
    phoneScore: 0,
    // Personal information fields
    icNumber: '',
    birthday: '',
    gender: '' as Client['gender'] | '',
    religion: '' as Client['religion'] | '',
    // Financial information
    income: 0,
    incomeCategory: '' as Client['incomeCategory'] | '',
    // Enhanced address fields
    addressLine1: '',
    addressLine2: '',
    city: '',
    postcode: '',
    state: '',
    // Behavioral data
    behaviour: '',
    interest: '',
    // Multi-contact arrays
    phoneNumbers: [] as ClientPhoneNumber[],
    emails: [] as ClientEmail[],
  });

  // Validation handlers
  const handlers = createValidationHandlers(validation, formData, setFormData, {
    validateOnChange: true,
    validateOnBlur: true
  });

  useEffect(() => {
    if (client && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: client.name,
        email: client.email,
        phone: client.phone,
        address: client.address,
        status: client.status,
        utmSource: client.utmSource,
        tags: Array.isArray(client.tags) ? client.tags : [],
        category: client.category,
        ltvSegment: client.ltvSegment,
        engagementLevel: client.engagementLevel,
        priority: client.priority,
        notes: client.notes,
        suggestedAction: client.suggestedAction,
        lastActivity: client.lastActivity,
        totalSpent: client.totalSpent,
        transactionCount: client.transactionCount,
        customFields: client.customFields,
        emailVerified: client.emailVerified ?? true,
        phoneVerified: client.phoneVerified ?? true,
        // Scoring fields
        nameScore: client.nameScore || 0,
        emailScore: client.emailScore || 0,
        phoneScore: client.phoneScore || 0,
        // Personal information fields
        icNumber: client.icNumber || '',
        birthday: client.birthday ? client.birthday.toISOString().split('T')[0] : '',
        gender: client.gender || '',
        religion: client.religion || '',
        // Financial information
        income: client.income || 0,
        incomeCategory: client.incomeCategory || '',
        // Enhanced address fields
        addressLine1: client.addressLine1 || '',
        addressLine2: client.addressLine2 || '',
        city: client.city || '',
        postcode: client.postcode || '',
        state: client.state || '',
        // Behavioral data
        behaviour: client.behaviour || '',
        interest: client.interest || '',
        // Multi-contact arrays - create from main table data if multi-contact arrays are empty
        phoneNumbers: client.phoneNumbers && client.phoneNumbers.length > 0
          ? client.phoneNumbers
          : client.phone
            ? [{
                id: 'primary-phone',
                phoneNumber: client.phone,
                isPrimary: true,
                phoneVerified: client.phoneVerified ?? true,
                phoneScore: client.phoneScore || 0,
                phoneCarrier: client.phoneCarrier
              }]
            : [],
        emails: client.emails && client.emails.length > 0
          ? client.emails
          : client.email
            ? [{
                id: 'primary-email',
                emailAddress: client.email,
                isPrimary: true,
                emailVerified: client.emailVerified ?? true,
                emailScore: client.emailScore || 0,
                emailDeliverability: client.emailDeliverability
              }]
            : [],
      });
      setIsEditing(mode === 'edit');
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        status: 'active',
        utmSource: 'facebook',
        tags: [],
        category: 'First Timer',
        ltvSegment: 'Silver',
        engagementLevel: 'Cold',
        priority: 'Medium',
        notes: '',
        suggestedAction: '',
        lastActivity: new Date(),
        totalSpent: 0,
        transactionCount: 0,
        customFields: {},
        emailVerified: false,
        phoneVerified: false,
        // Scoring fields
        nameScore: 0,
        emailScore: 0,
        phoneScore: 0,
        // Personal information fields
        icNumber: '',
        birthday: '',
        gender: '',
        religion: '',
        // Financial information
        income: 0,
        incomeCategory: '',
        // Enhanced address fields
        addressLine1: '',
        addressLine2: '',
        city: '',
        postcode: '',
        state: '',
        // Behavioral data
        behaviour: '',
        interest: '',
        // Multi-contact arrays
        phoneNumbers: [],
        emails: [],
      });
      setIsEditing(true);
    }
    setError(null);
  }, [client, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;

    // Validate form before submission
    const validationResult = validation.validateForm(formData, 'client');
    if (!validationResult.isValid) {
      setError('Please fix the validation errors before submitting.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (mode === 'create') {
        await addClient(formData);
        showSuccess('Client Created', `${formData.name} has been successfully added to your clients!`);
      } else if (mode === 'edit' && client) {
        await updateClient(client.id, formData);
        showSuccess('Client Updated', `${formData.name} has been successfully updated!`);
      }
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Save Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // If a score field was changed, notify the parent component
      if ((field === 'nameScore' || field === 'emailScore' || field === 'phoneScore') && onScoreChange) {
        onScoreChange({
          nameScore: field === 'nameScore' ? value : newData.nameScore,
          emailScore: field === 'emailScore' ? value : newData.emailScore,
          phoneScore: field === 'phoneScore' ? value : newData.phoneScore,
        });
      }

      return newData;
    });
  };

  const handleDelete = async () => {
    if (!client) return;
    
    setLoading(true);
    try {
      deleteClient(client.id);
      showSuccess('Client Deleted', 'Client has been successfully deleted');
      onClose();
    } catch (error) {
      console.error('Error deleting client:', error);
      const errorMessage = 'Failed to delete client. Please try again.';
      setError(errorMessage);
      showError('Delete Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async () => {
    if (!client) return;
    
    setLoading(true);
    try {
      const duplicateData = {
        ...formData,
        name: `${formData.name} (Copy)`,
        email: '', // Clear email to avoid duplicates
      };
      
      await addClient(duplicateData);
      showSuccess('Client Duplicated', 'Client has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating client:', error);
      const errorMessage = 'Failed to duplicate client. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    handleInputChange('tags', tags);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {client ? (isEditing ? 'Edit Client' : 'Client Details') : 'Create New Client'}
            </h2>
            {client && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{client.email}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {client && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto bg-white dark:bg-gray-800">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </div>
            )}

            {/* Validation debug info (only in development) */}
            {process.env.NODE_ENV === 'development' && !validation.isFormValid && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
                <p className="text-sm text-yellow-800 dark:text-yellow-300 font-medium">Form Validation Issues:</p>
                <ul className="text-xs text-yellow-700 dark:text-yellow-400 mt-1">
                  {validation.errors.map((error, index) => (
                    <li key={index}>• {error.field}: {error.message}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    {...getValidatedInputProps('name', validation, formData, handlers)}
                    disabled={!isEditing}
                    required
                  />
                  <FormErrorMessage {...getErrorMessageProps('name', validation)} />
                </div>

                <MultiEmailManager
                  emails={formData.emails}
                  onChange={(emails) => handleInputChange('emails', emails)}
                  disabled={!isEditing}
                  maxEmails={5}
                />

                <MultiPhoneManager
                  phoneNumbers={formData.phoneNumbers}
                  onChange={(phoneNumbers) => handleInputChange('phoneNumbers', phoneNumbers)}
                  disabled={!isEditing}
                  maxPhones={5}
                />

              </div>
            </div>

            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Personal Information</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    IC Number
                  </label>
                  <input
                    type="text"
                    value={formData.icNumber}
                    onChange={(e) => handleInputChange('icNumber', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Enter IC number"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Birthday
                  </label>
                  <input
                    type="date"
                    value={formData.birthday}
                    onChange={(e) => handleInputChange('birthday', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    disabled={!isEditing}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gender
                    </label>
                    <CustomDropdown
                      options={[
                        { value: '', label: 'Select Gender' },
                        { value: 'Male', label: 'Male' },
                        { value: 'Female', label: 'Female' }
                      ]}
                      value={formData.gender}
                      onChange={(value) => handleInputChange('gender', value)}
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Religion
                    </label>
                    <CustomDropdown
                      options={[
                        { value: '', label: 'Select Religion' },
                        { value: 'Muslim', label: 'Muslim' },
                        { value: 'Non-Muslim', label: 'Non-Muslim' }
                      ]}
                      value={formData.religion}
                      onChange={(value) => handleInputChange('religion', value)}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Monthly Income (RM)
                    </label>
                    <input
                      type="number"
                      value={formData.income}
                      onChange={(e) => handleInputChange('income', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Income Category
                    </label>
                    <CustomDropdown
                      options={[
                        { value: '', label: 'Select Category' },
                        { value: 'Low', label: 'Low' },
                        { value: 'Medium', label: 'Medium' },
                        { value: 'High', label: 'High' }
                      ]}
                      value={formData.incomeCategory}
                      onChange={(value) => handleInputChange('incomeCategory', value)}
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Address Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Address Information</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address Line 1
                  </label>
                  <input
                    type="text"
                    value={formData.addressLine1}
                    onChange={(e) => handleInputChange('addressLine1', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter address line 1"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address Line 2
                  </label>
                  <input
                    type="text"
                    value={formData.addressLine2}
                    onChange={(e) => handleInputChange('addressLine2', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter address line 2 (optional)"
                    disabled={!isEditing}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City
                    </label>
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter city"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Postcode
                    </label>
                    <input
                      type="text"
                      value={formData.postcode}
                      onChange={(e) => handleInputChange('postcode', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter postcode"
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      State
                    </label>
                    <input
                      type="text"
                      value={formData.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter state"
                      disabled={!isEditing}
                    />
                  </div>

                </div>
              </div>
            </div>

            {/* Classification */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Classification</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status *
                  </label>
                  <CustomDropdown
                    options={VALIDATION_ENUMS.CLIENT_STATUS.map(status => ({ value: status, label: status }))}
                    value={formData.status}
                    onChange={handlers.handleSelectChange('status')}
                    disabled={!isEditing}
                    className={validation.hasFieldError('status') ? 'border-red-500' : ''}
                  />
                  <FormErrorMessage {...getErrorMessageProps('status', validation)} />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <CustomDropdown
                    options={VALIDATION_ENUMS.CLIENT_CATEGORY.map(category => ({ value: category, label: category }))}
                    value={formData.category}
                    onChange={handlers.handleSelectChange('category')}
                    disabled={!isEditing}
                    className={validation.hasFieldError('category') ? 'border-red-500' : ''}
                  />
                  <FormErrorMessage {...getErrorMessageProps('category', validation)} />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    LTV Segment
                  </label>
                  <CustomDropdown
                    options={VALIDATION_ENUMS.LTV_SEGMENT.map(segment => ({ value: segment, label: segment }))}
                    value={formData.ltvSegment}
                    onChange={handlers.handleSelectChange('ltvSegment')}
                    disabled={!isEditing}
                    className={validation.hasFieldError('ltvSegment') ? 'border-red-500' : ''}
                  />
                  <FormErrorMessage {...getErrorMessageProps('ltvSegment', validation)} />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Engagement Level
                  </label>
                  <CustomDropdown
                    options={VALIDATION_ENUMS.ENGAGEMENT_LEVEL.map(level => ({ value: level, label: level }))}
                    value={formData.engagementLevel}
                    onChange={handlers.handleSelectChange('engagementLevel')}
                    disabled={!isEditing}
                    className={validation.hasFieldError('engagementLevel') ? 'border-red-500' : ''}
                  />
                  <FormErrorMessage {...getErrorMessageProps('engagementLevel', validation)} />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                  </label>
                  <CustomDropdown
                    options={VALIDATION_ENUMS.PRIORITY.map(priority => ({ value: priority, label: priority }))}
                    value={formData.priority}
                    onChange={handlers.handleSelectChange('priority')}
                    disabled={!isEditing}
                    className={validation.hasFieldError('priority') ? 'border-red-500' : ''}
                  />
                  <FormErrorMessage {...getErrorMessageProps('priority', validation)} />
                </div>
              </div>
            </div>

            {/* UTM & Tracking */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">UTM & Tracking</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    UTM Source
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'facebook', label: 'Facebook' },
                      { value: 'instagram', label: 'Instagram' },
                      { value: 'tiktok', label: 'TikTok' },
                      { value: 'google', label: 'Google' },
                      { value: 'youtube', label: 'YouTube' },
                      { value: 'whatsapp', label: 'WhatsApp' },
                      { value: 'referral', label: 'Referral' },
                      { value: 'website', label: 'Website' },
                      { value: 'email', label: 'Email' },
                      { value: 'phone', label: 'Phone' },
                    ]}
                    value={formData.utmSource}
                    onChange={(value) => handleInputChange('utmSource', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    value={Array.isArray(formData.tags) ? formData.tags.join(', ') : ''}
                    onChange={(e) => handleTagsChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter tags separated by commas"
                    disabled={!isEditing}
                  />
                  <p className="text-xs text-gray-500 mt-1">Separate multiple tags with commas</p>
                </div>
              </div>
            </div>

            {/* Financial Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Financial Information</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Spent (RM)
                  </label>
                  <input
                    type="number"
                    value={formData.totalSpent}
                    onChange={(e) => handleInputChange('totalSpent', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step="0.01"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Transaction Count
                  </label>
                  <input
                    type="number"
                    value={formData.transactionCount}
                    onChange={(e) => handleInputChange('transactionCount', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Scoring Fields */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Scoring</h3>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Name Score
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max="10"
                      step="1"
                      value={formData.nameScore}
                      onChange={(e) => handleInputChange('nameScore', parseInt(e.target.value) || 0)}
                      className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-600 dark:accent-blue-400"
                      disabled={!isEditing}
                      title="Score based on name completeness and quality (0-10 points)"
                    />
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">0</span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                        {formData.nameScore}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">10</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Score
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max="50"
                      step="1"
                      value={formData.emailScore}
                      onChange={(e) => handleInputChange('emailScore', parseInt(e.target.value) || 0)}
                      className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-600 dark:accent-blue-400"
                      disabled={!isEditing}
                      title="Score based on email validity, deliverability, and verification (0-50 points)"
                    />
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">0</span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                        {formData.emailScore}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">50</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Score
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max="40"
                      step="1"
                      value={formData.phoneScore}
                      onChange={(e) => handleInputChange('phoneScore', parseInt(e.target.value) || 0)}
                      className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-600 dark:accent-blue-400"
                      disabled={!isEditing}
                      title="Score based on phone validity, carrier quality, and verification (0-40 points)"
                    />
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">0</span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                        {formData.phoneScore}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">40</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Behavioral Data */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Behavioral Data</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Behavior
                  </label>
                  <textarea
                    value={formData.behaviour}
                    onChange={(e) => handleInputChange('behaviour', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Describe client behavior patterns..."
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Interests
                  </label>
                  <textarea
                    value={formData.interest}
                    onChange={(e) => handleInputChange('interest', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="List client interests and preferences..."
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Notes & Actions */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Notes & Actions</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Suggested Action
                </label>
                <textarea
                  value={formData.suggestedAction}
                  onChange={(e) => handleInputChange('suggestedAction', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4 sm:p-6 bg-gray-50 dark:bg-gray-800">
            <div className="space-y-3">

              {/* Desktop: Secondary Actions - Horizontal Layout (moved above primary button) */}
              <div className="hidden sm:flex sm:space-x-2">
                {client && (
                  <button
                    type="button"
                    onClick={handleDuplicate}
                    disabled={loading}
                    className="w-full px-3 py-2 bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px]"
                  >
                    <Copy className="w-4 h-4" />
                    <span className="hidden sm:inline">Duplicate</span>
                  </button>
                )}
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full px-3 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px]"
                >
                  <X className="w-4 h-4" />
                  <span className="hidden sm:inline">Cancel</span>
                </button>
                {client && (
                  <button
                    type="button"
                    onClick={handleDelete}
                    disabled={loading}
                    className="w-full px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px]"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span className="hidden sm:inline">Delete</span>
                  </button>
                )}
              </div>

              {/* Desktop: Primary Action Button - Full Width (moved to bottom) */}
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={loading || !validation.isFormValid}
                className={`hidden sm:flex w-full px-4 py-3 rounded-lg transition-colors items-center justify-center gap-2 font-medium min-h-[44px] ${
                  loading || !validation.isFormValid
                    ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                <Save className="w-4 h-4" />
                {loading ? 'Saving...' : mode === 'create' ? 'Create Client' : 'Save Changes'}
              </button>

              {/* Mobile: 3-row layout structure */}
              <div className="sm:hidden space-y-2">
                {/* Row 1: Duplicate + Delete buttons (50/50 split) - only for existing clients */}
                {client && (
                  <div className="grid gap-2" style={{ gridTemplateColumns: '1fr 1fr' }}>
                    <button
                      type="button"
                      onClick={handleDuplicate}
                      disabled={loading}
                      className="px-3 py-2 bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px]"
                    >
                      <Copy className="w-4 h-4" />
                      Duplicate
                    </button>
                    <button
                      type="button"
                      onClick={handleDelete}
                      disabled={loading}
                      className="px-3 py-2 bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px]"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </div>
                )}

                {/* Row 2: Cancel Button - Full Width */}
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full px-3 py-2 bg-gray-600 text-white hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px]"
                >
                  <X className="w-4 h-4" />
                  Cancel
                </button>

                {/* Row 3: Save Changes Button - Full Width */}
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading || !validation.isFormValid}
                  className={`w-full px-3 py-2 rounded-lg transition-colors flex items-center justify-center gap-2 min-h-[44px] ${
                    loading || !validation.isFormValid
                      ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  <Save className="w-4 h-4" />
                  {loading ? 'Saving...' : mode === 'create' ? 'Create Client' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientSidebar;
