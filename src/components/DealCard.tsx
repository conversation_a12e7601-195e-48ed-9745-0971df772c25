import React, { useState } from 'react';
import { Deal, DEAL_PRIORITIES, DEAL_SIZES } from '../types/deal';
import {
  Calendar,
  DollarSign,
  User,
  Building2,
  Clock,
  AlertTriangle,
  TrendingUp,
  Tag,
  MoreVertical,
  FileText,
  Receipt,
  Edit,
  Trash2,
  Eye,
  Copy
} from 'lucide-react';
import ActionButtons from './ActionButtons';

interface DealCardProps {
  deal: Deal;
  onClick: () => void;
  onCreateQuotation?: (deal: Deal) => void;
  onGenerateInvoice?: (deal: Deal) => void;
  onEdit?: (deal: Deal) => void;
  onDuplicate?: (deal: Deal) => void;
  onDelete?: (deal: Deal) => void;
}

const DealCard: React.FC<DealCardProps> = ({
  deal,
  onClick,
  onCreateQuotation,
  onGenerateInvoice,
  onEdit,
  onDuplicate,
  onDelete
}) => {
  const [showActions, setShowActions] = useState(false);
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-MY', {
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  };

  const isOverdue = deal.expectedCloseDate && new Date(deal.expectedCloseDate) < new Date();
  const priorityConfig = DEAL_PRIORITIES[deal.priority];

  return (
    <div
      onClick={onClick}
      className="bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-all duration-200"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClick();
            }}
            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
            title="View Deal"
          >
            <Eye className="w-4 h-4" />
          </button>
          <div className="flex-1 min-w-0">
            <h4 className="font-semibold text-gray-900 text-sm leading-tight truncate">
              {deal.title}
            </h4>
            <p className="text-xs text-gray-500 mt-1">{deal.dealNumber}</p>
          </div>
        </div>

        <div className="flex items-center gap-1 ml-2">
          {/* Priority Badge */}
          <span
            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
            style={{
              color: priorityConfig.color,
              backgroundColor: priorityConfig.bgColor,
            }}
          >
            {deal.priority}
          </span>

          {/* Action Buttons */}
          <div onClick={(e) => e.stopPropagation()}>
            <ActionButtons
              size="sm"
              actions={[
                {
                  label: 'Edit',
                  icon: Edit,
                  onClick: () => onEdit?.(deal),
                  color: 'green'
                },
                // Conditional action for Proposal stage
                ...(deal.pipelineStage === 'proposal' ? [{
                  label: 'Create Quotation',
                  icon: FileText,
                  onClick: () => onCreateQuotation?.(deal),
                  color: 'purple'
                }] : []),
                // Conditional action for Won stage
                ...(deal.pipelineStage === 'won' ? [{
                  label: 'Create Invoice',
                  icon: Receipt,
                  onClick: () => onGenerateInvoice?.(deal),
                  color: 'green'
                }] : []),
                {
                  label: 'Duplicate',
                  icon: Copy,
                  onClick: () => onDuplicate?.(deal),
                  color: 'blue'
                },
                {
                  label: 'Delete',
                  icon: Trash2,
                  onClick: () => onDelete?.(deal),
                  color: 'red'
                }
              ]}
            />
          </div>
        </div>
      </div>

      {/* Deal Value */}
      <div className="flex items-center gap-2 mb-3">
        <DollarSign className="h-4 w-4 text-green-600" />
        <span className="font-bold text-green-700">{formatCurrency(deal.value)}</span>
        <span className="text-xs text-gray-500">• {deal.dealSize}</span>
      </div>

      {/* Lead/Prospect Info */}
      {deal.lead ? (
        <div className="flex items-center gap-2 mb-3">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-sm text-blue-700 truncate">{deal.lead.name}</span>
          <span className="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded">Lead</span>
        </div>
      ) : deal.leadId ? (
        <div className="flex items-center gap-2 mb-3">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-sm text-blue-700 truncate">Lead ID: {deal.leadId}</span>
          <span className="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded">Lead</span>
        </div>
      ) : (
        <div className="flex items-center gap-2 mb-3">
          <User className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-500 truncate">No lead assigned</span>
        </div>
      )}

      {/* Client Info */}
      {deal.client && (
        <div className="flex items-center gap-2 mb-3">
          <Building2 className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-700 truncate">{deal.client.name}</span>
        </div>
      )}

      {/* Assigned User */}
      {deal.assignedTo && (
        <div className="flex items-center gap-2 mb-3">
          <User className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-700 truncate">{deal.assignedTo.name}</span>
        </div>
      )}

      {/* Probability Bar */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-gray-500">Probability</span>
          <span className="text-xs font-medium text-gray-700">{deal.probability}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${deal.probability}%` }}
          />
        </div>
      </div>

      {/* Expected Close Date */}
      {deal.expectedCloseDate && (
        <div className={`flex items-center gap-2 mb-3 ${isOverdue ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'}`}>
          {isOverdue ? (
            <AlertTriangle className="h-4 w-4" />
          ) : (
            <Calendar className="h-4 w-4" />
          )}
          <span className="text-sm">
            {isOverdue ? 'Overdue: ' : 'Due: '}
            {formatDate(deal.expectedCloseDate)}
          </span>
        </div>
      )}

      {/* Days in Stage */}
      <div className="flex items-center gap-2 mb-3">
        <Clock className="h-4 w-4 text-gray-400 dark:text-gray-500" />
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {deal.daysInCurrentStage} days in stage
        </span>
      </div>

      {/* Tags */}
      {deal.tags.length > 0 && (
        <div className="flex items-center gap-1 mb-3">
          <Tag className="h-3 w-3 text-gray-400" />
          <div className="flex flex-wrap gap-1">
            {deal.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-blue-100 text-blue-800"
              >
                {tag}
              </span>
            ))}
            {deal.tags.length > 2 && (
              <span className="text-xs text-gray-500">+{deal.tags.length - 2}</span>
            )}
          </div>
        </div>
      )}

      {/* Deal Type */}
      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-500 dark:text-gray-400">{deal.dealType}</span>

        {/* Activity Indicator */}
        {deal.lastActivity && (
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-green-500" />
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {Math.floor((Date.now() - new Date(deal.lastActivity).getTime()) / (24 * 60 * 60 * 1000))}d ago
            </span>
          </div>
        )}
      </div>

      {/* Next Action */}
      {deal.nextAction && (
        <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-orange-400 rounded-full" />
            <span className="text-xs text-gray-700 dark:text-gray-300 truncate">{deal.nextAction}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default DealCard;
