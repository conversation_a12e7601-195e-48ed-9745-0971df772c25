import React, { useState, useEffect } from 'react';
import { Save } from 'lucide-react';
import Modal from './Modal';
import CustomDropdown from './CustomDropdown';
import PhoneInput from './PhoneInput';
import FormErrorMessage from './FormErrorMessage';
import { Lead, useLeads } from '../contexts/LeadContext';
import { useToast } from '../contexts/ToastContext';
import { useFormValidation, createValidationHandlers, getValidatedInputProps, getErrorMessageProps } from '../hooks/useFormValidation';
import { VALIDATION_ENUMS } from '../utils/validation';

interface LeadModalProps {
  isOpen: boolean;
  onClose: () => void;
  lead?: Lead;
  mode: 'create' | 'edit' | 'view';
}

const LeadModal: React.FC<LeadModalProps> = ({ isOpen, onClose, lead, mode }) => {
  const { addLead, updateLead } = useLeads();
  const { showSuccess, showError } = useToast();

  // Form validation
  const validation = useFormValidation({ validateOnChange: true, validateOnBlur: true });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    utmSource: 'facebook',
    utmCampaign: '',
    utmMedium: '',
    utmContent: '',
    utmTerm: '',
    channel: '',
    leadType: 'capture' as Lead['leadType'],
    tags: [] as string[],
    title: '',
    description: '',
    status: 'new' as Lead['status'],
    opportunityStatus: 'contacted' as Lead['opportunityStatus'],
    priority: 'Medium' as Lead['priority'],
    engagementLevel: 'Cold' as Lead['engagementLevel'],
    ltvSegment: 'Silver' as Lead['ltvSegment'],
    source: '',
    assignedTo: '',
    estimatedValue: 0,
    probability: 10,
    expectedCloseDate: undefined as Date | undefined,
    notes: '',
    internalRemarks: '',
    suggestedAction: '',
    lastActivity: new Date(),
    lastContacted: undefined as Date | undefined,
    lifecycleStage: 0,
    documents: [] as string[],
    isRecycled: false,
    recycledAt: undefined as Date | undefined,
    convertedAt: undefined as Date | undefined,
    convertedToClientId: '',
    clientId: '',
  });

  // Validation handlers
  const handlers = createValidationHandlers(validation, formData, setFormData, {
    validateOnChange: true,
    validateOnBlur: true
  });

  useEffect(() => {
    if (lead && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: lead.name || '',
        email: lead.email || '',
        phone: lead.phone || '',
        company: lead.company || '',
        utmSource: lead.utmSource || 'facebook',
        utmCampaign: lead.utmCampaign || '',
        utmMedium: lead.utmMedium || '',
        utmContent: lead.utmContent || '',
        utmTerm: lead.utmTerm || '',
        channel: lead.channel || '',
        leadType: lead.leadType,
        tags: lead.tags,
        title: lead.title || '',
        description: lead.description || '',
        status: lead.status,
        opportunityStatus: lead.opportunityStatus || 'contacted',
        priority: lead.priority,
        engagementLevel: lead.engagementLevel,
        ltvSegment: lead.ltvSegment || 'Silver',
        source: lead.source || '',
        assignedTo: lead.assignedTo || '',
        estimatedValue: lead.estimatedValue || 0,
        probability: lead.probability || 10,
        expectedCloseDate: lead.expectedCloseDate,
        notes: lead.notes || '',
        internalRemarks: lead.internalRemarks || '',
        suggestedAction: lead.suggestedAction || '',
        lastActivity: lead.lastActivity || new Date(),
        lastContacted: lead.lastContacted,
        lifecycleStage: lead.lifecycleStage,
        documents: lead.documents || [],
        isRecycled: lead.isRecycled,
        recycledAt: lead.recycledAt,
        convertedAt: lead.convertedAt,
        convertedToClientId: lead.convertedToClientId || '',
        clientId: lead.clientId || '',
      });
    } else {
      // Reset form for new lead
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        utmSource: 'facebook',
        utmCampaign: '',
        utmMedium: '',
        utmContent: '',
        utmTerm: '',
        channel: '',
        leadType: 'capture',
        tags: [],
        title: '',
        description: '',
        status: 'new',
        opportunityStatus: 'contacted',
        priority: 'Medium',
        engagementLevel: 'Cold',
        ltvSegment: 'Silver',
        source: '',
        assignedTo: '',
        estimatedValue: 0,
        probability: 10,
        expectedCloseDate: undefined,
        notes: '',
        internalRemarks: '',
        suggestedAction: '',
        lastActivity: new Date(),
        lastContacted: undefined,
        lifecycleStage: 0,
        documents: [],
        isRecycled: false,
        recycledAt: undefined,
        convertedAt: undefined,
        convertedToClientId: '',
        clientId: '',
      });
    }
  }, [lead, mode]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    const validationResult = validation.validateForm(formData, 'lead');
    if (!validationResult.isValid) {
      showError('Please fix the validation errors before submitting.');
      return;
    }

    try {
      if (mode === 'create') {
        addLead(formData);
        showSuccess('Lead Created', `${formData.name} has been successfully added to your leads!`);
      } else if (mode === 'edit' && lead) {
        updateLead(lead.id, formData);
        showSuccess('Lead Updated', `${formData.name} has been successfully updated!`);
      }

      onClose();
    } catch (error) {
      console.error('Error saving lead:', error);
      showError('Save Failed', error instanceof Error ? error.message : 'Failed to save the lead. Please try again.');
    }
  };

  const isReadOnly = mode === 'view';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Add New Lead' : mode === 'edit' ? 'Edit Lead' : 'Lead Details'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              type="text"
              {...getValidatedInputProps('name', validation, formData, handlers)}
              required
              disabled={isReadOnly}
            />
            <FormErrorMessage {...getErrorMessageProps('name', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <input
              type="email"
              {...getValidatedInputProps('email', validation, formData, handlers)}
              required
              disabled={isReadOnly}
            />
            <FormErrorMessage {...getErrorMessageProps('email', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <PhoneInput
              value={formData.phone}
              onChange={handlers.handleSelectChange('phone')}
              placeholder="Enter phone number"
              disabled={isReadOnly}
              className={`w-full ${validation.hasFieldError('phone') ? 'border-red-500' : ''}`}
            />
            <FormErrorMessage {...getErrorMessageProps('phone', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company
            </label>
            <input
              type="text"
              {...getValidatedInputProps('company', validation, formData, handlers)}
              disabled={isReadOnly}
            />
            <FormErrorMessage {...getErrorMessageProps('company', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              UTM Source
            </label>
            <CustomDropdown
              options={[
                { value: 'google', label: 'Google' },
                { value: 'facebook', label: 'Facebook' },
                { value: 'instagram', label: 'Instagram' },
                { value: 'tiktok', label: 'TikTok' },
                { value: 'youtube', label: 'YouTube' },
                { value: 'whatsapp', label: 'WhatsApp' },
                { value: 'linkedin', label: 'LinkedIn' },
                { value: 'twitter', label: 'Twitter' },
                { value: 'email', label: 'Email' },
                { value: 'referral', label: 'Referral' },
                { value: 'direct', label: 'Direct' },
                { value: 'other', label: 'Other' },
              ]}
              value={formData.utmSource}
              onChange={(value) => setFormData({ ...formData, utmSource: value })}
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status *
            </label>
            <CustomDropdown
              options={VALIDATION_ENUMS.LEAD_STATUS.map(status => ({ value: status, label: status }))}
              value={formData.status}
              onChange={handlers.handleSelectChange('status')}
              disabled={isReadOnly}
              className={validation.hasFieldError('status') ? 'border-red-500' : ''}
            />
            <FormErrorMessage {...getErrorMessageProps('status', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Lead Type *
            </label>
            <CustomDropdown
              options={VALIDATION_ENUMS.LEAD_TYPE.map(type => ({ value: type, label: type }))}
              value={formData.leadType}
              onChange={handlers.handleSelectChange('leadType')}
              disabled={isReadOnly}
              className={validation.hasFieldError('leadType') ? 'border-red-500' : ''}
            />
            <FormErrorMessage {...getErrorMessageProps('leadType', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LTV Segment
            </label>
            <CustomDropdown
              options={VALIDATION_ENUMS.LTV_SEGMENT.map(segment => ({ value: segment, label: segment }))}
              value={formData.ltvSegment || 'Silver'}
              onChange={handlers.handleSelectChange('ltvSegment')}
              disabled={isReadOnly}
              className={validation.hasFieldError('ltvSegment') ? 'border-red-500' : ''}
            />
            <FormErrorMessage {...getErrorMessageProps('ltvSegment', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Engagement Level
            </label>
            <CustomDropdown
              options={VALIDATION_ENUMS.ENGAGEMENT_LEVEL.map(level => ({ value: level, label: level }))}
              value={formData.engagementLevel}
              onChange={handlers.handleSelectChange('engagementLevel')}
              disabled={isReadOnly}
              className={validation.hasFieldError('engagementLevel') ? 'border-red-500' : ''}
            />
            <FormErrorMessage {...getErrorMessageProps('engagementLevel', validation)} />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <CustomDropdown
              options={VALIDATION_ENUMS.PRIORITY.map(priority => ({ value: priority, label: priority }))}
              value={formData.priority}
              onChange={handlers.handleSelectChange('priority')}
              disabled={isReadOnly}
              className={validation.hasFieldError('priority') ? 'border-red-500' : ''}
            />
            <FormErrorMessage {...getErrorMessageProps('priority', validation)} />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Address
          </label>
          <textarea
            value={formData.internalRemarks}
            onChange={(e) => setFormData({ ...formData, internalRemarks: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
            placeholder="Enter address or location details..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            rows={4}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Suggested Action
          </label>
          <input
            type="text"
            value={formData.suggestedAction}
            onChange={(e) => setFormData({ ...formData, suggestedAction: e.target.value })}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        {!isReadOnly && (
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!validation.isFormValid}
              className={`px-4 py-2 rounded-md transition-colors flex items-center space-x-2 ${
                !validation.isFormValid
                  ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              <Save className="w-4 h-4" />
              <span>{mode === 'create' ? 'Create Lead' : 'Save Changes'}</span>
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

export default LeadModal;
