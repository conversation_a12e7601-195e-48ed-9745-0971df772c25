import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDeal } from '../contexts/DealContext';
import { useInvoices } from '../contexts/InvoiceContext';
import { useQuotations } from '../contexts/QuotationContext';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { Deal, PipelineStage, PIPELINE_STAGES, CreateDealRequest } from '../types/deal';
import { useUsers } from '../hooks/useUsers';
import KanbanColumn from './KanbanColumn';
import DealCard from './DealCard';
import DealModal from './DealModal';
import DealStatusModal from './DealStatusModal';
import { TrendingUp } from 'lucide-react';

interface KanbanBoardProps {
  className?: string;
  searchTerm?: string;
  selectedFilters?: {
    pipelineStage: string;
    assignedTo: string;
    priority: string;
  };
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({
  className = '',
  searchTerm = '',
  selectedFilters = { pipelineStage: '', assignedTo: '', priority: '' }
}) => {
  const navigate = useNavigate();
  const {
    deals,
    stats,
    loading,
    error,
    filters,
    fetchDeals,
    moveDealStage,
    deleteDeal,
    createDeal,
    setFilters
  } = useDeal();

  const { users, loading: usersLoading } = useUsers();
  const { createInvoiceFromDeal, invoices } = useInvoices();
  const { createQuotation } = useQuotations();
  const { showSuccess, showError } = useToast();
  const { confirm } = useConfirmation();

  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [showDealModal, setShowDealModal] = useState(false);
  const [dealModalMode, setDealModalMode] = useState<'view' | 'edit' | 'create'>('view');
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [statusModalData, setStatusModalData] = useState<{deal: Deal | null; targetStage: PipelineStage; showQuotationOption?: boolean}>({deal: null, targetStage: 'prospecting'});

  // All pipeline stages including won/lost
  const allPipelineStages: PipelineStage[] = [
    'prospecting',
    'qualification',
    'proposal',
    'negotiation',
    'closing',
    'won',
    'lost'
  ];

  useEffect(() => {
    // Only fetch deals on initial load, not on every filter change
    if (deals.length === 0) {
      fetchDeals();
    }
  }, []);

  // Filter deals based on props from parent component
  const filteredDeals = useMemo(() => {
    return deals.filter(deal => {
      const matchesSearch = !searchTerm ||
        deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal.dealNumber.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilters = Object.entries(selectedFilters).every(([key, value]) => {
        if (!value) return true;
        if (key === 'assignedTo') return deal.assignedTo?.id?.toString() === value;
        if (key === 'pipelineStage') return deal.pipelineStage === value;
        return deal[key as keyof Deal] === value;
      });

      return matchesSearch && matchesFilters;
    });
  }, [deals, searchTerm, selectedFilters]);

  const handleDealClick = (deal: Deal) => {
    setSelectedDeal(deal);
    setDealModalMode('view');
    setShowDealModal(true);
  };

  const handleEditDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    setDealModalMode('edit');
    setShowDealModal(true);
  };

  const handleDuplicateDeal = async (deal: Deal) => {
    try {
      // Create a new deal based on the current deal
      const duplicateData: CreateDealRequest = {
        title: `${deal.title} (Copy)`,
        description: deal.description,
        value: deal.value,
        expectedRevenue: deal.expectedRevenue || deal.value,
        currency: deal.currency,
        stage: deal.stage,
        status: deal.status,
        priority: deal.priority,
        dealType: deal.dealType,
        tags: [...deal.tags],
        competitors: [...deal.competitors],
        competitiveAdvantage: deal.competitiveAdvantage,
        notes: `Duplicated from deal: ${deal.title} (${deal.dealNumber})`,
        internalNotes: deal.internalNotes,
        autoFollowUpEnabled: deal.autoFollowUpEnabled,
        followUpFrequencyDays: deal.followUpFrequencyDays,
        leadId: deal.leadId && typeof deal.leadId === 'string' && deal.leadId.startsWith('lead-') ? '' : deal.leadId, // Filter out frontend lead IDs
        assignedTo: deal.assignedTo?.id
      };

      await createDeal(duplicateData);
      showSuccess('Deal duplicated successfully!');
    } catch (error) {
      console.error('Error duplicating deal:', error);
      showError('Failed to duplicate deal. Please try again.');
    }
  };

  const handleDeleteDeal = async (deal: Deal) => {
    const confirmed = await confirm({
      title: 'Delete Deal',
      message: `Are you sure you want to delete "${deal.title}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        await deleteDeal(deal.id);
        showSuccess(`Deal "${deal.title}" deleted successfully!`);
      } catch (error) {
        console.error('Failed to delete deal:', error);
        showError(`Failed to delete deal: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  const handleStatusModalConfirm = async (data: { createInvoice?: boolean; createQuotation?: boolean; notes?: string; lossReason?: string; lossDetails?: string }) => {
    if (!statusModalData.deal) return;

    try {
      const { deal, targetStage } = statusModalData;

      let updateData: any = {
        pipelineStage: targetStage,
        notes: data.notes || `Deal moved from ${PIPELINE_STAGES[deal.pipelineStage].name} to ${PIPELINE_STAGES[targetStage].name}`
      };

      if (targetStage === 'lost') {
        updateData.lossReason = data.lossReason;
        if (data.lossDetails) {
          updateData.notes = `${updateData.notes}. Details: ${data.lossDetails}`;
        }
      }

      await moveDealStage(deal.id, updateData);

      // Handle invoice creation for won deals
      if (targetStage === 'won' && data.createInvoice) {
        try {
          // Check if deal has client_id or lead_id
          if (!deal.clientId && !deal.leadId) {
            throw new Error('Deal must have either a client or lead to create an invoice');
          }

          // Create invoice from deal data with enhanced inheritance
          const invoiceData = {
            client_id: deal.clientId || null,
            lead_id: deal.leadId || null, // Include lead_id for deals without clients
            deal_id: deal.id,
            title: `Invoice for ${deal.title}`,
            description: deal.description || `Invoice generated from deal: ${deal.title}`,
            currency: deal.currency || 'MYR',
            priority: deal.priority?.toLowerCase() || 'medium' as const,
            tax_rate: 6,
            discount_amount: 0,
            terms_conditions: 'Payment terms: 50% advance, 50% upon completion\nAll services will be conducted according to Islamic principles\nDelivery timeline as specified in project schedule',
            // Enhanced data inheritance
            notes: deal.notes ? `Deal Notes: ${deal.notes}` : '',
            internal_notes: deal.internalNotes || '',
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
            items: [{
              item_name: deal.title,
              description: deal.description || `Service for ${deal.title}`,
              unit: 'service',
              quantity: 1,
              unit_price: deal.value,
              discount_rate: 0,
              discount_amount: 0,
              line_total: deal.value,
              sort_order: 1,
            }]
          };

          await createInvoiceFromDeal(deal.id, invoiceData);
          showSuccess('Deal updated and invoice created successfully!');
        } catch (invoiceError) {
          console.error('Failed to create invoice:', invoiceError);
          showError(`Deal updated successfully, but failed to create invoice: ${invoiceError instanceof Error ? invoiceError.message : 'Unknown error'}`);
        }
      }

      // Handle quotation creation for proposal stage
      if (data.createQuotation) {
        try {
          // Check if deal has client_id or lead_id
          if (!deal.clientId && !deal.leadId) {
            throw new Error('Deal must have either a client or lead to create a quotation');
          }

          // Create quotation from deal data with enhanced inheritance
          const quotationData = {
            client_id: deal.clientId || null,
            lead_id: deal.leadId || null,
            deal_id: deal.id,
            title: `Quotation for ${deal.title}`,
            description: deal.description || `Quotation generated from deal: ${deal.title}`,
            currency: deal.currency || 'MYR',
            priority: deal.priority?.toLowerCase() || 'medium' as const,
            tax_rate: 6,
            discount_amount: 0,
            terms_conditions: 'Quotation valid for 30 days\nAll services will be conducted according to Islamic principles\nPayment terms: 50% advance, 50% upon completion',
            // Enhanced data inheritance
            notes: deal.notes ? `Deal Notes: ${deal.notes}` : '',
            internal_notes: deal.internalNotes || '',
            validity_days: 30,
            items: [{
              item_name: deal.title,
              description: deal.description || `Service for ${deal.title}`,
              unit: 'service',
              quantity: 1,
              unit_price: deal.value,
              discount_rate: 0,
              discount_amount: 0,
              line_total: deal.value,
              sort_order: 1,
            }]
          };

          await createQuotation(quotationData);
          showSuccess('Deal moved to proposal and quotation created successfully!');
        } catch (quotationError) {
          console.error('Failed to create quotation:', quotationError);
          showError(`Deal updated successfully, but failed to create quotation: ${quotationError instanceof Error ? quotationError.message : 'Unknown error'}`);
        }
      } else if (!data.createInvoice) {
        showSuccess('Deal status updated successfully!');
      }

      // Close modal and refresh deals
      setShowStatusModal(false);
      setStatusModalData({deal: null, targetStage: 'prospecting'});
      fetchDeals();
    } catch (error) {
      console.error('Failed to update deal status:', error);
      showError(`Failed to update deal status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleCreateQuotation = (deal: Deal) => {
    // Simply navigate to quotations page

    navigate('/quotations');
  };

  const handleGenerateInvoice = (deal: Deal) => {
    // Simply navigate to invoices page

    navigate('/invoices');
  };

  // Get filtered deals by stage
  const getFilteredDealsByStage = (stage: PipelineStage) => {
    return filteredDeals.filter(deal => deal.pipelineStage === stage);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading deals from API...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️</div>
          <h3 className="text-lg font-medium text-red-800 mb-2">Connection Error</h3>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Stats */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Deal Pipeline</h1>
            <p className="text-gray-600">Manage your sales opportunities</p>
          </div>
        </div>

        {/* Quick Stats */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-600">Active Deals</span>
              </div>
              <div className="mt-1">
                <span className="text-2xl font-bold text-blue-900">{stats.activeDeals}</span>
                <span className="text-sm text-blue-600 ml-2">{formatCurrency(stats.activeValue)}</span>
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-600">Won Deals</span>
              </div>
              <div className="mt-1">
                <span className="text-2xl font-bold text-green-900">{stats.wonDeals}</span>
                <span className="text-sm text-green-600 ml-2">{formatCurrency(stats.wonValue)}</span>
              </div>
            </div>

            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <span className="text-sm font-medium text-purple-600">Conversion Rate</span>
              </div>
              <div className="mt-1">
                <span className="text-2xl font-bold text-purple-900">{stats.conversionRate.toFixed(1)}%</span>
              </div>
            </div>

            <div className="bg-orange-50 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-orange-600" />
                <span className="text-sm font-medium text-orange-600">Avg Deal Size</span>
              </div>
              <div className="mt-1">
                <span className="text-2xl font-bold text-orange-900">{formatCurrency(stats.avgDealValue)}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Kanban Board */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex gap-6 overflow-x-auto pb-4" style={{ padding: '8px' }}>
          {allPipelineStages.map((stage) => {
            const stageDeals = getFilteredDealsByStage(stage);
            const stageInfo = PIPELINE_STAGES[stage];
            const stageValue = stageDeals.reduce((sum, deal) => sum + deal.value, 0);

            return (
              <div key={stage} className={stage === 'won' || stage === 'lost' ? 'relative' : ''}>
                {(stage === 'won' || stage === 'lost') && (
                  <div className={`absolute -top-2 -left-2 -right-2 h-1 rounded-t-lg ${
                    stage === 'won' ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                )}
                <KanbanColumn
                  stage={stage}
                  title={stageInfo.name}
                  description={stageInfo.description}
                  color={stageInfo.color}
                  count={stageDeals.length}
                  value={stageValue}
                >
                {stageDeals.map((deal) => (
                  <DealCard
                    key={deal.id}
                    deal={deal}
                    onClick={() => handleDealClick(deal)}
                    onCreateQuotation={handleCreateQuotation}
                    onGenerateInvoice={handleGenerateInvoice}
                    onEdit={handleEditDeal}
                    onDuplicate={handleDuplicateDeal}
                    onDelete={handleDeleteDeal}
                  />
                ))}
                </KanbanColumn>
              </div>
            );
          })}
        </div>
      </div>

      {/* Deal Modal */}
      {showDealModal && (
        <DealModal
          deal={selectedDeal}
          isOpen={showDealModal}
          mode={dealModalMode}
          onClose={() => {
            setShowDealModal(false);
            setSelectedDeal(null);
            setDealModalMode('view');
          }}
        />
      )}

      {/* Deal Status Modal */}
      <DealStatusModal
        isOpen={showStatusModal}
        onClose={() => {
          setShowStatusModal(false);
          setStatusModalData({deal: null, targetStage: 'prospecting'});
        }}
        deal={statusModalData.deal}
        targetStage={statusModalData.targetStage}
        showQuotationOption={statusModalData.showQuotationOption}
        onConfirm={handleStatusModalConfirm}
      />
    </div>
  );
};

export default KanbanBoard;
