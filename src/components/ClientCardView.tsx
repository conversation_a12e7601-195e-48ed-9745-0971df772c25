import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
  User, Mail, Phone, MapPin, Eye, Edit, Trash2,
  DollarSign, Calendar, Tag, TrendingUp, Star, Check, ShieldCheck
} from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { Client } from '../contexts/ClientContext';
import { calculateClientScore, getClientOverallScore, getScoreColor } from '../utils/clientScoring';
import ActionDropdown from './ActionDropdown';
import ProfileCompletionProgress from './ProfileCompletionProgress';

// Custom hook for animated percentage counting
const useAnimatedCounter = (targetValue: number, duration: number, delay: number = 0, resetKey?: string) => {
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    setCurrentValue(0); // Reset to 0 when target changes

    const startTime = Date.now() + delay;
    const incrementInterval = 45; // Update every 45ms for smooth animation

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;

      if (elapsed < 0) return; // Wait for delay

      if (elapsed >= duration) {
        setCurrentValue(targetValue);
        clearInterval(timer);
        return;
      }

      const progress = elapsed / duration;
      // Use ease-out curve for smooth deceleration
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const newValue = targetValue * easedProgress;

      setCurrentValue(Math.min(newValue, targetValue));
    }, incrementInterval);

    return () => clearInterval(timer);
  }, [targetValue, duration, delay, resetKey]);

  return Math.round(currentValue);
};

// Component for animated score display
interface AnimatedScoreDisplayProps {
  score: number;
  colorClass: string;
  clientId: string;
  triggerKey: string;
}

const AnimatedScoreDisplay: React.FC<AnimatedScoreDisplayProps> = ({
  score,
  colorClass,
  clientId,
  triggerKey
}) => {
  const animatedScore = useAnimatedCounter(score, 2250, 300, triggerKey); // 2250ms duration, 300ms delay, reset on triggerKey change

  return (
    <span className={`text-2xl font-bold ${colorClass} transition-all duration-2250 ease-out`}>
      {animatedScore}%
    </span>
  );
};

interface ClientCardViewProps {
  clients: Client[];
  onView: (client: Client) => void;
  onEdit: (client: Client) => void;
  onDelete: (client: Client) => void;
  selectedClients: string[];
  onToggleSelection: (clientId: string) => void;
}

interface CircularProgressProps {
  percentage: number;
  size: number;
  strokeWidth: number;
  color: string;
  children: React.ReactNode;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  size,
  strokeWidth,
  color,
  children
}) => {
  const [animatedPercentage, setAnimatedPercentage] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage);
    }, 100);

    return () => clearTimeout(timer);
  }, [percentage]);

  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (animatedPercentage / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-200"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className={`${color} transition-all duration-1500 ease-out`}
          strokeLinecap="round"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        {children}
      </div>
    </div>
  );
};

interface AnimatedProgressBarProps {
  percentage: number;
  color: string;
  label: string;
}

const AnimatedProgressBar: React.FC<AnimatedProgressBarProps> = ({
  percentage,
  color,
  label
}) => {
  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const animatedCount = useAnimatedCounter(percentage, 1800, 200); // 1800ms duration, 200ms delay

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage);
    }, 200);

    return () => clearTimeout(timer);
  }, [percentage]);

  return (
    <div className="mb-4">
      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
        <span>{label}</span>
        <span>{animatedCount}%</span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded h-2">
        <div
          className={`${color} h-2 rounded transition-all duration-1800 ease-out`}
          style={{ width: `${animatedPercentage}%` }}
        ></div>
      </div>
    </div>
  );
};

const ClientCardView: React.FC<ClientCardViewProps> = ({
  clients,
  onView,
  onEdit,
  onDelete,
  selectedClients,
  onToggleSelection
}) => {

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hot': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Warm': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Cold': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      case 'Frozen': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'High': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Low': return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'Platinum': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300';
      case 'Gold+': return 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300';
      case 'Gold': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Silver': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getEngagementIcon = (engagement: string) => {
    switch (engagement) {
      case 'Hot': return '🔥';
      case 'Warm': return '☀️';
      case 'Cold': return '❄️';
      case 'Frozen': return '🧊';
      default: return '📊';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-MY', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(new Date(date));
  };

  const getTotalSpentColor = (totalSpent: number) => {
    if (totalSpent >= 2000) return 'text-green-600';
    if (totalSpent >= 1000) return 'text-yellow-600';
    if (totalSpent >= 500) return 'text-orange-600';
    return 'text-gray-600';
  };

  const getProgressPercentage = (ltvSegment: string, engagementLevel: string) => {
    // Special case: Silver + Frozen engagement = 0%
    if (ltvSegment === 'Silver' && engagementLevel === 'Frozen') return 0;

    // LTV segment-based progress mapping
    switch (ltvSegment) {
      case 'Platinum': return 100;
      case 'Gold+': return 75;
      case 'Gold': return 50;
      case 'Silver': return 25;
      default: return 25; // Default to Silver
    }
  };

  const getLtvProgressColor = (ltvSegment: string, engagementLevel: string) => {
    // Special case: Silver + Frozen engagement = red
    if (ltvSegment === 'Silver' && engagementLevel === 'Frozen') return 'bg-red-600';

    // LTV segment-based color mapping
    switch (ltvSegment) {
      case 'Platinum': return 'bg-green-600';
      case 'Gold+': return 'bg-yellow-600';
      case 'Gold': return 'bg-orange-600';
      case 'Silver': return 'bg-red-600';
      default: return 'bg-red-600'; // Default to red
    }
  };

  // Get verification status directly from client data (no caching to ensure real-time updates)
  const getVerificationStatus = (client: Client) => {
    return {
      emailVerified: client.emailVerified ?? false,
      phoneVerified: client.phoneVerified ?? false
    };
  };

  // Use centralized scoring function for consistency across all views

  const getOverallScoreColor = (score: number) => {
    return getScoreColor(score);
  };

  const getDataQuality = (client: Client) => {
    // Use database data quality if available
    if (client.dataQuality) {
      return client.dataQuality.toLowerCase();
    }

    // Fallback to calculated data quality
    const { emailVerified, phoneVerified } = getVerificationStatus(client);

    if (emailVerified && phoneVerified) return 'excellent';
    if (emailVerified || phoneVerified) return 'good';
    return 'poor';
  };

  const getDataQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'good': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      case 'fair': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300';
      case 'poor': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getDataQualityTextColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-600 dark:text-green-400';
      case 'good': return 'text-blue-600 dark:text-blue-400';
      case 'fair': return 'text-orange-600 dark:text-orange-400';
      case 'poor': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  // Create a trigger key for animations based on client data changes
  const animationTriggerKey = clients.map(c => `${c.id}-${c.name}-${c.ltvSegment}-${c.engagementLevel}-${c.category}-${c.priority}-${c.emailVerified}-${c.phoneVerified}-${c.totalSpent}-${c.transactionCount}`).join(',');

  const getUtmSourceColor = (utmSource: string) => {
    switch (utmSource.toLowerCase()) {
      case 'facebook':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'instagram':
        return 'bg-pink-100 text-pink-700 border-pink-200';
      case 'tiktok':
        return 'bg-black text-white border-gray-800';
      case 'google':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'youtube':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'whatsapp':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'referral':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'email':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'website':
        return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'phone':
        return 'bg-green-100 text-green-700 border-green-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getPhoneCarrierColor = (carrier: string) => {
    if (!carrier) return 'bg-gray-100 text-gray-700';

    switch (carrier.toLowerCase()) {
      case 'maxis':
        return 'bg-purple-100 text-purple-700';
      case 'celcom':
        return 'bg-blue-100 text-blue-700';
      case 'digi':
        return 'bg-yellow-100 text-yellow-700';
      case 'u mobile':
        return 'bg-pink-100 text-pink-700';
      case 'tunetalk':
        return 'bg-indigo-100 text-indigo-700';
      case 'yes':
        return 'bg-green-100 text-green-700';
      case 'altel':
        return 'bg-teal-100 text-teal-700';
      case 'xox':
        return 'bg-orange-100 text-orange-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {clients.map((client) => {
        const progressPercentage = getProgressPercentage(client.ltvSegment, client.engagementLevel);
        const ltvProgressColor = getLtvProgressColor(client.ltvSegment, client.engagementLevel);
        const totalSpentColor = getTotalSpentColor(client.totalSpent);
        const overallScore = getClientOverallScore(client);
        const overallScoreColor = getOverallScoreColor(overallScore);
        const dataQuality = getDataQuality(client);
        const dataQualityColor = getDataQualityColor(dataQuality);
        const dataQualityTextColor = getDataQualityTextColor(dataQuality);
        const utmSourceColor = getUtmSourceColor(client.utmSource);
        const { emailVerified, phoneVerified } = getVerificationStatus(client);

        return (
          <div
            key={client.id}
            className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 cursor-pointer ${
              selectedClients.includes(client.id) ? 'ring-2 ring-blue-500 border-blue-500' : ''
            }`}
            onClick={(e) => {
              e.stopPropagation();
              onToggleSelection(client.id);
            }}
          >
            {/* Header with overall score, UTM source, and actions */}
            <div className="flex justify-between items-start mb-4">
              {/* Overall Score (Top Left) */}
              <div className="text-left">
                <AnimatedScoreDisplay
                  score={overallScore}
                  colorClass={dataQualityTextColor}
                  clientId={client.id}
                  triggerKey={animationTriggerKey}
                />
                <div className="text-xs text-gray-500 dark:text-gray-400">Overall Score</div>
              </div>

              {/* UTM Source Badge and Actions (Top Right) */}
              <div className="flex items-center space-x-2">
                <div className={`px-2 py-1 rounded-md text-xs border font-medium ${utmSourceColor}`}>
                  {client.utmSource}
                </div>

                {/* 3-dotted menu moved to top-right corner */}
                <div onClick={(e) => e.stopPropagation()}>
                  <ActionDropdown
                    actions={[
                      {
                        label: 'View',
                        icon: Eye,
                        onClick: () => onView(client),
                        color: 'text-blue-600'
                      },
                      {
                        label: 'Edit',
                        icon: Edit,
                        onClick: () => onEdit(client),
                        color: 'text-blue-600'
                      },
                      {
                        label: 'Delete',
                        icon: Trash2,
                        onClick: () => onDelete(client),
                        color: 'text-red-600'
                      }
                    ]}
                  />
                </div>
              </div>
            </div>

            {/* Client Avatar and Name */}
            <div className="flex flex-col items-center mb-4">
              <div className="mb-3">
                <CircularProgress
                  percentage={overallScore}
                  size={80}
                  strokeWidth={4}
                  color={dataQualityTextColor}
                >
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl font-bold">
                      {client.name.split(' ')[0][0]}
                    </span>
                  </div>
                </CircularProgress>
              </div>
              
              <h3 className="text-lg font-bold text-gray-900 dark:text-white text-center mb-1">
                {client.name}
              </h3>

              {/* Data Quality Badge */}
              <div className={`px-2 py-1 rounded-md text-xs font-medium mb-3 ${dataQualityColor}`}>
                Data Quality: {dataQuality.charAt(0).toUpperCase() + dataQuality.slice(1)}
              </div>

              {/* Contact Info */}
              <div className="flex flex-col items-center space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center space-x-1">
                  <Mail className="w-3 h-3" />
                  <span className="truncate max-w-[180px]">{client.email}</span>
                  {emailVerified && (
                    <div className="relative group">
                      <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full p-0.5 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200">
                        <ShieldCheck className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                        Email Verified
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-1">
                  <Phone className="w-3 h-3" />
                  <span>{client.phone}</span>
                  {phoneVerified && (
                    <div className="relative group">
                      <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full p-0.5 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors duration-200">
                        <ShieldCheck className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                        Phone Verified
                      </div>
                    </div>
                  )}
                  {client.phoneCarrier && (
                    <span className={`px-1.5 py-0.5 text-xs rounded ${getPhoneCarrierColor(client.phoneCarrier)}`}>
                      {client.phoneCarrier}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Status Badges */}
            <div className="flex flex-wrap gap-2 mb-4">
              <div className={`flex-auto px-2 py-1 rounded text-xs font-medium text-center whitespace-nowrap ${getStatusColor(client.engagementLevel)}`}>
                {client.engagementLevel}
              </div>
              <div className={`flex-auto px-2 py-1 rounded text-xs font-medium text-center whitespace-nowrap ${getStatusColor(client.category)}`}>
                {client.category}
              </div>
              <div className={`flex-auto px-2 py-1 rounded text-xs font-medium text-center whitespace-nowrap ${getStatusColor(client.ltvSegment)}`}>
                {client.ltvSegment}
              </div>
              <div className={`flex-auto px-2 py-1 rounded text-xs font-medium text-center whitespace-nowrap ${getStatusColor(client.priority)}`}>
                {client.priority}
              </div>
            </div>

            {/* Profile Completion Progress */}
            <div className="mb-4">
              <ProfileCompletionProgress
                client={client}
                showBreakdown={false}
                className=""
              />
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className={`text-lg font-bold ${totalSpentColor}`}>
                  {formatCurrency(client.totalSpent)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Total Spent</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  {client.transactionCount}
                </div>
                <div className="text-xs text-gray-500">Transactions</div>
              </div>
            </div>

            {/* Last Activity */}
            <div className="mb-4">
              <div className="flex items-center justify-center space-x-1 text-xs text-gray-500">
                <Calendar className="w-3 h-3" />
                <span>Last activity: {formatDate(client.lastActivity)}</span>
              </div>
            </div>

          </div>
        );
      })}
    </div>
  );
};

export default ClientCardView;
