import React, { useState, useEffect } from 'react';
import { Save } from 'lucide-react';
import Modal from './Modal';
import CustomDropdown from './CustomDropdown';
import { Transaction, useTransactions } from '../contexts/TransactionContext';

interface TransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction?: Transaction;
  mode: 'create' | 'edit' | 'view';
}

const TransactionModal: React.FC<TransactionModalProps> = ({ isOpen, onClose, transaction, mode }) => {
  const { addTransaction, updateTransaction } = useTransactions();
  const [formData, setFormData] = useState({
    type: 'Invoice' as Transaction['type'],
    clientName: '',
    clientId: '',
    amount: 0,
    item: '',
    status: 'Pending' as Transaction['status'],
    paymentMethod: 'ToyyibPay',
    utmSource: 'direct',
    description: '',
  });

  useEffect(() => {
    if (transaction && (mode === 'edit' || mode === 'view')) {
      setFormData({
        type: transaction.type,
        clientName: transaction.clientName,
        clientId: transaction.clientId,
        amount: transaction.amount,
        item: transaction.item,
        status: transaction.status,
        paymentMethod: transaction.paymentMethod,
        utmSource: transaction.utmSource,
        description: transaction.description || '',
      });
    } else {
      setFormData({
        type: 'Invoice',
        clientName: '',
        clientId: '',
        amount: 0,
        item: '',
        status: 'Pending',
        paymentMethod: 'ToyyibPay',
        utmSource: 'direct',
        description: '',
      });
    }
  }, [transaction, mode]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'create') {
      addTransaction(formData);
    } else if (mode === 'edit' && transaction) {
      updateTransaction(transaction.id, formData);
    }
    
    onClose();
  };

  const isReadOnly = mode === 'view';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create Invoice' : mode === 'edit' ? 'Edit Transaction' : 'Transaction Details'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type
            </label>
            <CustomDropdown
              options={[
                { value: 'Purchase', label: 'Purchase' },
                { value: 'Donation', label: 'Donation' },
                { value: 'Invoice', label: 'Invoice' },
              ]}
              value={formData.type}
              onChange={(value) => setFormData({ ...formData, type: value as Transaction['type'] })}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Client Name
            </label>
            <input
              type="text"
              value={formData.clientName}
              onChange={(e) => setFormData({ ...formData, clientName: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Item/Service
            </label>
            <input
              type="text"
              value={formData.item}
              onChange={(e) => setFormData({ ...formData, item: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount (RM)
            </label>
            <input
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: Number(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="0"
              step="0.01"
              required
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <CustomDropdown
              options={[
                { value: 'Pending', label: 'Pending' },
                { value: 'Completed', label: 'Completed' },
                { value: 'Failed', label: 'Failed' },
              ]}
              value={formData.status}
              onChange={(value) => setFormData({ ...formData, status: value as Transaction['status'] })}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Method
            </label>
            <CustomDropdown
              options={[
                { value: 'ToyyibPay', label: 'ToyyibPay' },
                { value: 'Cash', label: 'Cash' },
                { value: 'Bank Transfer', label: 'Bank Transfer' },
                { value: 'QR Code', label: 'QR Code' },
                { value: 'Credit Card', label: 'Credit Card' },
              ]}
              value={formData.paymentMethod}
              onChange={(value) => setFormData({ ...formData, paymentMethod: value })}
              className="w-full"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={4}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        {!isReadOnly && (
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{mode === 'create' ? 'Create Transaction' : 'Save Changes'}</span>
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

export default TransactionModal;