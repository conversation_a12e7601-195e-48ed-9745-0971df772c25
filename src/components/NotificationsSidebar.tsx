import React, { useState, useEffect } from 'react';
import { X, Check, Trash2, User, AlertCircle, Info, CheckCircle, AlertTriangle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useNotifications } from '../contexts/NotificationContext';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { apiService } from '../services/api';
import ActionButtons from './ActionButtons';
import { useNavigate } from 'react-router-dom';

interface NotificationsSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationsSidebar: React.FC<NotificationsSidebarProps> = ({ isOpen, onClose }) => {
  const { notifications, loading, markAsRead, markAllAsRead, deleteNotification, fetchNotifications } = useNotifications();
  const { showSuccess, showError } = useToast();
  const { confirm } = useConfirmation();
  const navigate = useNavigate();
  const [filter, setFilter] = useState<'all' | 'unread' | 'user_registration'>('all');

  useEffect(() => {
    if (isOpen) {
      fetchNotifications();
    }
  }, [isOpen, fetchNotifications]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'user_registration':
        return <User className="w-5 h-5 text-blue-500" />;
      case 'user_approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500';
      case 'high':
        return 'border-l-orange-500';
      case 'normal':
        return 'border-l-blue-500';
      case 'low':
        return 'border-l-gray-500';
      default:
        return 'border-l-blue-500';
    }
  };

  const handleApproveUser = async (userId: number, notificationId: string) => {
    const confirmed = await confirm({
      title: 'Approve User Registration',
      message: 'Are you sure you want to approve this user? This will activate their account and grant them access to the system.',
      confirmText: 'Approve',
      cancelText: 'Cancel',
      type: 'info'
    });

    if (confirmed) {
      try {
        await apiService.post(`/auth/approve-user/${userId}`);
        showSuccess('User approved successfully');
        await markAsRead(notificationId);
        await fetchNotifications();
      } catch (error: any) {
        showError(error.message || 'Failed to approve user');
      }
    }
  };

  const handleNotificationClick = (notification: any) => {
    markAsRead(notification.id);
    if (notification.action_url) {
      // Close sidebar and navigate
      onClose();
      navigate(notification.action_url);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') return !notification.read_at;
    if (filter === 'user_registration') return notification.type === 'user_registration';
    return true;
  });

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div 
        className="fixed right-0 top-0 h-full w-full sm:w-96 bg-white dark:bg-gray-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out"
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">All Notifications</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { key: 'all', label: 'All' },
            { key: 'unread', label: 'Unread' },
            { key: 'user_registration', label: 'Registrations' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                filter === tab.key
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Actions */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={markAllAsRead}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            Mark all as read
          </button>
        </div>

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              Loading notifications...
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              No notifications found
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border-b border-gray-100 dark:border-gray-700 border-l-4 ${
                  !notification.read_at ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                } ${getPriorityColor(notification.priority)}`}
              >
                <div className="flex items-start space-x-3">
                  {getIcon(notification.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {notification.title}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          {notification.message}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                          </span>
                          {notification.priority === 'high' && (
                            <span className="text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 px-2 py-1 rounded-full">
                              High
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons for User Registration Notifications */}
                    {notification.type === 'user_registration' && notification.data?.user_id && (
                      <div className="mt-3 flex items-center space-x-2">
                        <ActionButtons
                          actions={[
                            {
                              label: 'Approve User',
                              icon: Check,
                              onClick: () => handleApproveUser(notification.data.user_id, notification.id),
                              color: 'blue'
                            },
                            {
                              label: 'View Details',
                              icon: User,
                              onClick: () => handleNotificationClick(notification),
                              color: 'green'
                            },
                            {
                              label: 'Delete Notification',
                              icon: Trash2,
                              onClick: () => deleteNotification(notification.id),
                              color: 'red'
                            }
                          ]}
                          size="sm"
                        />
                      </div>
                    )}

                    {/* Action Buttons for Other Notifications */}
                    {notification.type !== 'user_registration' && (
                      <div className="mt-3 flex items-center space-x-2">
                        <ActionButtons
                          actions={[
                            ...(notification.action_url ? [{
                              label: 'View Details',
                              icon: User,
                              onClick: () => handleNotificationClick(notification),
                              color: 'blue' as const
                            }] : []),
                            {
                              label: 'Delete Notification',
                              icon: Trash2,
                              onClick: () => deleteNotification(notification.id),
                              color: 'red' as const
                            }
                          ]}
                          size="sm"
                        />
                      </div>
                    )}
                  </div>
                  {!notification.read_at && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </>
  );
};

export default NotificationsSidebar;
