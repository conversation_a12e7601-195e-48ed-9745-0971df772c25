import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertTriangle,
  X,
  ArrowRight,
  ArrowLeft,
  RefreshCw,
  AlertCircle,
  Download
} from 'lucide-react';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { useClients } from '../contexts/ClientContext';
import { apiService } from '../services/api';
import { logger } from '../utils/logger';
import CustomDropdown from './CustomDropdown';
import ImportExplanation from './ImportExplanation';

interface CsvField {
  label: string;
  required: boolean;
  type: string;
  options?: string[];
  min?: number;
  max?: number;
}

interface ParseResult {
  headers: string[];
  preview: Record<string, any>[];
  totalRows: number;
}

interface ImportError {
  row: number;
  error: string;
  category: 'validation' | 'database' | 'processing' | 'format' | 'duplicate';
  field?: string;
  value?: string;
  expected_format?: string;
  severity: 'error' | 'warning';
  data?: Record<string, any>;
}

interface ImportDuplicate {
  row: number;
  existing_id: number;
  identifier: string;
  field: string;
  value: string;
  action: 'skipped' | 'updated';
  data?: Record<string, any>;
}

interface ImportResult {
  total_rows: number;
  processed: number;
  created: number;
  updated: number;
  skipped: number;
  errors: ImportError[];
  duplicates: ImportDuplicate[];
  // Enhanced reporting
  error_categories: Record<string, number>;
  processing_time: number;
  batch_size: number;
  success_rate: number;
}

interface DataImportWizardProps {
  dataType: string;
  fileFormat: string;
  onImportComplete?: () => void;
}

const DataImportWizard: React.FC<DataImportWizardProps> = ({ 
  dataType,
  fileFormat,
  onImportComplete 
}) => {
  const [step, setStep] = useState<'upload' | 'mapping' | 'importing' | 'results'>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [parseResult, setParseResult] = useState<ParseResult | null>(null);
  const [availableFields, setAvailableFields] = useState<Record<string, CsvField>>({});
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [importOptions, setImportOptions] = useState({
    skip_duplicates: true,
    update_existing: false,
    batch_size: 100
  });
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [importProgressText, setImportProgressText] = useState('Preparing import...');
  const [uploadProgress, setUploadProgress] = useState(0);

  const { showSuccess, showError, showInfo } = useToast();
  const { confirm } = useConfirmation();
  const { refreshClients } = useClients();

  // Load available fields when component mounts
  useEffect(() => {
    loadAvailableFields();
  }, []);

  const loadAvailableFields = async () => {
    try {
      const response = await apiService.get('/csv-import/fields');
      setAvailableFields(response.fields || {});
    } catch (error) {
      console.error('Error loading available fields:', error);
      showError('Error', 'Failed to load available fields');
    }
  };

  const getAcceptedFileTypes = () => {
    switch (fileFormat) {
      case 'csv':
        return {
          'text/csv': ['.csv'],
          'text/plain': ['.txt'],
          'application/csv': ['.csv']
        };
      case 'excel':
        return {
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
          'application/vnd.ms-excel': ['.xls']
        };
      case 'json':
        return {
          'application/json': ['.json'],
          'text/json': ['.json']
        };
      default:
        return {
          'text/csv': ['.csv'],
          'text/plain': ['.txt'],
          'application/csv': ['.csv']
        };
    }
  };

  const getFileFormatLabel = () => {
    switch (fileFormat) {
      case 'csv':
        return 'CSV';
      case 'excel':
        return 'Excel';
      case 'json':
        return 'JSON';
      default:
        return 'CSV';
    }
  };

  const getMaxFileSize = () => {
    // Excel files can be larger than CSV/JSON
    return fileFormat === 'excel' ? 100 * 1024 * 1024 : 50 * 1024 * 1024; // 100MB for Excel, 50MB for others
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const selectedFile = acceptedFiles[0];
    if (!selectedFile) return;

    setFile(selectedFile);
    setUploadProgress(0);
    
    try {
      showInfo('Upload', `Parsing ${getFileFormatLabel()} file...`);
      
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('format', fileFormat);
      formData.append('data_type', dataType);
      
      const response = await apiService.post('/csv-import/parse', formData);
      setParseResult(response);
      setStep('mapping');
      
      showSuccess('Success', `${getFileFormatLabel()} file parsed successfully`);
      
      // Auto-map fields with improved matching logic
      const autoMapping: Record<string, string> = {};
      response.headers.forEach((header: string) => {
        const lowerHeader = header.toLowerCase().replace(/[^a-z0-9]/g, '');
        
        // Direct field name matches
        const directMatches: Record<string, string> = {
          'name': 'name',
          'fullname': 'name',
          'clientname': 'name',
          'customername': 'name',
          'email': 'email',
          'emailaddress': 'email',
          'phone': 'phone',
          'phonenumber': 'phone',
          'mobile': 'phone',
          'company': 'company',
          'organization': 'company',
          'notes': 'notes',
          'note': 'notes',
          'description': 'notes',
          'status': 'status',
          'priority': 'priority',
          'category': 'category',
          'customercategory': 'customer_category', // Fix: customer_category should map to customer_category
          'segment': 'ltv_segment',
          'ltvsegment': 'ltv_segment',
          'engagement': 'engagement_level',
          'engagementlevel': 'engagement_level',
          'utmsource': 'utm_source',
          'source': 'utm_source',
          // 'tags' field removed - not available in CSV structure, should skip
          'totalspent': 'total_spent',
          'spent': 'total_spent',
          'revenue': 'total_spent',
          'transactions': 'transactions', // Fix: transactions should map to transactions field
          'transactioncount': 'transaction_count', // Keep transaction_count separate
          'lastactivity': 'last_activity',
          'activity': 'last_activity',
          'createdat': 'created_at',
          'created': 'created_at',
          'date': 'created_at',
          // Address fields - structured
          'addressline1': 'address_line_1',
          'address1': 'address_line_1',
          'addressline2': 'address_line_2',
          'address2': 'address_line_2',
          'city': 'city',
          'state': 'state',
          'country': 'country',
          'postcode': 'postcode',
          'postalcode': 'postcode',
          'zipcode': 'postcode',
          'zip': 'postcode',
          // Financial fields
          'income': 'income',
          'incomecategory': 'income_category',
          // Personal information
          'icnumber': 'ic_number',
          'birthday': 'birthday',
          'gender': 'gender',
          'religion': 'religion',
          // Verification fields
          'emailverified': 'email_verified',
          'phoneverified': 'phone_verified',
          // Score fields
          'namescore': 'name_score',
          'emailscore': 'email_score',
          'phonescore': 'phone_score',
          'overallscore': 'overall_score',
          // Validation fields
          'emaildeliverability': 'email_deliverability',
          'phonevalidity': 'phone_validity',
          'phonecarrier': 'phone_carrier',
          'dataquality': 'data_quality',
          // Behavioral data
          'behaviour': 'behaviour',
          'behavior': 'behaviour',
          'interest': 'interest'
        };

        if (directMatches[lowerHeader]) {
          autoMapping[header] = directMatches[lowerHeader];
          return;
        }

        // Fallback to fuzzy matching
        const matchingField = Object.keys(availableFields).find(field => {
          const lowerField = field.toLowerCase().replace(/[^a-z0-9]/g, '');
          return lowerField === lowerHeader ||
                 lowerField.includes(lowerHeader) ||
                 lowerHeader.includes(lowerField);
        });

        if (matchingField) {
          autoMapping[header] = matchingField;
        }
      });
      setMapping(autoMapping);
      
    } catch (error) {
      console.error('Error parsing file:', error);
      showError('Error', `Failed to parse ${getFileFormatLabel()} file. Please check the file format.`);
    }
  }, [availableFields, showSuccess, showError, showInfo, fileFormat, dataType]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedFileTypes(),
    maxFiles: 1,
    maxSize: getMaxFileSize()
  });

  const handleMappingChange = (csvHeader: string, field: string) => {
    setMapping(prev => ({
      ...prev,
      [csvHeader]: field
    }));
  };

  const handleImport = async () => {
    if (!file || !parseResult) return;

    const confirmed = await confirm({
      title: 'Confirm Import',
      message: `Are you sure you want to import ${(parseResult.totalRows - 1).toLocaleString()} data records? This action cannot be undone.`,
      confirmText: 'Import',
      cancelText: 'Cancel'
    });

    if (!confirmed) return;

    setImporting(true);
    setStep('importing');
    setImportProgress(0);
    setImportProgressText('Preparing import...');

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('format', fileFormat);
      formData.append('data_type', dataType);

      // Send mapping as FormData array structure (Laravel expects array format)
      Object.entries(mapping).forEach(([csvHeader, field]) => {
        if (field) { // Only include mapped fields
          formData.append(`mapping[${csvHeader}]`, field);
        }
      });

      // Send options as FormData structure with proper boolean values
      formData.append('options[skip_duplicates]', importOptions.skip_duplicates ? '1' : '0');
      formData.append('options[update_existing]', importOptions.update_existing ? '1' : '0');
      formData.append('options[batch_size]', importOptions.batch_size.toString());

      for (let [key, value] of formData.entries()) {

      }

      // Enhanced progress updates with detailed stages
      const progressSteps = [
        { progress: 5, text: 'Uploading file...', detail: 'Transferring data to server' },
        { progress: 15, text: 'Parsing CSV structure...', detail: 'Reading headers and validating format' },
        { progress: 25, text: 'Validating data quality...', detail: 'Checking required fields and data types' },
        { progress: 35, text: 'Detecting duplicates...', detail: 'Comparing with existing records' },
        { progress: 50, text: 'Processing records...', detail: 'Cleaning and transforming data' },
        { progress: 70, text: 'Importing to database...', detail: 'Inserting records in batches' },
        { progress: 85, text: 'Updating relationships...', detail: 'Linking related data' },
        { progress: 95, text: 'Finalizing import...', detail: 'Generating summary and cleanup' }
      ];

      // Start enhanced progress animation
      let currentStep = 0;
      const progressInterval = setInterval(() => {
        if (currentStep < progressSteps.length) {
          const step = progressSteps[currentStep];
          setImportProgress(step.progress);
          setImportProgressText(`${step.text} - ${step.detail}`);
          currentStep++;
        }
      }, 600);

      const response = await apiService.post('/csv-import/import', formData);

      // Clear progress interval and set to exactly 100%
      clearInterval(progressInterval);
      setImportProgress(100); // Set to exactly 100% when import completes
      setImportProgressText('Import completed!');
      setImportResult(response.results);
      setStep('results');

      showSuccess('Success', `Import completed! Created ${response.results.created} records.`);

      // Save import history
      try {
        await apiService.post('/import-history', {
          file_name: file?.name || 'Unknown',
          file_size: file?.size || 0,
          data_type: dataType,
          file_format: fileFormat,
          total_rows: response.results.total_rows,
          processed: response.results.processed,
          created: response.results.created,
          updated: response.results.updated,
          skipped: response.results.skipped,
          errors_count: response.results.errors.length,
          duplicates_count: response.results.duplicates?.length || 0,
          processing_time: response.results.processing_time || 0,
          success_rate: response.results.success_rate || 0,
          error_details: response.results.errors || [],
          import_options: importOptions,
          field_mapping: mapping,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
        });

        showSuccess('Import History', 'Import details saved to history');
      } catch (historyError) {
        console.error('Failed to save import history:', historyError);
        showError('Import History', 'Failed to save import history. Import was successful but history was not recorded.');
        // Don't fail the import if history saving fails
      }

      // Log the import activity
      logger.log(
        'Data Import',
        dataType.charAt(0).toUpperCase() + dataType.slice(1),
        `Imported ${getFileFormatLabel()} file: ${response.results.total_rows} rows processed, ${response.results.created} created, ${response.results.updated} updated, ${response.results.skipped} skipped, ${response.results.errors.length} errors`,
        'Admin',
        'Data Management',
        {
          file_name: file?.name,
          file_size: file?.size,
          data_type: dataType,
          file_format: fileFormat,
          total_rows: response.results.total_rows,
          processed: response.results.processed,
          created: response.results.created,
          updated: response.results.updated,
          skipped: response.results.skipped,
          errors: response.results.errors.length,
          duplicates: response.results.duplicates?.length || 0
        }
      );

      // Refresh client data if clients were imported (created OR updated)
      if (dataType === 'clients' && (response.results.created > 0 || response.results.updated > 0)) {

        try {
          await refreshClients();

        } catch (refreshError) {
          console.error('Failed to refresh client data:', refreshError);
          // Don't show error to user as the main operation succeeded
        }
      }

      // Don't call onImportComplete here - let user review results first
    } catch (error: any) {
      console.error('Import error:', error);

      // Reset progress on error
      setImportProgress(0);
      setImportProgressText('Import failed');

      // Extract more detailed error information
      let errorMessage = 'Failed to import data. Please try again.';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        // Handle validation errors
        const validationErrors = Object.values(error.response.data.errors).flat();
        errorMessage = validationErrors.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }

      showError('Import Failed', errorMessage);
      setStep('mapping');
    } finally {
      setImporting(false);
    }
  };

  const resetWizard = () => {
    setStep('upload');
    setFile(null);
    setParseResult(null);
    setMapping({});
    setImportResult(null);
    setImportProgress(0);
    setImportProgressText('Preparing import...');
  };

  const renderStepIndicator = () => {
    const steps = [
      { id: 'upload', name: 'Upload File', icon: Upload },
      { id: 'mapping', name: 'Map Columns', icon: FileText },
      { id: 'importing', name: 'Import Data', icon: RefreshCw },
      { id: 'results', name: 'View Results', icon: CheckCircle }
    ];

    const currentStepIndex = steps.findIndex(s => s.id === step);

    return (
      <div className="flex items-center justify-between mb-8">
        {steps.map((stepItem, index) => {
          const Icon = stepItem.icon;
          const isActive = stepItem.id === step;
          const isCompleted = index < currentStepIndex;
          
          return (
            <div key={stepItem.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                isActive 
                  ? 'border-blue-600 bg-blue-600 text-white' 
                  : isCompleted 
                    ? 'border-green-600 bg-green-600 text-white'
                    : 'border-gray-300 bg-white text-gray-400'
              }`}>
                <Icon className="w-5 h-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
              }`}>
                {stepItem.name}
              </span>
              {index < steps.length - 1 && (
                <ArrowRight className="w-4 h-4 text-gray-300 mx-4" />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Download error report function
  const downloadErrorReport = (errors: ImportError[], type: 'errors' | 'duplicates') => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const filename = `import_${type}_report_${timestamp}.csv`;

    let csvContent = '';

    if (type === 'errors') {
      // CSV headers for errors
      csvContent = 'Row,Category,Severity,Field,Value,Error,Expected Format\n';

      // CSV data for errors
      errors.forEach(error => {
        const row = [
          error.row,
          error.category || 'unknown',
          error.severity || 'error',
          error.field || '',
          error.value || '',
          `"${error.error.replace(/"/g, '""')}"`,
          error.expected_format || ''
        ].join(',');
        csvContent += row + '\n';
      });
    } else {
      // CSV headers for duplicates
      csvContent = 'Row,Field,Value,Action,Existing ID,Identifier\n';

      // CSV data for duplicates
      (errors as any[]).forEach(duplicate => {
        const row = [
          duplicate.row,
          duplicate.field || '',
          duplicate.value || '',
          duplicate.action || 'skipped',
          duplicate.existing_id || '',
          `"${duplicate.identifier?.replace(/"/g, '""') || ''}"`
        ].join(',');
        csvContent += row + '\n';
      });
    }

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Log the download
    logger.log(
      'Data Export',
      'Import Report',
      `Downloaded ${type} report: ${filename}`,
      'Admin',
      'Data Management'
    );
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Import {dataType.charAt(0).toUpperCase() + dataType.slice(1)} Data
        </h3>
        <p className="text-gray-600">
          Import data from {getFileFormatLabel()} format
        </p>
      </div>

      {renderStepIndicator()}

      {/* Step Content will be added in the next part */}
      <div className="min-h-[400px]">
        {step === 'upload' && (
          <div className="space-y-6">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                {isDragActive ? 'Drop the file here' : `Upload ${getFileFormatLabel()} file`}
              </p>
              <p className="text-gray-600 mb-4">
                Drag and drop your file here, or click to browse
              </p>
              <p className="text-sm text-gray-500">
                Supported formats: {getFileFormatLabel()} • Max size: {Math.round(getMaxFileSize() / 1024 / 1024)}MB
              </p>
            </div>

            {file && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <FileText className="w-8 h-8 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">{file.name}</p>
                    <p className="text-sm text-gray-600">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <button
                    onClick={() => setFile(null)}
                    className="ml-auto p-1 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {step === 'mapping' && parseResult && (
          <div className="space-y-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">File Preview</h4>
              <p className="text-blue-700 text-sm">
                Found {(parseResult.totalRows - 1).toLocaleString()} data records with {parseResult.headers.length} columns (excluding header row)
              </p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-4">Map Columns</h4>
              <p className="text-gray-600 mb-4">
                Match your file columns to the appropriate fields
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {parseResult.headers.map((header) => (
                  <div key={header} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="mb-3">
                      <span className="font-medium text-gray-900 text-sm block mb-1">{header}</span>
                      {parseResult.preview[0] && (
                        <p className="text-xs text-gray-600 truncate">
                          Example: {parseResult.preview[0][header]}
                        </p>
                      )}
                    </div>
                    <div className="w-full">
                      <CustomDropdown
                        options={[
                          { value: '', label: 'Skip this column' },
                          ...Object.entries(availableFields).map(([key, field]) => ({
                            value: key,
                            label: field.label + (field.required ? ' *' : '')
                          }))
                        ]}
                        value={mapping[header] || ''}
                        onChange={(value) => handleMappingChange(header, value)}
                        className="w-full text-sm"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">Import Options</h4>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={importOptions.skip_duplicates}
                    onChange={(e) => setImportOptions(prev => ({
                      ...prev,
                      skip_duplicates: e.target.checked
                    }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Skip duplicate records</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={importOptions.update_existing}
                    onChange={(e) => setImportOptions(prev => ({
                      ...prev,
                      update_existing: e.target.checked
                    }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Update existing records</span>
                </label>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <button
                onClick={() => setStep('upload')}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </button>
              <button
                onClick={handleImport}
                disabled={Object.keys(mapping).length === 0}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                <span>Start Import</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {step === 'importing' && (
          <div className="py-8">
            <div className="max-w-2xl mx-auto">
              {/* Enhanced Progress Circle with Animation */}
              <div className="text-center mb-8">
                <div className="relative w-24 h-24 mx-auto mb-6">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                  <div
                    className="absolute inset-0 rounded-full border-4 border-blue-600 transition-all duration-1000 ease-out"
                    style={{
                      borderTopColor: 'transparent',
                      borderRightColor: 'transparent',
                      transform: `rotate(${(importProgress / 100) * 360}deg)`
                    }}
                  ></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Upload className="w-10 h-10 text-blue-600 animate-pulse" />
                  </div>
                </div>

                {/* Progress Percentage with Animation */}
                <div className="mb-4">
                  <span className="text-4xl font-bold text-blue-600 transition-all duration-500">
                    {Math.round(importProgress)}%
                  </span>
                </div>

                {/* Enhanced Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-4 mb-6 overflow-hidden shadow-inner">
                  <div
                    className="bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 h-4 rounded-full transition-all duration-1000 ease-out relative"
                    style={{ width: `${importProgress}%` }}
                  >
                    <div className="absolute inset-0 bg-white opacity-20 animate-pulse rounded-full"></div>
                  </div>
                </div>

                {/* Progress Text */}
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Importing Data</h3>
                <p className="text-gray-600 transition-all duration-500 text-base">{importProgressText}</p>
              </div>

              {/* Real-time Statistics Grid */}
              {parseResult && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{(parseResult.totalRows - 1).toLocaleString()}</div>
                    <div className="text-sm text-blue-800">Data Records</div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(((parseResult.totalRows - 1) * importProgress) / 100).toLocaleString()}
                    </div>
                    <div className="text-sm text-green-800">Processed</div>
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {Math.round((parseResult.totalRows - 1) - ((parseResult.totalRows - 1) * importProgress) / 100).toLocaleString()}
                    </div>
                    <div className="text-sm text-yellow-800">Remaining</div>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {Math.round(((parseResult.totalRows - 1) * importProgress) / 100 / 60).toLocaleString()}/min
                    </div>
                    <div className="text-sm text-purple-800">Processing Rate</div>
                  </div>
                </div>
              )}

              {/* Processing Stage Indicator */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Current Stage</h4>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-600 rounded-full animate-pulse"></div>
                  <span className="text-gray-700">{importProgressText}</span>
                </div>

                {/* Performance Indicator */}
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Performance</span>
                    <span className={`font-medium ${importProgress > 90 ? 'text-yellow-600' : 'text-green-600'}`}>
                      {importProgress > 90 ? 'Optimizing final steps...' : 'Running smoothly'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'results' && importResult && (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Import Complete!</h3>
              <p className="text-gray-600">Your data has been successfully processed</p>
            </div>

            {/* Enhanced Summary Statistics */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 border border-blue-200">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                Import Summary
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{(importResult.total_rows - 1).toLocaleString()}</div>
                  <div className="text-gray-600">Data Rows</div>
                  <div className="text-xs text-gray-500 mt-1">Excluding header</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{importResult.processed.toLocaleString()}</div>
                  <div className="text-gray-600">Successfully Processed</div>
                  <div className="text-xs text-gray-500 mt-1">Data rows only</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{Math.max(0, importResult.total_rows - importResult.processed - 1).toLocaleString()}</div>
                  <div className="text-gray-600">Failed Records</div>
                  <div className="text-xs text-gray-500 mt-1">Excluding header</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {importResult.total_rows > 1 ? Math.round((importResult.processed / (importResult.total_rows - 1)) * 100) : 0}%
                  </div>
                  <div className="text-gray-600">Success Rate</div>
                  <div className="text-xs text-gray-500 mt-1">Data rows only</div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="mt-4 pt-4 border-t border-blue-200">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Processing Speed:</span>
                    <span className="font-medium text-blue-600 ml-2">
                      {Math.round(importResult.processed / 60).toLocaleString()} records/min
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Data Quality:</span>
                    <span className={`font-medium ml-2 ${
                      importResult.total_rows > 1 && importResult.processed / (importResult.total_rows - 1) > 0.95 ? 'text-green-600' :
                      importResult.total_rows > 1 && importResult.processed / (importResult.total_rows - 1) > 0.85 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {importResult.total_rows > 1 && importResult.processed / (importResult.total_rows - 1) > 0.95 ? 'Excellent' :
                       importResult.total_rows > 1 && importResult.processed / (importResult.total_rows - 1) > 0.85 ? 'Good' : 'Needs Review'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-green-600">{importResult.created}</p>
                <p className="text-sm text-green-700">Created</p>
              </div>
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-blue-600">{importResult.updated}</p>
                <p className="text-sm text-blue-700">Updated</p>
              </div>
              <div className="bg-yellow-50 rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-yellow-600">{importResult.skipped}</p>
                <p className="text-sm text-yellow-700">Skipped</p>
              </div>
              <div className="bg-red-50 rounded-lg p-4 text-center">
                <p className="text-2xl font-bold text-red-600">{importResult.errors.length}</p>
                <p className="text-sm text-red-700">Errors</p>
              </div>
            </div>

            {/* Enhanced Duplicates Section */}
            {importResult.duplicates && importResult.duplicates.length > 0 && (
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-yellow-900 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Duplicate Records ({importResult.duplicates.length})
                  </h4>
                  <button
                    onClick={() => downloadErrorReport(importResult.duplicates as any, 'duplicates')}
                    className="text-sm text-yellow-700 hover:text-yellow-900 underline flex items-center"
                  >
                    <Download className="w-3 h-3 mr-1" />
                    Download Report
                  </button>
                </div>
                <div className="bg-yellow-100 rounded-lg p-4 mb-4">
                  <h5 className="font-medium text-yellow-900 mb-2">Why were these records skipped?</h5>
                  <p className="text-sm text-yellow-700 mb-2">
                    These records were identified as duplicates because they match existing records in the database based on:
                  </p>
                  <ul className="text-sm text-yellow-700 list-disc list-inside space-y-1">
                    <li><strong>Email addresses</strong> - Records with the same email as existing clients</li>
                    <li><strong>Phone numbers</strong> - Records with the same phone number as existing clients</li>
                    <li><strong>Names</strong> - Records with identical names (as a fallback)</li>
                  </ul>
                  <p className="text-sm text-yellow-700 mt-2">
                    This prevents duplicate entries and maintains data integrity. You can enable "Update Existing" to modify existing records instead.
                  </p>
                </div>
                <p className="text-sm text-yellow-700 mb-3">
                  <strong>Duplicate Records Found:</strong>
                </p>
                <div className="max-h-48 overflow-y-auto space-y-2">
                  {importResult.duplicates.slice(0, 15).map((duplicate, index) => (
                    <div key={index} className="text-sm bg-yellow-100 rounded p-3 border-l-4 border-yellow-400">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-yellow-900">
                            Row {duplicate.row} - {duplicate.field || 'Unknown field'}
                          </div>
                          <div className="text-yellow-700 mt-1">
                            Value: <span className="font-mono">"{duplicate.value || duplicate.identifier}"</span>
                          </div>
                          <div className="text-yellow-600 text-xs mt-1">
                            Existing ID: {duplicate.existing_id}
                          </div>
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          duplicate.action === 'updated' ? 'bg-blue-200 text-blue-800' : 'bg-yellow-200 text-yellow-800'
                        }`}>
                          {duplicate.action || 'skipped'}
                        </span>
                      </div>
                    </div>
                  ))}
                  {importResult.duplicates.length > 15 && (
                    <div className="text-sm text-yellow-600 font-medium text-center py-2">
                      ... and {importResult.duplicates.length - 15} more duplicates (download full report for details)
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Enhanced Errors Section */}
            {importResult.errors.length > 0 && (
              <div className="bg-red-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-red-900 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    Import Errors ({importResult.errors.length})
                  </h4>
                  <button
                    onClick={() => downloadErrorReport(importResult.errors, 'errors')}
                    className="text-sm text-red-700 hover:text-red-900 underline flex items-center"
                  >
                    <Download className="w-3 h-3 mr-1" />
                    Download Report
                  </button>
                </div>

                {/* Error Categories */}
                {importResult.error_categories && Object.keys(importResult.error_categories).length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-red-800 mb-2">Error Categories:</h5>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(importResult.error_categories).map(([category, count]) => (
                        <span key={category} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {category}: {count}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                <p className="text-sm text-red-700 mb-3">
                  These records failed to import due to validation or processing errors:
                </p>

                <div className="max-h-60 overflow-y-auto space-y-2">
                  {importResult.errors.slice(0, 20).map((error, index) => (
                    <div key={index} className="text-sm bg-red-100 rounded p-3 border-l-4 border-red-400">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="font-medium text-red-900">
                            Row {error.row} - {error.category || 'Unknown'} error
                          </div>
                          <div className="text-red-700 mt-1">{error.error}</div>
                          {error.field && (
                            <div className="text-red-600 text-xs mt-1">
                              Field: <span className="font-mono">{error.field}</span>
                              {error.value && (
                                <span> | Value: <span className="font-mono">"{error.value}"</span></span>
                              )}
                            </div>
                          )}
                          {error.expected_format && (
                            <div className="text-red-600 text-xs mt-1">
                              Expected: {error.expected_format}
                            </div>
                          )}
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          error.severity === 'error' ? 'bg-red-200 text-red-800' : 'bg-yellow-200 text-yellow-800'
                        }`}>
                          {error.severity || 'error'}
                        </span>
                      </div>
                    </div>
                  ))}
                  {importResult.errors.length > 20 && (
                    <div className="text-sm text-red-600 font-medium text-center py-2">
                      ... and {importResult.errors.length - 20} more errors (download full report for details)
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Import Explanation */}
            <ImportExplanation
              totalRows={importResult.total_rows}
              processed={importResult.processed}
              created={importResult.created}
              updated={importResult.updated}
              skipped={importResult.skipped}
              errors={importResult.errors.length}
            />

            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={resetWizard}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Import Another File
              </button>
              {onImportComplete && (
                <button
                  onClick={() => {
                    if (onImportComplete) {
                      onImportComplete();
                    }
                  }}
                  className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Done
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataImportWizard;
