import React, { useState, useEffect } from 'react';
import { X, Save, Eye, Edit, Trash2, Copy, Upload, Image as ImageIcon } from 'lucide-react';
import CustomDropdown from './CustomDropdown';
import { Product, useProducts } from '../contexts/ProductContext';
import { useToast } from '../contexts/ToastContext';

interface ProductSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  product?: Product;
  mode: 'create' | 'edit' | 'view';
}

const ProductSidebar: React.FC<ProductSidebarProps> = ({ isOpen, onClose, product, mode }) => {
  const { addProduct, updateProduct, deleteProduct } = useProducts();
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !product);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    category: 'Books' as Product['category'],
    price: 0,
    stock: 0,
    sold: 0,
    description: '',
    status: 'Active' as Product['status'],
    image: '',
  });

  useEffect(() => {
    if (product && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: product.name,
        category: product.category,
        price: product.price,
        stock: product.stock,
        sold: product.sold,
        description: product.description,
        status: product.status,
        image: product.image,
      });
      setIsEditing(mode === 'edit');
    } else {
      setFormData({
        name: '',
        category: 'Books',
        price: 0,
        stock: 0,
        sold: 0,
        description: '',
        status: 'Active',
        image: '',
      });
      setIsEditing(true);
    }
    setError(null);
  }, [product, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;

    setLoading(true);
    setError(null);

    try {
      if (mode === 'create') {
        await addProduct(formData);
        showSuccess('Product Created', `${formData.name} has been successfully added to your products!`);
      } else if (mode === 'edit' && product) {
        await updateProduct(product.id, formData);
        showSuccess('Product Updated', `${formData.name} has been successfully updated!`);
      }
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Save Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDelete = async () => {
    if (!product) return;
    
    setLoading(true);
    try {
      await deleteProduct(product.id);
      showSuccess('Product Deleted', 'Product has been successfully deleted');
      onClose();
    } catch (error) {
      console.error('Error deleting product:', error);
      const errorMessage = 'Failed to delete product. Please try again.';
      setError(errorMessage);
      showError('Delete Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async () => {
    if (!product) return;
    
    setLoading(true);
    try {
      const duplicateData = {
        ...formData,
        name: `${formData.name} (Copy)`,
        sold: 0, // Reset sold count for duplicate
      };
      
      await addProduct(duplicateData);
      showSuccess('Product Duplicated', 'Product has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating product:', error);
      const errorMessage = 'Failed to duplicate product. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, you would upload to a server and get back a URL
      // For now, we'll create a local URL for preview
      const imageUrl = URL.createObjectURL(file);
      handleInputChange('image', imageUrl);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {product ? (isEditing ? 'Edit Product' : 'Product Details') : 'Create New Product'}
            </h2>
            {product && (
              <p className="text-sm text-gray-600 mt-1">{product.category}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {product && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Product Image */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Product Image</h3>
              
              <div className="flex flex-col items-center space-y-4">
                {formData.image ? (
                  <div className="relative">
                    <img
                      src={formData.image}
                      alt="Product preview"
                      className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                    />
                    {isEditing && (
                      <button
                        type="button"
                        onClick={() => handleInputChange('image', '')}
                        className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <ImageIcon className="w-8 h-8 text-gray-400" />
                  </div>
                )}
                
                {isEditing && (
                  <div>
                    <label className="cursor-pointer inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Image
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </label>
                  </div>
                )}
              </div>
            </div>

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'Books', label: 'Books' },
                      { value: 'Merchandise', label: 'Merchandise' },
                      { value: 'Programs', label: 'Programs' },
                      { value: 'Donations', label: 'Donations' },
                    ]}
                    value={formData.category}
                    onChange={(value) => handleInputChange('category', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Pricing & Inventory */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Pricing & Inventory</h3>
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price (RM) *
                  </label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step="0.01"
                    required
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stock Quantity
                  </label>
                  <input
                    type="number"
                    value={formData.stock}
                    onChange={(e) => handleInputChange('stock', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    disabled={!isEditing}
                  />
                </div>

                {product && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Units Sold
                    </label>
                    <input
                      type="number"
                      value={formData.sold}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-600"
                      disabled
                    />
                    <p className="text-xs text-gray-500 mt-1">This field is automatically updated</p>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'Active', label: 'Active' },
                      { value: 'Inactive', label: 'Inactive' },
                      { value: 'Discontinued', label: 'Discontinued' },
                    ]}
                    value={formData.status}
                    onChange={(value) => handleInputChange('status', value)}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Product Statistics */}
            {product && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Statistics</h3>
                
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Value</p>
                      <p className="text-lg font-semibold">RM {(formData.price * formData.stock).toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Revenue Generated</p>
                      <p className="text-lg font-semibold">RM {(formData.price * formData.sold).toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Stock Status</p>
                      <p className={`text-sm font-medium ${formData.stock < 10 ? 'text-red-600' : formData.stock < 50 ? 'text-yellow-600' : 'text-green-600'}`}>
                        {formData.stock < 10 ? 'Low Stock' : formData.stock < 50 ? 'Medium Stock' : 'Good Stock'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Conversion Rate</p>
                      <p className="text-sm font-medium">
                        {formData.stock + formData.sold > 0 ? ((formData.sold / (formData.stock + formData.sold)) * 100).toFixed(1) : 0}%
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-800">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-4">
              <div className="flex flex-col sm:flex-row gap-2">
                {product && (
                  <>
                    <button
                      type="button"
                      onClick={handleDuplicate}
                      disabled={loading}
                      className="w-full sm:w-auto px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      <Copy className="w-4 h-4" />
                      Duplicate
                    </button>
                    <button
                      type="button"
                      onClick={handleDelete}
                      disabled={loading}
                      className="w-full sm:w-auto px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? 'Saving...' : mode === 'create' ? 'Create Product' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductSidebar;
