import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  X, 
  ArrowRight,
  Download,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { apiService } from '../services/api';
import CustomDropdown from './CustomDropdown';

interface CsvField {
  label: string;
  required: boolean;
  type: string;
  options?: string[];
  min?: number;
  max?: number;
}

interface ParseResult {
  headers: string[];
  preview: Record<string, any>[];
  totalRows: number;
}

interface ImportError {
  row: number;
  error: string;
  category: 'validation' | 'database' | 'processing' | 'format' | 'duplicate';
  field?: string;
  value?: string;
  expected_format?: string;
  severity: 'error' | 'warning';
  data?: Record<string, any>;
}

interface ImportDuplicate {
  row: number;
  existing_id: number;
  identifier: string;
  field: string;
  value: string;
  action: 'skipped' | 'updated';
  data?: Record<string, any>;
}

interface ImportResult {
  total_rows: number;
  processed: number;
  created: number;
  updated: number;
  skipped: number;
  errors: ImportError[];
  duplicates: ImportDuplicate[];
  // Enhanced reporting
  error_categories: Record<string, number>;
  processing_time: number;
  batch_size: number;
  success_rate: number;
}

interface CsvImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete?: () => void;
}

const CsvImportModal: React.FC<CsvImportModalProps> = ({ 
  isOpen, 
  onClose, 
  onImportComplete 
}) => {
  const [step, setStep] = useState<'upload' | 'mapping' | 'importing' | 'results'>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [parseResult, setParseResult] = useState<ParseResult | null>(null);
  const [availableFields, setAvailableFields] = useState<Record<string, CsvField>>({});
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [importOptions, setImportOptions] = useState({
    skip_duplicates: true,
    update_existing: false,
    batch_size: 100
  });
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [importProgressText, setImportProgressText] = useState('Preparing import...');

  const { showSuccess, showError, showInfo } = useToast();
  const { confirm } = useConfirmation();

  // Load available fields when modal opens
  useEffect(() => {
    if (isOpen && Object.keys(availableFields).length === 0) {
      loadAvailableFields();
    }
  }, [isOpen]);

  const loadAvailableFields = async () => {
    try {
      const response = await apiService.get('/csv-import/fields');
      setAvailableFields(response.fields || {});
    } catch (error) {
      console.error('Error loading available fields:', error);
      showError('Error', 'Failed to load available fields');
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const selectedFile = acceptedFiles[0];
    if (!selectedFile) return;

    setFile(selectedFile);
    setUploadProgress(0);
    
    try {
      showInfo('Upload', 'Parsing CSV file...');
      
      const formData = new FormData();
      formData.append('file', selectedFile);
      
      const response = await apiService.post('/csv-import/parse', formData);
      setParseResult(response);
      setStep('mapping');
      
      showSuccess('Success', 'CSV file parsed successfully');
      
      // Auto-map fields with improved matching logic
      const autoMapping: Record<string, string> = {};
      response.headers.forEach((header: string) => {
        const lowerHeader = header.toLowerCase().replace(/[^a-z0-9]/g, '');

        // Define specific mappings for common variations
        const fieldMappings: Record<string, string> = {
          'id': 'uuid',
          'uuid': 'uuid',
          'name': 'name',
          'fullname': 'name',
          'clientname': 'name',
          'customername': 'name',
          'email': 'email',
          'emailaddress': 'email',
          'phone': 'phone',
          'phonenumber': 'phone',
          'mobile': 'phone',
          'contact': 'phone',
          'company': 'company',
          'address': 'address',
          'addressline1': 'address_line_1',
          'address1': 'address_line_1',
          'addressline2': 'address_line_2',
          'address2': 'address_line_2',
          'city': 'city',
          'state': 'state',
          'country': 'country',
          'postcode': 'postcode',
          'postalcode': 'postal_code',
          'zip': 'postal_code',
          'zipcode': 'postal_code',
          'icnumber': 'ic_number',
          'ic': 'ic_number',
          'nric': 'ic_number',
          'birthday': 'birthday',
          'birthdate': 'birthday',
          'dateofbirth': 'birthday',
          'dob': 'birthday',
          'gender': 'gender',
          'sex': 'gender',
          'religion': 'religion',
          'income': 'income',
          'salary': 'income',
          'totalspent': 'total_spent',
          'spent': 'total_spent',
          'amount': 'total_spent',
          'transactioncount': 'transaction_count',
          'transactions': 'transactions',
          'txns': 'transactions',
          'purchases': 'transactions',
          'status': 'status',
          'category': 'category',
          'type': 'category',
          'ltvsegment': 'ltv_segment',
          'ltv': 'ltv_segment',
          'segment': 'ltv_segment',
          'engagementlevel': 'engagement_level',
          'engagement': 'engagement_level',
          'priority': 'priority',
          'namescore': 'name_score',
          'emailscore': 'email_score',
          'phonescore': 'phone_score',
          'overallscore': 'overall_score',
          'score': 'overall_score',
          'emaildeliverability': 'email_deliverability',
          'phonevalidity': 'phone_validity',
          'phonecarrier': 'phone_carrier',
          'carrier': 'phone_carrier',
          'dataquality': 'data_quality',
          'quality': 'data_quality',
          'emailverified': 'email_verified',
          'phoneverified': 'phone_verified',
          'verified': 'email_verified',
          'utmsource': 'utm_source',
          'source': 'utm_source',
          'notes': 'notes',
          'remarks': 'notes_remarks',
          'comments': 'notes',
          'suggestedaction': 'suggested_action',
          'action': 'suggested_action',
          'customercategory': 'customer_category',
          'behaviour': 'behaviour',
          'behavior': 'behaviour',
          'interest': 'interest'
        };

        // First try exact mapping
        if (fieldMappings[lowerHeader]) {
          autoMapping[header] = fieldMappings[lowerHeader];
          return;
        }

        // Fallback to fuzzy matching
        const matchingField = Object.keys(availableFields).find(field => {
          const lowerField = field.toLowerCase().replace(/[^a-z0-9]/g, '');
          return lowerField === lowerHeader ||
                 lowerField.includes(lowerHeader) ||
                 lowerHeader.includes(lowerField);
        });

        if (matchingField) {
          autoMapping[header] = matchingField;
        }
      });
      setMapping(autoMapping);
      
    } catch (error) {
      console.error('Error parsing file:', error);
      showError('Error', 'Failed to parse CSV file. Please check the file format.');
    }
  }, [availableFields, showSuccess, showError, showInfo]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'text/plain': ['.txt']
    },
    maxFiles: 1,
    maxSize: 50 * 1024 * 1024 // 50MB
  });

  const handleMappingChange = (csvColumn: string, dbField: string) => {
    setMapping(prev => ({
      ...prev,
      [csvColumn]: dbField
    }));
  };

  const handleImport = async () => {
    if (!file || !parseResult) return;

    const confirmed = await confirm({
      title: 'Confirm Import',
      message: `Are you sure you want to import ${(parseResult.totalRows - 1).toLocaleString()} data records? This action cannot be undone.`,
      confirmText: 'Import',
      cancelText: 'Cancel',
      type: 'warning'
    });

    if (!confirmed) return;

    setImporting(true);
    setStep('importing');
    setUploadProgress(0);
    setImportProgress(0);
    setImportProgressText('Preparing import...');

    try {
      showInfo('Import', 'Starting CSV import...');

      // Simulate progress updates during import
      const progressSteps = [
        { progress: 15, text: 'Uploading file...' },
        { progress: 30, text: 'Validating data...' },
        { progress: 50, text: 'Processing records...' },
        { progress: 75, text: 'Importing to database...' },
        { progress: 95, text: 'Finalizing import...' }
      ];

      // Start progress animation
      let currentStep = 0;
      const progressInterval = setInterval(() => {
        if (currentStep < progressSteps.length) {
          const step = progressSteps[currentStep];
          setImportProgress(step.progress);
          setImportProgressText(step.text);
          currentStep++;
        }
      }, 600);

      const formData = new FormData();
      formData.append('file', file);

      // Send mapping as FormData array structure
      Object.entries(mapping).forEach(([csvColumn, dbField]) => {
        if (dbField) { // Only include mapped fields
          formData.append(`mapping[${csvColumn}]`, dbField);
        }
      });

      // Send options as FormData structure with proper boolean values
      formData.append('options[skip_duplicates]', importOptions.skip_duplicates ? '1' : '0');
      formData.append('options[update_existing]', importOptions.update_existing ? '1' : '0');
      formData.append('options[batch_size]', importOptions.batch_size.toString());

      const response = await apiService.post('/csv-import/import', formData);

      // Clear progress interval and set to exactly 100%
      clearInterval(progressInterval);
      setImportProgress(100); // Set to exactly 100% when import completes
      setImportProgressText('Import completed!');

      setImportResult(response.results);
      setStep('results');

      showSuccess('Success', `Import completed! Created ${response.results.created} records.`);

      // Save import history
      try {
        await apiService.post('/import-history', {
          file_name: file?.name || 'Unknown',
          file_size: file?.size || 0,
          data_type: 'clients', // CsvImportModal is typically used for clients
          file_format: 'csv',
          total_rows: response.results.total_rows,
          processed: response.results.processed,
          created: response.results.created,
          updated: response.results.updated,
          skipped: response.results.skipped,
          errors_count: response.results.errors.length,
          duplicates_count: response.results.duplicates?.length || 0,
          processing_time: response.results.processing_time || 0,
          success_rate: response.results.success_rate || 0,
          error_details: response.results.errors || [],
          import_options: importOptions,
          field_mapping: mapping,
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
        });

        showSuccess('Import History', 'Import details saved to history');
      } catch (historyError) {
        console.error('Failed to save CSV import history:', historyError);
        showError('Import History', 'Failed to save import history. Import was successful but history was not recorded.');
        // Don't fail the import if history saving fails
      }

      if (onImportComplete) {
        onImportComplete();
      }
    } catch (error) {
      console.error('Error importing file:', error);

      // Reset progress on error
      setImportProgress(0);
      setImportProgressText('Import failed');

      showError('Error', 'Failed to import CSV file. Please try again.');
      setStep('mapping');
    } finally {
      setImporting(false);
    }
  };

  const resetImport = () => {
    setStep('upload');
    setFile(null);
    setParseResult(null);
    setMapping({});
    setImportResult(null);
    setUploadProgress(0);
    setImportProgress(0);
    setImportProgressText('Preparing import...');
  };

  const handleClose = () => {
    if (importing) {
      return; // Prevent closing during import
    }
    resetImport();
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !importing) {
      handleClose();
    }
  };

  // Handle ESC key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !importing) {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isOpen, importing]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4"
      onClick={handleOverlayClick}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh'
      }}
    >
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Import Clients from CSV
          </h2>
          {!importing && (
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${step === 'upload' ? 'text-blue-600' : step === 'mapping' || step === 'importing' || step === 'results' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'upload' ? 'bg-blue-100' : step === 'mapping' || step === 'importing' || step === 'results' ? 'bg-green-100' : 'bg-gray-100'}`}>
                {step === 'mapping' || step === 'importing' || step === 'results' ? <CheckCircle className="w-4 h-4" /> : '1'}
              </div>
              <span className="text-sm font-medium">Upload File</span>
            </div>
            
            <ArrowRight className="w-4 h-4 text-gray-400" />
            
            <div className={`flex items-center space-x-2 ${step === 'mapping' ? 'text-blue-600' : step === 'importing' || step === 'results' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'mapping' ? 'bg-blue-100' : step === 'importing' || step === 'results' ? 'bg-green-100' : 'bg-gray-100'}`}>
                {step === 'importing' || step === 'results' ? <CheckCircle className="w-4 h-4" /> : '2'}
              </div>
              <span className="text-sm font-medium">Map Columns</span>
            </div>
            
            <ArrowRight className="w-4 h-4 text-gray-400" />
            
            <div className={`flex items-center space-x-2 ${step === 'importing' ? 'text-blue-600' : step === 'results' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'importing' ? 'bg-blue-100' : step === 'results' ? 'bg-green-100' : 'bg-gray-100'}`}>
                {step === 'results' ? <CheckCircle className="w-4 h-4" /> : step === 'importing' ? <RefreshCw className="w-4 h-4 animate-spin" /> : '3'}
              </div>
              <span className="text-sm font-medium">Import</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {step === 'upload' && (
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  Upload a CSV file to import client data. The file should contain client information with headers.
                </p>
              </div>

              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  isDragActive 
                    ? 'border-blue-400 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                {isDragActive ? (
                  <p className="text-blue-600 font-medium">Drop the CSV file here...</p>
                ) : (
                  <div>
                    <p className="text-gray-600 font-medium mb-2">
                      Drag & drop a CSV file here, or click to select
                    </p>
                    <p className="text-sm text-gray-500">
                      Maximum file size: 50MB
                    </p>
                  </div>
                )}
              </div>

              {file && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <FileText className="w-8 h-8 text-blue-600" />
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {step === 'mapping' && parseResult && (
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  Map CSV columns to database fields. Required fields are marked with an asterisk (*).
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-blue-800">
                    <strong>File:</strong> {file?.name} • <strong>Data Rows:</strong> {(parseResult.totalRows - 1).toLocaleString()} (excluding header)
                  </p>
                </div>
              </div>

              {/* Column Mapping */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Column Mapping</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {parseResult.headers.map((header, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="mb-2">
                        <label className="block text-sm font-medium text-gray-700">
                          CSV Column: <span className="font-mono text-blue-600">{header}</span>
                        </label>
                      </div>
                      <CustomDropdown
                        value={mapping[header] || ''}
                        onChange={(value) => handleMappingChange(header, value)}
                        options={[
                          { value: '', label: 'Skip this column' },
                          ...Object.entries(availableFields).map(([key, field]) => ({
                            value: key,
                            label: `${field.label}${field.required ? ' *' : ''}`
                          }))
                        ]}
                        placeholder="Select database field"
                      />
                      {parseResult.preview.length > 0 && (
                        <div className="mt-2 text-xs text-gray-500">
                          <strong>Sample:</strong> {parseResult.preview[0][header] || 'N/A'}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Import Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Import Options</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={importOptions.skip_duplicates}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, skip_duplicates: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Skip duplicates</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={importOptions.update_existing}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, update_existing: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Update existing</span>
                  </label>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">Batch size:</label>
                    <input
                      type="number"
                      min="10"
                      max="1000"
                      value={importOptions.batch_size}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, batch_size: parseInt(e.target.value) || 100 }))}
                      className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Preview */}
              {parseResult.preview.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Data Preview</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {parseResult.headers.map((header, index) => (
                            <th key={index} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {header}
                              {mapping[header] && (
                                <div className="text-blue-600 normal-case">
                                  → {availableFields[mapping[header]]?.label}
                                </div>
                              )}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {parseResult.preview.slice(0, 3).map((row, rowIndex) => (
                          <tr key={rowIndex}>
                            {parseResult.headers.map((header, colIndex) => (
                              <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {row[header] || '-'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <button
                  onClick={() => setStep('upload')}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={handleImport}
                  disabled={Object.keys(mapping).length === 0}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Start Import
                </button>
              </div>
            </div>
          )}

          {step === 'importing' && (
            <div className="space-y-6 text-center py-8">
              <div className="flex flex-col items-center space-y-6">
                {/* Progress Circle Icon */}
                <div className="relative w-20 h-20">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                  <div
                    className="absolute inset-0 rounded-full border-4 border-blue-600 transition-all duration-500 ease-out"
                    style={{
                      borderTopColor: 'transparent',
                      borderRightColor: 'transparent',
                      transform: `rotate(${(importProgress / 100) * 360}deg)`
                    }}
                  ></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Upload className="w-8 h-8 text-blue-600" />
                  </div>
                </div>

                {/* Progress Percentage */}
                <div>
                  <span className="text-3xl font-bold text-blue-600 transition-all duration-300">
                    {Math.round(Math.min(100, importProgress))}%
                  </span>
                </div>

                {/* Progress Bar */}
                <div className="w-full max-w-md">
                  <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${Math.min(100, importProgress)}%` }}
                    ></div>
                  </div>
                </div>

                {/* Progress Text */}
                <div>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">Importing Data</h3>
                  <p className="text-gray-600 transition-all duration-300">{importProgressText}</p>

                  {/* Estimated Records */}
                  {parseResult && (
                    <p className="text-sm text-gray-500 mt-2">
                      Processing {(parseResult.totalRows - 1).toLocaleString()} data records...
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {step === 'results' && importResult && (
            <div className="space-y-6">
              <div className="text-center">
                <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">Import Complete!</h3>
                <p className="text-gray-600">Your CSV data has been successfully imported.</p>
              </div>

              {/* Results Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{importResult.total_rows - 1}</div>
                  <div className="text-sm text-blue-800">Data Rows</div>
                  <div className="text-xs text-blue-600 mt-1">Excluding header</div>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{importResult.created}</div>
                  <div className="text-sm text-green-800">Created</div>
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">{importResult.updated}</div>
                  <div className="text-sm text-yellow-800">Updated</div>
                </div>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-gray-600">{importResult.skipped}</div>
                  <div className="text-sm text-gray-800">Skipped</div>
                </div>
              </div>

              {/* Errors */}
              {importResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                    <h4 className="font-medium text-red-800">Errors ({importResult.errors.length})</h4>
                  </div>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {importResult.errors.slice(0, 10).map((error, index) => (
                      <div key={index} className="text-sm text-red-700">
                        Row {error.row}: {error.error}
                      </div>
                    ))}
                    {importResult.errors.length > 10 && (
                      <div className="text-sm text-red-600 font-medium">
                        ... and {importResult.errors.length - 10} more errors
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Duplicates */}
              {importResult.duplicates.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                    <h4 className="font-medium text-yellow-800">Duplicates Found ({importResult.duplicates.length})</h4>
                  </div>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {importResult.duplicates.slice(0, 10).map((duplicate, index) => (
                      <div key={index} className="text-sm text-yellow-700">
                        Row {duplicate.row}: {duplicate.identifier} (ID: {duplicate.existing_id})
                      </div>
                    ))}
                    {importResult.duplicates.length > 10 && (
                      <div className="text-sm text-yellow-600 font-medium">
                        ... and {importResult.duplicates.length - 10} more duplicates
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <button
                  onClick={resetImport}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Import Another File
                </button>
                <button
                  onClick={handleClose}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CsvImportModal;
