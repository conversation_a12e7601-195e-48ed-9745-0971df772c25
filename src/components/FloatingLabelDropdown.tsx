import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface DropdownOption {
  value: string;
  label: string;
}

interface FloatingLabelDropdownProps {
  id: string;
  name: string;
  value: string;
  onChange: (value: string) => void;
  label: string;
  options: DropdownOption[];
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

const FloatingLabelDropdown: React.FC<FloatingLabelDropdownProps> = ({
  id,
  name,
  value,
  onChange,
  label,
  options,
  required = false,
  disabled = false,
  className = '',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Determine if label should be floating
  const isFloating = isFocused || value.length > 0 || isOpen;

  // Get display text for selected value
  const selectedOption = options.find(option => option.value === value);
  const displayText = selectedOption ? selectedOption.label : '';

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setIsFocused(true);
      }
    }
  };

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setIsFocused(false);
  };

  const handleLabelClick = () => {
    if (!disabled) {
      handleToggle();
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        handleToggle();
        break;
      case 'Escape':
        setIsOpen(false);
        setIsFocused(false);
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setIsFocused(true);
        }
        break;
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Dropdown Button */}
      <button
        type="button"
        id={id}
        name={name}
        onClick={handleToggle}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        className={`
          w-full px-3 pt-6 pb-2 pr-10 border rounded-lg transition-all duration-200 ease-in-out text-left
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
          ${disabled ? 'bg-gray-50 dark:bg-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-700 cursor-pointer'}
          ${isFocused || isOpen ? 'border-blue-500' : 'border-gray-300 dark:border-gray-600'}
          text-gray-900 dark:text-white
        `}
      >
        <span className={displayText ? '' : 'text-transparent'}>
          {displayText || 'placeholder'}
        </span>
      </button>

      {/* Floating Label */}
      <label
        htmlFor={id}
        onClick={handleLabelClick}
        className={`
          absolute left-3 transition-all duration-200 ease-in-out cursor-pointer
          ${isFloating 
            ? 'top-1 text-xs text-blue-600 dark:text-blue-400' 
            : 'top-1/2 -translate-y-1/2 text-sm text-gray-500 dark:text-gray-400'
          }
          ${disabled ? 'cursor-not-allowed' : ''}
          pointer-events-none
        `}
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {/* Dropdown Arrow */}
      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
        <ChevronDown 
          className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </div>

      {/* Dropdown Options */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-auto">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleOptionSelect(option.value)}
              className={`
                w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors
                ${value === option.value ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}
                ${option.value === '' ? 'text-gray-500 dark:text-gray-400' : ''}
              `}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default FloatingLabelDropdown;
