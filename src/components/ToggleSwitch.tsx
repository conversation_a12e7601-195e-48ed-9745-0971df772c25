import React from 'react';

interface ToggleSwitchProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  label?: string;
  description?: string;
  showLabels?: boolean;
  enabledLabel?: string;
  disabledLabel?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  enabled,
  onChange,
  disabled = false,
  size = 'md',
  className = '',
  label,
  description,
  showLabels = false,
  enabledLabel = 'On',
  disabledLabel = 'Off'
}) => {
  const sizeClasses = {
    sm: {
      container: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4'
    },
    md: {
      container: 'w-11 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-5'
    },
    lg: {
      container: 'w-14 h-7',
      thumb: 'w-6 h-6',
      translate: 'translate-x-7'
    }
  };

  const currentSize = sizeClasses[size];

  const handleClick = () => {
    if (!disabled) {
      onChange(!enabled);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
      e.preventDefault();
      onChange(!enabled);
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      {label && (
        <div className="flex-1 mr-4">
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {label}
          </div>
          {description && (
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {description}
            </div>
          )}
        </div>
      )}
      
      <div className="flex items-center space-x-3">
        {showLabels && (
          <span className={`text-sm font-medium ${
            !enabled ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'
          }`}>
            {disabledLabel}
          </span>
        )}
        
        <button
          type="button"
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className={`
            relative inline-flex items-center ${currentSize.container} rounded-full
            transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            ${disabled 
              ? 'bg-gray-200 dark:bg-gray-600 cursor-not-allowed' 
              : enabled 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500'
            }
          `}
          role="switch"
          aria-checked={enabled}
          aria-disabled={disabled}
        >
          <span
            className={`
              ${currentSize.thumb} inline-block rounded-full bg-white shadow-lg transform transition-transform duration-200 ease-in-out
              ${enabled ? currentSize.translate : 'translate-x-0'}
            `}
          />
        </button>
        
        {showLabels && (
          <span className={`text-sm font-medium ${
            enabled ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'
          }`}>
            {enabledLabel}
          </span>
        )}
      </div>
    </div>
  );
};

export default ToggleSwitch;
