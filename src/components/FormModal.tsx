import React, { useState, useEffect } from 'react';
import { Save, Plus, Trash2 } from 'lucide-react';
import Modal from './Modal';
import CustomDropdown from './CustomDropdown';

interface FormField {
  id: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'textarea' | 'checkbox';
  label: string;
  required: boolean;
  options?: string[];
}

interface Form {
  id?: string;
  name: string;
  description: string;
  fields: FormField[];
  linkedProduct?: string;
  status: 'Active' | 'Draft' | 'Archived';
  submissions?: number;
  conversionRate?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface FormModalProps {
  isOpen: boolean;
  onClose: () => void;
  form?: Form;
  mode: 'create' | 'edit' | 'view';
  onSave: (form: Form) => void;
}

const FormModal: React.FC<FormModalProps> = ({ isOpen, onClose, form, mode, onSave }) => {
  const [formData, setFormData] = useState<Form>({
    name: '',
    description: '',
    fields: [],
    linkedProduct: '',
    status: 'Draft',
    submissions: 0,
    conversionRate: 0,
  });

  useEffect(() => {
    if (form && (mode === 'edit' || mode === 'view')) {
      setFormData(form);
    } else {
      setFormData({
        name: '',
        description: '',
        fields: [],
        linkedProduct: '',
        status: 'Draft',
        submissions: 0,
        conversionRate: 0,
      });
    }
  }, [form, mode]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  const addField = () => {
    const newField: FormField = {
      id: Date.now().toString(),
      type: 'text',
      label: '',
      required: false,
    };
    setFormData({
      ...formData,
      fields: [...formData.fields, newField],
    });
  };

  const updateField = (index: number, field: FormField) => {
    const updatedFields = [...formData.fields];
    updatedFields[index] = field;
    setFormData({
      ...formData,
      fields: updatedFields,
    });
  };

  const removeField = (index: number) => {
    const updatedFields = formData.fields.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      fields: updatedFields,
    });
  };

  const isReadOnly = mode === 'view';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create New Form' : mode === 'edit' ? 'Edit Form' : 'Form Details'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Form Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              required
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <CustomDropdown
              options={[
                { value: 'Active', label: 'Active' },
                { value: 'Draft', label: 'Draft' },
                { value: 'Archived', label: 'Archived' },
              ]}
              value={formData.status}
              onChange={(value) => setFormData({ ...formData, status: value as Form['status'] })}
              className="w-full"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Linked Product (Optional)
          </label>
          <input
            type="text"
            value={formData.linkedProduct}
            onChange={(e) => setFormData({ ...formData, linkedProduct: e.target.value })}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter product name"
            disabled={isReadOnly}
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Form Fields</h3>
            {!isReadOnly && (
              <button
                type="button"
                onClick={addField}
                className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors flex items-center space-x-1"
              >
                <Plus className="w-4 h-4" />
                <span>Add Field</span>
              </button>
            )}
          </div>

          <div className="space-y-4">
            {formData.fields.map((field, index) => (
              <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Field Label
                    </label>
                    <input
                      type="text"
                      value={field.label}
                      onChange={(e) => updateField(index, { ...field, label: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Field Type
                    </label>
                    <CustomDropdown
                      options={[
                        { value: 'text', label: 'Text' },
                        { value: 'email', label: 'Email' },
                        { value: 'phone', label: 'Phone' },
                        { value: 'select', label: 'Select' },
                        { value: 'textarea', label: 'Textarea' },
                        { value: 'checkbox', label: 'Checkbox' },
                      ]}
                      value={field.type}
                      onChange={(value) => updateField(index, { ...field, type: value as FormField['type'] })}
                      className="w-full"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={field.required}
                        onChange={(e) => updateField(index, { ...field, required: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        disabled={isReadOnly}
                      />
                      <label className="text-sm text-gray-700">Required</label>
                    </div>
                    {!isReadOnly && (
                      <button
                        type="button"
                        onClick={() => removeField(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {field.type === 'select' && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Options (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={field.options?.join(', ') || ''}
                      onChange={(e) => updateField(index, { 
                        ...field, 
                        options: e.target.value.split(',').map(opt => opt.trim()).filter(opt => opt) 
                      })}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Option 1, Option 2, Option 3"
                      disabled={isReadOnly}
                    />
                  </div>
                )}
              </div>
            ))}

            {formData.fields.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No fields added yet. Click "Add Field" to get started.
              </div>
            )}
          </div>
        </div>

        {!isReadOnly && (
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{mode === 'create' ? 'Create Form' : 'Save Changes'}</span>
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

export default FormModal;