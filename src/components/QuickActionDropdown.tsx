import React, { useEffect, useRef } from 'react';
import { Users, MessageSquare, FormInput, Package, BarChart3, Activity, Target, Briefcase } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface QuickActionDropdownProps {
  onClose: () => void;
}

const QuickActionDropdown: React.FC<QuickActionDropdownProps> = ({ onClose }) => {
  const navigate = useNavigate();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const quickActions = [
    { id: 'new-client', name: 'New Client', icon: Users, action: () => navigate('/clients') },
    { id: 'view-leads', name: 'View Leads', icon: Target, action: () => navigate('/leads') },
    { id: 'view-deals', name: 'View Deals', icon: Briefcase, action: () => navigate('/deals') },
    { id: 'new-campaign', name: 'New Campaign', icon: MessageSquare, action: () => navigate('/engagements') },
    { id: 'new-form', name: 'New Form', icon: FormInput, action: () => navigate('/forms') },
    { id: 'new-product', name: 'New Product', icon: Package, action: () => navigate('/products') },
    { id: 'view-reports', name: 'View Reports', icon: BarChart3, action: () => navigate('/') },
    { id: 'view-activities', name: 'View Activities', icon: Activity, action: () => navigate('/logs') },
  ];

  const handleAction = (action: () => void) => {
    action();
    onClose();
  };

  // Handle ESC key and click outside
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div
      ref={dropdownRef}
      className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 max-w-[calc(100vw-2rem)] mr-4 sm:mr-0 transition-colors duration-300"
    >
      <div className="p-2">
        <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-100 dark:border-gray-700 mb-2">
          Quick Actions
        </div>
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={() => handleAction(action.action)}
              className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors"
            >
              <Icon className="w-4 h-4" />
              <span>{action.name}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default QuickActionDropdown;