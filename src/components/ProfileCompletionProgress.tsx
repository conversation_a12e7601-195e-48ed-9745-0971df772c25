import React, { useState, useEffect } from 'react';
import { CheckCircle, Circle, ChevronDown, ChevronUp } from 'lucide-react';
import { Client } from '../contexts/ClientContext';
import { calculateProfileCompletion, ProfileCompletionScore } from '../utils/profileCompletion';

interface ProfileCompletionProgressProps {
  client: Client;
  showBreakdown?: boolean;
  className?: string;
}

const ProfileCompletionProgress: React.FC<ProfileCompletionProgressProps> = ({
  client,
  showBreakdown = false,
  className = ''
}) => {
  const [completionData, setCompletionData] = useState<ProfileCompletionScore | null>(null);
  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const data = calculateProfileCompletion(client);
    setCompletionData(data);

    // Animate the percentage counter
    const duration = 1500; // 1.5 seconds
    const steps = 75; // Number of animation steps
    const increment = data.percentage / steps;
    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;
      setAnimatedPercentage(Math.min(Math.round(increment * currentStep), data.percentage));
      
      if (currentStep >= steps) {
        clearInterval(timer);
        setAnimatedPercentage(data.percentage);
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [client]);

  if (!completionData) {
    return <div className="animate-pulse bg-gray-200 dark:bg-gray-600 h-4 rounded"></div>;
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-blue-500';
    if (percentage >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getTextColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 dark:text-green-400';
    if (percentage >= 60) return 'text-blue-600 dark:text-blue-400';
    if (percentage >= 40) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  const categoryNames = {
    core: 'Core Information',
    business: 'Business Data',
    actions: 'Verification & Actions',
    personal: 'Personal Information',
    address: 'Address Details',
    behavioral: 'Behavioral Data'
  };

  const fieldNames = {
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    transactions: 'Transactions',
    totalSpent: 'Total Spent',
    transactionCount: 'Transaction Count',
    utmSource: 'UTM Source',
    tags: 'Tags',
    customerCategory: 'Customer Category',
    ltvSegment: 'LTV Segment',
    engagementLevel: 'Engagement Level',
    priority: 'Priority',
    suggestedNextAction: 'Suggested Next Action',
    phoneVerified: 'Phone Verified',
    emailVerified: 'Email Verified',
    icNumber: 'IC Number',
    birthday: 'Birthday',
    gender: 'Gender',
    religion: 'Religion',
    income: 'Income',
    incomeCategory: 'Income Category',
    addressLine1: 'Address Line 1',
    addressLine2: 'Address Line 2',
    city: 'City',
    postcode: 'Postcode',
    state: 'State',
    behaviour: 'Behavior',
    interest: 'Interest'
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Profile Completion</span>
          <span className={`text-sm font-bold ${getTextColor(completionData.percentage)}`}>
            {animatedPercentage}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3">
          <div
            className={`h-3 rounded-full transition-all duration-1200 ease-out ${getProgressColor(completionData.percentage)}`}
            style={{ width: `${animatedPercentage}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {completionData.score} of {completionData.maxScore} points completed
        </div>
      </div>

      {/* Breakdown Toggle */}
      {showBreakdown && (
        <div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <span>View Details</span>
            {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>

          <div className="mt-4 space-y-4 border-t border-gray-200 dark:border-gray-600 pt-4">
            {/* Always show Core Information and Business Data */}
            {Object.entries(completionData.breakdown)
              .filter(([categoryKey]) => categoryKey === 'core' || categoryKey === 'business')
              .map(([categoryKey, categoryData]) => (
                <div key={categoryKey} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      {categoryNames[categoryKey as keyof typeof categoryNames]}
                    </h4>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {categoryData.score}/{categoryData.maxScore}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {Object.entries(categoryData.fields).map(([fieldKey, fieldData]) => (
                      <div key={fieldKey} className="flex items-center space-x-2">
                        {fieldData.completed ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <Circle className="w-4 h-4 text-gray-300 dark:text-gray-600" />
                        )}
                        <span className={`text-xs ${fieldData.completed ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
                          {fieldNames[fieldKey as keyof typeof fieldNames]}
                        </span>
                        <span className="text-xs text-gray-400 dark:text-gray-500">
                          ({fieldData.score}/{fieldData.maxScore})
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

            {/* Show remaining sections only when details are expanded */}
            {showDetails && (
              <>
                {Object.entries(completionData.breakdown)
                  .filter(([categoryKey]) => categoryKey !== 'core' && categoryKey !== 'business')
                  .map(([categoryKey, categoryData]) => (
                    <div key={categoryKey} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <h4 className="text-sm font-medium text-gray-800">
                          {categoryNames[categoryKey as keyof typeof categoryNames]}
                        </h4>
                        <span className="text-sm text-gray-600">
                          {categoryData.score}/{categoryData.maxScore}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {Object.entries(categoryData.fields).map(([fieldKey, fieldData]) => (
                          <div key={fieldKey} className="flex items-center space-x-2">
                            {fieldData.completed ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <Circle className="w-4 h-4 text-gray-300" />
                            )}
                            <span className={`text-xs ${fieldData.completed ? 'text-gray-900' : 'text-gray-500'}`}>
                              {fieldNames[fieldKey as keyof typeof fieldNames]}
                            </span>
                            <span className="text-xs text-gray-400">
                              ({fieldData.score}/{fieldData.maxScore})
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileCompletionProgress;
