import React, { useState, useEffect } from 'react';
import { Deal, CreateDealRequest, UpdateDealRequest, PIPELINE_STAGES, PipelineStage } from '../types/deal';
import { useDeal } from '../contexts/DealContext';
import { useLeads } from '../contexts/LeadContext';
import { useQuotations } from '../contexts/QuotationContext';
import { useInvoices } from '../contexts/InvoiceContext';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { apiService } from '../services/api';
import CustomDropdown from './CustomDropdown';
import {
  X,
  Save,
  Calendar,
  DollarSign,
  User,
  Building2,
  FileText,
  Clock,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Copy
} from 'lucide-react';

interface DealModalProps {
  deal: Deal | null;
  isOpen: boolean;
  onClose: () => void;
  mode?: 'view' | 'edit' | 'create';
}

const DealModal: React.FC<DealModalProps> = ({ deal, isOpen, onClose, mode = 'view' }) => {
  const { createDeal, updateDeal, users } = useDeal();
  const { leads: frontendLeads } = useLeads();
  const { createQuotation } = useQuotations();
  const { createInvoiceFromDeal } = useInvoices();
  const { showSuccess, showError } = useToast();
  const { confirm } = useConfirmation();
  const [backendLeads, setBackendLeads] = useState<any[]>([]);
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !deal);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'notes' | 'audit'>('notes');

  const [formData, setFormData] = useState<CreateDealRequest>({
    title: '',
    description: '',
    value: 0,
    expectedRevenue: 0,
    currency: 'MYR',
    pipelineStage: 'prospecting',
    probability: 10,
    dealSize: 'Medium',
    priority: 'Medium',
    dealType: 'New Business',
    tags: [],
    competitors: [],
    notes: '',
    internalNotes: '',
    autoFollowUpEnabled: true,
    followUpFrequencyDays: 7,
    leadId: '',
    assignedTo: ''
  });

  // Fetch leads from backend API
  const fetchBackendLeads = async () => {
    try {
      const response = await apiService.getLeads({ per_page: 100 });
      setBackendLeads(response.data || []);
    } catch (error) {
      console.error('Failed to fetch backend leads:', error);
      setBackendLeads([]);
    }
  };

  // Combine frontend and backend leads with deduplication
  const allLeads = (() => {
    const leadMap = new Map();

    // Add frontend leads first
    frontendLeads.forEach(lead => {
      const key = `${lead.email}-${lead.name}`.toLowerCase();
      if (!leadMap.has(key)) {
        leadMap.set(key, {
          id: lead.id,
          name: lead.name,
          email: lead.email,
          company: lead.company,
          source: 'frontend'
        });
      }
    });

    // Add backend leads, but skip if already exists
    backendLeads.forEach(lead => {
      const key = `${lead.email}-${lead.name}`.toLowerCase();
      if (!leadMap.has(key)) {
        leadMap.set(key, {
          id: lead.id.toString(),
          name: lead.name,
          email: lead.email,
          company: lead.company,
          source: 'backend'
        });
      }
    });

    return Array.from(leadMap.values());
  })();

  useEffect(() => {
    // Fetch backend leads when modal opens
    if (isOpen) {
      cleanupLocalStorageLeads();
      fetchBackendLeads();
    }
  }, [isOpen]);

  useEffect(() => {
    if (deal) {
      setFormData({
        title: deal.title,
        description: deal.description || '',
        value: deal.value,
        expectedRevenue: deal.expectedRevenue || deal.value,
        currency: deal.currency,
        pipelineStage: deal.pipelineStage,
        probability: deal.probability,
        dealSize: deal.dealSize,
        expectedCloseDate: deal.expectedCloseDate,
        priority: deal.priority,
        dealType: deal.dealType,
        tags: deal.tags,
        competitors: deal.competitors,
        competitiveAdvantage: deal.competitiveAdvantage,
        notes: deal.notes || '',
        internalNotes: deal.internalNotes || '',
        nextAction: deal.nextAction,
        autoFollowUpEnabled: deal.autoFollowUpEnabled,
        followUpFrequencyDays: deal.followUpFrequencyDays,
        assignedTo: deal.assignedTo?.id,
        clientId: deal.clientId,
        leadId: deal.leadId
      });
      setIsEditing(mode === 'edit');
    } else {
      // Reset form for new deal
      setFormData({
        title: '',
        description: '',
        value: 0,
        expectedRevenue: 0,
        currency: 'MYR',
        pipelineStage: 'prospecting',
        probability: 10,
        dealSize: 'Medium',
        priority: 'Medium',
        dealType: 'New Business',
        tags: [],
        competitors: [],
        notes: '',
        internalNotes: '',
        autoFollowUpEnabled: true,
        followUpFrequencyDays: 7,
        leadId: '',
        assignedTo: ''
      });
      setIsEditing(true);
    }
    setError(null);
  }, [deal]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Prepare form data for submission
      const submitData = { ...formData };

      // Handle lead ID: convert frontend leads to backend leads, or use existing backend leads
      if (submitData.leadId) {
        // Check if leadId is a string that starts with 'lead-' (frontend lead)
        if (typeof submitData.leadId === 'string' && submitData.leadId.startsWith('lead-')) {

          // Find the frontend lead in localStorage
          const frontendLead = allLeads.find(lead => lead.id === submitData.leadId);
          if (frontendLead && frontendLead.source === 'frontend') {
            try {
              // Convert frontend lead to backend lead
              const backendLead = await apiService.createLead({
                name: frontendLead.name || '',
                email: frontendLead.email || '',
                phone: frontendLead.phone || '',
                company: frontendLead.company || '',
                status: frontendLead.status || 'new',
                source: 'converted_from_frontend',
                notes: frontendLead.notes || '',
                tags: frontendLead.tags || [],
                utm_source: frontendLead.utmSource || '',
                utm_campaign: frontendLead.utmCampaign || '',
                utm_medium: frontendLead.utmMedium || ''
              });

              submitData.leadId = backendLead.id;

              // Remove the frontend lead from localStorage since it's now in the database
              const frontendLeads = JSON.parse(localStorage.getItem('leads') || '[]');
              const updatedLeads = frontendLeads.filter((lead: any) => lead.id !== frontendLead.id);
              localStorage.setItem('leads', JSON.stringify(updatedLeads));

              // Lead conversion is now silent - no user notification needed
            } catch (error) {
              console.error('Failed to convert frontend lead to backend:', error);
              showError('Failed to convert lead to database. Please try again.');
              return;
            }
          } else {
            delete submitData.leadId;
          }
        } else {
          // Keep backend lead IDs as strings - the API service will convert them
        }
      }

      // Debug logging for deal update
      if (deal) {
          dealId: deal.id,
          originalLeadId: deal.leadId,
          formDataLeadId: formData.leadId,
          submitDataLeadId: submitData.leadId,
          fullSubmitData: submitData
        });
      }

      // Check if stage changed and offer appropriate creation options
      const stageChanged = deal && deal.pipelineStage !== submitData.pipelineStage;
      const movedToWon = stageChanged && submitData.pipelineStage === 'won';

      if (deal) {
        await updateDeal(deal.id, submitData as UpdateDealRequest);

        // Note: Quotation and invoice creation popups removed - use action buttons in deal cards instead
        showSuccess('Deal updated successfully!');
      } else {
        await createDeal(submitData);
        showSuccess('Deal created successfully!');
      }
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateDealRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleReopenDeal = async (deal: Deal) => {
    try {
      setLoading(true);

      // Update deal to negotiation stage
      const updatedData = {
        pipelineStage: 'negotiation' as PipelineStage,
        lossReason: undefined,
        lossDetails: undefined,
        notes: deal.notes + '\n\n[REOPENED] Deal reopened for negotiation on ' + new Date().toLocaleDateString(),
      };

      await updateDeal(deal.id, updatedData as UpdateDealRequest);

      // Log the reopening activity
      await fetch(`/api/deals/${deal.id}/reopen`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: 'Deal reopened for negotiation',
          notes: 'Moved from lost back to negotiation stage'
        }),
      });

      onClose();
    } catch (error) {
      console.error('Error reopening deal:', error);
      setError('Failed to reopen deal. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicateDeal = async (deal: Deal) => {
    try {
      setLoading(true);

      // Create a new deal based on the current deal
      const duplicateData: CreateDealRequest = {
        title: `${deal.title} (Copy)`,
        description: deal.description,
        value: deal.value,
        expectedRevenue: deal.expectedRevenue || deal.value,
        currency: deal.currency,
        pipelineStage: 'prospecting', // Start new deals at prospecting
        probability: 10, // Reset probability for new deal
        dealSize: deal.dealSize,
        priority: deal.priority,
        dealType: deal.dealType,
        tags: [...deal.tags],
        competitors: [...deal.competitors],
        competitiveAdvantage: deal.competitiveAdvantage,
        notes: `Duplicated from deal: ${deal.title} (${deal.dealNumber})`,
        internalNotes: deal.internalNotes,
        autoFollowUpEnabled: deal.autoFollowUpEnabled,
        followUpFrequencyDays: deal.followUpFrequencyDays,
        leadId: deal.leadId && typeof deal.leadId === 'string' && deal.leadId.startsWith('lead-') ? '' : deal.leadId, // Filter out frontend lead IDs
        assignedTo: deal.assignedTo?.id
      };

      await createDeal(duplicateData);
      showSuccess('Deal duplicated successfully!');
      onClose();
    } catch (error) {
      console.error('Error duplicating deal:', error);
      const errorMessage = 'Failed to duplicate deal. Please try again.';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {deal ? (isEditing ? 'Edit Deal' : 'Deal Details') : 'Create New Deal'}
            </h2>
            {deal && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{deal.dealNumber}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {deal && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-500 dark:text-gray-400"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {isEditing ? (
            /* Edit Form */
            <form onSubmit={handleSubmit} className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto p-6" style={{ maxHeight: 'calc(100vh - 140px)' }}>
                {error && (
                  <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-6">
                    <p className="text-red-800 dark:text-red-300">{error}</p>
                  </div>
                )}

              {/* All Form Fields in One View */}
              <div className="space-y-8">
                {/* Basic Information Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Basic Information</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Deal Title *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter deal title"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter deal description"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Lead/Prospect
                      </label>
                      <CustomDropdown
                        options={[
                          { value: '', label: 'Select a lead (optional)' },
                          ...allLeads.map(lead => ({
                            value: lead.id.toString(),
                            label: `${lead.name || 'Unnamed'} ${lead.email ? `(${lead.email})` : ''} ${lead.company ? `- ${lead.company}` : ''}`
                          }))
                        ]}
                        value={formData.leadId || ''}
                        onChange={(value) => handleInputChange('leadId', value)}
                        placeholder="Select a lead to associate with this deal"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Associate this deal with an existing lead/prospect
                      </p>
                    </div>
                  </div>
                </div>

                {/* Financial Details Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Financial Details</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Deal Value *
                      </label>
                      <input
                        type="number"
                        required
                        min="0"
                        step="0.01"
                        value={formData.value}
                        onChange={(e) => handleInputChange('value', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Expected Revenue
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.expectedRevenue}
                        onChange={(e) => handleInputChange('expectedRevenue', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Deal Size
                      </label>
                      <CustomDropdown
                        options={[
                          { value: 'Small', label: 'Small (RM 1K - 10K)' },
                          { value: 'Medium', label: 'Medium (RM 10K - 50K)' },
                          { value: 'Large', label: 'Large (RM 50K - 200K)' },
                          { value: 'Enterprise', label: 'Enterprise (RM 200K+)' }
                        ]}
                        value={formData.dealSize}
                        onChange={(value) => handleInputChange('dealSize', value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Deal Type
                      </label>
                      <CustomDropdown
                        options={[
                          { value: 'New Business', label: 'New Business' },
                          { value: 'Upsell', label: 'Upsell' },
                          { value: 'Cross-sell', label: 'Cross-sell' },
                          { value: 'Renewal', label: 'Renewal' },
                          { value: 'Expansion', label: 'Expansion' }
                        ]}
                        value={formData.dealType}
                        onChange={(value) => handleInputChange('dealType', value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Expected Close Date
                      </label>
                      <input
                        type="date"
                        value={formData.expectedCloseDate ? new Date(formData.expectedCloseDate).toISOString().split('T')[0] : ''}
                        onChange={(e) => handleInputChange('expectedCloseDate', e.target.value ? new Date(e.target.value) : undefined)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                {/* Pipeline & Settings Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Pipeline & Settings</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Pipeline Stage
                      </label>
                      <CustomDropdown
                        options={Object.entries(PIPELINE_STAGES).map(([key, stage]) => ({
                          value: key,
                          label: stage.name
                        }))}
                        value={formData.pipelineStage}
                        onChange={(value) => handleInputChange('pipelineStage', value as PipelineStage)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Probability (%)
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={formData.probability}
                        onChange={(e) => handleInputChange('probability', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Priority
                      </label>
                      <CustomDropdown
                        options={[
                          { value: 'Low', label: 'Low' },
                          { value: 'Medium', label: 'Medium' },
                          { value: 'High', label: 'High' },
                          { value: 'Critical', label: 'Critical' }
                        ]}
                        value={formData.priority}
                        onChange={(value) => handleInputChange('priority', value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Assigned To
                      </label>
                      <CustomDropdown
                        options={[
                          { value: '', label: 'Select user (optional)' },
                          ...users.map(user => ({
                            value: user.id,
                            label: `${user.name} (${user.department || 'No Department'})`
                          }))
                        ]}
                        value={formData.assignedTo || ''}
                        onChange={(value) => handleInputChange('assignedTo', value)}
                        placeholder="Assign this deal to a team member"
                      />
                    </div>
                  </div>
                </div>

                {/* Notes & Actions Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Notes & Actions</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Notes
                      </label>
                      <textarea
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Add notes about this deal"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Internal Notes
                      </label>
                      <textarea
                        value={formData.internalNotes}
                        onChange={(e) => handleInputChange('internalNotes', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Internal notes (not visible to client)"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Next Action
                      </label>
                      <input
                        type="text"
                        value={formData.nextAction || ''}
                        onChange={(e) => handleInputChange('nextAction', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="What's the next action for this deal?"
                      />
                    </div>
                  </div>
                </div>
              </div>
              </div>

              {/* Form Actions */}
              <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-6 py-4">
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2">
                  <button
                    type="button"
                    onClick={() => deal ? setIsEditing(false) : onClose()}
                    className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>

                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full sm:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                  >
                    {loading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {deal ? 'Update Deal' : 'Create Deal'}
                  </button>
                </div>
              </div>
            </form>
          ) : deal ? (
            /* View Mode */
              <div className="p-6 space-y-6">
                {/* Deal Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="md:col-span-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{deal.title}</h3>
                    {deal.description && (
                      <p className="text-gray-600 dark:text-gray-400 mb-4">{deal.description}</p>
                    )}

                    {/* Special Actions for Lost Deals */}
                    {deal.pipelineStage === 'lost' && (
                      <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-4">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                          <span className="font-medium text-red-800 dark:text-red-300">Deal Lost</span>
                        </div>
                        <p className="text-red-700 dark:text-red-300 text-sm mb-3">
                          This deal is marked as lost. You can reopen it for negotiation if circumstances change.
                        </p>
                        <button
                          onClick={() => handleReopenDeal(deal)}
                          className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors flex items-center gap-2"
                        >
                          <RefreshCw className="h-4 w-4" />
                          Reopen for Negotiation
                        </button>
                      </div>
                    )}
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="font-semibold text-green-700">{formatCurrency(deal.value)}</div>
                          <div className="text-sm text-gray-500">Deal Value</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5 text-blue-600" />
                        <div>
                          <div className="font-semibold text-blue-700">{deal.probability}%</div>
                          <div className="text-sm text-gray-500">Probability</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <div className="text-sm text-gray-500 mb-1">Pipeline Stage</div>
                      <span
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium"
                        style={{
                          backgroundColor: `${PIPELINE_STAGES[deal.pipelineStage].color}20`,
                          color: PIPELINE_STAGES[deal.pipelineStage].color,
                        }}
                      >
                        {PIPELINE_STAGES[deal.pipelineStage].name}
                      </span>
                    </div>
                    
                    <div>
                      <div className="text-sm text-gray-500 mb-1">Priority</div>
                      <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-red-100 text-red-800">
                        {deal.priority}
                      </span>
                    </div>
                    
                    <div>
                      <div className="text-sm text-gray-500 mb-1">Deal Size</div>
                      <span className="text-sm font-medium">{deal.dealSize}</span>
                    </div>
                  </div>
                </div>

                {/* Additional Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-gray-200">
                  {deal.assignedTo && (
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="font-medium">{deal.assignedTo.name}</div>
                        <div className="text-sm text-gray-500">Assigned To</div>
                      </div>
                    </div>
                  )}
                  
                  {deal.client && (
                    <div className="flex items-center gap-3">
                      <Building2 className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="font-medium">{deal.client.name}</div>
                        <div className="text-sm text-gray-500">Client</div>
                      </div>
                    </div>
                  )}
                  
                  {deal.expectedCloseDate && (
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="font-medium">{formatDate(deal.expectedCloseDate)}</div>
                        <div className="text-sm text-gray-500">Expected Close Date</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="font-medium">{deal.daysInPipeline} days</div>
                      <div className="text-sm text-gray-500">In Pipeline</div>
                    </div>
                  </div>
                </div>

                {/* Notes and Audit Trail Tabs */}
                <div className="pt-6 border-t border-gray-200">
                  {/* Tab Navigation */}
                  <div className="flex border-b border-gray-200 mb-4">
                    <button
                      onClick={() => setActiveTab('notes')}
                      className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                        activeTab === 'notes'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Notes
                      </div>
                    </button>
                    <button
                      onClick={() => setActiveTab('audit')}
                      className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                        activeTab === 'audit'
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Audit Trail
                      </div>
                    </button>
                  </div>

                  {/* Tab Content */}
                  {activeTab === 'notes' ? (
                    <div className="space-y-4">
                      {deal.notes && (
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-5 w-5 text-gray-400" />
                            <span className="font-medium text-gray-700">Notes</span>
                          </div>
                          <p className="text-gray-600 bg-gray-50 rounded-lg p-3">{deal.notes}</p>
                        </div>
                      )}

                      {deal.internalNotes && (
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <AlertTriangle className="h-5 w-5 text-orange-400" />
                            <span className="font-medium text-gray-700">Internal Notes</span>
                          </div>
                          <p className="text-gray-600 bg-orange-50 rounded-lg p-3">{deal.internalNotes}</p>
                        </div>
                      )}

                      {!deal.notes && !deal.internalNotes && (
                        <div className="text-center py-8 text-gray-500">
                          <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                          <p>No notes available for this deal</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {/* Mock Audit Trail */}
                      {[
                        {
                          id: 1,
                          action: 'Deal Created',
                          description: `Deal "${deal.title}" was created`,
                          user: deal.assignedTo?.name || 'System Administrator',
                          timestamp: deal.createdAt,
                          type: 'create'
                        },
                        {
                          id: 2,
                          action: 'Stage Changed',
                          description: `Deal moved to ${PIPELINE_STAGES[deal.pipelineStage].name}`,
                          user: deal.stageChangedBy || deal.assignedTo?.name || 'System Administrator',
                          timestamp: deal.stageChangedAt || deal.updatedAt,
                          type: 'update'
                        },
                        {
                          id: 3,
                          action: 'Value Updated',
                          description: `Deal value set to ${formatCurrency(deal.value)}`,
                          user: deal.assignedTo?.name || 'System Administrator',
                          timestamp: deal.updatedAt,
                          type: 'update'
                        }
                      ].map((activity) => (
                        <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                            activity.type === 'create' ? 'bg-green-500' :
                            activity.type === 'update' ? 'bg-blue-500' : 'bg-gray-500'
                          }`} />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                              <p className="text-xs text-gray-500">{formatDate(activity.timestamp)}</p>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                            <p className="text-xs text-gray-500 mt-1">by {activity.user}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default DealModal;
