import React, { useEffect, useState, useCallback } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  onClose
}) => {
  const [isExiting, setIsExiting] = useState(false);

  const handleClose = useCallback(() => {
    setIsExiting(true);
    // Wait for animation to complete before removing
    setTimeout(() => {
      onClose(id);
    }, 300);
  }, [id, onClose]);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [id, duration, handleClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
      default:
        return <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
    }
  };

  const getStyles = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-white dark:bg-gray-800',
          border: 'border-l-4 border-l-green-500 border-r border-t border-b border-gray-200 dark:border-gray-700',
          titleColor: 'text-green-800 dark:text-green-300',
          messageColor: 'text-green-700 dark:text-green-400',
          closeColor: 'text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300'
        };
      case 'error':
        return {
          bg: 'bg-white dark:bg-gray-800',
          border: 'border-l-4 border-l-red-500 border-r border-t border-b border-gray-200 dark:border-gray-700',
          titleColor: 'text-red-800 dark:text-red-300',
          messageColor: 'text-red-700 dark:text-red-400',
          closeColor: 'text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300'
        };
      case 'warning':
        return {
          bg: 'bg-white dark:bg-gray-800',
          border: 'border-l-4 border-l-amber-500 border-r border-t border-b border-gray-200 dark:border-gray-700',
          titleColor: 'text-amber-800 dark:text-amber-300',
          messageColor: 'text-amber-700 dark:text-amber-400',
          closeColor: 'text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300'
        };
      case 'info':
        return {
          bg: 'bg-white dark:bg-gray-800',
          border: 'border-l-4 border-l-blue-500 border-r border-t border-b border-gray-200 dark:border-gray-700',
          titleColor: 'text-blue-800 dark:text-blue-300',
          messageColor: 'text-blue-700 dark:text-blue-400',
          closeColor: 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
        };
      default:
        return {
          bg: 'bg-white dark:bg-gray-800',
          border: 'border-l-4 border-l-blue-500 border-r border-t border-b border-gray-200 dark:border-gray-700',
          titleColor: 'text-blue-800 dark:text-blue-300',
          messageColor: 'text-blue-700 dark:text-blue-400',
          closeColor: 'text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
        };
    }
  };

  const styles = getStyles();

  return (
    <div
      className={`
        ${styles.bg} ${styles.border}
        rounded-r-lg shadow-lg pointer-events-auto
        transform transition-all duration-300 ease-in-out
        ${isExiting ? 'toast-slide-out' : 'toast-slide-in'}
        w-96 max-w-sm
      `}
    >
      <div className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-0.5">
            {getIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <div className="mb-1">
              <p className={`text-sm font-semibold ${styles.titleColor}`}>
                {title}
              </p>
            </div>
            <div>
              <p className={`text-sm ${styles.messageColor}`}>
                {message}
              </p>
            </div>
          </div>
          <div className="flex-shrink-0">
            <button
              className={`inline-flex rounded-md p-1.5 ${styles.closeColor} transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 focus:ring-indigo-500 dark:focus:ring-indigo-400`}
              onClick={handleClose}
            >
              <span className="sr-only">Close</span>
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Toast;
