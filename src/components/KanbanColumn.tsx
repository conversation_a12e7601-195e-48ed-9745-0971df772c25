import React from 'react';
import { PipelineStage } from '../types/deal';

interface KanbanColumnProps {
  stage: PipelineStage;
  title: string;
  description: string;
  color: string;
  count: number;
  value: number;
  children: React.ReactNode;
}

const KanbanColumn: React.FC<KanbanColumnProps> = ({
  stage,
  title,
  description,
  color,
  count,
  value,
  children
}) => {

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="flex-shrink-0 w-80 bg-gray-50 rounded-lg transition-all duration-200 relative"
    >
      {/* Column Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: color }}
            />
            <h3 className="font-semibold text-gray-900">{title}</h3>
            <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
              {count}
            </span>
          </div>
        </div>
        
        <p className="text-sm text-gray-600 mb-2">{description}</p>
        
        {value > 0 && (
          <div className="text-sm font-medium text-gray-900">
            {formatCurrency(value)}
          </div>
        )}
      </div>

      {/* Column Content */}
      <div className="p-4 space-y-3 min-h-[400px] max-h-[600px] overflow-y-auto">
        {children}

        {count === 0 && (
          <div className="text-center py-8 text-gray-500">
            <div className="text-sm">No deals in {title.toLowerCase()}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KanbanColumn;
