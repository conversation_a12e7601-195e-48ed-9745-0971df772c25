import React, { useState, useEffect } from 'react';
import {
  Download,
  FileText,
  X,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { useToast } from '../contexts/ToastContext';
import { useAuth } from '../contexts/AuthContext';
import { logger } from '../utils/logger';
import CustomDropdown from './CustomDropdown';

interface DataExportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DataExportModal: React.FC<DataExportModalProps> = ({ isOpen, onClose }) => {
  const [exportDataType, setExportDataType] = useState('clients');
  const [exportFileFormat, setExportFileFormat] = useState('csv');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const { showSuccess, showError, showInfo } = useToast();
  const { token } = useAuth();

  // Handle ESC key and body overflow
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !isExporting) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose, isExporting]);

  const dataTypeOptions = [
    { value: 'clients', label: 'Clients' },
    { value: 'leads', label: 'Leads' },
    { value: 'deals', label: 'Deals' },
    { value: 'quotations', label: 'Quotations' },
    { value: 'invoices', label: 'Invoices' },
    { value: 'transactions', label: 'Transactions' },
    { value: 'products', label: 'Products' }
  ];

  const formatOptions = [
    { value: 'csv', label: 'CSV (.csv)' },
    { value: 'excel', label: 'Excel (.xlsx)' },
    { value: 'json', label: 'JSON (.json)' }
  ];

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      showInfo('Export', 'Preparing export...');
      setExportProgress(25);

      // Get current timestamp for filename
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `${exportDataType}_export_${timestamp}`;

      setExportProgress(50);

      // Make the export request
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if token exists
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'}/data/export`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          data_type: exportDataType,
          format: exportFileFormat
        })
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      setExportProgress(75);

      // Get the blob data
      const blob = await response.blob();
      
      // Determine file extension and MIME type
      let fileExtension = exportFileFormat;
      let mimeType = 'text/csv';
      
      switch (exportFileFormat) {
        case 'csv':
          mimeType = 'text/csv';
          fileExtension = 'csv';
          break;
        case 'excel':
          mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          fileExtension = 'xlsx';
          break;
        case 'json':
          mimeType = 'application/json';
          fileExtension = 'json';
          break;
      }

      setExportProgress(90);

      // Create download link
      const url = window.URL.createObjectURL(new Blob([blob], { type: mimeType }));
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.${fileExtension}`;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setExportProgress(100);

      showSuccess('Export Complete', `${exportDataType.charAt(0).toUpperCase() + exportDataType.slice(1)} data has been exported successfully`);

      // Log the export activity
      logger.log(
        'Data Export',
        exportDataType.charAt(0).toUpperCase() + exportDataType.slice(1),
        `Exported ${exportDataType} data as ${exportFileFormat.toUpperCase()} file: ${filename}.${fileExtension}`,
        'Admin',
        'Data Management',
        {
          data_type: exportDataType,
          file_format: exportFileFormat,
          file_name: `${filename}.${fileExtension}`,
          estimated_size: getEstimatedFileSize()
        }
      );

      // Close modal after successful export
      setTimeout(() => {
        onClose();
      }, 1500);
      
    } catch (error) {
      console.error('Export error:', error);
      showError('Export Failed', 'Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const getEstimatedFileSize = () => {
    // Rough estimates based on data type and format
    const baseSizes = {
      clients: 50,
      leads: 30,
      deals: 40,
      quotations: 35,
      invoices: 45,
      transactions: 25,
      products: 20
    };

    const formatMultipliers = {
      csv: 1,
      excel: 1.5,
      json: 2
    };

    const baseSize = baseSizes[exportDataType as keyof typeof baseSizes] || 30;
    const multiplier = formatMultipliers[exportFileFormat as keyof typeof formatMultipliers] || 1;
    
    return Math.round(baseSize * multiplier);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-50"
          onClick={!isExporting ? onClose : undefined}
        />

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-in-out sm:my-8 sm:align-middle w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Download className="w-5 h-5 mr-2" />
            Export Data
          </h2>
          <button
            onClick={onClose}
            disabled={isExporting}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Data Type
            </label>
            <CustomDropdown
              options={dataTypeOptions}
              value={exportDataType}
              onChange={setExportDataType}
              disabled={isExporting}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Export Format
            </label>
            <CustomDropdown
              options={formatOptions}
              value={exportFileFormat}
              onChange={setExportFileFormat}
              disabled={isExporting}
            />
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Export Details</h4>
            <div className="space-y-1 text-sm text-gray-600">
              <p>Data Type: <span className="font-medium">{dataTypeOptions.find(opt => opt.value === exportDataType)?.label}</span></p>
              <p>Format: <span className="font-medium">{formatOptions.find(opt => opt.value === exportFileFormat)?.label}</span></p>
              <p>Estimated Size: <span className="font-medium">~{getEstimatedFileSize()}KB</span></p>
            </div>
          </div>

          {isExporting && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Loader className="w-4 h-4 animate-spin text-blue-600" />
                <span className="text-sm text-gray-600">Exporting data...</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${exportProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 text-center">{exportProgress}% complete</p>
            </div>
          )}
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            disabled={isExporting}
            className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            {isExporting ? (
              <>
                <Loader className="w-4 h-4 animate-spin" />
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                <span>Export Data</span>
              </>
            )}
          </button>
        </div>
        </div>
      </div>
    </div>
  );
};

export default DataExportModal;
