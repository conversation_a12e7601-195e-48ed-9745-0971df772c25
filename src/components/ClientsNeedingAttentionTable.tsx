import React from 'react';
import { Client } from '../contexts/ClientContext';
import { DataQualityBadge } from './AnimatedComponents';
import { AlertTriangle, Eye, MessageCircle } from 'lucide-react';

interface ClientsNeedingAttentionTableProps {
  clients: Client[];
  onViewClient?: (client: Client) => void;
  onContactClient?: (client: Client) => void;
}

const ClientsNeedingAttentionTable: React.FC<ClientsNeedingAttentionTableProps> = ({
  clients,
  onViewClient,
  onContactClient
}) => {
  const getUrgencyLevel = (client: Client) => {
    const score = client.overallScore || 0;
    const dataQuality = client.dataQuality || 'Poor';
    
    if (score < 40 || dataQuality === 'Poor') return 'high';
    if (score < 60 || dataQuality === 'Fair') return 'medium';
    return 'low';
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getRecommendedAction = (client: Client) => {
    const score = client.overallScore || 0;
    const dataQuality = client.dataQuality || 'Poor';
    
    if (!client.emailVerified && !client.phoneVerified) {
      return 'Verify contact information';
    }
    if (dataQuality === 'Poor') {
      return 'Update data quality';
    }
    if (score < 40) {
      return 'Immediate engagement required';
    }
    if (client.engagementLevel === 'Cold' || client.engagementLevel === 'Frozen') {
      return 'Re-engagement campaign';
    }
    return 'Follow up required';
  };

  const getDaysSinceLastActivity = (lastActivity: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - lastActivity.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <h3 className="text-lg font-semibold text-gray-900">Clients Needing Attention</h3>
          </div>
          <button className="text-blue-600 text-sm hover:underline flex items-center space-x-1">
            <Eye className="w-4 h-4" />
            <span>View All</span>
          </button>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Urgency
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Score
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Data Quality
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Activity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Recommended Action
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {clients.slice(0, 10).map((client) => {
              const urgency = getUrgencyLevel(client);
              const daysSinceActivity = getDaysSinceLastActivity(client.lastActivity);
              
              return (
                <tr key={client.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-gray-600 text-sm font-medium">
                          {client.name.split(' ')[0][0]}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{client.name}</p>
                        <p className="text-xs text-gray-500">{client.email}</p>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded border text-xs font-medium ${getUrgencyColor(urgency)}`}>
                      {urgency.charAt(0).toUpperCase() + urgency.slice(1)}
                    </span>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <div className="w-12 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-1500 ease-out ${
                            (client.overallScore || 0) >= 60 ? 'bg-green-500' :
                            (client.overallScore || 0) >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${client.overallScore || 0}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-600 dark:text-gray-400">{client.overallScore || 0}%</span>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <DataQualityBadge
                      score={client.overallScore || 0}
                      verified={client.emailVerified || false}
                      label={client.dataQuality || 'Poor'}
                    />
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      <p>{daysSinceActivity} days ago</p>
                      <p className="text-xs text-gray-400">
                        {client.lastActivity.toLocaleDateString()}
                      </p>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <p className="text-xs text-gray-600 max-w-32">
                      {getRecommendedAction(client)}
                    </p>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onViewClient?.(client)}
                        className="text-blue-600 hover:text-blue-900 font-medium"
                      >
                        View
                      </button>
                      <button
                        onClick={() => onContactClient?.(client)}
                        className="text-green-600 hover:text-green-900 font-medium flex items-center space-x-1"
                      >
                        <MessageCircle className="w-3 h-3" />
                        <span>Contact</span>
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ClientsNeedingAttentionTable;
