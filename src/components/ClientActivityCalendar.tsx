import React, { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight, Phone, Mail, Calendar, DollarSign, FileText, AlertTriangle } from 'lucide-react';
import { Client } from '../contexts/ClientContext';

interface ActivityEvent {
  id: string;
  date: Date;
  type: 'call' | 'meeting' | 'email' | 'transaction' | 'quotation' | 'urgent';
  title: string;
  description?: string;
  amount?: number;
}

interface ClientActivityCalendarProps {
  client: Client;
}

const ClientActivityCalendar: React.FC<ClientActivityCalendarProps> = ({ client }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedEvent, setSelectedEvent] = useState<ActivityEvent | null>(null);

  // Generate mock activity events based on client data
  const generateActivityEvents = (client: Client): ActivityEvent[] => {
    const events: ActivityEvent[] = [];
    const now = new Date();
    
    // Add client creation event
    events.push({
      id: 'created',
      date: client.createdAt,
      type: 'meeting',
      title: 'Client Onboarded',
      description: 'Initial client registration and setup'
    });

    // Add last activity event
    if (client.lastActivity) {
      events.push({
        id: 'last-activity',
        date: client.lastActivity,
        type: 'email',
        title: 'Last Interaction',
        description: 'Most recent client activity'
      });
    }

    // Generate transaction events based on transaction count
    for (let i = 0; i < Math.min(client.transactionCount, 10); i++) {
      const transactionDate = new Date(now.getTime() - (i * 7 + Math.random() * 30) * 24 * 60 * 60 * 1000);
      events.push({
        id: `transaction-${i}`,
        date: transactionDate,
        type: 'transaction',
        title: `Transaction #${1000 + i}`,
        description: 'Payment received',
        amount: Math.floor(Math.random() * 1000) + 100
      });
    }

    // Add engagement-based events
    if (client.engagementLevel === 'Hot') {
      // Add recent call events
      for (let i = 0; i < 3; i++) {
        const callDate = new Date(now.getTime() - (i * 3 + Math.random() * 7) * 24 * 60 * 60 * 1000);
        events.push({
          id: `call-${i}`,
          date: callDate,
          type: 'call',
          title: 'Follow-up Call',
          description: 'Client engagement call'
        });
      }
    }

    // Add priority-based urgent events
    if (client.priority === 'High') {
      const urgentDate = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      events.push({
        id: 'urgent',
        date: urgentDate,
        type: 'urgent',
        title: 'High Priority Follow-up',
        description: 'Urgent client matter requiring attention'
      });
    }

    // Add quotation events
    const quotationDate = new Date(now.getTime() - Math.random() * 14 * 24 * 60 * 60 * 1000);
    events.push({
      id: 'quotation',
      date: quotationDate,
      type: 'quotation',
      title: 'Quotation Sent',
      description: 'Proposal sent to client'
    });

    return events.sort((a, b) => b.date.getTime() - a.date.getTime());
  };

  const events = useMemo(() => generateActivityEvents(client), [client]);

  // Calendar calculations
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const firstDayWeekday = firstDayOfMonth.getDay();
  const daysInMonth = lastDayOfMonth.getDate();

  // Generate calendar days
  const calendarDays = [];
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayWeekday; i++) {
    calendarDays.push(null);
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  // Get events for a specific day
  const getEventsForDay = (day: number) => {
    const dayDate = new Date(year, month, day);
    return events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate.getDate() === day && 
             eventDate.getMonth() === month && 
             eventDate.getFullYear() === year;
    });
  };

  // Event type styling
  const getEventTypeStyle = (type: ActivityEvent['type']) => {
    switch (type) {
      case 'call': return 'bg-blue-500';
      case 'meeting': return 'bg-green-500';
      case 'email': return 'bg-orange-500';
      case 'transaction': return 'bg-purple-500';
      case 'quotation': return 'bg-indigo-500';
      case 'urgent': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getEventIcon = (type: ActivityEvent['type']) => {
    switch (type) {
      case 'call': return Phone;
      case 'meeting': return Calendar;
      case 'email': return Mail;
      case 'transaction': return DollarSign;
      case 'quotation': return FileText;
      case 'urgent': return AlertTriangle;
      default: return Calendar;
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Activity Calendar</h3>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigateMonth('prev')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronLeft className="w-4 h-4 text-gray-600" />
          </button>
          <h4 className="text-lg font-medium text-gray-900 min-w-[140px] text-center">
            {monthNames[month]} {year}
          </h4>
          <button
            onClick={() => navigateMonth('next')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronRight className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1 mb-4 flex-1">
        {/* Day headers */}
        {dayNames.map(day => (
          <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
            {day}
          </div>
        ))}
        
        {/* Calendar days */}
        {calendarDays.map((day, index) => {
          if (day === null) {
            return <div key={`empty-${index}`} className="p-2 h-20"></div>;
          }

          const dayEvents = getEventsForDay(day);
          const isToday = new Date().getDate() === day &&
                         new Date().getMonth() === month &&
                         new Date().getFullYear() === year;

          return (
            <div
              key={`day-${year}-${month}-${day}`}
              className={`p-2 h-20 border border-gray-200 rounded-lg relative ${
                isToday ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
              }`}
            >
              <div className={`text-sm font-medium ${isToday ? 'text-blue-600' : 'text-gray-900'}`}>
                {day}
              </div>

              {/* Event indicators */}
              <div className="mt-1 space-y-1">
                {dayEvents.slice(0, 2).map((event, eventIndex) => {
                  const Icon = getEventIcon(event.type);
                  return (
                    <div
                      key={`${event.id}-${day}-${eventIndex}`}
                      className={`w-full h-1.5 rounded-full ${getEventTypeStyle(event.type)} cursor-pointer`}
                      onClick={() => setSelectedEvent(event)}
                      title={event.title}
                    ></div>
                  );
                })}
                {dayEvents.length > 2 && (
                  <div className="text-xs text-gray-500">+{dayEvents.length - 2} more</div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Event Legend */}
      <div className="flex flex-wrap gap-4 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
          <span className="text-gray-600">Calls</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="text-gray-600">Meetings</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-orange-500"></div>
          <span className="text-gray-600">Emails</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-purple-500"></div>
          <span className="text-gray-600">Transactions</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <span className="text-gray-600">Urgent</span>
        </div>
      </div>

      {/* Event Details Modal/Tooltip */}
      {selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-xl shadow-lg max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-900">{selectedEvent.title}</h4>
              <button
                onClick={() => setSelectedEvent(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                <strong>Date:</strong> {selectedEvent.date.toLocaleDateString()}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Type:</strong> {selectedEvent.type.charAt(0).toUpperCase() + selectedEvent.type.slice(1)}
              </p>
              {selectedEvent.description && (
                <p className="text-sm text-gray-600">
                  <strong>Description:</strong> {selectedEvent.description}
                </p>
              )}
              {selectedEvent.amount && (
                <p className="text-sm text-gray-600">
                  <strong>Amount:</strong> RM {selectedEvent.amount.toFixed(2)}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientActivityCalendar;
