import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../contexts/ToastContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  requireManager?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  requireManager = false
}) => {
  const { isAuthenticated, isAdmin, isManager, loading } = useAuth();
  const { showError } = useToast();
  const location = useLocation();

  // Check admin requirement - must be called before any early returns
  useEffect(() => {
    if (requireAdmin && !isAdmin && isAuthenticated && !loading) {
      showError('You don\'t have permission to access this page. Admin access required.');
    }
  }, [requireAdmin, isAdmin, isAuthenticated, loading, showError]);

  // Check manager requirement (admin also has manager access) - must be called before any early returns
  useEffect(() => {
    if (requireManager && !isManager && !isAdmin && isAuthenticated && !loading) {
      showError('You don\'t have permission to access this page. Manager access required.');
    }
  }, [requireManager, isManager, isAdmin, isAuthenticated, loading, showError]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Redirect to dashboard if access denied
  if (requireAdmin && !isAdmin && isAuthenticated && !loading) {
    return <Navigate to="/" replace />;
  }

  if (requireManager && !isManager && !isAdmin && isAuthenticated && !loading) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
