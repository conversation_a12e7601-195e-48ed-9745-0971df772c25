import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Save, Plus, Trash2 } from 'lucide-react';
import { useQuotations, Quotation, QuotationItem } from '../contexts/QuotationContext';
import { useClients } from '../contexts/ClientContext';
import { useToast } from '../contexts/ToastContext';
import CustomDropdown from './CustomDropdown';

interface QuotationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  quotation?: Quotation;
  mode: 'create' | 'edit' | 'view';
}

const QuotationWizard: React.FC<QuotationWizardProps> = ({ isOpen, onClose, quotation, mode }) => {
  const { createQuotation, updateQuotation } = useQuotations();
  const { clients } = useClients();
  const { showSuccess, showError } = useToast();

  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    priority: 'medium' as const,
    tax_rate: 6,
    discount_amount: 0,
    currency: 'MYR',
    validity_days: 30,
    terms_conditions: '',
    notes: '',
    internal_notes: '',
    expected_close_date: '',
  });

  const [items, setItems] = useState<QuotationItem[]>([]);

  const steps = [
    { id: 1, title: 'Basic Information', description: 'Quotation details and client' },
    { id: 2, title: 'Items & Pricing', description: 'Add products and services' },
    { id: 3, title: 'Terms & Notes', description: 'Terms, conditions and notes' },
    { id: 4, title: 'Review & Submit', description: 'Review all details' },
  ];

  useEffect(() => {
    if (quotation && (mode === 'edit' || mode === 'view')) {
      setFormData({
        title: quotation.title,
        description: quotation.description || '',
        client_id: quotation.client_id || '',
        priority: quotation.priority,
        tax_rate: quotation.tax_rate,
        discount_amount: quotation.discount_amount,
        currency: quotation.currency,
        validity_days: quotation.validity_days,
        terms_conditions: quotation.terms_conditions || '',
        notes: quotation.notes || '',
        internal_notes: quotation.internal_notes || '',
        expected_close_date: quotation.expected_close_date ? quotation.expected_close_date.split('T')[0] : '',
      });
      setItems(quotation.items || []);
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        client_id: '',
        priority: 'medium',
        tax_rate: 6,
        discount_amount: 0,
        currency: 'MYR',
        validity_days: 30,
        terms_conditions: 'Payment terms: 50% advance, 50% upon completion\nAll services will be conducted according to Islamic principles\nDelivery timeline as specified in project schedule',
        notes: '',
        internal_notes: '',
        expected_close_date: '',
      });
      setItems([{
        item_name: '',
        description: '',
        unit: 'pcs',
        quantity: 1,
        unit_price: 0,
        discount_rate: 0,
        discount_amount: 0,
        line_total: 0,
        sort_order: 1,
      }]);
    }
    setCurrentStep(1);
  }, [quotation, mode]);

  const addItem = () => {
    const newItem: QuotationItem = {
      item_name: '',
      description: '',
      unit: 'pcs',
      quantity: 1,
      unit_price: 0,
      discount_rate: 0,
      discount_amount: 0,
      line_total: 0,
      sort_order: items.length + 1,
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof QuotationItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate line total
    const item = updatedItems[index];
    const subtotal = item.quantity * item.unit_price;
    const discount = item.discount_rate > 0 
      ? subtotal * (item.discount_rate / 100)
      : item.discount_amount;
    item.line_total = subtotal - discount;
    
    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = subtotal * (formData.tax_rate / 100);
    const total = subtotal + taxAmount - formData.discount_amount;
    
    return { subtotal, taxAmount, total };
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (mode === 'view') return;

    setLoading(true);
    try {
      // Handle client ID: filter out frontend client IDs (string IDs starting with 'client-')
      let clientId = formData.client_id;
      if (clientId && typeof clientId === 'string' && clientId.startsWith('client-')) {

        clientId = null; // Don't send frontend client IDs to backend
      } else if (clientId === '') {
        clientId = null; // Convert empty string to null
      }

      const quotationData = {
        ...formData,
        client_id: clientId,
        items: items.map((item, index) => ({
          ...item,
          sort_order: index + 1,
        })),
      };

      if (mode === 'create') {
        await createQuotation(quotationData);
        showSuccess('Success', 'Quotation created successfully');
      } else if (mode === 'edit' && quotation) {
        await updateQuotation(quotation.id, quotationData);
        showSuccess('Success', 'Quotation updated successfully');
      }

      onClose();
    } catch (error) {
      console.error('Error saving quotation:', error);
      showError('Error', `Failed to ${mode === 'create' ? 'create' : 'update'} quotation. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const isReadOnly = mode === 'view';

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-4">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative bg-white rounded-xl shadow-xl w-full max-w-5xl max-h-[95vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Create New Quotation' : 
               mode === 'edit' ? 'Edit Quotation' : 'View Quotation'}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              Step {currentStep} of {steps.length}: {steps[currentStep - 1].description}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep >= step.id 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step.id}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-500' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-6" style={{ maxHeight: 'calc(95vh - 200px)' }}>
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Title *
                    </label>
                    <input
                      type="text"
                      required
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Client
                    </label>
                    <CustomDropdown
                      options={clients.map(client => ({ value: client.id, label: client.name }))}
                      value={formData.client_id}
                      onChange={(value) => setFormData({ ...formData, client_id: value })}
                      placeholder="Select client"
                      disabled={isReadOnly}
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <CustomDropdown
                      options={[
                        { value: 'low', label: 'Low' },
                        { value: 'medium', label: 'Medium' },
                        { value: 'high', label: 'High' },
                        { value: 'urgent', label: 'Urgent' },
                      ]}
                      value={formData.priority}
                      onChange={(value) => setFormData({ ...formData, priority: value as any })}
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Expected Close Date
                    </label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={formData.expected_close_date}
                      onChange={(e) => setFormData({ ...formData, expected_close_date: e.target.value })}
                      disabled={isReadOnly}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Items & Pricing */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Items & Pricing</h3>
                  {!isReadOnly && (
                    <button
                      type="button"
                      onClick={addItem}
                      className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Add Item</span>
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {items.map((item, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Item Name *
                          </label>
                          <input
                            type="text"
                            required
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={item.item_name}
                            onChange={(e) => updateItem(index, 'item_name', e.target.value)}
                            disabled={isReadOnly}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Quantity *
                          </label>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            required
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={item.quantity}
                            onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                            disabled={isReadOnly}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Unit Price (MYR) *
                          </label>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            required
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={item.unit_price}
                            onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                            disabled={isReadOnly}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Discount %
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="100"
                            step="0.01"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={item.discount_rate}
                            onChange={(e) => updateItem(index, 'discount_rate', parseFloat(e.target.value) || 0)}
                            disabled={isReadOnly}
                          />
                        </div>

                        <div className="flex items-end">
                          <div className="w-full">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Line Total
                            </label>
                            <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-700">
                              MYR {item.line_total.toFixed(2)}
                            </div>
                          </div>
                          {!isReadOnly && items.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              className="ml-2 p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={item.description}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          disabled={isReadOnly}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pricing Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Pricing Summary</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>MYR {calculateTotals().subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({formData.tax_rate}%):</span>
                      <span>MYR {calculateTotals().taxAmount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Discount:</span>
                      <span>-MYR {formData.discount_amount.toFixed(2)}</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between font-semibold">
                      <span>Total:</span>
                      <span>MYR {calculateTotals().total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Terms & Notes */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Terms & Notes</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tax Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={formData.tax_rate}
                      onChange={(e) => setFormData({ ...formData, tax_rate: parseFloat(e.target.value) || 0 })}
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Amount (MYR)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={formData.discount_amount}
                      onChange={(e) => setFormData({ ...formData, discount_amount: parseFloat(e.target.value) || 0 })}
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency
                    </label>
                    <CustomDropdown
                      options={[
                        { value: 'MYR', label: 'MYR - Malaysian Ringgit' },
                        { value: 'USD', label: 'USD - US Dollar' },
                        { value: 'SGD', label: 'SGD - Singapore Dollar' },
                      ]}
                      value={formData.currency}
                      onChange={(value) => setFormData({ ...formData, currency: value })}
                      disabled={isReadOnly}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Validity (Days)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="365"
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={formData.validity_days}
                      onChange={(e) => setFormData({ ...formData, validity_days: parseInt(e.target.value) || 30 })}
                      disabled={isReadOnly}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Terms & Conditions
                  </label>
                  <textarea
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={formData.terms_conditions}
                    onChange={(e) => setFormData({ ...formData, terms_conditions: e.target.value })}
                    disabled={isReadOnly}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Client Visible)
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    disabled={isReadOnly}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Internal Notes (Private)
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={formData.internal_notes}
                    onChange={(e) => setFormData({ ...formData, internal_notes: e.target.value })}
                    disabled={isReadOnly}
                  />
                </div>
              </div>
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Review & Submit</h3>

                {/* Basic Info Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Basic Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Title:</span>
                      <p className="font-medium">{formData.title}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Client:</span>
                      <p className="font-medium">
                        {clients.find(c => c.id === formData.client_id)?.name || 'Not selected'}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Priority:</span>
                      <p className="font-medium capitalize">{formData.priority}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Validity:</span>
                      <p className="font-medium">{formData.validity_days} days</p>
                    </div>
                  </div>
                </div>

                {/* Items Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Items ({items.length})</h4>
                  <div className="space-y-2">
                    {items.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{item.item_name} (x{item.quantity})</span>
                        <span>MYR {item.line_total.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Final Pricing */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Final Pricing</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>MYR {calculateTotals().subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({formData.tax_rate}%):</span>
                      <span>MYR {calculateTotals().taxAmount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Discount:</span>
                      <span>-MYR {formData.discount_amount.toFixed(2)}</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between font-semibold text-lg">
                      <span>Total:</span>
                      <span>MYR {calculateTotals().total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
          <button
            type="button"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>

          <div className="flex items-center space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors bg-white"
            >
              {mode === 'view' ? 'Close' : 'Cancel'}
            </button>

            {currentStep < steps.length ? (
              <button
                type="button"
                onClick={nextStep}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <span>Next</span>
                <ChevronRight className="w-4 h-4" />
              </button>
            ) : (
              !isReadOnly && (
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  <span>{mode === 'create' ? 'Create Quotation' : 'Update Quotation'}</span>
                </button>
              )
            )}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default QuotationWizard;
