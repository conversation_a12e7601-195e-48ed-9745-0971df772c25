import React, { useState } from 'react';
import { Plus, Trash2, Star, <PERSON>Off } from 'lucide-react';
import PhoneInputWithVerification from './PhoneInputWithVerification';
import { ClientPhoneNumber } from '../contexts/ClientContext';

interface MultiPhoneManagerProps {
  phoneNumbers: ClientPhoneNumber[];
  onChange: (phoneNumbers: ClientPhoneNumber[]) => void;
  disabled?: boolean;
  maxPhones?: number;
}

const MultiPhoneManager: React.FC<MultiPhoneManagerProps> = ({
  phoneNumbers,
  onChange,
  disabled = false,
  maxPhones = 5
}) => {
  const [nextId, setNextId] = useState(phoneNumbers.length + 1);

  const addPhoneNumber = () => {
    if (phoneNumbers.length >= maxPhones) return;

    const newPhone: ClientPhoneNumber = {
      id: `new-${nextId}`,
      phoneNumber: '',
      isPrimary: phoneNumbers.length === 0, // First phone is primary by default
      phoneVerified: true,
      phoneScore: 0,
      phoneCarrier: undefined
    };

    onChange([...phoneNumbers, newPhone]);
    setNextId(nextId + 1);
  };

  const removePhoneNumber = (id: string) => {
    const updatedPhones = phoneNumbers.filter(phone => phone.id !== id);
    
    // If we removed the primary phone and there are still phones left, make the first one primary
    if (updatedPhones.length > 0 && !updatedPhones.some(phone => phone.isPrimary)) {
      updatedPhones[0].isPrimary = true;
    }
    
    onChange(updatedPhones);
  };

  const updatePhoneNumber = (id: string, updates: Partial<ClientPhoneNumber>) => {
    const updatedPhones = phoneNumbers.map(phone => {
      if (phone.id === id) {
        return { ...phone, ...updates };
      }
      return phone;
    });
    
    onChange(updatedPhones);
  };

  const setPrimaryPhone = (id: string) => {
    const updatedPhones = phoneNumbers.map(phone => ({
      ...phone,
      isPrimary: phone.id === id
    }));
    
    onChange(updatedPhones);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Phone Numbers
        </label>
        {!disabled && phoneNumbers.length < maxPhones && (
          <button
            type="button"
            onClick={addPhoneNumber}
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Phone
          </button>
        )}
      </div>

      <div className="space-y-3">
        {phoneNumbers.map((phone, index) => (
          <div key={phone.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">
                  Phone {index + 1}
                </span>
                {phone.isPrimary && (
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                    Primary
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                {!phone.isPrimary && phoneNumbers.length > 1 && !disabled && (
                  <button
                    type="button"
                    onClick={() => setPrimaryPhone(phone.id)}
                    className="text-gray-400 hover:text-yellow-500"
                    title="Set as primary"
                  >
                    <StarOff className="w-4 h-4" />
                  </button>
                )}
                
                {phone.isPrimary && (
                  <Star className="w-4 h-4 text-yellow-500" title="Primary phone" />
                )}
                
                {phoneNumbers.length > 1 && !disabled && (
                  <button
                    type="button"
                    onClick={() => removePhoneNumber(phone.id)}
                    className="text-gray-400 hover:text-red-500"
                    title="Remove phone"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            <PhoneInputWithVerification
              value={phone.phoneNumber}
              verified={phone.phoneVerified}
              onChange={(value) => updatePhoneNumber(phone.id, { phoneNumber: value })}
              onVerificationChange={(verified) => updatePhoneNumber(phone.id, { phoneVerified: verified })}
              disabled={disabled}
              placeholder="Enter phone number"
            />
            
            {phone.phoneCarrier && (
              <div className="mt-2">
                <span className="text-xs text-gray-500">
                  Carrier: <span className="font-medium">{phone.phoneCarrier}</span>
                </span>
              </div>
            )}
          </div>
        ))}

        {phoneNumbers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p className="text-sm">No phone numbers added yet.</p>
            {!disabled && (
              <button
                type="button"
                onClick={addPhoneNumber}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm"
              >
                Add your first phone number
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MultiPhoneManager;
