import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Trash2,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Loader,
  Shield
} from 'lucide-react';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { useClients } from '../contexts/ClientContext';
import { apiService } from '../services/api';
import { logger } from '../utils/logger';
import CustomDropdown from './CustomDropdown';

interface DataClearModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DataClearModal: React.FC<DataClearModalProps> = ({ isOpen, onClose }) => {
  const [clearDataType, setClearDataType] = useState('');
  const [isClearing, setIsClearing] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [modalHeight, setModalHeight] = useState('auto');
  const [hardDelete, setHardDelete] = useState(false);

  const { showSuccess, showError } = useToast();
  const { confirm } = useConfirmation();
  const { clearClientCache } = useClients();

  const dataTypeOptions = [
    { value: '', label: 'Select data type...' },
    { value: 'clients', label: 'Clients Data' },
    { value: 'leads', label: 'Leads Data' },
    { value: 'deals', label: 'Deals Data' },
    { value: 'quotations', label: 'Quotations Data' },
    { value: 'invoices', label: 'Invoices Data' },
    { value: 'products', label: 'Products Data' },
    { value: 'transactions', label: 'Transactions Data' },
    { value: 'all', label: 'All Data (DANGER!)' },
  ];

  // Handle ESC key and body overflow
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !isClearing) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose, isClearing]);

  // Reset dropdown state when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setIsDropdownOpen(false);
      setModalHeight('auto');
    }
  }, [isOpen]);

  // Calculate optimal height when dropdown state changes
  useEffect(() => {
    if (isDropdownOpen) {
      // Calculate the height needed for dropdown
      const dropdownOptionsCount = dataTypeOptions.length;
      const dropdownItemHeight = 40; // Height per option (py-2 + text)
      const maxDropdownHeight = 240; // max-h-60 = 240px
      const actualDropdownHeight = Math.min(dropdownOptionsCount * dropdownItemHeight, maxDropdownHeight);

      // Calculate base modal content height
      const headerHeight = 80; // Header with title and close button
      const warningHeight = 100; // Warning section
      const dropdownLabelHeight = 60; // Label and dropdown button
      const confirmationHeight = 80; // Confirmation input section
      const buttonHeight = 60; // Action buttons
      const padding = 48; // Various paddings (p-6 = 24px * 2)

      const baseContentHeight = headerHeight + warningHeight + dropdownLabelHeight + confirmationHeight + buttonHeight + padding;
      const totalNeededHeight = baseContentHeight + actualDropdownHeight + 20; // 20px extra buffer

      // Ensure it doesn't exceed viewport (leave 5% margin)
      const maxViewportHeight = window.innerHeight * 0.95;
      const finalHeight = Math.min(totalNeededHeight, maxViewportHeight);

      setModalHeight(`${finalHeight}px`);
    } else {
      setModalHeight('auto');
    }
  }, [isDropdownOpen, dataTypeOptions.length]);

  const getDataTypeInfo = (dataType: string) => {
    const deleteType = hardDelete ? 'Hard Delete' : 'Soft Delete';
    const deleteDescription = hardDelete
      ? 'Records will be permanently removed from the database and cannot be recovered.'
      : 'Records will be marked as deleted but can be recovered by administrators.';

    const info = {
      clients: {
        description: `All client records, contact information, and related data (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all client profiles and their associated information.`,
        warning: `Client relationships with deals, invoices, and transactions will be affected. ${deleteDescription}`
      },
      leads: {
        description: `All lead records and prospect information (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all lead data and conversion tracking.`,
        warning: `Lead-to-client conversion history will be ${hardDelete ? 'permanently lost' : 'hidden but recoverable'}. ${deleteDescription}`
      },
      deals: {
        description: `All deal records, pipeline data, and sales information (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all deal tracking and sales pipeline data.`,
        warning: `Deal history and revenue tracking will be ${hardDelete ? 'permanently lost' : 'hidden but recoverable'}. ${deleteDescription}`
      },
      quotations: {
        description: `All quotation records and pricing information (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all quotations and pricing history.`,
        warning: `Quotation templates and pricing data will be ${hardDelete ? 'permanently lost' : 'hidden but recoverable'}. ${deleteDescription}`
      },
      invoices: {
        description: `All invoice records and billing information (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all invoicing data and payment tracking.`,
        warning: `Financial records and payment history will be ${hardDelete ? 'permanently lost' : 'hidden but recoverable'}. ${deleteDescription}`
      },
      transactions: {
        description: `All transaction records and payment data (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all transaction history and financial data.`,
        warning: `Revenue tracking and financial reports will be ${hardDelete ? 'permanently affected' : 'temporarily affected but recoverable'}. ${deleteDescription}`
      },
      products: {
        description: `All product records, inventory, and catalog data (${deleteType})`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} all product information and inventory tracking.`,
        warning: `Product relationships in deals and invoices will be ${hardDelete ? 'permanently affected' : 'temporarily affected but recoverable'}. ${deleteDescription}`
      },
      all: {
        description: `COMPLETE DATABASE ${hardDelete ? 'HARD DELETE' : 'SOFT DELETE'} - All data will be ${hardDelete ? 'permanently deleted' : 'marked as deleted'}`,
        impact: `This will ${hardDelete ? 'permanently remove' : 'soft delete'} ALL data from your CRM system.`,
        warning: `This action will ${hardDelete ? 'permanently reset your entire CRM. ALL DATA WILL BE LOST PERMANENTLY' : 'hide all data but it can be recovered by administrators'}. ${deleteDescription}`
      }
    };

    return info[dataType as keyof typeof info] || {
      description: 'Selected data type',
      impact: 'This will remove the selected data.',
      warning: 'This action cannot be undone.'
    };
  };

  const handleClearData = async () => {
    if (!clearDataType) {
      showError('Error', 'Please select a data type to clear');
      return;
    }

    const dataTypeLabel = clearDataType === 'all' ? 'ALL DATA' : `${clearDataType.toUpperCase()} DATA`;
    const deletePrefix = hardDelete ? 'HARD DELETE' : 'SOFT DELETE';
    const requiredConfirmation = clearDataType === 'all' ? `${deletePrefix} ALL DATA` : `${deletePrefix} ${clearDataType.toUpperCase()}`;
    
    if (confirmationText !== requiredConfirmation) {
      showError('Error', `Please type "${requiredConfirmation}" to confirm`);
      return;
    }

    const confirmed = await confirm({
      title: 'Final Confirmation',
      message: `This is your final warning. Are you absolutely sure you want to ${hardDelete ? 'permanently delete' : 'soft delete'} ${dataTypeLabel}? ${hardDelete ? 'This action cannot be undone and will remove all related records permanently.' : 'This action will hide the data but it can be recovered by administrators.'}`,
      confirmText: hardDelete ? 'DELETE PERMANENTLY' : 'SOFT DELETE',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (!confirmed) return;

    setIsClearing(true);

    try {
      const endpoint = `/data/clear/${clearDataType}${hardDelete ? '?hard=true' : ''}`;
      await apiService.delete(endpoint);

      const deleteTypeText = hardDelete ? 'permanently deleted' : 'soft deleted';
      showSuccess('Data Cleared', `${dataTypeLabel} has been successfully ${deleteTypeText}`);

      // Log the clear operation
      logger.log(
        'Data Deletion',
        clearDataType === 'all' ? 'All Data' : dataTypeLabel,
        `${hardDelete ? 'Hard' : 'Soft'} deleted ${dataTypeLabel} from the system${hardDelete ? ' - PERMANENT DELETION' : ' - RECOVERABLE'}`,
        'Admin',
        'Data Management',
        {
          data_type: clearDataType,
          operation: hardDelete ? 'hard_delete_data' : 'soft_delete_data',
          delete_type: hardDelete ? 'hard' : 'soft',
          confirmation_required: true,
          impact: clearDataType === 'all' ? 'complete_system_reset' : `${clearDataType}_data_${hardDelete ? 'loss' : 'hidden'}`,
          recoverable: !hardDelete
        }
      );

      setClearDataType('');
      setConfirmationText('');

      // Clear client cache if clients or all data was cleared
      if (clearDataType === 'clients' || clearDataType === 'all') {
        try {
          await clearClientCache();
        } catch (cacheError) {
          console.error('Failed to clear client cache:', cacheError);
          // Don't show error to user as the main operation succeeded
        }
      }

      // Close modal after successful operation
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (error: any) {
      console.error('Clear data error:', error);
      showError('Clear Failed', error.message || 'Failed to clear data. Please try again.');
    } finally {
      setIsClearing(false);
    }
  };

  const selectedDataInfo = clearDataType ? getDataTypeInfo(clearDataType) : null;
  const deletePrefix = hardDelete ? 'HARD DELETE' : 'SOFT DELETE';
  const requiredConfirmation = clearDataType === 'all' ? `${deletePrefix} ALL DATA` : clearDataType ? `${deletePrefix} ${clearDataType.toUpperCase()}` : '';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-50"
          onClick={!isClearing ? onClose : undefined}
        />

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div
          className={`inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-in-out sm:my-8 sm:align-middle w-full max-w-2xl ${
            isDropdownOpen ? '' : 'overflow-y-auto'
          }`}
          style={{
            height: modalHeight,
            maxHeight: isDropdownOpen ? '95vh' : '90vh'
          }}
        >
          <div className={`h-full ${isDropdownOpen ? 'overflow-visible' : 'overflow-y-auto'}`}>
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-red-200 bg-red-50">
          <h2 className="text-xl font-semibold text-red-900 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Clear Data - DANGER ZONE
          </h2>
          <button
            onClick={onClose}
            disabled={isClearing}
            className="p-2 hover:bg-red-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-red-900">Warning</h3>
                <p className="text-sm text-red-700 mt-1">
                  This action will permanently delete data from your CRM. This cannot be undone.
                  Make sure you have a backup if needed.
                </p>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Data Type to Clear
            </label>
            <CustomDropdown
              options={dataTypeOptions}
              value={clearDataType}
              onChange={setClearDataType}
              disabled={isClearing}
              onOpenChange={setIsDropdownOpen}
            />
          </div>

          {/* Hard Delete Toggle */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-1">
                <h4 className="font-medium text-yellow-900 mb-3">Delete Type</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  <label className="flex items-start cursor-pointer p-3 border border-yellow-300 rounded-lg hover:bg-yellow-100 transition-colors">
                    <input
                      type="radio"
                      name="deleteType"
                      checked={!hardDelete}
                      onChange={() => setHardDelete(false)}
                      disabled={isClearing}
                      className="mr-3 mt-0.5 text-blue-600 focus:ring-blue-500"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-900">Soft Delete</span>
                      <p className="text-xs text-gray-600 mt-1">Records are hidden but can be recovered</p>
                    </div>
                  </label>
                  <label className="flex items-start cursor-pointer p-3 border border-red-300 rounded-lg hover:bg-red-50 transition-colors">
                    <input
                      type="radio"
                      name="deleteType"
                      checked={hardDelete}
                      onChange={() => setHardDelete(true)}
                      disabled={isClearing}
                      className="mr-3 mt-0.5 text-red-600 focus:ring-red-500"
                    />
                    <div>
                      <span className="text-sm font-medium text-red-700">Hard Delete</span>
                      <p className="text-xs text-red-600 mt-1">Records are permanently removed (CANNOT BE RECOVERED)</p>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {selectedDataInfo && (
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <h4 className="font-medium text-gray-900">What will be deleted:</h4>
              <p className="text-sm text-gray-700">{selectedDataInfo.description}</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                  <p className="text-sm text-yellow-800 font-medium">Impact:</p>
                  <p className="text-sm text-yellow-700">{selectedDataInfo.impact}</p>
                </div>
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-sm text-red-800 font-medium">Warning:</p>
                  <p className="text-sm text-red-700">{selectedDataInfo.warning}</p>
                </div>
              </div>
            </div>
          )}

          {clearDataType && (
            <div>
              <label className="block text-sm font-medium text-red-700 mb-2">
                Type "{requiredConfirmation}" to confirm:
              </label>
              <input
                type="text"
                value={confirmationText}
                onChange={(e) => setConfirmationText(e.target.value)}
                disabled={isClearing}
                className="w-full px-3 py-2 border border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                placeholder={requiredConfirmation}
              />
            </div>
          )}

          {isClearing && (
            <div className="flex items-center space-x-2 text-red-600">
              <Loader className="w-4 h-4 animate-spin" />
              <span className="text-sm">Clearing data...</span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-end space-x-3 p-4 sm:p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            disabled={isClearing}
            className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleClearData}
            disabled={!clearDataType || confirmationText !== requiredConfirmation || isClearing}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            {isClearing ? (
              <>
                <Loader className="w-4 h-4 animate-spin" />
                <span>Clearing...</span>
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4" />
                <span>Clear Data</span>
              </>
            )}
          </button>
        </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataClearModal;
