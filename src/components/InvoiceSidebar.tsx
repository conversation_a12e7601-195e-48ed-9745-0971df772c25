import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Save, Eye, Edit, Copy } from 'lucide-react';
import { useInvoices, Invoice, InvoiceItem } from '../contexts/InvoiceContext';
import { useClients } from '../contexts/ClientContext';
import { useLeads } from '../contexts/LeadContext';
import { useToast } from '../contexts/ToastContext';
import { useDeal } from '../contexts/DealContext';
import { apiService } from '../services/api';
import CustomDropdown from './CustomDropdown';

interface InvoiceSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: Invoice;
  mode: 'create' | 'edit' | 'view';
}

const InvoiceSidebar: React.FC<InvoiceSidebarProps> = ({ isOpen, onClose, invoice, mode }) => {
  const { createInvoice, updateInvoice, deleteInvoice } = useInvoices();
  const { clients: frontendClients } = useClients();
  const { leads: frontendLeads } = useLeads();
  const { deals } = useDeal();
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !invoice);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Removed backend API calls - using only frontend context data for consistency

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    client_id: '',
    lead_id: '',
    quotation_id: '',
    deal_id: '',
    priority: 'medium' as const,
    tax_rate: 6,
    discount_amount: 0,
    currency: 'MYR',
    terms_conditions: '',
    notes: '',
    internal_notes: '',
    issue_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  });

  const [items, setItems] = useState<InvoiceItem[]>([]);

  // Removed backend API calls - using only frontend context data for consistency

  useEffect(() => {
    if (invoice && (mode === 'edit' || mode === 'view')) {
      // Determine client/lead selection value (similar to QuotationSidebar)
      let selectedValue = '';
      if (invoice.lead_id) {
        selectedValue = `lead-${invoice.lead_id}`;
      } else if (invoice.client_id) {
        selectedValue = invoice.client_id;
      } else if (invoice.client?.id) {
        selectedValue = invoice.client.id.toString();
      }

      setFormData({
        title: invoice.title,
        description: invoice.description || '',
        client_id: selectedValue,
        lead_id: '',
        quotation_id: invoice.quotation_id || '',
        deal_id: invoice.deal_id || '',
        priority: invoice.priority,
        tax_rate: Number(invoice.tax_rate) || 6,
        discount_amount: Number(invoice.discount_amount) || 0,
        currency: invoice.currency || 'MYR',
        terms_conditions: invoice.terms_conditions || '',
        notes: invoice.notes || '',
        internal_notes: invoice.internal_notes || '',
        issue_date: invoice.issue_date || new Date().toISOString().split('T')[0],
        due_date: invoice.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      });
      setItems(invoice.items || []);
      setIsEditing(mode === 'edit');
    } else {
      // Reset form for create mode
      setFormData({
        title: '',
        description: '',
        client_id: '',
        lead_id: '',
        quotation_id: '',
        deal_id: '',
        priority: 'medium',
        tax_rate: 6,
        discount_amount: 0,
        currency: 'MYR',
        terms_conditions: 'Payment terms: 50% advance, 50% upon completion\nAll services will be conducted according to Islamic principles\nDelivery timeline as specified in project schedule',
        notes: '',
        internal_notes: '',
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      });
      setItems([{
        item_name: '',
        description: '',
        unit: 'pcs',
        quantity: 1,
        unit_price: 0,
        discount_rate: 0,
        discount_amount: 0,
        line_total: 0,
        sort_order: 1,
      }]);
      setIsEditing(true);
    }
    setError(null);
  }, [invoice, mode]);

  const addItem = () => {
    const newItem: InvoiceItem = {
      item_name: '',
      description: '',
      unit: 'pcs',
      quantity: 1,
      unit_price: 0,
      discount_rate: 0,
      discount_amount: 0,
      line_total: 0,
      sort_order: items.length + 1,
    };
    setItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate line total when quantity, unit_price, or discount changes
    if (['quantity', 'unit_price', 'discount_rate', 'discount_amount'].includes(field)) {
      const item = updatedItems[index];
      const subtotal = item.quantity * item.unit_price;
      const discount = item.discount_rate > 0 
        ? subtotal * (item.discount_rate / 100)
        : item.discount_amount;
      updatedItems[index].line_total = subtotal - discount;
    }
    
    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const taxAmount = subtotal * (formData.tax_rate / 100);
    const total = subtotal + taxAmount - formData.discount_amount;
    
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        throw new Error('Invoice title is required');
      }

      if (items.length === 0 || !items.some(item => item.item_name.trim())) {
        throw new Error('At least one item with a name is required');
      }

      // Handle client/lead selection with proper ID processing
      let clientId = null;
      let leadId = null;

      if (formData.client_id) {
        if (typeof formData.client_id === 'string' && formData.client_id.startsWith('lead-')) {
          // Lead selected - extract ID and convert to integer if it's a backend lead
          const extractedLeadId = formData.client_id.replace('lead-', '');

          // Check if it's a backend lead (numeric) or frontend lead (string)
          if (/^\d+$/.test(extractedLeadId)) {
            // Backend lead - use as integer
            leadId = parseInt(extractedLeadId);

          } else {
            // Frontend lead - need to handle conversion or skip for now

            // For now, we'll skip frontend leads as they need special handling
          }
        } else if (typeof formData.client_id === 'string' && formData.client_id.startsWith('client-')) {
          // Frontend client selected - don't send to backend

        } else {
          // Backend client selected - ensure it's an integer
          clientId = typeof formData.client_id === 'string' ? parseInt(formData.client_id) : formData.client_id;

        }
      }

      const { subtotal, taxAmount, total } = calculateTotals();

      const invoiceData = {
        title: formData.title,
        description: formData.description,
        client_id: clientId,
        lead_id: leadId,
        quotation_id: formData.quotation_id || null,
        deal_id: formData.deal_id || null,
        priority: formData.priority,
        tax_rate: formData.tax_rate,
        discount_amount: formData.discount_amount,
        currency: formData.currency,
        terms_conditions: formData.terms_conditions,
        notes: formData.notes,
        internal_notes: formData.internal_notes,
        issue_date: formData.issue_date,
        due_date: formData.due_date,
        subtotal,
        tax_amount: taxAmount,
        total_amount: total,
        items: items.filter(item => item.item_name.trim()).map((item, index) => ({
          ...item,
          sort_order: index + 1,
        })),
        status: 'draft' as const,
        payment_status: 'pending' as const,
      };

      if (mode === 'edit' && invoice) {
        await updateInvoice(invoice.id, invoiceData);
        showSuccess('Success', 'Invoice updated successfully');
      } else {
        await createInvoice(invoiceData);
        showSuccess('Success', 'Invoice created successfully');
      }
      
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDelete = async () => {
    if (!invoice) return;
    
    setLoading(true);
    try {
      await deleteInvoice(invoice.id);
      showSuccess('Invoice Deleted', 'Invoice has been successfully deleted');
      onClose();
    } catch (error) {
      console.error('Error deleting invoice:', error);
      const errorMessage = 'Failed to delete invoice. Please try again.';
      setError(errorMessage);
      showError('Delete Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async () => {
    if (!invoice) return;
    
    setLoading(true);
    try {
      const duplicateData = {
        ...formData,
        title: `${formData.title} (Copy)`,
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      };
      
      const { subtotal, taxAmount, total } = calculateTotals();
      
      await createInvoice({
        ...duplicateData,
        subtotal,
        tax_amount: taxAmount,
        total_amount: total,
        items: items.filter(item => item.item_name.trim()),
        status: 'draft' as const,
        payment_status: 'pending' as const,
      });
      
      showSuccess('Invoice Duplicated', 'Invoice has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating invoice:', error);
      const errorMessage = 'Failed to duplicate invoice. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Use only frontend clients for consistency
  const allClients = frontendClients.map(client => ({
    id: client.id,
    name: client.name,
    email: client.email,
    source: 'frontend'
  }));

  // Use only frontend leads for consistency
  const allLeads = frontendLeads.map(lead => ({
    id: lead.id,
    name: lead.name || 'Unnamed',
    email: lead.email,
    source: 'frontend'
  }));

  // Combine clients and leads for dropdown
  const allClientsAndLeads = [
    { value: '', label: 'Select a client or lead' },
    ...allClients.map(client => ({
      value: client.id,
      label: `${client.name} (Client) ${client.email ? `- ${client.email}` : ''}`
    })),
    ...allLeads.map(lead => ({
      value: `lead-${lead.id}`,
      label: `${lead.name} (Lead) ${lead.email ? `- ${lead.email}` : ''}`
    }))
  ];

  const { subtotal, taxAmount, total } = calculateTotals();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {invoice ? (isEditing ? 'Edit Invoice' : 'Invoice Details') : 'Create New Invoice'}
            </h2>
            {invoice && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{invoice.invoice_number}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {invoice && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-500 dark:text-gray-400"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Invoice Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!isEditing}
                    placeholder="Enter invoice title"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                    placeholder="Enter invoice description"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Client or Lead
                  </label>
                  <CustomDropdown
                    options={allClientsAndLeads}
                    value={formData.client_id}
                    onChange={(value) => handleInputChange('client_id', value)}
                    disabled={!isEditing}
                    placeholder="Select a client or lead"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Select a client or lead for this invoice.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Associated Deal (Optional)
                  </label>
                  <CustomDropdown
                    options={[
                      { value: '', label: 'No deal association' },
                      ...deals.map(deal => ({
                        value: deal.id,
                        label: `${deal.title} - ${deal.value ? `RM ${deal.value}` : 'No value'} (${deal.status})`
                      }))
                    ]}
                    value={formData.deal_id}
                    onChange={(value) => handleInputChange('deal_id', value)}
                    disabled={!isEditing}
                    placeholder="Select a deal (optional)"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Optionally associate this invoice with a deal.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'low', label: 'Low' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'high', label: 'High' },
                      { value: 'urgent', label: 'Urgent' },
                    ]}
                    value={formData.priority}
                    onChange={(value) => handleInputChange('priority', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Issue Date
                  </label>
                  <input
                    type="date"
                    value={formData.issue_date}
                    onChange={(e) => handleInputChange('issue_date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Due Date
                  </label>
                  <input
                    type="date"
                    value={formData.due_date}
                    onChange={(e) => handleInputChange('due_date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Currency
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'MYR', label: 'MYR (Malaysian Ringgit)' },
                      { value: 'USD', label: 'USD (US Dollar)' },
                      { value: 'SGD', label: 'SGD (Singapore Dollar)' },
                    ]}
                    value={formData.currency}
                    onChange={(value) => handleInputChange('currency', value)}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Items Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Items</h3>
                {isEditing && (
                  <button
                    type="button"
                    onClick={addItem}
                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add Item
                  </button>
                )}
              </div>

              <div className="space-y-4">
                {items.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">Item {index + 1}</h4>
                      {isEditing && items.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeItem(index)}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Item Name *
                        </label>
                        <input
                          type="text"
                          value={item.item_name}
                          onChange={(e) => updateItem(index, 'item_name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                          disabled={!isEditing}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Description
                        </label>
                        <textarea
                          value={item.description || ''}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Unit
                        </label>
                        <input
                          type="text"
                          value={item.unit}
                          onChange={(e) => updateItem(index, 'unit', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quantity *
                        </label>
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0.01"
                          step="0.01"
                          required
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Unit Price ({formData.currency})
                        </label>
                        <input
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => updateItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          step="0.01"
                          disabled={!isEditing}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Discount (%)
                        </label>
                        <input
                          type="number"
                          value={item.discount_rate || 0}
                          onChange={(e) => updateItem(index, 'discount_rate', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          max="100"
                          step="0.01"
                          disabled={!isEditing}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Line Total
                        </label>
                        <div className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-gray-900 font-medium">
                          {formData.currency} {(item.line_total || 0).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Totals Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Totals</h3>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tax Rate (%)
                    </label>
                    <input
                      type="number"
                      value={formData.tax_rate}
                      onChange={(e) => handleInputChange('tax_rate', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      max="100"
                      step="0.01"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Amount ({formData.currency})
                    </label>
                    <input
                      type="number"
                      value={formData.discount_amount}
                      onChange={(e) => handleInputChange('discount_amount', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      min="0"
                      step="0.01"
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Subtotal:</span>
                    <span className="text-sm font-medium">{formData.currency} {subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tax ({formData.tax_rate}%):</span>
                    <span className="text-sm font-medium">{formData.currency} {taxAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Discount:</span>
                    <span className="text-sm font-medium">-{formData.currency} {formData.discount_amount.toFixed(2)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between">
                    <span className="font-medium">Total:</span>
                    <span className="font-bold text-lg">{formData.currency} {total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Terms and Notes */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Terms & Notes</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Terms & Conditions
                </label>
                <textarea
                  value={formData.terms_conditions}
                  onChange={(e) => handleInputChange('terms_conditions', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Internal Notes
                </label>
                <textarea
                  value={formData.internal_notes}
                  onChange={(e) => handleInputChange('internal_notes', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-800">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-4">
              <div className="flex flex-col sm:flex-row gap-2">
                {invoice && (
                  <>
                    <button
                      type="button"
                      onClick={handleDuplicate}
                      disabled={loading}
                      className="w-full sm:w-auto px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      <Copy className="w-4 h-4" />
                      Duplicate
                    </button>
                    <button
                      type="button"
                      onClick={handleDelete}
                      disabled={loading}
                      className="w-full sm:w-auto px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? 'Saving...' : mode === 'create' ? 'Create Invoice' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoiceSidebar;
