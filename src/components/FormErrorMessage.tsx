import React from 'react';
import { AlertCircle } from 'lucide-react';

interface FormErrorMessageProps {
  show: boolean;
  message: string;
  id?: string;
  className?: string;
}

const FormErrorMessage: React.FC<FormErrorMessageProps> = ({
  show,
  message,
  id,
  className = 'form-error mt-1'
}) => {
  if (!show || !message) return null;

  return (
    <div id={id} className={`flex items-center gap-1 ${className}`} role="alert">
      <AlertCircle className="w-4 h-4 flex-shrink-0" />
      <span>{message}</span>
    </div>
  );
};

export default FormErrorMessage;
