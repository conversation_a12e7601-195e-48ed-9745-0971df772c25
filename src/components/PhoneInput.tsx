import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface Country {
  code: string;
  name: string;
  flag: string;
  dialCode: string;
  format?: string;
}

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const countries: Country[] = [
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾', dialCode: '+60', format: '+60 XX-XXXX XXXX' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬', dialCode: '+65', format: '+65 XXXX XXXX' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩', dialCode: '+62', format: '+62 XXX-XXXX-XXXX' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭', dialCode: '+66', format: '+66 X-XXXX-XXXX' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭', dialCode: '+63', format: '+63 XXX XXX XXXX' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳', dialCode: '+84', format: '+84 XXX XXX XXX' },
  { code: 'US', name: 'United States', flag: '🇺🇸', dialCode: '+1', format: '+1 (XXX) XXX-XXXX' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', dialCode: '+44', format: '+44 XXXX XXX XXX' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', dialCode: '+61', format: '+61 XXX XXX XXX' },
  { code: 'SA', name: 'Saudi Arabia', flag: '🇸🇦', dialCode: '+966', format: '+966 XX XXX XXXX' },
  { code: 'AE', name: 'UAE', flag: '🇦🇪', dialCode: '+971', format: '+971 XX XXX XXXX' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬', dialCode: '+20', format: '+20 XXX XXX XXXX' },
];

const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  placeholder = "Enter phone number",
  className = "",
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]); // Default to Malaysia
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Parse existing value on mount
  useEffect(() => {
    if (value) {
      // Try to find country by dial code with + prefix
      let country = countries.find(c => value.startsWith(c.dialCode));

      if (!country) {
        // Try to find country by dial code without + prefix
        country = countries.find(c => value.startsWith(c.dialCode.substring(1)));
      }

      if (country) {
        setSelectedCountry(country);
        // Extract phone number part, handling both +60 and 60 prefixes
        const dialCodeToRemove = value.startsWith('+') ? country.dialCode : country.dialCode.substring(1);
        const phoneNumberPart = value.substring(dialCodeToRemove.length).trim();

        // Remove any leading spaces or dashes
        const cleanPhoneNumber = phoneNumberPart.replace(/^[\s-]+/, '');
        setPhoneNumber(cleanPhoneNumber);
      } else {
        // If no country code found, treat entire value as phone number
        setPhoneNumber(value);
      }
    }
  }, [value]); // Add value as dependency to re-parse when value changes

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setIsOpen(false);
    // Only include phone number if it exists and isn't empty
    const fullNumber = phoneNumber && phoneNumber.trim() ? `${country.dialCode} ${phoneNumber.trim()}` : country.dialCode;
    onChange(fullNumber);
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Remove any non-digit characters except spaces and dashes for formatting
    const cleanValue = inputValue.replace(/[^\d\s-]/g, '');

    setPhoneNumber(cleanValue);

    // Only combine with country code if there's actually a phone number
    const fullNumber = cleanValue && cleanValue.trim() ? `${selectedCountry.dialCode} ${cleanValue.trim()}` : selectedCountry.dialCode;
    onChange(fullNumber);
  };

  const formatPhoneNumber = (number: string): string => {
    // Basic formatting for Malaysian numbers
    if (selectedCountry.code === 'MY') {
      const digits = number.replace(/\D/g, '');
      if (digits.length <= 2) return digits;
      if (digits.length <= 6) return `${digits.slice(0, 2)}-${digits.slice(2)}`;
      return `${digits.slice(0, 2)}-${digits.slice(2, 6)} ${digits.slice(6, 10)}`;
    }
    
    // For other countries, just return the input
    return number;
  };

  return (
    <div className={`relative ${className}`}>
      <div className={`flex rounded-md transition-all ${isFocused ? 'ring-2 ring-blue-500' : ''}`}>
        {/* Country Selector */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => !disabled && setIsOpen(!isOpen)}
            disabled={disabled}
            className={`flex items-center space-x-2 px-3 py-2 border border-r-0 rounded-l-md text-sm focus:outline-none transition-colors ${
              isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
            } ${
              disabled
                ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <span className="text-lg">{selectedCountry.flag}</span>
            <span className="text-gray-700 dark:text-gray-300">{selectedCountry.dialCode}</span>
            <ChevronDown className={`w-4 h-4 text-gray-400 dark:text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>

          {isOpen && (
            <div className="absolute z-50 w-64 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
              <div className="py-1">
                {countries.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    onClick={() => handleCountrySelect(country)}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 flex items-center space-x-3 ${
                      selectedCountry.code === country.code ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' : 'text-gray-900 dark:text-white'
                    }`}
                  >
                    <span className="text-lg">{country.flag}</span>
                    <span className="flex-1">{country.name}</span>
                    <span className="text-gray-500 dark:text-gray-400">{country.dialCode}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <input
          type="tel"
          value={formatPhoneNumber(phoneNumber)}
          onChange={handlePhoneNumberChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          className={`flex-1 px-3 py-2 border border-l-0 rounded-r-md text-sm focus:outline-none transition-colors ${
            isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
          } ${
            disabled ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed' : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
          }`}
        />
      </div>

      {/* Format hint */}
      {selectedCountry.format && (
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Format: {selectedCountry.format}
        </p>
      )}
    </div>
  );
};

export default PhoneInput;
