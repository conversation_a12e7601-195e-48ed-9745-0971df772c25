import React, { useState, useEffect } from 'react';
import { X, Save, Send, Calendar, Users, Eye, Edit, Copy, Mail, MessageCircle } from 'lucide-react';
import CustomDropdown from './CustomDropdown';
import { useToast } from '../contexts/ToastContext';

interface CampaignSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'email' | 'whatsapp';
  campaign?: any;
  mode?: 'create' | 'edit' | 'view';
}

const CampaignSidebar: React.FC<CampaignSidebarProps> = ({ 
  isOpen, 
  onClose, 
  type, 
  campaign, 
  mode = 'create' 
}) => {
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !campaign);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    recipients: 'all',
    scheduleType: 'now',
    scheduleDate: '',
    scheduleTime: '',
  });

  const emailTemplates = [
    { 
      value: 'welcome', 
      label: 'Welcome New Client', 
      content: 'Assalamualaikum and welcome to Islamic Books Community!\n\nWe\'re excited to have you join our community of knowledge seekers. You\'ll receive updates about our latest Islamic books, educational programs, and special offers.\n\nMay Allah bless your journey of learning.\n\nBarakallahu feeki,\nIslamic Books Team' 
    },
    { 
      value: 'donation-thank', 
      label: 'Donation Thank You', 
      content: 'Assalamualaikum,\n\nJazakallahu khairan for your generous donation to our Save Gaza campaign. Your contribution will help provide essential aid to our brothers and sisters in need.\n\nMay Allah reward you abundantly for your kindness.\n\nBarakallahu feeki,\nSave Gaza Team' 
    },
    { 
      value: 'product-launch', 
      label: 'New Product Launch', 
      content: 'Assalamualaikum,\n\nExciting news! We\'ve just launched our new Islamic merchandise collection featuring beautiful calligraphy and inspiring designs.\n\nCheck out our latest collection and enjoy 20% off your first purchase.\n\nBarakallahu feeki,\nIslamic Books Team' 
    },
    { 
      value: 'seminar-invite', 
      label: 'Seminar Invitation', 
      content: 'Assalamualaikum,\n\nYou\'re invited to our upcoming Islamic Youth Seminar: "Building Strong Islamic Character in Modern Times"\n\nDate: This Saturday\nTime: 2:00 PM - 5:00 PM\nVenue: Community Center\n\nRegister now to secure your spot!\n\nBarakallahu feeki,\nEvent Team' 
    },
  ];

  const whatsappTemplates = [
    { 
      value: 'follow-up', 
      label: 'Purchase Follow Up', 
      content: 'Assalamualaikum! 🌙\n\nHow did you like your recent purchase from Islamic Books? We\'d love to hear your feedback!\n\nIf you have any questions, feel free to reach out. JazakAllahu khairan! 📚✨' 
    },
    { 
      value: 'reminder', 
      label: 'Seminar Reminder', 
      content: 'Assalamualaikum! 🕌\n\nReminder: Your Islamic seminar is tomorrow at 2 PM. We\'re excited to see you there!\n\nLocation: Community Center\nTime: 2:00 PM\n\nSee you soon! 🤲' 
    },
    { 
      value: 'donation-update', 
      label: 'Donation Update', 
      content: 'Assalamualaikum! 🤲\n\nAlhamdulillah! Thanks to your support, we\'ve reached 75% of our Save Gaza goal.\n\nYour generosity is making a real difference. May Allah reward you! 🇵🇸❤️' 
    },
    { 
      value: 'new-arrival', 
      label: 'New Arrival', 
      content: 'Assalamualaikum! 📚\n\nNew Islamic books just arrived! Check out our latest collection of inspiring reads.\n\nSpecial launch offer: 15% off all new arrivals! 🎉\n\nShop now: [link]' 
    },
  ];

  const templates = type === 'email' ? emailTemplates : whatsappTemplates;

  useEffect(() => {
    if (campaign && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: campaign.name || '',
        subject: campaign.subject || '',
        content: campaign.content || '',
        recipients: campaign.recipients || 'all',
        scheduleType: campaign.scheduleType || 'now',
        scheduleDate: campaign.scheduleDate || '',
        scheduleTime: campaign.scheduleTime || '',
      });
      setIsEditing(mode === 'edit');
    } else {
      setFormData({
        name: '',
        subject: '',
        content: '',
        recipients: 'all',
        scheduleType: 'now',
        scheduleDate: '',
        scheduleTime: '',
      });
      setIsEditing(true);
    }
    setError(null);
    setSelectedTemplate('');
  }, [campaign, mode, type]);

  const handleTemplateChange = (templateValue: string) => {
    setSelectedTemplate(templateValue);
    const template = templates.find(t => t.value === templateValue);
    if (template) {
      setFormData({
        ...formData,
        subject: template.label,
        content: template.content,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.name.trim()) {
        throw new Error('Campaign name is required');
      }

      if (!formData.content.trim()) {
        throw new Error('Campaign content is required');
      }

      if (type === 'email' && !formData.subject.trim()) {
        throw new Error('Email subject is required');
      }

      if (formData.scheduleType === 'later' && !formData.scheduleDate) {
        throw new Error('Schedule date is required for scheduled campaigns');
      }

      // Simulate campaign creation/update
      const actionText = formData.scheduleType === 'now' ? 'sent' : 'scheduled';
      const campaignType = type === 'email' ? 'Email' : 'WhatsApp';
      
      showSuccess(
        `${campaignType} Campaign ${mode === 'create' ? 'Created' : 'Updated'}`, 
        `Your ${campaignType.toLowerCase()} campaign has been successfully ${actionText}!`
      );
      
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Campaign Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDuplicate = async () => {
    if (!campaign) return;
    
    setLoading(true);
    try {
      const duplicateData = {
        ...formData,
        name: `${formData.name} (Copy)`,
        scheduleType: 'now',
        scheduleDate: '',
        scheduleTime: '',
      };

      showSuccess('Campaign Duplicated', 'Campaign has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating campaign:', error);
      const errorMessage = 'Failed to duplicate campaign. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getRecipientCount = () => {
    switch (formData.recipients) {
      case 'all': return 1247;
      case 'clients': return 892;
      case 'leads': return 355;
      case 'hot-leads': return 89;
      case 'recent': return 156;
      default: return 0;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-3">
            {type === 'email' ? (
              <Mail className="w-6 h-6 text-blue-600" />
            ) : (
              <MessageCircle className="w-6 h-6 text-green-600" />
            )}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {campaign ? (isEditing ? 'Edit Campaign' : 'Campaign Details') : `New ${type === 'email' ? 'Email' : 'WhatsApp'} Campaign`}
              </h2>
              {campaign && (
                <p className="text-sm text-gray-600 mt-1">{campaign.status} • {campaign.recipients || 0} recipients</p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {campaign && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Campaign Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Campaign Details</h3>
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Campaign Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!isEditing}
                    placeholder={`Enter ${type} campaign name`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Template (Optional)
                  </label>
                  <CustomDropdown
                    options={[
                      { value: '', label: 'Choose a template...' },
                      ...templates.map(template => ({
                        value: template.value,
                        label: template.label
                      }))
                    ]}
                    value={selectedTemplate}
                    onChange={handleTemplateChange}
                    disabled={!isEditing}
                  />
                </div>

                {type === 'email' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject Line *
                    </label>
                    <input
                      type="text"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                      disabled={!isEditing}
                      placeholder="Enter email subject"
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message Content *
                  </label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!isEditing}
                    placeholder={`Enter your ${type} message content...`}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {type === 'whatsapp' ? 'Keep it concise and engaging for WhatsApp' : 'Use a clear and compelling message'}
                  </p>
                </div>
              </div>
            </div>

            {/* Recipients */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Recipients</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Send To
                </label>
                <CustomDropdown
                  options={[
                    { value: 'all', label: 'All Contacts (1,247)' },
                    { value: 'clients', label: 'Clients Only (892)' },
                    { value: 'leads', label: 'Leads Only (355)' },
                    { value: 'hot-leads', label: 'Hot Leads (89)' },
                    { value: 'recent', label: 'Recent Activity (156)' },
                  ]}
                  value={formData.recipients}
                  onChange={(value) => handleInputChange('recipients', value)}
                  disabled={!isEditing}
                />
                <p className="text-sm text-gray-600 mt-2">
                  This campaign will be sent to <span className="font-medium">{getRecipientCount()}</span> recipients
                </p>
              </div>
            </div>

            {/* Scheduling */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Scheduling</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  When to Send
                </label>
                <CustomDropdown
                  options={[
                    { value: 'now', label: 'Send Now' },
                    { value: 'later', label: 'Schedule for Later' },
                  ]}
                  value={formData.scheduleType}
                  onChange={(value) => handleInputChange('scheduleType', value)}
                  disabled={!isEditing}
                />
              </div>

              {formData.scheduleType === 'later' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date
                    </label>
                    <input
                      type="date"
                      value={formData.scheduleDate}
                      onChange={(e) => handleInputChange('scheduleDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!isEditing}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Time
                    </label>
                    <input
                      type="time"
                      value={formData.scheduleTime}
                      onChange={(e) => handleInputChange('scheduleTime', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 p-6 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                {campaign && (
                  <button
                    type="button"
                    onClick={handleDuplicate}
                    disabled={loading}
                    className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Duplicate
                  </button>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading}
                  className={`w-full sm:w-auto px-4 py-2 text-white rounded-lg transition-colors flex items-center justify-center gap-2 ${
                    formData.scheduleType === 'now'
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {formData.scheduleType === 'now' ? <Send className="w-4 h-4" /> : <Calendar className="w-4 h-4" />}
                  {loading ? 'Processing...' : formData.scheduleType === 'now' ? 'Send Campaign' : 'Schedule Campaign'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CampaignSidebar;
