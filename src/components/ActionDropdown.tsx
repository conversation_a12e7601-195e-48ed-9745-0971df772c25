import React, { useState, useRef, useEffect } from 'react';
import { MoreVertical } from 'lucide-react';

interface ActionItem {
  label: string;
  icon: React.ComponentType<any> | React.ReactElement;
  onClick: () => void;
  color?: string;
  disabled?: boolean;
}

interface ActionDropdownProps {
  actions: ActionItem[];
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({ actions }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'bottom' | 'top'>('bottom');
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const calculatePosition = () => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 200; // Approximate dropdown height

    // Check if there's enough space below
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    const isTop = spaceBelow < dropdownHeight && spaceAbove > spaceBelow;
    setDropdownPosition(isTop ? 'top' : 'bottom');

    // Use simple relative positioning
    setDropdownStyle({
      position: 'absolute',
      right: '0',
      top: isTop ? 'auto' : '100%',
      bottom: isTop ? '100%' : 'auto',
      zIndex: 9999,
      maxHeight: '300px',
      overflowY: 'auto'
    });
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  const handleToggle = () => {
    if (!isOpen) {
      calculatePosition();
      // Force a re-render to update position
      setTimeout(() => {
        if (buttonRef.current) {
          calculatePosition();
        }
      }, 0);
    }
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative inline-block dropdown-container">
      <button
        ref={buttonRef}
        onClick={handleToggle}
        className="p-2 rounded-md text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
      >
        <MoreVertical className="w-4 h-4" />
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 dropdown-menu"
          style={dropdownStyle}
        >
          <div className="py-1">
            {actions.map((action, index) => (
              <button
                key={index}
                onClick={() => {
                  if (!action.disabled) {
                    action.onClick();
                    setIsOpen(false);
                  }
                }}
                disabled={action.disabled}
                className={`w-full flex items-center space-x-2 px-3 py-2 text-sm transition-colors ${
                  action.disabled
                    ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
                onMouseEnter={(e) => {
                  if (!action.disabled && action.color) {
                    e.currentTarget.style.color = action.color.replace('text-', '').replace('-600', '');
                    // Convert Tailwind color classes to actual colors
                    const colorMap: Record<string, string> = {
                      'text-red-600': '#dc2626',
                      'text-green-600': '#16a34a',
                      'text-blue-600': '#2563eb',
                      'text-yellow-600': '#ca8a04',
                      'text-purple-600': '#9333ea',
                      'text-orange-600': '#ea580c',
                    };
                    if (colorMap[action.color]) {
                      e.currentTarget.style.color = colorMap[action.color];
                    }
                  }
                }}
                onMouseLeave={(e) => {
                  if (!action.disabled) {
                    // Check if dark mode is active
                    const isDark = document.documentElement.classList.contains('dark');
                    e.currentTarget.style.color = isDark ? '#d1d5db' : '#374151'; // text-gray-300 : text-gray-700
                  }
                }}
              >
                {React.isValidElement(action.icon)
                  ? React.cloneElement(action.icon, { className: "w-4 h-4" })
                  : React.createElement(action.icon as React.ComponentType<any>, { className: "w-4 h-4" })
                }
                <span>{action.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionDropdown;