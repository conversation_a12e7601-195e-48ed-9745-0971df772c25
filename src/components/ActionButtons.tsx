import React from 'react';

interface ActionItem {
  label: string;
  icon: React.ComponentType<any>;
  onClick: () => void;
  color?: 'green' | 'yellow' | 'red' | 'blue' | 'purple' | 'orange';
  disabled?: boolean;
}

interface ActionButtonsProps {
  actions: ActionItem[];
  size?: 'sm' | 'md';
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ actions, size = 'md' }) => {
  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'green':
        return 'text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30';
      case 'yellow':
        return 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 dark:hover:bg-yellow-900/30';
      case 'red':
        return 'text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30';
      case 'blue':
        return 'text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30';
      case 'purple':
        return 'text-gray-400 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/30';
      case 'orange':
        return 'text-gray-400 hover:text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/30';
      default:
        return 'text-gray-400 hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700';
    }
  };

  const getSizeClasses = () => {
    return size === 'sm' ? 'p-2' : 'p-2';
  };

  const getIconSize = () => {
    return size === 'sm' ? 'w-3.5 h-3.5' : 'w-4 h-4';
  };

  return (
    <div className="flex items-center">
      {actions.map((action, index) => {
        const Icon = action.icon;
        const isFirst = index === 0;
        const isLast = index === actions.length - 1;

        return (
          <button
            key={index}
            onClick={action.onClick}
            disabled={action.disabled}
            className={`
              ${getSizeClasses()}
              ${getColorClasses(action.color)}
              transition-colors border border-gray-200 dark:border-gray-600
              ${isFirst ? 'rounded-l-lg' : ''}
              ${isLast ? 'rounded-r-lg' : ''}
              ${!isFirst ? '-ml-px' : ''}
              ${action.disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            title={action.label}
          >
            <Icon className={getIconSize()} />
          </button>
        );
      })}
    </div>
  );
};

export default ActionButtons;
