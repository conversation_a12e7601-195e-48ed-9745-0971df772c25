import React, { useState, useEffect } from 'react';
import { Save } from 'lucide-react';
import Modal from './Modal';
import CustomDropdown from './CustomDropdown';
import PhoneInput from './PhoneInput';
import { Client, useClients } from '../contexts/ClientContext';

interface ClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  client?: Client;
  mode: 'create' | 'edit' | 'view';
}

const ClientModal: React.FC<ClientModalProps> = ({ isOpen, onClose, client, mode }) => {
  const { addClient, updateClient } = useClients();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    status: 'active' as Client['status'],
    utmSource: 'facebook',
    tags: [] as string[],
    category: 'First Timer' as Client['category'],
    ltvSegment: 'Silver' as Client['ltvSegment'],
    engagementLevel: 'Cold' as Client['engagementLevel'],
    priority: 'Medium' as Client['priority'],
    notes: '',
    suggestedAction: '',
    lastActivity: new Date(),
    totalSpent: 0,
    transactionCount: 0,
    customFields: {},
  });

  useEffect(() => {
    if (client && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: client.name,
        email: client.email,
        phone: client.phone,
        address: client.address,
        status: client.status,
        utmSource: client.utmSource,
        tags: client.tags,
        category: client.category,
        ltvSegment: client.ltvSegment,
        engagementLevel: client.engagementLevel,
        priority: client.priority,
        notes: client.notes,
        suggestedAction: client.suggestedAction,
        lastActivity: client.lastActivity,
        totalSpent: client.totalSpent,
        transactionCount: client.transactionCount,
        customFields: client.customFields,
      });
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        status: 'active',
        utmSource: 'facebook',
        tags: [],
        category: 'First Timer',
        ltvSegment: 'Silver',
        engagementLevel: 'Cold',
        priority: 'Medium',
        notes: '',
        suggestedAction: '',
        lastActivity: new Date(),
        totalSpent: 0,
        transactionCount: 0,
        customFields: {},
      });
    }
  }, [client, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (mode === 'create') {
        await addClient(formData);
      } else if (mode === 'edit' && client) {
        await updateClient(client.id, formData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving client:', error);
    }
  };

  const isReadOnly = mode === 'view';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Add New Client' : mode === 'edit' ? 'Edit Client' : 'Client Details'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isReadOnly}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <PhoneInput
              value={formData.phone}
              onChange={(value) => setFormData({ ...formData, phone: value })}
              placeholder="Enter phone number"
              disabled={isReadOnly}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              UTM Source
            </label>
            <CustomDropdown
              options={[
                { value: 'facebook', label: 'Facebook' },
                { value: 'instagram', label: 'Instagram' },
                { value: 'tiktok', label: 'TikTok' },
                { value: 'google', label: 'Google' },
                { value: 'youtube', label: 'YouTube' },
                { value: 'whatsapp', label: 'WhatsApp' },
                { value: 'referral', label: 'Referral' },
              ]}
              value={formData.utmSource}
              onChange={(value) => setFormData({ ...formData, utmSource: value })}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            {formData.category === 'Lead' ? (
              <div className="w-full px-3 py-2 border border-gray-200 rounded-md bg-orange-50">
                <span className="text-orange-700 font-medium">Lead</span>
              </div>
            ) : (
              <CustomDropdown
                options={[
                  { value: 'First Timer', label: 'First Timer' },
                  { value: 'Retainer', label: 'Retainer' },
                  { value: 'Loyal', label: 'Loyal' },
                  { value: 'Advocator', label: 'Advocator' },
                ]}
                value={formData.category}
                onChange={(value) => setFormData({ ...formData, category: value as Client['category'] })}
                className="w-full"
                disabled={isReadOnly}
              />
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LTV Segment
            </label>
            <CustomDropdown
              options={[
                { value: 'Silver', label: 'Silver' },
                { value: 'Gold', label: 'Gold' },
                { value: 'Gold+', label: 'Gold+' },
                { value: 'Platinum', label: 'Platinum' },
              ]}
              value={formData.ltvSegment}
              onChange={(value) => setFormData({ ...formData, ltvSegment: value as Client['ltvSegment'] })}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Engagement Level
            </label>
            <CustomDropdown
              options={[
                { value: 'Hot', label: 'Hot' },
                { value: 'Warm', label: 'Warm' },
                { value: 'Cold', label: 'Cold' },
                { value: 'Frozen', label: 'Frozen' },
              ]}
              value={formData.engagementLevel}
              onChange={(value) => setFormData({ ...formData, engagementLevel: value as Client['engagementLevel'] })}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <CustomDropdown
              options={[
                { value: 'High', label: 'High' },
                { value: 'Medium', label: 'Medium' },
                { value: 'Low', label: 'Low' },
              ]}
              value={formData.priority}
              onChange={(value) => setFormData({ ...formData, priority: value as Client['priority'] })}
              className="w-full"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Address
          </label>
          <textarea
            value={formData.address}
            onChange={(e) => setFormData({ ...formData, address: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            rows={4}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Suggested Action
          </label>
          <input
            type="text"
            value={formData.suggestedAction}
            onChange={(e) => setFormData({ ...formData, suggestedAction: e.target.value })}
            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isReadOnly}
          />
        </div>

        {!isReadOnly && (
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{mode === 'create' ? 'Create Client' : 'Save Changes'}</span>
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

export default ClientModal;