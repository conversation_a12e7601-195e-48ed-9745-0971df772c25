import React from 'react';
import CustomDropdown from './CustomDropdown';

const DropdownPositioningDemo: React.FC = () => {
  const demoOptions = [
    { value: '10', label: '10 items' },
    { value: '25', label: '25 items' },
    { value: '50', label: '50 items' },
    { value: '100', label: '100 items' },
  ];

  const [selectedValue, setSelectedValue] = React.useState('25');

  return (
    <div className="p-8 space-y-8">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-blue-900 mb-2">
          Smart Dropdown Positioning Demo
        </h2>
        <p className="text-blue-700 text-sm">
          This dropdown automatically detects viewport constraints and positions itself optimally.
          Try scrolling to the bottom of the page and opening the dropdown - it will appear upward!
        </p>
      </div>

      {/* Top of page dropdown - should open downward */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-md font-medium text-gray-900 mb-4">
          Dropdown at Top of Page (opens downward)
        </h3>
        <div className="w-48">
          <CustomDropdown
            options={demoOptions}
            value={selectedValue}
            onChange={setSelectedValue}
            placeholder="Select items per page"
          />
        </div>
      </div>

      {/* Spacer to create scrollable content */}
      <div className="space-y-4">
        {Array.from({ length: 20 }, (_, i) => (
          <div key={i} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900">Content Block {i + 1}</h4>
            <p className="text-gray-600 text-sm mt-1">
              This is placeholder content to create a scrollable page. 
              Scroll down to test the smart positioning feature.
            </p>
          </div>
        ))}
      </div>

      {/* Bottom of page dropdown - should open upward */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-md font-medium text-gray-900 mb-4">
          Dropdown at Bottom of Page (opens upward)
        </h3>
        <div className="w-48">
          <CustomDropdown
            options={demoOptions}
            value={selectedValue}
            onChange={setSelectedValue}
            placeholder="Select items per page"
          />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          This dropdown should open upward when you're at the bottom of the page!
        </p>
      </div>
    </div>
  );
};

export default DropdownPositioningDemo;
