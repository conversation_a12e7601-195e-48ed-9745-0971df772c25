import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Car<PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';

interface AcquisitionData {
  month: string;
  total: number;
  facebook: number;
  google: number;
  tiktok: number;
  instagram: number;
  direct: number;
  referral: number;
  other: number;
}

interface ClientAcquisitionTrendsChartProps {
  data: AcquisitionData[];
  height?: number;
  title?: string;
}

const ClientAcquisitionTrendsChart: React.FC<ClientAcquisitionTrendsChartProps> = ({
  data,
  height = 300,
  title = 'Client Acquisition Trends'
}) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey}: {entry.value} clients
            </p>
          ))}
          <div className="border-t border-gray-200 mt-2 pt-2">
            <p className="text-sm font-medium text-gray-900">
              Total: {payload[0]?.payload?.total || 0} clients
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const sourceColors = {
    facebook: '#1877f2',
    google: '#4285f4',
    tiktok: '#000000',
    instagram: '#e4405f',
    direct: '#10b981',
    referral: '#8b5cf6',
    other: '#6b7280'
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="month" 
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
          />
          <YAxis 
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            wrapperStyle={{ fontSize: '12px' }}
            iconType="line"
          />
          
          {/* Main sources */}
          <Line
            type="monotone"
            dataKey="facebook"
            stroke={sourceColors.facebook}
            strokeWidth={2}
            dot={{ fill: sourceColors.facebook, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
            name="Facebook"
          />
          <Line
            type="monotone"
            dataKey="google"
            stroke={sourceColors.google}
            strokeWidth={2}
            dot={{ fill: sourceColors.google, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
            name="Google"
          />
          <Line
            type="monotone"
            dataKey="tiktok"
            stroke={sourceColors.tiktok}
            strokeWidth={2}
            dot={{ fill: sourceColors.tiktok, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
            name="TikTok"
          />
          <Line
            type="monotone"
            dataKey="instagram"
            stroke={sourceColors.instagram}
            strokeWidth={2}
            dot={{ fill: sourceColors.instagram, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
            name="Instagram"
          />
          <Line
            type="monotone"
            dataKey="direct"
            stroke={sourceColors.direct}
            strokeWidth={2}
            dot={{ fill: sourceColors.direct, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
            name="Direct"
          />
          <Line
            type="monotone"
            dataKey="referral"
            stroke={sourceColors.referral}
            strokeWidth={2}
            dot={{ fill: sourceColors.referral, strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
            name="Referral"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ClientAcquisitionTrendsChart;
