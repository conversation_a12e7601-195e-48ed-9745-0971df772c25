import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend
} from 'recharts';

interface DistributionData {
  name: string;
  value: number;
  percentage: number;
  color?: string;
}

interface DistributionChartProps {
  data: DistributionData[];
  height?: number;
  type?: 'bar' | 'pie';
  title?: string;
  showPercentage?: boolean;
}

const DistributionChart: React.FC<DistributionChartProps> = ({
  data,
  height = 300,
  type = 'bar',
  title,
  showPercentage = true
}) => {
  const formatValue = (value: number) => {
    return value.toLocaleString();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-white">{data.name || label}</p>
          <p className="text-sm text-blue-600 dark:text-blue-400">
            Count: {formatValue(data.value)}
          </p>
          {showPercentage && (
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Percentage: {data.percentage}%
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 dark:text-white">{data.name}</p>
          <p className="text-sm text-blue-600 dark:text-blue-400">
            Count: {formatValue(data.value)}
          </p>
          {showPercentage && (
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Percentage: {data.percentage}%
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  if (type === 'pie') {
    return (
      <div className="w-full">
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        )}
        <ResponsiveContainer width="100%" height={height}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color || `hsl(${index * 45}, 70%, 60%)`} />
              ))}
            </Pie>
            <Tooltip content={<CustomPieTooltip />} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="rgb(229 231 235)" className="dark:stroke-gray-600" />
          <XAxis
            dataKey="name"
            tick={{ fontSize: 12, fill: 'rgb(107 114 128)' }}
            axisLine={{ stroke: 'rgb(229 231 235)' }}
            className="dark:fill-gray-300"
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis
            tick={{ fontSize: 12, fill: 'rgb(107 114 128)' }}
            axisLine={{ stroke: 'rgb(229 231 235)' }}
            className="dark:fill-gray-300"
            tickFormatter={formatValue}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="value"
            radius={[4, 4, 0, 0]}
            fill="#3b82f6"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color || '#3b82f6'} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default DistributionChart;
