import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface ScoreDistributionData {
  range: string;
  count: number;
  percentage: number;
}

interface ScoreDistributionChartProps {
  data: ScoreDistributionData[];
  height?: number;
  title?: string;
  scoreType?: 'overall' | 'phone' | 'email';
}

const ScoreDistributionChart: React.FC<ScoreDistributionChartProps> = ({
  data,
  height = 300,
  title,
  scoreType = 'overall'
}) => {
  const getBarColor = (range: string) => {
    const score = parseInt(range.split('-')[0]);
    if (score >= 90) return '#10b981'; // Green
    if (score >= 80) return '#3b82f6'; // Blue
    if (score >= 70) return '#f59e0b'; // Yellow
    if (score >= 60) return '#f97316'; // Orange
    return '#ef4444'; // Red
  };

  const formatValue = (value: number) => {
    return value.toLocaleString();
  };

  // Validate and sanitize data
  const validData = React.useMemo(() => {
    if (!Array.isArray(data)) {
      return [];
    }

    return data.map(item => ({
      range: item.range || 'Unknown',
      count: typeof item.count === 'number' ? item.count : 0,
      percentage: typeof item.percentage === 'number' ? item.percentage : 0
    }));
  }, [data]);

  // Show loading state if no data
  if (!data || data.length === 0) {
    return (
      <div className="w-full" style={{ height }}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        )}
        <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">Loading chart data...</p>
          </div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;

      // Safely handle potentially undefined values
      const count = data.count !== undefined ? data.count : 0;
      const percentage = data.percentage !== undefined ? data.percentage : 0;

      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">Score Range: {label}</p>
          <p className="text-sm text-blue-600">
            Count: {formatValue(count)}
          </p>
          <p className="text-sm text-gray-600">
            Percentage: {typeof percentage === 'number' ? percentage.toFixed(1) : '0.0'}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={validData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="range" 
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
          />
          <YAxis 
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
            tickFormatter={formatValue}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="count"
            radius={[4, 4, 0, 0]}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(entry.range)} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ScoreDistributionChart;
