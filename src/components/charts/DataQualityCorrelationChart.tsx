import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Cell
} from 'recharts';

interface CorrelationData {
  dataQuality: string;
  engagementLevel: string;
  count: number;
  x: number; // Numeric representation for plotting
  y: number; // Numeric representation for plotting
}

interface DataQualityCorrelationChartProps {
  data: CorrelationData[];
  height?: number;
  title?: string;
}

const DataQualityCorrelationChart: React.FC<DataQualityCorrelationChartProps> = ({
  data,
  height = 300,
  title = 'Data Quality vs Engagement Level'
}) => {
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">
            {data.dataQuality} Quality
          </p>
          <p className="text-sm text-gray-600">
            Engagement: {data.engagement<PERSON><PERSON><PERSON>}
          </p>
          <p className="text-sm text-blue-600">
            Clients: {data.count}
          </p>
        </div>
      );
    }
    return null;
  };

  const getColor = (dataQuality: string, engagementLevel: string) => {
    // Color based on combination of quality and engagement
    if (dataQuality === 'Excellent' && engagementLevel === 'Hot') return '#10b981';
    if (dataQuality === 'Excellent' && engagementLevel === 'Warm') return '#3b82f6';
    if (dataQuality === 'Good' && engagementLevel === 'Hot') return '#06b6d4';
    if (dataQuality === 'Good' && engagementLevel === 'Warm') return '#8b5cf6';
    if (dataQuality === 'Fair') return '#f59e0b';
    return '#ef4444'; // Poor quality or Cold/Frozen engagement
  };

  const getSize = (count: number) => {
    // Size bubbles based on count (min 20, max 100)
    const maxCount = Math.max(...data.map(d => d.count));
    return Math.max(20, (count / maxCount) * 80);
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <ScatterChart
          margin={{ top: 20, right: 30, bottom: 40, left: 40 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            type="number"
            dataKey="x"
            domain={[0, 4]}
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
            tickFormatter={(value) => {
              const qualities = ['', 'Poor', 'Fair', 'Good', 'Excellent'];
              return qualities[value] || '';
            }}
            label={{ value: 'Data Quality', position: 'insideBottom', offset: -10 }}
          />
          <YAxis 
            type="number"
            dataKey="y"
            domain={[0, 4]}
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
            tickFormatter={(value) => {
              const levels = ['', 'Frozen', 'Cold', 'Warm', 'Hot'];
              return levels[value] || '';
            }}
            label={{ value: 'Engagement Level', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Scatter
            data={data}
            fill="#8884d8"
          />
        </ScatterChart>
      </ResponsiveContainer>
      
      {/* Legend */}
      <div className="mt-4 text-xs text-gray-600">
        <p className="mb-2 font-medium">Bubble size represents number of clients</p>
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500" />
            <span>High Quality + Hot</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500" />
            <span>High Quality + Warm</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500" />
            <span>Fair Quality</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500" />
            <span>Poor Quality/Cold</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataQualityCorrelationChart;
