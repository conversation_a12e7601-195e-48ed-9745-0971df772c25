import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';

interface RevenueData {
  period: string;
  revenue: number;
  profit?: number;
}

interface RevenueChartProps {
  data: RevenueData[];
  height?: number;
  showProfit?: boolean;
  title?: string;
}

const RevenueChart: React.FC<RevenueChartProps> = ({
  data,
  height = 300,
  showProfit = false,
  title = 'Revenue Analytics'
}) => {
  const formatCurrency = (value: number) => {
    return `RM${value.toLocaleString()}`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'revenue' ? 'Revenue' : 'Profit'}: {formatCurrency(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="period" 
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
          />
          <YAxis 
            tick={{ fontSize: 12, fill: '#6b7280' }}
            axisLine={{ stroke: '#e5e7eb' }}
            tickFormatter={formatCurrency}
          />
          <Tooltip content={<CustomTooltip />} />
          {showProfit && <Legend />}
          <Bar 
            dataKey="revenue" 
            fill="#3b82f6" 
            radius={[4, 4, 0, 0]}
            name="Revenue"
          />
          {showProfit && (
            <Bar 
              dataKey="profit" 
              fill="#10b981" 
              radius={[4, 4, 0, 0]}
              name="Profit"
            />
          )}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RevenueChart;
