import React, { useState, useEffect } from 'react';
import { X, Save, Plus, Trash2, Eye, Edit, Copy, Share, BarChart3 } from 'lucide-react';
import CustomDropdown from './CustomDropdown';
import { useToast } from '../contexts/ToastContext';

interface FormField {
  id: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'textarea' | 'checkbox';
  label: string;
  required: boolean;
  options?: string[];
}

interface Form {
  id?: string;
  name: string;
  description: string;
  fields: FormField[];
  linkedProduct?: string;
  status: 'Active' | 'Draft' | 'Archived';
  submissions?: number;
  conversionRate?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface FormSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  form?: Form;
  mode: 'create' | 'edit' | 'view';
  onSave: (form: Form) => void;
}

const FormSidebar: React.FC<FormSidebarProps> = ({ isOpen, onClose, form, mode, onSave }) => {
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !form);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<Form>({
    name: '',
    description: '',
    fields: [],
    linkedProduct: '',
    status: 'Draft',
    submissions: 0,
    conversionRate: 0,
  });

  useEffect(() => {
    if (form && (mode === 'edit' || mode === 'view')) {
      setFormData(form);
      setIsEditing(mode === 'edit');
    } else {
      setFormData({
        name: '',
        description: '',
        fields: [],
        linkedProduct: '',
        status: 'Draft',
        submissions: 0,
        conversionRate: 0,
      });
      setIsEditing(true);
    }
    setError(null);
  }, [form, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.name.trim()) {
        throw new Error('Form name is required');
      }

      if (formData.fields.length === 0) {
        throw new Error('At least one form field is required');
      }

      // Validate that all fields have labels
      const invalidFields = formData.fields.filter(field => !field.label.trim());
      if (invalidFields.length > 0) {
        throw new Error('All form fields must have labels');
      }

      onSave(formData);
      showSuccess('Form Saved', `${formData.name} has been successfully ${mode === 'create' ? 'created' : 'updated'}!`);
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Save Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Form, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addField = () => {
    const newField: FormField = {
      id: Date.now().toString(),
      type: 'text',
      label: '',
      required: false,
    };
    setFormData({
      ...formData,
      fields: [...formData.fields, newField],
    });
  };

  const updateField = (index: number, field: FormField) => {
    const updatedFields = [...formData.fields];
    updatedFields[index] = field;
    setFormData({
      ...formData,
      fields: updatedFields,
    });
  };

  const removeField = (index: number) => {
    const updatedFields = formData.fields.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      fields: updatedFields,
    });
  };

  const handleDuplicate = async () => {
    if (!form) return;
    
    setLoading(true);
    try {
      const duplicateData = {
        ...formData,
        name: `${formData.name} (Copy)`,
        status: 'Draft' as const,
        submissions: 0,
        conversionRate: 0,
      };
      
      onSave(duplicateData);
      showSuccess('Form Duplicated', 'Form has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating form:', error);
      const errorMessage = 'Failed to duplicate form. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleShare = () => {
    if (form?.id) {
      const shareUrl = `${window.location.origin}/forms/${form.id}`;
      navigator.clipboard.writeText(shareUrl);
      showSuccess('Link Copied', 'Form share link has been copied to clipboard');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {form ? (isEditing ? 'Edit Form' : 'Form Details') : 'Create New Form'}
            </h2>
            {form && (
              <p className="text-sm text-gray-600 mt-1">{form.status} • {form.submissions || 0} submissions</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {form && !isEditing && (
              <>
                <button
                  onClick={handleShare}
                  className="px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors flex items-center gap-2"
                >
                  <Share className="w-4 h-4" />
                  Share
                </button>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Form Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={!isEditing}
                    placeholder="Enter form name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'Active', label: 'Active' },
                      { value: 'Draft', label: 'Draft' },
                      { value: 'Archived', label: 'Archived' },
                    ]}
                    value={formData.status}
                    onChange={(value) => handleInputChange('status', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                    placeholder="Describe the purpose of this form"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Linked Product (Optional)
                  </label>
                  <input
                    type="text"
                    value={formData.linkedProduct || ''}
                    onChange={(e) => handleInputChange('linkedProduct', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isEditing}
                    placeholder="Link to a specific product"
                  />
                </div>
              </div>
            </div>

            {/* Form Statistics */}
            {form && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Statistics</h3>
                
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Submissions</p>
                      <p className="text-2xl font-bold text-blue-600">{formData.submissions || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Conversion Rate</p>
                      <p className="text-2xl font-bold text-green-600">{(formData.conversionRate || 0).toFixed(1)}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Created</p>
                      <p className="text-sm font-medium">
                        {form.createdAt ? new Date(form.createdAt).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Last Updated</p>
                      <p className="text-sm font-medium">
                        {form.updatedAt ? new Date(form.updatedAt).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Form Fields */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Form Fields</h3>
                {isEditing && (
                  <button
                    type="button"
                    onClick={addField}
                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add Field
                  </button>
                )}
              </div>

              <div className="space-y-4">
                {formData.fields.map((field, index) => (
                  <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">Field {index + 1}</h4>
                      {isEditing && (
                        <button
                          type="button"
                          onClick={() => removeField(index)}
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Field Label *
                        </label>
                        <input
                          type="text"
                          value={field.label}
                          onChange={(e) => updateField(index, { ...field, label: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          disabled={!isEditing}
                          placeholder="Enter field label"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Field Type
                        </label>
                        <CustomDropdown
                          options={[
                            { value: 'text', label: 'Text' },
                            { value: 'email', label: 'Email' },
                            { value: 'phone', label: 'Phone' },
                            { value: 'select', label: 'Select' },
                            { value: 'textarea', label: 'Textarea' },
                            { value: 'checkbox', label: 'Checkbox' },
                          ]}
                          value={field.type}
                          onChange={(value) => updateField(index, { ...field, type: value as FormField['type'] })}
                          disabled={!isEditing}
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={field.required}
                            onChange={(e) => updateField(index, { ...field, required: e.target.checked })}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            disabled={!isEditing}
                          />
                          <span className="text-sm font-medium text-gray-700">Required field</span>
                        </label>
                      </div>

                      {field.type === 'select' && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Options (comma-separated)
                          </label>
                          <input
                            type="text"
                            value={field.options?.join(', ') || ''}
                            onChange={(e) => updateField(index, { 
                              ...field, 
                              options: e.target.value.split(',').map(opt => opt.trim()).filter(opt => opt) 
                            })}
                            className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Option 1, Option 2, Option 3"
                            disabled={!isEditing}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {formData.fields.length === 0 && (
                  <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                    <Plus className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p>No fields added yet.</p>
                    {isEditing && <p className="text-sm">Click "Add Field" to get started.</p>}
                  </div>
                )}
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 p-6 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                {form && (
                  <>
                    <button
                      type="button"
                      onClick={handleDuplicate}
                      disabled={loading}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors flex items-center gap-2"
                    >
                      <Copy className="w-4 h-4" />
                      Duplicate
                    </button>
                  </>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={loading}
                  className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {loading ? 'Saving...' : mode === 'create' ? 'Create Form' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FormSidebar;
