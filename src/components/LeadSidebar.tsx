import React, { useState, useEffect } from 'react';
import { X, Save, Eye, Edit, Trash2, Copy } from 'lucide-react';
import CustomDropdown from './CustomDropdown';
import PhoneInput from './PhoneInput';
import { Lead, useLeads } from '../contexts/LeadContext';
import { useToast } from '../contexts/ToastContext';

interface LeadSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  lead?: Lead;
  mode: 'create' | 'edit' | 'view';
}

const LeadSidebar: React.FC<LeadSidebarProps> = ({ isOpen, onClose, lead, mode }) => {
  const { addLead, updateLead, deleteLead } = useLeads();
  const { showSuccess, showError } = useToast();
  const [isEditing, setIsEditing] = useState(mode === 'edit' || mode === 'create' || !lead);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    utmSource: 'facebook',
    utmCampaign: '',
    utmMedium: '',
    utmContent: '',
    utmTerm: '',
    channel: '',
    leadType: 'capture' as Lead['leadType'],
    tags: [] as string[],
    title: '',
    description: '',
    status: 'new' as Lead['status'],
    opportunityStatus: 'contacted' as Lead['opportunityStatus'],
    priority: 'Medium' as Lead['priority'],
    engagementLevel: 'Cold' as Lead['engagementLevel'],
    ltvSegment: 'Silver' as Lead['ltvSegment'],
    source: '',
    assignedTo: '',
    estimatedValue: 0,
    probability: 10,
    expectedCloseDate: undefined as Date | undefined,
    notes: '',
    internalRemarks: '',
    suggestedAction: '',
    lastActivity: new Date(),
    lastContacted: undefined as Date | undefined,
    lifecycleStage: 0,
    documents: [] as string[],
    isRecycled: false,
    recycledAt: undefined as Date | undefined,
    convertedAt: undefined as Date | undefined,
    convertedToClientId: '',
    clientId: '',
  });

  useEffect(() => {
    if (lead && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: lead.name || '',
        email: lead.email || '',
        phone: lead.phone || '',
        company: lead.company || '',
        utmSource: lead.utmSource || 'facebook',
        utmCampaign: lead.utmCampaign || '',
        utmMedium: lead.utmMedium || '',
        utmContent: lead.utmContent || '',
        utmTerm: lead.utmTerm || '',
        channel: lead.channel || '',
        leadType: lead.leadType,
        tags: lead.tags,
        title: lead.title || '',
        description: lead.description || '',
        status: lead.status,
        opportunityStatus: lead.opportunityStatus || 'contacted',
        priority: lead.priority,
        engagementLevel: lead.engagementLevel,
        ltvSegment: lead.ltvSegment || 'Silver',
        source: lead.source || '',
        assignedTo: lead.assignedTo || '',
        estimatedValue: lead.estimatedValue || 0,
        probability: lead.probability || 10,
        expectedCloseDate: lead.expectedCloseDate,
        notes: lead.notes || '',
        internalRemarks: lead.internalRemarks || '',
        suggestedAction: lead.suggestedAction || '',
        lastActivity: lead.lastActivity || new Date(),
        lastContacted: lead.lastContacted,
        lifecycleStage: lead.lifecycleStage || 0,
        documents: lead.documents || [],
        isRecycled: lead.isRecycled || false,
        recycledAt: lead.recycledAt,
        convertedAt: lead.convertedAt,
        convertedToClientId: lead.convertedToClientId || '',
        clientId: lead.clientId || '',
      });
      setIsEditing(mode === 'edit');
    } else {
      // Reset form for new lead
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        utmSource: 'facebook',
        utmCampaign: '',
        utmMedium: '',
        utmContent: '',
        utmTerm: '',
        channel: '',
        leadType: 'capture',
        tags: [],
        title: '',
        description: '',
        status: 'new',
        opportunityStatus: 'contacted',
        priority: 'Medium',
        engagementLevel: 'Cold',
        ltvSegment: 'Silver',
        source: '',
        assignedTo: '',
        estimatedValue: 0,
        probability: 10,
        expectedCloseDate: undefined,
        notes: '',
        internalRemarks: '',
        suggestedAction: '',
        lastActivity: new Date(),
        lastContacted: undefined,
        lifecycleStage: 0,
        documents: [],
        isRecycled: false,
        recycledAt: undefined,
        convertedAt: undefined,
        convertedToClientId: '',
        clientId: '',
      });
      setIsEditing(true);
    }
    setError(null);
  }, [lead, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (mode === 'create') {
        addLead(formData);
        showSuccess('Lead Created', `${formData.name} has been successfully added to your leads!`);
      } else if (mode === 'edit' && lead) {
        updateLead(lead.id, formData);
        showSuccess('Lead Updated', `${formData.name} has been successfully updated!`);
      }
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      showError('Save Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDelete = async () => {
    if (!lead) return;
    
    setLoading(true);
    try {
      await deleteLead(lead.id);
      showSuccess('Lead Deleted', 'Lead has been successfully deleted');
      onClose();
    } catch (error) {
      console.error('Error deleting lead:', error);
      const errorMessage = 'Failed to delete lead. Please try again.';
      setError(errorMessage);
      showError('Delete Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async () => {
    if (!lead) return;
    
    setLoading(true);
    try {
      const duplicateData = {
        ...formData,
        name: `${formData.name} (Copy)`,
        email: '', // Clear email to avoid duplicates
      };
      
      addLead(duplicateData);
      showSuccess('Lead Duplicated', 'Lead has been successfully duplicated');
      onClose();
    } catch (error) {
      console.error('Error duplicating lead:', error);
      const errorMessage = 'Failed to duplicate lead. Please try again.';
      setError(errorMessage);
      showError('Duplicate Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[60] overflow-hidden">
      {/* Background overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Right Sidebar */}
      <div className="fixed right-0 top-0 h-full w-full sm:max-w-xl bg-white dark:bg-gray-800 shadow-xl flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {lead ? (isEditing ? 'Edit Lead' : 'Lead Details') : 'Create New Lead'}
            </h2>
            {lead && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{lead.email}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {lead && !isEditing && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                >
                  Edit
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto bg-white dark:bg-gray-800">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </div>
            )}

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    required
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    required
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone
                  </label>
                  <PhoneInput
                    value={formData.phone}
                    onChange={(value) => handleInputChange('phone', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Company
                  </label>
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Lead Classification */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Lead Classification</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Lead Type
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'capture', label: 'Lead Capture' },
                      { value: 'opportunity', label: 'Sales Opportunity' },
                    ]}
                    value={formData.leadType}
                    onChange={(value) => handleInputChange('leadType', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'new', label: 'New' },
                      { value: 'contacted', label: 'Contacted' },
                      { value: 'engaged', label: 'Engaged' },
                      { value: 'qualified', label: 'Qualified' },
                      { value: 'converted', label: 'Converted' },
                      { value: 'disqualified', label: 'Disqualified' },
                    ]}
                    value={formData.status}
                    onChange={(value) => handleInputChange('status', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Priority
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'Low', label: 'Low' },
                      { value: 'Medium', label: 'Medium' },
                      { value: 'High', label: 'High' },
                    ]}
                    value={formData.priority}
                    onChange={(value) => handleInputChange('priority', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Engagement Level
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'Hot', label: 'Hot' },
                      { value: 'Warm', label: 'Warm' },
                      { value: 'Cold', label: 'Cold' },
                      { value: 'Frozen', label: 'Frozen' },
                    ]}
                    value={formData.engagementLevel}
                    onChange={(value) => handleInputChange('engagementLevel', value)}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Sales Information */}
            {formData.leadType === 'opportunity' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Sales Information</h3>

                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Title
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Estimated Value (RM)
                    </label>
                    <input
                      type="number"
                      value={formData.estimatedValue}
                      onChange={(e) => handleInputChange('estimatedValue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                      min="0"
                      step="0.01"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Probability (%)
                    </label>
                    <input
                      type="number"
                      value={formData.probability}
                      onChange={(e) => handleInputChange('probability', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                      min="0"
                      max="100"
                      disabled={!isEditing}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Expected Close Date
                    </label>
                    <input
                      type="date"
                      value={formData.expectedCloseDate ? formData.expectedCloseDate.toISOString().split('T')[0] : ''}
                      onChange={(e) => handleInputChange('expectedCloseDate', e.target.value ? new Date(e.target.value) : undefined)}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* UTM Tracking */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">UTM Tracking</h3>

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    UTM Source
                  </label>
                  <CustomDropdown
                    options={[
                      { value: 'facebook', label: 'Facebook' },
                      { value: 'instagram', label: 'Instagram' },
                      { value: 'tiktok', label: 'TikTok' },
                      { value: 'google', label: 'Google' },
                      { value: 'youtube', label: 'YouTube' },
                      { value: 'whatsapp', label: 'WhatsApp' },
                      { value: 'referral', label: 'Referral' },
                      { value: 'website', label: 'Website' },
                      { value: 'email', label: 'Email' },
                      { value: 'phone', label: 'Phone' },
                    ]}
                    value={formData.utmSource}
                    onChange={(value) => handleInputChange('utmSource', value)}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    UTM Campaign
                  </label>
                  <input
                    type="text"
                    value={formData.utmCampaign}
                    onChange={(e) => handleInputChange('utmCampaign', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    UTM Medium
                  </label>
                  <input
                    type="text"
                    value={formData.utmMedium}
                    onChange={(e) => handleInputChange('utmMedium', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Notes</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                  disabled={!isEditing}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Internal Remarks
                </label>
                <textarea
                  value={formData.internalRemarks}
                  onChange={(e) => handleInputChange('internalRemarks', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                />
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-800">
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
              {/* Row 1: Duplicate, Cancel, Delete (3 buttons in single row) */}
              <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 sm:flex-1">
                {lead && (
                  <button
                    type="button"
                    onClick={handleDuplicate}
                    disabled={loading}
                    className="w-full sm:w-auto px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center justify-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Duplicate
                  </button>
                )}
                <button
                  type="button"
                  onClick={onClose}
                  className="w-full sm:w-auto px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                {lead && (
                  <button
                    type="button"
                    onClick={handleDelete}
                    disabled={loading}
                    className="w-full sm:w-auto px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors flex items-center justify-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                )}
              </div>

              {/* Row 2: Save Changes (full-width button) */}
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={loading}
                className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
              >
                <Save className="w-4 h-4" />
                {loading ? 'Saving...' : mode === 'create' ? 'Create Lead' : 'Save Changes'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LeadSidebar;
