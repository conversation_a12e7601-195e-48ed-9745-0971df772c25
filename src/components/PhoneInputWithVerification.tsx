import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown, ShieldCheck, Shield } from 'lucide-react';

interface Country {
  code: string;
  name: string;
  flag: string;
  dialCode: string;
  format?: string;
}

interface PhoneInputWithVerificationProps {
  value: string;
  verified: boolean;
  onChange: (value: string) => void;
  onVerificationChange: (verified: boolean) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const countries: Country[] = [
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾', dialCode: '+60', format: '+60 XX-XXXX XXXX' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬', dialCode: '+65', format: '+65 XXXX XXXX' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩', dialCode: '+62', format: '+62 XXX-XXXX-XXXX' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭', dialCode: '+66', format: '+66 X-XXXX-XXXX' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭', dialCode: '+63', format: '+63 XXX XXX XXXX' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳', dialCode: '+84', format: '+84 XXX XXX XXX' },
  { code: 'US', name: 'United States', flag: '🇺🇸', dialCode: '+1', format: '+1 (XXX) XXX-XXXX' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', dialCode: '+44', format: '+44 XXXX XXX XXX' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', dialCode: '+61', format: '+61 XXX XXX XXX' },
  { code: 'SA', name: 'Saudi Arabia', flag: '🇸🇦', dialCode: '+966', format: '+966 XX XXX XXXX' },
  { code: 'AE', name: 'UAE', flag: '🇦🇪', dialCode: '+971', format: '+971 XX XXX XXXX' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬', dialCode: '+20', format: '+20 XXX XXX XXXX' },
];

const PhoneInputWithVerification: React.FC<PhoneInputWithVerificationProps> = ({
  value,
  verified,
  onChange,
  onVerificationChange,
  placeholder = "Enter phone number",
  className = "",
  disabled = false
}) => {
  const [isCountryOpen, setIsCountryOpen] = useState(false);
  const [isVerificationOpen, setIsVerificationOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country>(countries[0]);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [verificationDropdownPosition, setVerificationDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const [countryDropdownPosition, setCountryDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  const verificationDropdownRef = useRef<HTMLDivElement>(null);

  // Parse existing value on mount
  useEffect(() => {
    if (value) {
      // Try to find country by dial code with + prefix
      let country = countries.find(c => value.startsWith(c.dialCode));

      if (!country) {
        // Try to find country by dial code without + prefix
        country = countries.find(c => value.startsWith(c.dialCode.substring(1)));
      }

      if (country) {
        setSelectedCountry(country);
        // Extract phone number part, handling both +60 and 60 prefixes
        const dialCodeToRemove = value.startsWith('+') ? country.dialCode : country.dialCode.substring(1);
        const phoneNumberPart = value.substring(dialCodeToRemove.length).trim();

        // Remove any leading spaces or dashes
        const cleanPhoneNumber = phoneNumberPart.replace(/^[\s-]+/, '');
        setPhoneNumber(cleanPhoneNumber);
      } else {
        // If no country code found, treat entire value as phone number
        setPhoneNumber(value);
      }
    }
  }, [value]); // Add value as dependency to re-parse when value changes

  // Calculate country dropdown position when it opens
  useEffect(() => {
    if (isCountryOpen && countryDropdownRef.current) {
      const rect = countryDropdownRef.current.getBoundingClientRect();
      setCountryDropdownPosition({
        top: rect.bottom + window.scrollY + 4,
        left: rect.left + window.scrollX,
        width: Math.max(rect.width, 256) // Minimum 256px width for country dropdown
      });
    }
  }, [isCountryOpen]);

  // Calculate verification dropdown position when it opens
  useEffect(() => {
    if (isVerificationOpen && verificationDropdownRef.current) {
      const rect = verificationDropdownRef.current.getBoundingClientRect();
      setVerificationDropdownPosition({
        top: rect.bottom + window.scrollY + 4,
        left: rect.right + window.scrollX - 128, // 128px is the dropdown width (w-32)
        width: rect.width
      });
    }
  }, [isVerificationOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target as Node)) {
        setIsCountryOpen(false);
      }
      if (verificationDropdownRef.current && !verificationDropdownRef.current.contains(event.target as Node)) {
        setIsVerificationOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    setIsCountryOpen(false);
    // Only include phone number if it exists and isn't empty
    const fullNumber = phoneNumber && phoneNumber.trim() ? `${country.dialCode} ${phoneNumber.trim()}` : country.dialCode;
    onChange(fullNumber);
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const cleanValue = inputValue.replace(/[^\d\s-]/g, '');
    setPhoneNumber(cleanValue);
    // Only combine with country code if there's actually a phone number
    const fullNumber = cleanValue && cleanValue.trim() ? `${selectedCountry.dialCode} ${cleanValue.trim()}` : selectedCountry.dialCode;
    onChange(fullNumber);
  };

  const handleVerificationSelect = (isVerified: boolean) => {
    onVerificationChange(isVerified);
    setIsVerificationOpen(false);
  };

  const formatPhoneNumber = (number: string): string => {
    if (selectedCountry.code === 'MY') {
      const digits = number.replace(/\D/g, '');
      if (digits.length <= 2) return digits;
      if (digits.length <= 6) return `${digits.slice(0, 2)}-${digits.slice(2)}`;
      return `${digits.slice(0, 2)}-${digits.slice(2, 6)} ${digits.slice(6, 10)}`;
    }
    return number;
  };

  const verificationOptions = [
    { value: false, label: 'Unverified', icon: Shield, color: 'text-gray-500' },
    { value: true, label: 'Verified', icon: ShieldCheck, color: 'text-blue-600' }
  ];

  const currentOption = verificationOptions.find(opt => opt.value === verified) || verificationOptions[0];

  return (
    <div className={`relative w-full ${className}`}>
      <div className={`flex rounded-md transition-all overflow-hidden ${isFocused ? 'ring-2 ring-blue-500' : ''}`}>
        {/* Country Selector */}
        <div className="relative flex-shrink-0" ref={countryDropdownRef}>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (!disabled) {
                setIsCountryOpen(!isCountryOpen);
              }
            }}
            disabled={disabled}
            className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-2 border border-r-0 rounded-l-md text-sm focus:outline-none transition-colors h-10 leading-normal min-w-0 ${
              isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
            } ${
              disabled
                ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <span className="text-base sm:text-lg">{selectedCountry.flag}</span>
            <span className="text-gray-700 dark:text-gray-300 text-xs sm:text-sm">{selectedCountry.dialCode}</span>
            <ChevronDown className={`w-3 h-3 sm:w-4 sm:h-4 text-gray-400 transition-transform ${isCountryOpen ? 'rotate-180' : ''}`} />
          </button>

        </div>

        {/* Phone Number Input */}
        <input
          type="tel"
          value={formatPhoneNumber(phoneNumber)}
          onChange={handlePhoneNumberChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          className={`flex-1 min-w-0 px-2 sm:px-3 py-2 border border-l-0 border-r-0 text-sm focus:outline-none transition-colors h-10 leading-normal ${
            isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
          } ${
            disabled ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed' : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
          }`}
        />

        {/* Verification Selector */}
        <div className="relative flex-shrink-0" ref={verificationDropdownRef}>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (!disabled) {
                setIsVerificationOpen(!isVerificationOpen);
              }
            }}
            disabled={disabled}
            className={`flex items-center space-x-2 px-3 py-2 border border-l-0 rounded-r-md text-sm focus:outline-none transition-colors min-w-[100px] h-10 leading-normal ${
              isFocused ? 'border-blue-500 dark:border-blue-400' : 'border-gray-200 dark:border-gray-600'
            } ${
              disabled
                ? 'bg-gray-50 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <currentOption.icon className={`w-4 h-4 ${currentOption.color}`} />
            <span className="text-gray-700 dark:text-gray-300 text-xs">{currentOption.label}</span>
            <ChevronDown className={`w-3 h-3 text-gray-400 transition-transform ${isVerificationOpen ? 'rotate-180' : ''}`} />
          </button>

        </div>
      </div>

      {/* Portal-based country dropdown */}
      {isCountryOpen && createPortal(
        <div
          className="fixed z-[9999] w-64 sm:w-72 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto"
          style={{
            top: countryDropdownPosition.top,
            left: countryDropdownPosition.left,
            minWidth: countryDropdownPosition.width,
          }}
        >
          <div className="py-1">
            {countries.map((country) => (
              <button
                key={country.code}
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleCountrySelect(country);
                }}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 flex items-center space-x-3 ${
                  selectedCountry.code === country.code ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                }`}
              >
                <span className="text-lg">{country.flag}</span>
                <span className="flex-1">{country.name}</span>
                <span className="text-gray-500 dark:text-gray-400">{country.dialCode}</span>
              </button>
            ))}
          </div>
        </div>,
        document.body
      )}

      {/* Portal-based verification dropdown */}
      {isVerificationOpen && createPortal(
        <div
          className="fixed z-[9999] w-32 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg"
          style={{
            top: verificationDropdownPosition.top,
            left: verificationDropdownPosition.left,
          }}
        >
          <div className="py-1">
            {verificationOptions.map((option) => (
              <button
                key={option.label}
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleVerificationSelect(option.value);
                }}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 flex items-center space-x-2 ${
                  verified === option.value ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                }`}
              >
                <option.icon className={`w-4 h-4 ${option.color}`} />
                <span className="text-xs">{option.label}</span>
              </button>
            ))}
          </div>
        </div>,
        document.body
      )}

      {/* Format hint */}
      {selectedCountry.format && (
        <p className="mt-1 text-xs text-gray-500">
          Format: {selectedCountry.format}
        </p>
      )}
    </div>
  );
};

export default PhoneInputWithVerification;
