import React, { useState, useEffect } from 'react';
import { Shield, Eye, EyeOff, AlertTriangle, CheckCircle, XCircle, TestTube, Lock, Key, CreditCard } from 'lucide-react';
import { apiService } from '../services/api';
import { useToast } from '../contexts/ToastContext';
import ToggleSwitch from './ToggleSwitch';

interface SecuritySettings {
  toyyibpay_secret_key: string;
  toyyibpay_category_code: string;
  toyyibpay_sandbox: boolean;
  app_debug: boolean;
  log_level: string;
  two_factor_auth_enabled: boolean;
  security_status: SecurityStatus;
}

interface SecurityStatus {
  toyyibpay_configured: boolean;
  smtp_configured: boolean;
  using_default_credentials: string[];
  debug_mode_enabled: boolean;
  ssl_enabled: boolean;
  strong_passwords: boolean;
  overall_security_score: number;
}

const SecuritySettings: React.FC = () => {
  const { showSuccess, showError } = useToast();
  const [settings, setSettings] = useState<SecuritySettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [testResults, setTestResults] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/security-settings');
      setSettings(response.settings);
    } catch (error: any) {
      console.error('Failed to load security settings:', error);
      showError('Load Failed', error.message || 'Failed to load security settings. Please refresh the page.');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      setErrors({});

      const response = await apiService.post('/security-settings', settings);

      // Update security status
      setSettings(prev => prev ? {
        ...prev,
        security_status: response.security_status
      } : null);

      // Show success message
      showSuccess('Success', 'Security settings updated successfully!');

    } catch (error: any) {
      console.error('Failed to save security settings:', error);
      if (error.errors) {
        setErrors(error.errors);
        showError('Validation Error', 'Please check the form for errors and try again.');
      } else {
        showError('Save Failed', error.message || 'Failed to save security settings. Please try again.');
      }
    } finally {
      setSaving(false);
    }
  };

  // Real-time 2FA toggle handler
  const handle2FAToggle = async (enabled: boolean) => {
    if (!settings) return;

    try {
      // Update local state immediately for responsive UI
      setSettings(prev => prev ? { ...prev, two_factor_auth_enabled: enabled } : null);

      // Send update to backend immediately
      const updatedSettings = { ...settings, two_factor_auth_enabled: enabled };
      await apiService.post('/security-settings', updatedSettings);

      // Show appropriate message
      if (enabled) {
        showSuccess('2FA Enabled', 'Two-Factor Authentication has been enabled. Users will be required to use 2FA for login.');
      } else {
        showError('2FA Disabled', 'Two-Factor Authentication has been disabled. Account security is reduced. Consider re-enabling 2FA for better security.');
      }

    } catch (error: any) {
      console.error('Failed to update 2FA setting:', error);

      // Revert local state on error
      setSettings(prev => prev ? { ...prev, two_factor_auth_enabled: !enabled } : null);

      showError('2FA Update Failed', error.message || 'Failed to update Two-Factor Authentication setting. Please try again.');
    }
  };

  // Real-time Debug Mode toggle handler
  const handleDebugModeToggle = async (enabled: boolean) => {
    if (!settings) return;

    try {
      // Update local state immediately for responsive UI
      setSettings(prev => prev ? { ...prev, app_debug: enabled } : null);

      // Send update to backend immediately
      const updatedSettings = { ...settings, app_debug: enabled };
      await apiService.post('/security-settings', updatedSettings);

      // Show appropriate message
      if (enabled) {
        showError('Debug Mode Enabled', 'Debug mode has been enabled. This should only be used in development environments as it may expose sensitive information.');
      } else {
        showSuccess('Debug Mode Disabled', 'Debug mode has been disabled. Error logging is now in production mode.');
      }

    } catch (error: any) {
      console.error('Failed to update debug mode setting:', error);

      // Revert local state on error
      setSettings(prev => prev ? { ...prev, app_debug: !enabled } : null);

      showError('Debug Mode Update Failed', error.message || 'Failed to update debug mode setting. Please try again.');
    }
  };

  const testToyyibPayConnection = async () => {
    try {
      setTestResults(prev => ({ ...prev, toyyibpay: 'testing' }));

      const response = await apiService.post('/security-settings/test-toyyibpay');

      setTestResults(prev => ({ ...prev, toyyibpay: response.status }));
      showSuccess('ToyyibPay Test', response.message || 'Connection test successful!');

    } catch (error: any) {
      setTestResults(prev => ({ ...prev, toyyibpay: 'failed' }));
      showError('ToyyibPay Test Failed', error.message || 'Connection test failed. Please check your settings.');
    }
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="p-6 text-center">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">Failed to load security settings</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <div>
        <div className="flex items-center gap-3 mb-4">
          <Shield className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Security Overview</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center gap-2">
              {getStatusIcon(settings.security_status.toyyibpay_configured)}
              <span className="text-sm font-medium">Payment Gateway</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center gap-2">
              {getStatusIcon(settings.security_status.smtp_configured)}
              <span className="text-sm font-medium">Email Configuration</span>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Configure in Email tab
            </span>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center gap-2">
              {getStatusIcon(!settings.security_status.debug_mode_enabled)}
              <span className="text-sm font-medium">Production Mode</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Shield className={`w-8 h-8 ${getSecurityScoreColor(settings.security_status.overall_security_score)}`} />
          <div>
            <p className="font-semibold text-gray-900 dark:text-white">
              Security Score: <span className={getSecurityScoreColor(settings.security_status.overall_security_score)}>
                {settings.security_status.overall_security_score}/100
              </span>
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {settings.security_status.overall_security_score >= 80 ? 'Excellent security configuration' :
               settings.security_status.overall_security_score >= 60 ? 'Good security, some improvements needed' :
               'Security improvements required'}
            </p>
          </div>
        </div>

        {/* Security Warnings */}
        {(settings.security_status.using_default_credentials.length > 0 || settings.security_status.debug_mode_enabled) && (
          <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
              <div>
                <h4 className="font-semibold text-red-800 dark:text-red-200">Security Warnings</h4>
                <ul className="mt-2 text-sm text-red-700 dark:text-red-300 space-y-1">
                  {settings.security_status.using_default_credentials.length > 0 && (
                    <li>• Default credentials detected: {settings.security_status.using_default_credentials.join(', ')}</li>
                  )}
                  {settings.security_status.debug_mode_enabled && (
                    <li>• Debug mode is enabled in production environment</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Payment Gateway Settings */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <div className="flex items-center gap-3 mb-4">
          <CreditCard className="w-6 h-6 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Payment Gateway (ToyyibPay)</h3>
          <div className="ml-auto flex items-center gap-2">
            {testResults.toyyibpay === 'testing' && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            )}
            <button
              onClick={testToyyibPayConnection}
              disabled={testResults.toyyibpay === 'testing'}
              className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 w-full sm:w-auto"
            >
              <TestTube className="w-4 h-4" />
              Test Connection
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Secret Key *
            </label>
            <div className="relative">
              <input
                type={showPasswords.toyyibpay_secret_key ? 'text' : 'password'}
                value={settings.toyyibpay_secret_key}
                onChange={(e) => setSettings(prev => prev ? { ...prev, toyyibpay_secret_key: e.target.value } : null)}
                className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter your ToyyibPay secret key"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('toyyibpay_secret_key')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                {showPasswords.toyyibpay_secret_key ? (
                  <EyeOff className="w-4 h-4 text-gray-400" />
                ) : (
                  <Eye className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
            {errors.toyyibpay_secret_key && (
              <p className="mt-1 text-sm text-red-600">{errors.toyyibpay_secret_key}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category Code *
            </label>
            <input
              type="text"
              value={settings.toyyibpay_category_code}
              onChange={(e) => setSettings(prev => prev ? { ...prev, toyyibpay_category_code: e.target.value } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter your category code"
            />
            {errors.toyyibpay_category_code && (
              <p className="mt-1 text-sm text-red-600">{errors.toyyibpay_category_code}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={settings.toyyibpay_sandbox}
                onChange={(e) => setSettings(prev => prev ? { ...prev, toyyibpay_sandbox: e.target.checked } : null)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Sandbox Mode (for testing)
              </span>
            </label>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Security Note:</strong> Generate new API keys from your ToyyibPay dashboard. Never use example or default keys in production.
          </p>
        </div>
      </div>

      {/* System Security Settings */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <div className="flex items-center gap-3 mb-4">
          <Lock className="w-6 h-6 text-red-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">System Security</h3>
        </div>

        <div className="space-y-6">
          {/* Two-Factor Authentication Toggle */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <ToggleSwitch
              label="Two-Factor Authentication"
              description="Require 2FA for enhanced account security"
              enabled={settings.two_factor_auth_enabled}
              onChange={handle2FAToggle}
              size="md"
              showLabels={true}
              enabledLabel="Enabled"
              disabledLabel="Disabled"
            />
            {settings.two_factor_auth_enabled && (
              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  ✓ Two-Factor Authentication is active. All users will be required to set up 2FA on their next login.
                </p>
              </div>
            )}
          </div>

          {/* Debug Mode Toggle */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <ToggleSwitch
              label="Debug Mode"
              description="Enable detailed error logging for development"
              enabled={settings.app_debug}
              onChange={handleDebugModeToggle}
              size="md"
              showLabels={true}
              enabledLabel="Enabled"
              disabledLabel="Disabled"
            />
            {settings.app_debug && (
              <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-sm text-red-800 dark:text-red-200">
                  ⚠️ Debug mode is enabled. This should be disabled in production environments to prevent sensitive information exposure.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="w-full">
        <button
          onClick={handleSave}
          disabled={saving}
          className="w-full sm:w-auto flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
        >
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Key className="w-4 h-4" />
          )}
          {saving ? 'Saving...' : 'Save Security Settings'}
        </button>
      </div>
    </div>
  );
};

export default SecuritySettings;
