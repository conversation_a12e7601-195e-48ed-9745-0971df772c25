import React, { useEffect, useState } from 'react';
import { Radar<PERSON>hart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, Tooltip } from 'recharts';
import { Client } from '../contexts/ClientContext';

interface ClientPersonaChartProps {
  client: Client;
}

interface PersonaDimension {
  subject: string;
  value: number;
  fullMark: number;
}

const ClientPersonaChart: React.FC<ClientPersonaChartProps> = ({ client }) => {
  const [animatedData, setAnimatedData] = useState<PersonaDimension[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);

  // Calculate persona dimensions based on client data
  const calculatePersonaDimensions = (client: Client): PersonaDimension[] => {
    const engagementScore = {
      'Hot': 90,
      'Warm': 70,
      'Cold': 40,
      'Frozen': 20
    }[client.engagementLevel] || 40;

    const purchaseFrequency = Math.min((client.transactionCount / 10) * 100, 100);

    const loyaltyScore = {
      'Advocator': 95,
      'Loyal': 80,
      'Retainer': 60,
      'First Timer': 30
    }[client.category] || 30;

    const communicationPreference = client.email && client.phone ? 85 : 
                                   client.email || client.phone ? 60 : 30;

    const valueTier = {
      'Platinum': 95,
      'Gold+': 80,
      'Gold': 65,
      'Silver': 40
    }[client.ltvSegment] || 40;

    // Calculate retention probability based on multiple factors
    const daysSinceActivity = Math.floor((Date.now() - client.lastActivity.getTime()) / (1000 * 60 * 60 * 24));
    const activityRecency = daysSinceActivity <= 7 ? 90 : 
                           daysSinceActivity <= 30 ? 70 : 
                           daysSinceActivity <= 90 ? 50 : 30;
    
    const retentionProbability = Math.round((loyaltyScore + activityRecency + (client.transactionCount > 5 ? 20 : 0)) / 3);

    return [
      { subject: 'Engagement', value: engagementScore, fullMark: 100 },
      { subject: 'Purchase Frequency', value: purchaseFrequency, fullMark: 100 },
      { subject: 'Loyalty Score', value: loyaltyScore, fullMark: 100 },
      { subject: 'Communication', value: communicationPreference, fullMark: 100 },
      { subject: 'Value Tier', value: valueTier, fullMark: 100 },
      { subject: 'Retention Probability', value: retentionProbability, fullMark: 100 }
    ];
  };

  const dimensions = calculatePersonaDimensions(client);

  // Animate the chart data on mount
  useEffect(() => {
    setIsAnimating(true);
    // Start with zero values
    setAnimatedData(dimensions.map(d => ({ ...d, value: 0 })));

    // Animate to actual values after a short delay
    const timer = setTimeout(() => {
      setAnimatedData(dimensions);
      setIsAnimating(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [client.id]); // Re-animate when client changes

  // Color function for score interpretation
  const getColorByValue = (value: number): string => {
    if (value >= 80) return '#10b981'; // Green - Excellent
    if (value >= 60) return '#3b82f6'; // Blue - Good
    if (value >= 40) return '#f59e0b'; // Orange - Fair
    return '#ef4444'; // Red - Poor
  };

  // Custom tick component for multi-line labels
  const CustomAngleTick = ({ payload, x, y, textAnchor, ...props }: any) => {
    const splitLabels: { [key: string]: string[] } = {
      'Purchase Frequency': ['Purchase', 'Frequency'],
      'Retention Probability': ['Retention', 'Probability'],
      'Loyalty Score': ['Loyalty', 'Score']
    };

    const lines = splitLabels[payload.value] || [payload.value];

    return (
      <g transform={`translate(${x},${y})`}>
        {lines.map((line, index) => (
          <text
            key={index}
            x={0}
            y={index * 14 - (lines.length - 1) * 7}
            textAnchor={textAnchor}
            fill="#6b7280"
            fontSize="12"
            className="dark:fill-gray-300"
          >
            {line}
          </text>
        ))}
      </g>
    );
  };

  // Custom tooltip for the radar chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const value = payload[0].value;
      const getScoreInterpretation = (value: number) => {
        if (value >= 80) return 'Excellent';
        if (value >= 60) return 'Good';
        if (value >= 40) return 'Fair';
        return 'Poor';
      };

      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <p className="font-medium text-gray-900 dark:text-white">{label}</p>
          <p className="text-sm" style={{ color: getColorByValue(value) }}>
            {Math.round(value)} - {getScoreInterpretation(value)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6">Client Persona Analysis</h3>

      <div className="flex items-center justify-center flex-1 overflow-hidden">
        <div className="w-full h-full min-h-[300px] sm:min-h-[350px] lg:min-h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={animatedData} margin={{ top: 40, right: 40, bottom: 40, left: 40 }}>
              <PolarGrid
                stroke="#e5e7eb"
                className="dark:stroke-gray-600"
              />
              <PolarAngleAxis
                dataKey="subject"
                tick={<CustomAngleTick />}
                className="dark:fill-gray-300"
              />
              <PolarRadiusAxis
                angle={90}
                domain={[0, 100]}
                tick={false}
                className="dark:fill-gray-400"
              />
              <Radar
                name="Score"
                dataKey="value"
                stroke="#3b82f6"
                fill="rgba(59, 130, 246, 0.2)"
                strokeWidth={2}
                dot={{
                  r: 4,
                  fill: '#3b82f6',
                  strokeWidth: 2,
                  stroke: '#ffffff'
                }}
                animationDuration={1500}
                animationEasing="ease-out"
              />
              <Tooltip content={<CustomTooltip />} />
            </RadarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ClientPersonaChart;
