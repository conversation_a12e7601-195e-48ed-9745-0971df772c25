import React, { useState, useMemo } from 'react';
import {
  Plus, Search, Filter, Eye, Mail, MessageCircle,
  Edit, Trash2, Download, Upload, UserCheck,
  ChevronDown, X, Users, TrendingUp, Percent, Target,
  List, LayoutGrid
} from 'lucide-react';

import { useLeads, Lead } from '../contexts/LeadContext';
import { useClients } from '../contexts/ClientContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { useToast } from '../contexts/ToastContext';
import CustomDropdown from '../components/CustomDropdown';
import ActionButtons from '../components/ActionButtons';
import LeadSidebar from '../components/LeadSidebar';
import { apiService } from '../services/api';
import { logger } from '../utils/logger';

const Leads: React.FC = () => {
  const { leads, deleteLead } = useLeads();
  const { addClient } = useClients();
  const { confirm } = useConfirmation();
  const { showSuccess, showError } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    ltvSegment: '',
    engagementLevel: '',
    priority: '',
    utmSource: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'kanban'>('table');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedLead, setSelectedLead] = useState<Lead | undefined>(undefined);

  // Filter and search leads
  const filteredLeads = useMemo(() => {
    return leads.filter(lead => {
      const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lead.phone.includes(searchTerm);
      
      const matchesFilters = Object.entries(selectedFilters).every(([key, value]) => {
        if (!value) return true;
        return lead[key as keyof typeof selectedFilters] === value;
      });

      return matchesSearch && matchesFilters;
    });
  }, [leads, searchTerm, selectedFilters]);

  const handleFilterChange = (filterKey: string, value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
  };

  const clearFilters = () => {
    setSelectedFilters({
      ltvSegment: '',
      engagementLevel: '',
      priority: '',
      utmSource: '',
    });
  };

  const toggleLeadSelection = (leadId: string) => {
    setSelectedLeads(prev => 
      prev.includes(leadId) 
        ? prev.filter(id => id !== leadId)
        : [...prev, leadId]
    );
  };

  const selectAllLeads = () => {
    setSelectedLeads(filteredLeads.map(lead => lead.id));
  };

  const clearSelection = () => {
    setSelectedLeads([]);
  };

  const handleCreate = () => {
    setSelectedLead(undefined);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleView = (lead: Lead) => {
    setSelectedLead(lead);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleEdit = (lead: Lead) => {
    setSelectedLead(lead);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDelete = async (leadId: string) => {
    const lead = leads.find(l => l.id === leadId);
    const leadName = lead?.name || 'this lead';

    const confirmed = await confirm({
      title: 'Delete Lead',
      message: `Are you sure you want to delete ${leadName}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        deleteLead(leadId);
        showSuccess('Lead Deleted', `${leadName} has been successfully deleted.`);
      } catch (error) {
        showError('Delete Failed', 'Failed to delete the lead. Please try again.');
      }
    }
  };

  const handleConvertToClient = async (lead: Lead) => {
    const confirmed = await confirm({
      title: 'Convert Lead to Client (Direct)',
      message: `Convert ${lead.name} directly to Client? This bypasses the normal deal workflow and should only be used when the deal process was completed elsewhere.`,
      confirmText: 'Convert to Client',
      cancelText: 'Cancel',
      type: 'warning'
    });

    if (confirmed) {
      try {
        // Check if this is a mock lead (starts with 'lead-') or a real database lead (numeric)
        const isMockLead = lead.id.startsWith('lead-');

        if (isMockLead) {
          // Handle mock lead conversion locally
          const newClient = {
            id: `client-converted-${Date.now()}`,
            name: lead.name || 'Unknown',
            email: lead.email || '',
            phone: lead.phone || '',
            address: lead.address || '',
            status: 'active' as const,
            utmSource: lead.utmSource || 'direct',
            tags: lead.tags || [],
            category: 'First Timer' as const,
            ltvSegment: 'Silver' as const,
            engagementLevel: lead.engagementLevel || 'Cold' as const,
            priority: lead.priority || 'Medium' as const,
            notes: `${lead.notes || ''}\n\nConversion Notes: Direct conversion from leads page`,
            suggestedAction: 'Follow up on direct conversion',
            createdAt: new Date(),
            lastActivity: new Date(),
            totalSpent: 0,
            transactionCount: 0,
            customFields: {
              convertedFromLead: true,
              conversionType: 'direct',
              originalLeadId: lead.id,
              conversionReason: 'Manual conversion - deal process completed elsewhere',
              originalCategory: 'Direct Conversion' // Store the intended category in custom fields
            }
          };

          // Remove lead from local state and add client
          deleteLead(lead.id);
          await addClient(newClient);
        } else {
          // Handle real database lead conversion via API
          const result = await apiService.convertLeadToClient(lead.id, {
            reason: 'Manual conversion - deal process completed elsewhere',
            notes: 'Direct conversion from leads page'
          });

          // Remove lead from local state and add client
          deleteLead(lead.id);
          await addClient(result.client);
        }

        // Log the conversion
        logger.log(
          'Converted',
          'Lead to Client',
          `Converted lead ${lead.name} (${lead.email || 'No email'}) to client - Direct conversion bypassing deal workflow`,
          'Admin',
          lead.utmSource || 'direct'
        );

        showSuccess('Lead Converted', `${lead.name} has been successfully converted to a client! This conversion has been logged for audit purposes.`);
      } catch (error) {
        console.error('Conversion error:', error);
        showError('Conversion Failed', error instanceof Error ? error.message : 'Failed to convert the lead. Please try again.');
      }
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedLead(undefined);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hot': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Warm': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Cold': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      case 'Frozen': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'High': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Low': return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'Platinum': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300';
      case 'Gold+': return 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300';
      case 'Gold': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Silver': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'Lead': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  // Statistics calculations
  const totalLeads = leads.length;
  const hotLeads = leads.filter(l => l.engagementLevel === 'Hot').length;
  const convertedLeads = 0; // This would need to be tracked in a real app
  const conversionRate = totalLeads > 0 ? Math.round((convertedLeads / totalLeads) * 100) : 0;

  // Kanban view component
  const KanbanView = () => {
    const columns = [
      { id: 'Hot', title: 'Hot Leads', color: 'bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800' },
      { id: 'Warm', title: 'Warm Leads', color: 'bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800' },
      { id: 'Cold', title: 'Cold Leads', color: 'bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' },
      { id: 'Frozen', title: 'Frozen Leads', color: 'bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700' },
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {columns.map((column) => {
          const columnLeads = filteredLeads.filter(lead => lead.engagementLevel === column.id);

          return (
            <div key={column.id} className={`rounded-lg border-2 ${column.color} p-4`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">{column.title}</h3>
                <span className="bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-2 py-1 rounded-full text-sm font-medium">
                  {columnLeads.length}
                </span>
              </div>

              <div className="space-y-3">
                {columnLeads.map((lead) => (
                  <div key={lead.id} className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-8 bg-orange-600 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {lead.name.split(' ')[0][0]}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{lead.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{lead.utmSource}</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <span className={`px-2 py-1 rounded-md ${getStatusColor('Lead')}`}>
                        Lead
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">RM 0</span>
                    </div>

                    <div className="mt-2 flex items-center justify-between">
                      <span className={`px-2 py-1 text-xs rounded-md ${getStatusColor(lead.ltvSegment)}`}>
                        {lead.ltvSegment}
                      </span>
                      <button
                        onClick={() => handleView(lead)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-xs transition-colors"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                ))}

                {columnLeads.length === 0 && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
                    No leads in this category
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Leads</h1>
          <p className="text-gray-600 dark:text-gray-300">Manage your potential clients and lead generation</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button className="w-full sm:w-auto px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Import</span>
          </button>
          <button className="w-full sm:w-auto px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>

          <button
            onClick={handleCreate}
            className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Lead</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Leads</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalLeads}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Hot Leads</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{hotLeads}</p>
            </div>
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
              <TrendingUp className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Converted Leads</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{convertedLeads}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <Target className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Conversion Rate</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{conversionRate}%</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <Percent className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          {/* Clear Filters Button */}
          {(searchTerm || Object.values(selectedFilters).some(filter => filter)) && (
            <button
              onClick={clearFilters}
              className="flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              <X className="w-4 h-4" />
              <span>Clear filters</span>
            </button>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              placeholder="Search leads by name, email, or phone..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'table'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('kanban')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'kanban'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <LayoutGrid className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200 dark:border-gray-600 relative z-50">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">LTV Segment</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Segments' },
                  { value: 'Silver', label: 'Silver' },
                  { value: 'Gold', label: 'Gold' },
                  { value: 'Gold+', label: 'Gold+' },
                  { value: 'Platinum', label: 'Platinum' },
                ]}
                value={selectedFilters.ltvSegment}
                onChange={(value) => handleFilterChange('ltvSegment', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Engagement</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Levels' },
                  { value: 'Hot', label: 'Hot' },
                  { value: 'Warm', label: 'Warm' },
                  { value: 'Cold', label: 'Cold' },
                  { value: 'Frozen', label: 'Frozen' },
                ]}
                value={selectedFilters.engagementLevel}
                onChange={(value) => handleFilterChange('engagementLevel', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Priorities' },
                  { value: 'High', label: 'High' },
                  { value: 'Medium', label: 'Medium' },
                  { value: 'Low', label: 'Low' },
                ]}
                value={selectedFilters.priority}
                onChange={(value) => handleFilterChange('priority', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">UTM Source</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Sources' },
                  { value: 'facebook', label: 'Facebook' },
                  { value: 'instagram', label: 'Instagram' },
                  { value: 'tiktok', label: 'TikTok' },
                  { value: 'google', label: 'Google' },
                  { value: 'youtube', label: 'YouTube' },
                  { value: 'whatsapp', label: 'WhatsApp' },
                  { value: 'referral', label: 'Referral' },
                ]}
                value={selectedFilters.utmSource}
                onChange={(value) => handleFilterChange('utmSource', value)}
              />
            </div>

            <div className="md:col-span-2 lg:col-span-4 flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {filteredLeads.length} of {leads.length} leads
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedLeads.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                {selectedLeads.length} leads selected
              </span>
              <button
                onClick={clearSelection}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1 bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-700 transition-colors text-sm">
                Send Email
              </button>
              <button className="px-3 py-1 bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-700 transition-colors text-sm">
                Send WhatsApp
              </button>
              <button className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm">
                Export
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Leads Content */}
      {viewMode === 'kanban' ? (
        <KanbanView />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Mobile View */}
          <div className="block md:hidden">
            {filteredLeads.map((lead) => (
              <div key={lead.id} className="border-b border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <input
                      type="checkbox"
                      className="mt-1 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                      checked={selectedLeads.includes(lead.id)}
                      onChange={() => toggleLeadSelection(lead.id)}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-green-600 dark:text-green-400">
                            {lead.name?.charAt(0)?.toUpperCase() || 'L'}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {lead.name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {lead.email || lead.phone || 'No contact info'}
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Source:</span>
                          <span className="ml-1 text-gray-900 dark:text-white">{lead.source || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Status:</span>
                          <span className="ml-1 text-gray-900 dark:text-white">{lead.status || 'New'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="ml-2">
                    <ActionButtons
                      actions={[
                        {
                          label: 'Edit',
                          icon: Edit,
                          onClick: () => handleEdit(lead),
                          color: 'green'
                        },
                        {
                          label: 'Convert to Client',
                          icon: UserCheck,
                          onClick: () => handleConvertToClient(lead),
                          color: 'purple'
                        },
                        {
                          label: 'Send Email',
                          icon: Mail,
                          onClick: () =>,
                          color: 'blue'
                        },
                        {
                          label: 'Send WhatsApp',
                          icon: MessageCircle,
                          onClick: () =>,
                          color: 'green'
                        },
                        {
                          label: 'Delete',
                          icon: Trash2,
                          onClick: () => handleDelete(lead.id),
                          color: 'red'
                        }
                      ]}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                      checked={filteredLeads.length > 0 && selectedLeads.length === filteredLeads.length}
                      onChange={filteredLeads.length > 0 && selectedLeads.length === filteredLeads.length ? clearSelection : selectAllLeads}
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Lead
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    LTV Segment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Engagement
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredLeads.map((lead) => (
                  <tr key={lead.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                        checked={selectedLeads.includes(lead.id)}
                        onChange={() => toggleLeadSelection(lead.id)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => handleView(lead)}
                          className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                          title="View Lead"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <div className="w-10 h-10 bg-orange-600 rounded-md flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {lead.name.split(' ')[0][0]}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{lead.name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{lead.utmSource}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{lead.email}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">{lead.phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor('Lead')}`}>
                        Lead
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(lead.ltvSegment)}`}>
                        {lead.ltvSegment}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(lead.engagementLevel)}`}>
                        {lead.engagementLevel}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(lead.priority)}`}>
                        {lead.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex justify-center">
                        <ActionButtons
                          actions={[
                            {
                              label: 'Edit',
                              icon: Edit,
                              onClick: () => handleEdit(lead),
                              color: 'green'
                            },
                            {
                              label: 'Convert to Client',
                              icon: UserCheck,
                              onClick: () => handleConvertToClient(lead),
                              color: 'purple'
                            },
                            {
                              label: 'Send Email',
                              icon: Mail,
                              onClick: () =>,
                              color: 'blue'
                            },
                            {
                              label: 'Send WhatsApp',
                              icon: MessageCircle,
                              onClick: () =>,
                              color: 'green'
                            },
                            {
                              label: 'Delete',
                              icon: Trash2,
                              onClick: () => handleDelete(lead.id),
                              color: 'red'
                            }
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Lead Sidebar */}
      {isModalOpen && (
        <LeadSidebar
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          lead={selectedLead}
          mode={modalMode}
        />
      )}
    </div>
  );
};

export default Leads;