import React, { useState } from 'react';
import {
  Search, Send, Mail, MessageCircle,
  Users, FileText, Eye, Edit, Trash2, X
} from 'lucide-react';
import CustomDropdown from '../components/CustomDropdown';
import ActionButtons from '../components/ActionButtons';
import CampaignSidebar from '../components/CampaignSidebar';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { useToast } from '../contexts/ToastContext';
import { logger } from '../utils/logger';

const Communications: React.FC = () => {
  const { confirm } = useConfirmation();
  const { showSuccess, showError } = useToast();
  const [activeTab, setActiveTab] = useState('campaigns');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'email' | 'whatsapp'>('email');

  // Mock data for email campaigns
  const mockCampaigns = [
    {
      id: 1,
      name: '<PERSON>dan Book Collection',
      type: 'Email',
      status: 'Sent',
      recipients: 245,
      opened: 189,
      clicked: 67,
      sentDate: new Date('2024-01-15'),
      subject: 'Special Ramadan Collection - 30% Off All Islamic Books',
    },
    {
      id: 2,
      name: 'Save Gaza Donation Drive',
      type: 'Email',
      status: 'Scheduled',
      recipients: 512,
      opened: 0,
      clicked: 0,
      sentDate: new Date('2024-01-20'),
      subject: 'Help Our Brothers and Sisters in Gaza',
    },
    {
      id: 3,
      name: 'Youth Seminar Invitation',
      type: 'WhatsApp',
      status: 'Draft',
      recipients: 89,
      opened: 0,
      clicked: 0,
      sentDate: new Date('2024-01-25'),
      subject: 'Islamic Youth Seminar - Register Now!',
    },
    {
      id: 4,
      name: 'New Product Launch',
      type: 'Email',
      status: 'Sent',
      recipients: 378,
      opened: 298,
      clicked: 124,
      sentDate: new Date('2024-01-10'),
      subject: 'New Islamic Merchandise Collection Now Available',
    },
  ];

  // Mock data for templates
  const mockTemplates = [
    {
      id: 1,
      name: 'Welcome New Client',
      type: 'Email',
      subject: 'Welcome to Islamic Books Community!',
      lastUsed: new Date('2024-01-18'),
      usage: 45,
    },
    {
      id: 2,
      name: 'Donation Thank You',
      type: 'Email',
      subject: 'Thank You for Your Generous Donation',
      lastUsed: new Date('2024-01-17'),
      usage: 123,
    },
    {
      id: 3,
      name: 'Follow Up Purchase',
      type: 'WhatsApp',
      subject: 'How did you like your recent purchase?',
      lastUsed: new Date('2024-01-16'),
      usage: 89,
    },
    {
      id: 4,
      name: 'Seminar Reminder',
      type: 'Email',
      subject: 'Reminder: Islamic Seminar Tomorrow',
      lastUsed: new Date('2024-01-15'),
      usage: 67,
    },
  ];

  // Initialize state with mock data
  const [campaigns, setCampaigns] = useState(mockCampaigns);
  const [templates, setTemplates] = useState(mockTemplates);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Sent':
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-md';
      case 'Scheduled':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md';
      case 'Draft':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-md';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md';
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'Email' ? (
      <Mail className="w-4 h-4 text-blue-600 dark:text-blue-400" />
    ) : (
      <MessageCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
    );
  };

  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         campaign.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !selectedType || campaign.type === selectedType;
    return matchesSearch && matchesType;
  });

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !selectedType || template.type === selectedType;
    return matchesSearch && matchesType;
  });

  // Action handlers for campaigns
  const handleViewCampaign = (campaign: any) => {
    // Create a detailed view of the campaign
    const details = `Campaign: ${campaign.name}
Type: ${campaign.type}
Status: ${campaign.status}
Recipients: ${campaign.recipients}
Opened: ${campaign.opened}
Clicked: ${campaign.clicked}
Sent Date: ${campaign.sentDate.toLocaleDateString()}
Subject: ${campaign.subject}`;

    showSuccess('Campaign Details', details);

    // Log the view action
    logger.log(
      'Viewed',
      'Campaign',
      `Viewed campaign: ${campaign.name} (${campaign.type})`,
      'Admin',
      'engagements'
    );
  };

  const handleEditCampaign = (campaign: any) => {
    // For now, show an edit form simulation
    const newName = prompt('Edit campaign name:', campaign.name);
    if (newName && newName !== campaign.name) {
      const updatedCampaigns = campaigns.map(c =>
        c.id === campaign.id ? { ...c, name: newName } : c
      );
      setCampaigns(updatedCampaigns);

      // Log the edit action
      logger.log(
        'Updated',
        'Campaign',
        `Updated campaign: ${campaign.name} → ${newName}`,
        'Admin',
        'engagements'
      );

      showSuccess(`Campaign renamed to: ${newName}`);
    }
  };

  const handleDeleteCampaign = async (campaign: any) => {
    const confirmed = await confirm({
      title: 'Delete Campaign',
      message: `Are you sure you want to delete the campaign "${campaign.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      const updatedCampaigns = campaigns.filter(c => c.id !== campaign.id);
      setCampaigns(updatedCampaigns);

      // Log the deletion
      logger.log(
        'Deleted',
        'Campaign',
        `Deleted campaign: ${campaign.name} (${campaign.type})`,
        'Admin',
        'engagements'
      );

      showSuccess(`Campaign "${campaign.name}" deleted successfully`);
    }
  };

  // Action handlers for templates
  const handleViewTemplate = (template: any) => {
    const details = `Template: ${template.name}
Type: ${template.type}
Subject: ${template.subject}
Content: ${template.content}
Created: ${template.createdDate.toLocaleDateString()}`;

    showSuccess('Template Details', details);

    // Log the view action
    logger.log(
      'Viewed',
      'Template',
      `Viewed template: ${template.name} (${template.type})`,
      'Admin',
      'engagements'
    );
  };

  const handleEditTemplate = (template: any) => {
    const newName = prompt('Edit template name:', template.name);
    if (newName && newName !== template.name) {
      const updatedTemplates = templates.map(t =>
        t.id === template.id ? { ...t, name: newName } : t
      );
      setTemplates(updatedTemplates);

      // Log the edit action
      logger.log(
        'Updated',
        'Template',
        `Updated template: ${template.name} → ${newName}`,
        'Admin',
        'engagements'
      );

      showSuccess(`Template renamed to: ${newName}`);
    }
  };

  const handleDeleteTemplate = async (template: any) => {
    const confirmed = await confirm({
      title: 'Delete Template',
      message: `Are you sure you want to delete the template "${template.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      const updatedTemplates = templates.filter(t => t.id !== template.id);
      setTemplates(updatedTemplates);

      // Log the deletion
      logger.log(
        'Deleted',
        'Template',
        `Deleted template: ${template.name} (${template.type})`,
        'Admin',
        'engagements'
      );

      showSuccess(`Template "${template.name}" deleted successfully`);
    }
  };

  const handleNewEmailCampaign = () => {
    setModalType('email');
    setIsModalOpen(true);
  };

  const handleNewWhatsApp = () => {
    setModalType('whatsapp');
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const tabs = [
    { id: 'campaigns', name: 'Campaigns', icon: Send },
    { id: 'templates', name: 'Templates', icon: FileText },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Engagements</h1>
          <p className="text-gray-600 dark:text-gray-300">Manage email campaigns and WhatsApp communications</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button
            onClick={handleNewEmailCampaign}
            className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Mail className="w-4 h-4" />
            <span>New Email Campaign</span>
          </button>
          <button
            onClick={handleNewWhatsApp}
            className="w-full sm:w-auto px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
          >
            <MessageCircle className="w-4 h-4" />
            <span>New WhatsApp</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Campaigns</p>
              <p className="text-2xl font-bold text-gray-900">{campaigns.length}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Send className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Recipients</p>
              <p className="text-2xl font-bold text-gray-900">
                {campaigns.reduce((sum, campaign) => sum + campaign.recipients, 0)}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Open Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round(
                  campaigns.filter(c => c.status === 'Sent').reduce((sum, campaign) =>
                    sum + (campaign.opened / campaign.recipients * 100), 0
                  ) / campaigns.filter(c => c.status === 'Sent').length
                )}%
              </p>
            </div>
            <div className="p-3 bg-teal-100 rounded-lg">
              <Eye className="w-6 h-6 text-teal-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Templates</p>
              <p className="text-2xl font-bold text-gray-900">{mockTemplates.length}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <FileText className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {/* Clear Filters Button */}
            {(searchTerm || selectedType) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedType('');
                }}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
              >
                <X className="w-4 h-4" />
                <span>Clear filters</span>
              </button>
            )}

            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search campaigns and templates..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <CustomDropdown
              options={[
                { value: '', label: 'All Types' },
                { value: 'Email', label: 'Email' },
                { value: 'WhatsApp', label: 'WhatsApp' },
              ]}
              value={selectedType}
              onChange={setSelectedType}
              className="w-32"
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'campaigns' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCampaigns.map((campaign) => (
                <div key={campaign.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(campaign.type)}
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(campaign.status)}`}>
                        {campaign.status}
                      </span>
                    </div>
                    <ActionButtons
                      actions={[
                        {
                          label: 'View',
                          icon: Eye,
                          onClick: () => handleViewCampaign(campaign),
                          color: 'blue'
                        },
                        {
                          label: 'Edit',
                          icon: Edit,
                          onClick: () => handleEditCampaign(campaign),
                          color: 'green'
                        },
                        {
                          label: 'Delete',
                          icon: Trash2,
                          onClick: () => handleDeleteCampaign(campaign),
                          color: 'red'
                        }
                      ]}
                    />
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{campaign.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{campaign.subject}</p>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div>
                      <span className="text-gray-500">Recipients:</span>
                      <span className="ml-2 font-medium">{campaign.recipients}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Open Rate:</span>
                      <span className="ml-2 font-medium">
                        {campaign.recipients > 0 ? Math.round(campaign.opened / campaign.recipients * 100) : 0}%
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Opened:</span>
                      <span className="ml-2 font-medium">{campaign.opened}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Clicked:</span>
                      <span className="ml-2 font-medium">{campaign.clicked}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <span className="text-xs text-gray-500">
                      Sent {campaign.sentDate.toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'templates' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => (
                <div key={template.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(template.type)}
                      <span className="px-2 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-700">
                        Template
                      </span>
                    </div>
                    <ActionDropdown
                      actions={[
                        {
                          label: 'View',
                          icon: Eye,
                          onClick: () => handleViewTemplate(template),
                          color: 'text-blue-600'
                        },
                        {
                          label: 'Edit',
                          icon: Edit,
                          onClick: () => handleEditTemplate(template),
                          color: 'text-green-600'
                        },
                        {
                          label: 'Delete',
                          icon: Trash2,
                          onClick: () => handleDeleteTemplate(template),
                          color: 'text-red-600'
                        }
                      ]}
                    />
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{template.subject}</p>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 line-clamp-3">{template.content}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div>
                      <span className="text-gray-500">Usage:</span>
                      <span className="ml-2 font-medium">{template.usage} times</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Type:</span>
                      <span className="ml-2 font-medium">{template.type}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <span className="text-xs text-gray-500">
                      Last used {template.lastUsed.toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Campaign Sidebar */}
      {isModalOpen && (
        <CampaignSidebar
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          type={modalType}
        />
      )}
    </div>
  );
};

export default Communications;