import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { CreditCard, Building, Smartphone, Calendar, DollarSign, FileText, CheckCircle } from 'lucide-react';
import { apiService } from '../services/api';
import CustomDropdown from '../components/CustomDropdown';

interface InvoiceItem {
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface Invoice {
  id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  description: string;
  due_date: string;
  client: {
    name: string;
    email: string;
  };
  items: InvoiceItem[];
  created_at: string;
}

const PublicInvoice: React.FC = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const navigate = useNavigate();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'credit_card' | 'bank_transfer' | 'ewallet'>('credit_card');
  const [paymentDetails, setPaymentDetails] = useState<any>({});
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    if (invoiceId) {
      fetchInvoice();
    }
  }, [invoiceId]);

  const fetchInvoice = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/public/invoice/${invoiceId}`);
      setInvoice(response.data.invoice);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to load invoice');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentDetailsChange = (field: string, value: string) => {
    setPaymentDetails(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePayment = async () => {
    if (!invoice) return;

    try {
      setProcessing(true);
      const response = await apiService.post(`/public/invoice/${invoiceId}/pay`, {
        payment_method: paymentMethod,
        payment_details: paymentDetails
      });

      // Redirect to success page
      navigate(`/public/payment/${response.data.transaction_id}/success`);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Payment failed');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading invoice...</p>
        </div>
      </div>
    );
  }

  if (error || !invoice) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Invoice Not Found</h1>
          <p className="text-gray-600">{error || 'The requested invoice could not be found.'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Invoice Payment</h1>
          <p className="text-gray-600">Secure payment for Invoice #{invoice.invoice_number}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Invoice Details */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Invoice Details</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Invoice Number:</span>
                <span className="font-medium">{invoice.invoice_number}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Client:</span>
                <span className="font-medium">{invoice.client.name}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Due Date:</span>
                <span className="font-medium">{new Date(invoice.due_date).toLocaleDateString()}</span>
              </div>
              
              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-2">Items:</h3>
                {invoice.items.map((item, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{item.name} (x{item.quantity})</span>
                    <span>{invoice.currency} {item.total.toFixed(2)}</span>
                  </div>
                ))}
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total Amount:</span>
                  <span className="text-blue-600">{invoice.currency} {invoice.amount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Form */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Method</h2>
            
            {/* Payment Method Selection */}
            <div className="space-y-3 mb-6">
              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="payment_method"
                  value="credit_card"
                  checked={paymentMethod === 'credit_card'}
                  onChange={(e) => setPaymentMethod(e.target.value as any)}
                  className="mr-3"
                />
                <CreditCard className="w-5 h-5 mr-2 text-gray-600" />
                <span>Credit/Debit Card</span>
              </label>
              
              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="payment_method"
                  value="bank_transfer"
                  checked={paymentMethod === 'bank_transfer'}
                  onChange={(e) => setPaymentMethod(e.target.value as any)}
                  className="mr-3"
                />
                <Building className="w-5 h-5 mr-2 text-gray-600" />
                <span>Bank Transfer</span>
              </label>
              
              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="payment_method"
                  value="ewallet"
                  checked={paymentMethod === 'ewallet'}
                  onChange={(e) => setPaymentMethod(e.target.value as any)}
                  className="mr-3"
                />
                <Smartphone className="w-5 h-5 mr-2 text-gray-600" />
                <span>E-Wallet</span>
              </label>
            </div>

            {/* Payment Details Form */}
            <div className="space-y-4">
              {paymentMethod === 'credit_card' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Cardholder Name</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="John Doe"
                      onChange={(e) => handlePaymentDetailsChange('cardholder_name', e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="1234 5678 9012 3456"
                      onChange={(e) => handlePaymentDetailsChange('card_number', e.target.value)}
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Month</label>
                      <CustomDropdown
                        options={[
                          { value: '', label: 'MM' },
                          ...Array.from({ length: 12 }, (_, i) => ({
                            value: (i + 1).toString(),
                            label: String(i + 1).padStart(2, '0')
                          }))
                        ]}
                        value=""
                        onChange={(value) => handlePaymentDetailsChange('expiry_month', value)}
                        placeholder="MM"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
                      <CustomDropdown
                        options={[
                          { value: '', label: 'YYYY' },
                          ...Array.from({ length: 10 }, (_, i) => ({
                            value: (2024 + i).toString(),
                            label: (2024 + i).toString()
                          }))
                        ]}
                        value=""
                        onChange={(value) => handlePaymentDetailsChange('expiry_year', value)}
                        placeholder="YYYY"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="123"
                        maxLength={3}
                        onChange={(e) => handlePaymentDetailsChange('cvv', e.target.value)}
                      />
                    </div>
                  </div>
                </>
              )}

              {paymentMethod === 'bank_transfer' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                    <CustomDropdown
                      options={[
                        { value: '', label: 'Select Bank' },
                        { value: 'Maybank', label: 'Maybank' },
                        { value: 'CIMB Bank', label: 'CIMB Bank' },
                        { value: 'Public Bank', label: 'Public Bank' },
                        { value: 'RHB Bank', label: 'RHB Bank' },
                        { value: 'Hong Leong Bank', label: 'Hong Leong Bank' }
                      ]}
                      value=""
                      onChange={(value) => handlePaymentDetailsChange('bank_name', value)}
                      placeholder="Select Bank"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="**********"
                      onChange={(e) => handlePaymentDetailsChange('account_number', e.target.value)}
                    />
                  </div>
                </>
              )}

              {paymentMethod === 'ewallet' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">E-Wallet Type</label>
                    <CustomDropdown
                      options={[
                        { value: '', label: 'Select E-Wallet' },
                        { value: 'grabpay', label: 'GrabPay' },
                        { value: 'tng', label: 'Touch \'n Go eWallet' },
                        { value: 'boost', label: 'Boost' }
                      ]}
                      value=""
                      onChange={(value) => handlePaymentDetailsChange('ewallet_type', value)}
                      placeholder="Select E-Wallet"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                    <input
                      type="tel"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="+60123456789"
                      onChange={(e) => handlePaymentDetailsChange('phone_number', e.target.value)}
                    />
                  </div>
                </>
              )}
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <button
              onClick={handlePayment}
              disabled={processing}
              className="w-full mt-6 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {processing ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <DollarSign className="w-5 h-5" />
                  <span>Pay {invoice.currency} {invoice.amount.toFixed(2)}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicInvoice;
