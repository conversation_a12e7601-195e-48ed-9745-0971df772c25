import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import {
  ArrowLeft, Mail, MessageCircle, Edit, Star,
  Calendar, DollarSign, ShoppingBag, FileText,
  User, MapPin, Phone, Tag, Eye, ShieldCheck,
  CreditCard, Cake, Users, Heart, TrendingUp, MessageSquare
} from 'lucide-react';
import { useClients } from '../contexts/ClientContext';
import { useViewState } from '../contexts/ViewStateContext';
import { apiService } from '../services/api';
import { formatDistanceToNow } from 'date-fns';
import { calculateClientScore, getClientOverallScore } from '../utils/clientScoring';
import Button from '../components/Button';
import ClientPersonaChart from '../components/ClientPersonaChart';
import ProfileCompletionProgress from '../components/ProfileCompletionProgress';
import ClientActivityCalendar from '../components/ClientActivityCalendar';
import { CircularProgress, AnimatedScoreDisplay } from '../components/AnimatedComponents';
import ClientSidebar from '../components/ClientSidebar';

const ClientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { getClientById } = useClients();
  const { getRememberedViewMode } = useViewState();
  const [activeTab, setActiveTab] = useState('quotations');
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loadingTransactions, setLoadingTransactions] = useState(false);
  const [quotations, setQuotations] = useState<any[]>([]);
  const [loadingQuotations, setLoadingQuotations] = useState(false);
  const [isEditSidebarOpen, setIsEditSidebarOpen] = useState(false);
  const [scoreOverrides, setScoreOverrides] = useState<{ nameScore?: number; emailScore?: number; phoneScore?: number } | undefined>(undefined);

  const client = getClientById(id || '');

  // Calculate client scores using unified system
  const clientScores = calculateClientScore(client);

  // Use centralized function for consistent overall score across all views
  // Include score overrides for real-time updates when editing
  const overallScore = getClientOverallScore(client, scoreOverrides);

  // Handle score changes from the sidebar
  const handleScoreChange = (scores: { nameScore: number; emailScore: number; phoneScore: number }) => {
    setScoreOverrides(scores);
  };

  // Clear score overrides when sidebar closes
  const handleSidebarClose = () => {
    setIsEditSidebarOpen(false);
    setScoreOverrides(undefined);
  };

  // Get the back URL based on remembered view mode
  const getBackUrl = () => {
    const viewMode = getRememberedViewMode();
    return `/clients?view=${viewMode}`;
  };

  // Define fetch functions with useCallback to prevent unnecessary re-renders
  const fetchTransactions = useCallback(async () => {
    if (!client) return;

    try {
      setLoadingTransactions(true);
      const response = await apiService.getTransactions({
        client_id: parseInt(client.id),
        per_page: 20
      });
      setTransactions(response.data);
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
      setTransactions([]);
    } finally {
      setLoadingTransactions(false);
    }
  }, [client]);

  const fetchQuotations = useCallback(async () => {
    if (!client) return;

    try {
      setLoadingQuotations(true);
      const response = await apiService.getQuotations({
        client_id: parseInt(client.id),
        per_page: 20
      });
      setQuotations(response.data);
    } catch (error) {
      console.error('Failed to fetch quotations:', error);
      setQuotations([]);
    } finally {
      setLoadingQuotations(false);
    }
  }, [client]);

  // Fetch data when client changes or relevant tab is active
  useEffect(() => {
    if (client && activeTab === 'transactions') {
      fetchTransactions();
    }
    if (client && activeTab === 'quotations') {
      fetchQuotations();
    }
  }, [client, activeTab, fetchTransactions, fetchQuotations]);

  if (!client) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Client not found</h2>
        <Link to={getBackUrl()} className="text-teal-600 hover:text-teal-700 mt-4 inline-block">
          Back to Clients
        </Link>
      </div>
    );
  }

  const mockLogs = [
    { id: 1, action: 'Client created', date: client.createdAt, actor: 'SYSTEM' },
    { id: 2, action: 'Email sent: Welcome message', date: new Date(client.createdAt.getTime() + 24 * 60 * 60 * 1000), actor: 'Admin' },
    { id: 3, action: 'Purchase completed', date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), actor: 'SYSTEM' },
    { id: 4, action: 'Engagement level updated to Hot', date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), actor: 'Admin' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hot': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Warm': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Cold': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      case 'Frozen': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'High': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Low': return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'Platinum': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300';
      case 'Gold+': return 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300';
      case 'Gold': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Silver': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getPhoneCarrierColor = (carrier: string) => {
    if (!carrier) return 'bg-gray-100 text-gray-700';

    switch (carrier.toLowerCase()) {
      case 'maxis':
        return 'bg-purple-100 text-purple-700';
      case 'celcom':
        return 'bg-blue-100 text-blue-700';
      case 'digi':
        return 'bg-yellow-100 text-yellow-700';
      case 'u mobile':
        return 'bg-pink-100 text-pink-700';
      case 'tunetalk':
        return 'bg-indigo-100 text-indigo-700';
      case 'yes':
        return 'bg-green-100 text-green-700';
      case 'altel':
        return 'bg-teal-100 text-teal-700';
      case 'xox':
        return 'bg-orange-100 text-orange-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const tabs = [
    { id: 'quotations', name: 'Quotations', icon: FileText },
    { id: 'transactions', name: 'Transactions', icon: DollarSign },
    { id: 'logs', name: 'Activity Logs', icon: Calendar },
    { id: 'notes', name: 'Notes', icon: Edit },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        {/* Top section with back button and client name */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to={getBackUrl()} className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{client.name}</h1>
              {client.uuid && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Client ID: {client.uuid}
                </p>
              )}
            </div>
          </div>
          {/* Action buttons - hidden on mobile, shown on desktop */}
          <div className="hidden md:flex flex-row items-center gap-3">
            <Button variant="primary" icon={Mail}>
              Send Email
            </Button>
            <Button variant="secondary" icon={MessageCircle}>
              WhatsApp
            </Button>
            <Button variant="outline" icon={Edit} onClick={() => setIsEditSidebarOpen(true)}>
              Edit
            </Button>
          </div>
        </div>

        {/* Mobile action buttons - positioned below client name */}
        <div className="md:hidden flex flex-col space-y-2">
          <Button variant="primary" icon={Mail} className="w-full">
            Send Email
          </Button>
          <Button variant="secondary" icon={MessageCircle} className="w-full">
            WhatsApp
          </Button>
          <Button variant="outline" icon={Edit} onClick={() => setIsEditSidebarOpen(true)} className="w-full">
            Edit
          </Button>
        </div>
      </div>

      {/* Row 1: Overall Score Card (1/5) + Personal Information Card (2/5) + Profile Completion Progress Card (2/5) */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        {/* Overall Score Card - 1/5 width */}
        <div className="lg:col-span-1 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-lg text-white p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Overall Score</h3>
            <Star className="w-5 h-5 text-white" />
          </div>
          <div className="text-center mb-4">
            <div className="text-4xl font-bold text-white">
              {overallScore}%
            </div>
            <div className="text-sm text-blue-100 mt-1">Client Health</div>
          </div>
          <div className="mt-4 text-sm text-blue-100 space-y-2">
            <div className="flex justify-between items-center">
              <span>Name Score:</span>
              <span className="font-semibold">{client.nameScore || 0}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Email Score:</span>
              <span className="font-semibold">{client.emailScore || 0}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Phone Score:</span>
              <span className="font-semibold">{client.phoneScore || 0}%</span>
            </div>
          </div>

          {/* Performance Metrics Progress Bars - Horizontal Grid Layout */}
          <div className="mt-6 pt-4 border-t border-blue-400">
            <div className="grid grid-cols-2 gap-3">
              {/* LTV Progress */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-blue-100">LTV</span>
                  <span className="text-xs font-semibold text-white">{Math.round((client.totalSpent / 10000) * 100)}%</span>
                </div>
                <div className="w-full bg-blue-400 rounded-full h-1.5">
                  <div
                    className="bg-white h-1.5 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${Math.min((client.totalSpent / 10000) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              {/* Engagement Level */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-blue-100">Engagement</span>
                  <span className="text-xs font-semibold text-white">{clientScores.engagement}%</span>
                </div>
                <div className="w-full bg-blue-400 rounded-full h-1.5">
                  <div
                    className="bg-orange-300 h-1.5 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${clientScores.engagement}%` }}
                  ></div>
                </div>
              </div>

              {/* Data Quality Score */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-blue-100">Quality</span>
                  <span className="text-xs font-semibold text-white">{Math.round((client.email && client.phone && client.address ? 85 : client.email && client.phone ? 70 : client.email ? 50 : 30))}%</span>
                </div>
                <div className="w-full bg-blue-400 rounded-full h-1.5">
                  <div
                    className="bg-green-300 h-1.5 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${client.email && client.phone && client.address ? 85 : client.email && client.phone ? 70 : client.email ? 50 : 30}%` }}
                  ></div>
                </div>
              </div>

              {/* Retention Rate */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-blue-100">Retention</span>
                  <span className="text-xs font-semibold text-white">{clientScores.activity}%</span>
                </div>
                <div className="w-full bg-blue-400 rounded-full h-1.5">
                  <div
                    className="bg-purple-300 h-1.5 rounded-full transition-all duration-1000 ease-out"
                    style={{ width: `${clientScores.activity}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Last Activity */}
          <div className="mt-4 pt-4 border-t border-blue-400">
            <p className="text-sm text-blue-100">
              Last activity: {formatDistanceToNow(client.lastActivity, { addSuffix: true })}
            </p>
          </div>
        </div>

        {/* Personal Information Card - 2/5 width */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="space-y-4">
            {/* Personal Information Title */}
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <User className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" />
              Personal Information
            </h3>
            {/* Two-column layout for better space utilization - 60/40 split to accommodate business badges */}
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">

              {/* Column 1: Contact Information and Personal Details - 3/5 width for more badge space */}
              <div className="lg:col-span-3 space-y-6">
                {/* Contact Information with Verification Badges */}
                <div className="space-y-3">
                  {/* Primary Email */}
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-sm text-gray-900 dark:text-white">{client.email}</span>
                    <span className="px-1.5 py-0.5 text-xs font-medium rounded bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                      Primary
                    </span>
                    {client.emailVerified ? (
                      <div className="relative group">
                        <div className="bg-blue-100 rounded-full p-0.5 hover:bg-blue-200 transition-colors duration-200">
                          <ShieldCheck className="w-3 h-3 text-blue-600" />
                        </div>
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                          Email Verified
                        </div>
                      </div>
                    ) : (
                      <span className="px-2 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-600">
                        Unverified
                      </span>
                    )}
                  </div>

                  {/* Additional Emails */}
                  {client.emails && client.emails.filter(email => !email.isPrimary).map((email, index) => (
                    <div key={email.id} className="flex items-center space-x-2 ml-6">
                      <Mail className="w-3 h-3 text-gray-400" />
                      <span className="text-sm text-gray-700">{email.emailAddress}</span>
                      {email.emailVerified ? (
                        <div className="relative group">
                          <div className="bg-blue-100 rounded-full p-0.5 hover:bg-blue-200 transition-colors duration-200">
                            <ShieldCheck className="w-3 h-3 text-blue-600" />
                          </div>
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                            Email Verified
                          </div>
                        </div>
                      ) : (
                        <span className="px-1.5 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-600">
                          Unverified
                        </span>
                      )}
                      {email.emailDeliverability && (
                        <span className="px-1.5 py-0.5 text-xs font-medium rounded bg-green-100 text-green-700">
                          {email.emailDeliverability}
                        </span>
                      )}
                    </div>
                  ))}

                  {/* Primary Phone */}
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-900">{client.phone}</span>
                    <span className="px-1.5 py-0.5 text-xs font-medium rounded bg-blue-100 text-blue-700">
                      Primary
                    </span>
                    {client.phoneVerified ? (
                      <div className="relative group">
                        <div className="bg-blue-100 rounded-full p-0.5 hover:bg-blue-200 transition-colors duration-200">
                          <ShieldCheck className="w-3 h-3 text-blue-600" />
                        </div>
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                          Phone Verified
                        </div>
                      </div>
                    ) : (
                      <span className="px-2 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-600">
                        Unverified
                      </span>
                    )}
                    {client.phoneCarrier && (
                      <span className={`px-2 py-0.5 text-xs font-medium rounded ${getPhoneCarrierColor(client.phoneCarrier)}`}>
                        {client.phoneCarrier}
                      </span>
                    )}
                  </div>

                  {/* Additional Phone Numbers */}
                  {client.phoneNumbers && client.phoneNumbers.filter(phone => !phone.isPrimary).map((phone, index) => (
                    <div key={phone.id} className="flex items-center space-x-2 ml-6">
                      <Phone className="w-3 h-3 text-gray-400" />
                      <span className="text-sm text-gray-700">{phone.phoneNumber}</span>
                      {phone.phoneVerified ? (
                        <div className="relative group">
                          <div className="bg-blue-100 rounded-full p-0.5 hover:bg-blue-200 transition-colors duration-200">
                            <ShieldCheck className="w-3 h-3 text-blue-600" />
                          </div>
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                            Phone Verified
                          </div>
                        </div>
                      ) : (
                        <span className="px-1.5 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-600">
                          Unverified
                        </span>
                      )}
                      {phone.phoneCarrier && (
                        <span className={`px-1.5 py-0.5 text-xs font-medium rounded ${getPhoneCarrierColor(phone.phoneCarrier)}`}>
                          {phone.phoneCarrier}
                        </span>
                      )}
                    </div>
                  ))}
                </div>

                {/* Status Badges */}
                <div className="flex flex-wrap gap-2">
                  <span className={`flex-auto px-2 py-0.5 text-xs font-medium rounded text-center whitespace-nowrap ${getStatusColor(client.category)}`}>
                    {client.category}
                  </span>
                  <span className={`flex-auto px-2 py-0.5 text-xs font-medium rounded text-center whitespace-nowrap ${getStatusColor(client.ltvSegment)}`}>
                    {client.ltvSegment}
                  </span>
                  <span className={`flex-auto px-2 py-0.5 text-xs font-medium rounded text-center whitespace-nowrap ${getStatusColor(client.engagementLevel)}`}>
                    {client.engagementLevel}
                  </span>
                  <span className={`flex-auto px-2 py-0.5 text-xs font-medium rounded text-center whitespace-nowrap ${getStatusColor(client.priority)}`}>
                    {client.priority}
                  </span>
                </div>

                {/* Personal Information Details */}
                {(client.icNumber || client.birthday || client.gender || client.religion || client.income) && (
                  <div className="pt-6">
                    <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
                      <User className="w-4 h-4 mr-2" />
                      Personal Details
                    </h4>
                    <div className="space-y-3">
                      {client.icNumber && (
                        <div className="flex items-center space-x-2">
                          <CreditCard className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-600">IC Number:</span>
                          <span className="text-sm text-gray-900 font-mono">{client.icNumber}</span>
                        </div>
                      )}

                      {client.birthday && (
                        <div className="flex items-center space-x-2">
                          <Cake className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-600">Birthday:</span>
                          <span className="text-sm text-gray-900">{client.birthday.toLocaleDateString()}</span>
                        </div>
                      )}

                      {client.gender && (
                        <div className="flex items-center space-x-2">
                          <Users className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-600">Gender:</span>
                          <span className="text-sm text-gray-900">{client.gender}</span>
                        </div>
                      )}

                      {client.religion && (
                        <div className="flex items-center space-x-2">
                          <Heart className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-600">Religion:</span>
                          <span className="text-sm text-gray-900">{client.religion}</span>
                        </div>
                      )}

                      {((client.income && client.income > 0) || client.incomeCategory) && (
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-600">Income:</span>
                          <span className="text-sm text-gray-900">
                            {client.income && client.income > 0 && `RM ${client.income.toLocaleString()}`}
                            {client.income && client.income > 0 && client.incomeCategory && ' • '}
                            {client.incomeCategory && (
                              <span className={`px-3 py-1.5 text-sm font-medium rounded ${
                                client.incomeCategory === 'High' ? 'bg-green-100 text-green-700' :
                                client.incomeCategory === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-gray-100 text-gray-700'
                              }`}>
                                {client.incomeCategory}
                              </span>
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Column 2: Address Details and Behavioral Data - 2/5 width */}
              <div className="lg:col-span-2 space-y-6">
                {/* Enhanced Address Information */}
                {(client.addressLine1 || client.addressLine2 || client.city || client.postcode || client.state) && (
                  <div>
                    <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
                      <MapPin className="w-4 h-4 mr-2" />
                      Address Details
                    </h4>
                    <div className="space-y-2 text-sm text-gray-900">
                      {client.addressLine1 && <div>{client.addressLine1}</div>}
                      {client.addressLine2 && <div>{client.addressLine2}</div>}
                      <div className="flex flex-wrap gap-2">
                        {client.city && <span>{client.city}</span>}
                        {client.postcode && <span>{client.postcode}</span>}
                        {client.state && <span>{client.state}</span>}
                      </div>
                    </div>
                  </div>
                )}

                {/* Behavioral Information */}
                {(client.behaviour || client.interest) && (
                  <div className="pt-6 border-t border-gray-200">
                    <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Behavioral Data
                    </h4>
                    <div className="space-y-3">
                      {client.behaviour && (
                        <div>
                          <span className="text-sm text-gray-600 font-medium">Behavior:</span>
                          <p className="text-sm text-gray-900 mt-1">{client.behaviour}</p>
                        </div>
                      )}

                      {client.interest && (
                        <div>
                          <span className="text-sm text-gray-600 font-medium">Interests:</span>
                          <p className="text-sm text-gray-900 mt-1">{client.interest}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

            </div>
          </div>
        </div>

        {/* Profile Completion Progress Card - 2/5 width */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="space-y-4">
            <ProfileCompletionProgress client={client} showBreakdown={true} />
          </div>
        </div>

      </div>

      {/* Row 2: Financial Metrics + Engagement + Retention - 5 Equal-Width Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {/* Total Spent */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Spent</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">RM {client.totalSpent.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        {/* Transactions */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Transactions</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{client.transactionCount}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <ShoppingBag className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        {/* Average Order Value */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Avg Order Value</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">RM {Math.floor(client.totalSpent / client.transactionCount).toLocaleString()}</p>
            </div>
            <div className="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
              <Tag className="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
          </div>
        </div>

        {/* Engagement Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Engagement</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{clientScores.engagement}%</p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <User className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>

        {/* Retention Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Retention</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{clientScores.activity}%</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Row 3: Client Persona Analysis (2/5) and Activity Calendar (3/5) */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="lg:col-span-2">
          <ClientPersonaChart client={client} />
        </div>
        <div className="lg:col-span-3">
          <ClientActivityCalendar client={client} />
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex overflow-x-auto scrollbar-hide px-4 sm:px-6" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-3 sm:px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap flex-shrink-0 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  } ${tab.id !== tabs[tabs.length - 1].id ? 'mr-6 sm:mr-8' : ''}`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">

          {activeTab === 'transactions' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Transaction History</h3>
              {loadingTransactions ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 dark:text-gray-400 mt-2">Loading transactions...</p>
                </div>
              ) : transactions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">No transactions found for this client.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          transaction.type === 'payment' ? 'bg-green-100 dark:bg-green-900/30' :
                          transaction.type === 'invoice' ? 'bg-blue-100 dark:bg-blue-900/30' :
                          transaction.type === 'order' ? 'bg-purple-100 dark:bg-purple-900/30' : 'bg-gray-100 dark:bg-gray-600'
                        }`}>
                          <ShoppingBag className={`w-5 h-5 ${
                            transaction.type === 'payment' ? 'text-green-600 dark:text-green-400' :
                            transaction.type === 'invoice' ? 'text-blue-600 dark:text-blue-400' :
                            transaction.type === 'order' ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-400'
                          }`} />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{transaction.transaction_number}</p>
                          <p className="text-sm text-gray-500">
                            {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} • {new Date(transaction.created_at).toLocaleDateString()}
                          </p>
                          {transaction.notes && (
                            <p className="text-xs text-gray-400 mt-1">{transaction.notes}</p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">RM {parseFloat(transaction.total_amount).toFixed(2)}</p>
                        <p className={`text-sm px-2 py-1 rounded-full text-xs ${
                          transaction.status === 'completed' ? 'bg-green-100 text-green-700' :
                          transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                          transaction.status === 'draft' ? 'bg-gray-100 text-gray-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </p>
                        {transaction.payment_method && (
                          <p className="text-xs text-gray-500 mt-1">{transaction.payment_method}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'quotations' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quotations</h3>
              {loadingQuotations ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-500 mt-2">Loading quotations...</p>
                </div>
              ) : quotations.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No quotations found for this client.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {quotations.map((quotation) => (
                    <div key={quotation.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          quotation.status === 'accepted' ? 'bg-green-100' :
                          quotation.status === 'sent' ? 'bg-blue-100' :
                          quotation.status === 'draft' ? 'bg-gray-100' :
                          quotation.status === 'rejected' ? 'bg-red-100' : 'bg-yellow-100'
                        }`}>
                          <FileText className={`w-5 h-5 ${
                            quotation.status === 'accepted' ? 'text-green-600' :
                            quotation.status === 'sent' ? 'text-blue-600' :
                            quotation.status === 'draft' ? 'text-gray-600' :
                            quotation.status === 'rejected' ? 'text-red-600' : 'text-yellow-600'
                          }`} />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{quotation.quotation_number}</p>
                          <p className="text-sm text-gray-900 font-medium">{quotation.title}</p>
                          <p className="text-sm text-gray-500">
                            Created: {new Date(quotation.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{quotation.currency} {parseFloat(quotation.total_amount).toFixed(2)}</p>
                        <p className={`text-sm px-2 py-1 rounded-full text-xs ${
                          quotation.status === 'accepted' ? 'bg-green-100 text-green-700' :
                          quotation.status === 'sent' ? 'bg-blue-100 text-blue-700' :
                          quotation.status === 'draft' ? 'bg-gray-100 text-gray-700' :
                          quotation.status === 'rejected' ? 'bg-red-100 text-red-700' :
                          'bg-yellow-100 text-yellow-700'
                        }`}>
                          {quotation.status.charAt(0).toUpperCase() + quotation.status.slice(1)}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">Priority: {quotation.priority}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'logs' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Logs</h3>
              <div className="space-y-4">
                {mockLogs.map((log) => (
                  <div key={log.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{log.action}</p>
                      <p className="text-xs text-gray-500">
                        {log.date.toLocaleDateString()} • {log.actor}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'notes' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
              <textarea
                className="w-full h-32 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
                placeholder="Add notes about this client..."
                defaultValue={client.notes}
              />
              <div className="mt-4">
                <Button variant="primary">
                  Save Notes
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Client Edit Sidebar */}
      <ClientSidebar
        isOpen={isEditSidebarOpen}
        onClose={handleSidebarClose}
        client={client}
        mode="edit"
        onScoreChange={handleScoreChange}
      />
    </div>
  );
};

export default ClientDetail;