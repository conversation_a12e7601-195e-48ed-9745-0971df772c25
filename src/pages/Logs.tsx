import React, { useState, useEffect } from 'react';
import {
  Search, Download, Calendar, Filter, ChevronDown,
  User, Activity, Mail, MessageCircle,
  ShoppingBag, CreditCard, Edit, Eye, Trash2, X, Upload, FileText,
  CheckCircle, AlertCircle
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import CustomDropdown from '../components/CustomDropdown';
import ActionDropdown from '../components/ActionDropdown';
import ActionButtons from '../components/ActionButtons';
import Badge, { StatusBadge } from '../components/Badge';
import DateRangePicker from '../components/DateRangePicker';
import { apiService } from '../services/api';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import logger from '../utils/logger';

const Logs: React.FC = () => {
  const { showSuccess, showError } = useToast();
  const { confirm } = useConfirmation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedActor, setSelectedActor] = useState('');
  const [selectedAction, setSelectedAction] = useState('');
  const [selectedTarget, setSelectedTarget] = useState('');
  const [selectedUtmSource, setSelectedUtmSource] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: null as Date | null,
    endDate: null as Date | null,
    startTime: '00:00',
    endTime: '23:59'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedLogs, setSelectedLogs] = useState<string[]>([]);
  const [logs, setLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState<any>(null);
  const [showLogModal, setShowLogModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'activity' | 'imports'>('activity');
  const [importHistory, setImportHistory] = useState<any[]>([]);
  const [importLoading, setImportLoading] = useState(false);
  const [importStats, setImportStats] = useState<any>(null);
  const [selectedImport, setSelectedImport] = useState<any>(null);
  const [showImportModal, setShowImportModal] = useState(false);

  // Load logs from API and localStorage
  useEffect(() => {
    loadLogs();
    if (activeTab === 'imports') {
      loadImportHistory();
      loadImportStats();
    }
  }, [activeTab]);

  // Load import history
  const loadImportHistory = async () => {
    setImportLoading(true);
    try {
      const response = await apiService.get('/import-history');
      setImportHistory(response.data || []);
    } catch (error) {
      console.error('Failed to load import history:', error);
      setImportHistory([]);
    } finally {
      setImportLoading(false);
    }
  };

  // Load import statistics
  const loadImportStats = async () => {
    try {
      const response = await apiService.get('/import-history/statistics');
      setImportStats(response);
    } catch (error) {
      console.error('Failed to load import statistics:', error);
      setImportStats(null);
    }
  };

  // Handle ESC key and body overflow for modal
  useEffect(() => {
    if (showLogModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showLogModal]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showLogModal) {
        setShowLogModal(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [showLogModal]);

  const loadLogs = async () => {
    setLoading(true);
    try {
      // Try to load from API first
      const response = await apiService.getActivityLogs();
      const apiLogs = response.data || [];

      // Get localStorage logs from logger utility
      const localLogs = logger.getLogs();

      // Get test logs from dataManager localStorage (if any)
      const testLogsData = localStorage.getItem('kdt_logs_data');
      const testLogs = testLogsData ? JSON.parse(testLogsData) : [];

      // Combine and format logs
      const combinedLogs = [
        ...apiLogs.map((log: any) => ({
          id: log.id,
          timestamp: new Date(log.created_at),
          actor: log.performed_by || 'System',
          action: log.action,
          target: log.subject_name || log.related_name || 'Unknown',
          details: log.description,
          utmSource: log.utm_source || 'direct',
          ipAddress: log.ip_address || 'Unknown',
          userAgent: log.user_agent || 'Unknown',
        })),
        ...localLogs.map((log: any, index: number) => ({
          id: `local-${index}`,
          timestamp: new Date(log.timestamp),
          actor: log.actor,
          action: log.action,
          target: log.target,
          details: log.details,
          utmSource: log.utmSource || 'direct',
          ipAddress: 'Local',
          userAgent: 'Local Storage',
        })),
        ...testLogs.map((log: any, index: number) => ({
          id: `test-${index}`,
          timestamp: new Date(log.timestamp),
          actor: log.actor,
          action: log.action,
          target: log.entityId || 'Unknown',
          details: log.description,
          utmSource: 'test',
          ipAddress: log.metadata?.ipAddress || 'Test',
          userAgent: log.metadata?.userAgent || 'Test Data',
        }))
      ];

      // Sort by timestamp (newest first)
      combinedLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      setLogs(combinedLogs);
    } catch (error) {
      console.error('Failed to load logs:', error);
      // Fallback to localStorage only
      const localLogs = logger.getLogs();
      const testLogsData = localStorage.getItem('kdt_logs_data');
      const testLogs = testLogsData ? JSON.parse(testLogsData) : [];

      const formattedLogs = [
        ...localLogs.map((log: any, index: number) => ({
          id: `local-${index}`,
          timestamp: new Date(log.timestamp),
          actor: log.actor,
          action: log.action,
          target: log.target,
          details: log.details,
          utmSource: log.utmSource || 'direct',
          ipAddress: 'Local',
          userAgent: 'Local Storage',
        })),
        ...testLogs.map((log: any, index: number) => ({
          id: `test-${index}`,
          timestamp: new Date(log.timestamp),
          actor: log.actor,
          action: log.action,
          target: log.entityId || 'Unknown',
          details: log.description,
          utmSource: 'test',
          ipAddress: log.metadata?.ipAddress || 'Test',
          userAgent: log.metadata?.userAgent || 'Test Data',
        }))
      ];

      formattedLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      setLogs(formattedLogs);
    } finally {
      setLoading(false);
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch =
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.target.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesActor = !selectedActor || log.actor === selectedActor;
    const matchesAction = !selectedAction || log.action.includes(selectedAction);
    const matchesTarget = !selectedTarget || log.target.toLowerCase().includes(selectedTarget.toLowerCase());
    const matchesUtmSource = !selectedUtmSource || log.utmSource === selectedUtmSource;

    let matchesDate = true;
    if (dateRange.startDate || dateRange.endDate) {
      const logDate = new Date(log.timestamp);

      if (dateRange.startDate) {
        const startDateTime = new Date(dateRange.startDate);
        const [startHour, startMinute] = dateRange.startTime.split(':').map(Number);
        startDateTime.setHours(startHour, startMinute, 0, 0);
        matchesDate = matchesDate && logDate >= startDateTime;
      }

      if (dateRange.endDate) {
        const endDateTime = new Date(dateRange.endDate);
        const [endHour, endMinute] = dateRange.endTime.split(':').map(Number);
        endDateTime.setHours(endHour, endMinute, 59, 999);
        matchesDate = matchesDate && logDate <= endDateTime;
      }
    }

    return matchesSearch && matchesActor && matchesAction && matchesTarget && matchesUtmSource && matchesDate;
  });

  const getActionIcon = (action: string) => {
    if (action.includes('Email')) return <Mail className="w-4 h-4 text-blue-500" />;
    if (action.includes('WhatsApp')) return <MessageCircle className="w-4 h-4 text-green-500" />;
    if (action.includes('Purchase') || action.includes('Donation')) return <ShoppingBag className="w-4 h-4 text-purple-500" />;
    if (action.includes('Payment')) return <CreditCard className="w-4 h-4 text-green-500" />;
    if (action.includes('updated') || action.includes('added')) return <Edit className="w-4 h-4 text-yellow-500" />;
    if (action.includes('created')) return <User className="w-4 h-4 text-teal-500" />;
    return <Activity className="w-4 h-4 text-gray-500" />;
  };

  const getActorColor = (actor: string) => {
    switch (actor) {
      case 'Admin':
        return 'bg-red-100 text-red-700 rounded-md';
      case 'Staff':
        return 'bg-blue-100 text-blue-700 rounded-md';
      case 'SYSTEM':
        return 'bg-gray-100 text-gray-700 rounded-md';
      case 'Client':
        return 'bg-green-100 text-green-700 rounded-md';
      default:
        return 'bg-gray-100 text-gray-700 rounded-md';
    }
  };

  const totalLogs = logs.length;
  const systemLogs = logs.filter(log => log.actor === 'SYSTEM').length;
  const userLogs = logs.filter(log => log.actor !== 'SYSTEM').length;
  const todayLogs = logs.filter(log => {
    const today = new Date();
    const logDate = new Date(log.timestamp);
    return logDate.toDateString() === today.toDateString();
  }).length;

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedActor('');
    setSelectedAction('');
    setDateRange({
      startDate: null,
      endDate: null,
      startTime: '00:00',
      endTime: '23:59'
    });
  };

  const hasActiveFilters = searchTerm || selectedActor || selectedAction || dateRange.startDate || dateRange.endDate;

  const handleViewLog = (log: any) => {
    setSelectedLog(log);
    setShowLogModal(true);
  };

  const handleDeleteLog = async (logId: string | number) => {
    const confirmed = await confirm({
      title: 'Delete Log Entry',
      message: 'Are you sure you want to delete this log entry? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        // Remove from local state
        const updatedLogs = logs.filter(log => log.id !== logId);
        setLogs(updatedLogs);

        // If it's an API log, try to delete from server
        if (typeof logId === 'number' || (typeof logId === 'string' && !logId.startsWith('local-'))) {
          try {
            await apiService.deleteActivityLog(logId);
          } catch (apiError) {
            console.warn('Failed to delete log from server:', apiError);
            // Continue with local deletion even if server deletion fails
          }
        }

        // If it's a local log, remove from localStorage
        if (typeof logId === 'string' && logId.startsWith('local-')) {
          // For local logs, we need to update the logger
          // This is a simplified approach - in a real app you'd want better log management
          logger.clearLogs();
          updatedLogs
            .filter(log => typeof log.id === 'string' && log.id.startsWith('local-'))
            .forEach(log => {
              logger.log(log.action, log.target, log.details, log.actor, log.utmSource);
            });
        }

        // If it's a test log, remove from test data localStorage
        if (typeof logId === 'string' && logId.startsWith('test-')) {
          const testLogsData = localStorage.getItem('kdt_logs_data');
          if (testLogsData) {
            const testLogs = JSON.parse(testLogsData);
            const logIndex = parseInt(logId.replace('test-', ''));
            if (logIndex >= 0 && logIndex < testLogs.length) {
              testLogs.splice(logIndex, 1);
              localStorage.setItem('kdt_logs_data', JSON.stringify(testLogs));
            }
          }
        }

        showSuccess('Log entry deleted successfully!');
      } catch (error) {
        showError('Failed to delete log entry');
      }
    }
  };

  // Import History functions
  const handleViewImport = (importRecord: any) => {
    setSelectedImport(importRecord);
    setShowImportModal(true);
  };

  const handleDeleteImport = async (importId: string | number) => {
    const confirmed = await confirm({
      title: 'Delete Import Record',
      message: 'Are you sure you want to delete this import record? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        await apiService.delete(`/import-history/${importId}`);
        setImportHistory(importHistory.filter(record => record.id !== importId));
        showSuccess('Import record deleted successfully');
        // Reload stats after deletion
        loadImportStats();
      } catch (error: any) {
        console.error('Error deleting import record:', error);
        showError(error.message || 'Failed to delete import record');
      }
    }
  };

  const handleClearAllLogs = async () => {
    const confirmed = await confirm({
      title: 'Clear All Logs',
      message: 'Are you sure you want to clear all activity logs? This action cannot be undone.',
      confirmText: 'Clear All',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        // Clear database logs
        await apiService.clearAllActivityLogs();

        // Clear localStorage logs
        logger.clearLogs();

        // Clear test logs
        localStorage.removeItem('kdt_logs_data');

        // Clear local state
        setLogs([]);

        showSuccess('All logs cleared successfully');
      } catch (error: any) {
        showError(error.message || 'Failed to clear logs');
      }
    }
  };

  const toggleLogSelection = (logId: string) => {
    setSelectedLogs(prev =>
      prev.includes(logId)
        ? prev.filter(id => id !== logId)
        : [...prev, logId]
    );
  };

  const selectAllLogs = () => {
    setSelectedLogs(filteredLogs.map(log => log.id));
  };

  const clearSelection = () => {
    setSelectedLogs([]);
  };

  const handleBulkDelete = async () => {
    const confirmed = await confirm({
      title: 'Delete Logs',
      message: `Are you sure you want to delete ${selectedLogs.length} log(s)? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        for (const logId of selectedLogs) {
          await handleDeleteLog(logId);
        }
        showSuccess(`Successfully deleted ${selectedLogs.length} log(s)`);
        setSelectedLogs([]);
      } catch (error) {
        console.error('Error deleting logs:', error);
        showError('Failed to delete logs');
      }
    }
  };

  const handleBulkExport = () => {
    // TODO: Implement bulk export functionality
    showSuccess('Bulk export functionality coming soon');
  };

  const getUtmSourceColor = (utmSource: string) => {
    switch (utmSource.toLowerCase()) {
      case 'facebook':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'instagram':
        return 'bg-pink-100 text-pink-700 border-pink-200';
      case 'tiktok':
        return 'bg-black text-white border-gray-800';
      case 'google':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'youtube':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'whatsapp':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'referral':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900">Activity Logs</h1>
          <p className="text-gray-600">Track all system activities and user interactions</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button
            onClick={handleClearAllLogs}
            className="w-full sm:w-auto px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors flex items-center justify-center space-x-2"
          >
            <Trash2 className="w-4 h-4" />
            <span>Clear All Logs</span>
          </button>
          <button className="w-full sm:w-auto px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export CSV</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('activity')}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'activity'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Activity className="w-4 h-4" />
              <span>Activity Logs</span>
            </button>
            <button
              onClick={() => setActiveTab('imports')}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'imports'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Upload className="w-4 h-4" />
              <span>Import History</span>
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'activity' && (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Logs</p>
              <p className="text-2xl font-bold text-gray-900">{totalLogs}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Activity className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">System Logs</p>
              <p className="text-2xl font-bold text-gray-900">{systemLogs}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <Activity className="w-6 h-6 text-gray-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">User Actions</p>
              <p className="text-2xl font-bold text-gray-900">{userLogs}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <User className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Today's Activity</p>
              <p className="text-2xl font-bold text-gray-900">{todayLogs}</p>
            </div>
            <div className="p-3 bg-orange-50 rounded-lg">
              <Calendar className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        <div className="flex items-center space-x-4 mb-4">
          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
            >
              <X className="w-4 h-4" />
              <span>Clear filters</span>
            </button>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search logs by action, target, or details..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 pt-4 border-t border-gray-200">
            <CustomDropdown
              options={[
                { value: '', label: 'All Actors' },
                { value: 'Admin', label: 'Admin' },
                { value: 'Staff', label: 'Staff' },
                { value: 'SYSTEM', label: 'System' },
                { value: 'Client', label: 'Client' },
              ]}
              value={selectedActor}
              onChange={setSelectedActor}
              placeholder="Filter by actor"
            />

            <CustomDropdown
              options={[
                { value: '', label: 'All Actions' },
                { value: 'Email', label: 'Email Actions' },
                { value: 'WhatsApp', label: 'WhatsApp Actions' },
                { value: 'Purchase', label: 'Purchase Actions' },
                { value: 'updated', label: 'Update Actions' },
                { value: 'created', label: 'Create Actions' },
                { value: 'deleted', label: 'Delete Actions' },
                { value: 'viewed', label: 'View Actions' },
                { value: 'Payment', label: 'Payment Actions' },
                { value: 'Donation', label: 'Donation Actions' },
              ]}
              value={selectedAction}
              onChange={setSelectedAction}
              placeholder="Filter by action"
            />

            <CustomDropdown
              options={[
                { value: '', label: 'All Targets' },
                { value: 'Campaign', label: 'Campaign' },
                { value: 'Lead', label: 'Lead' },
                { value: 'Client', label: 'Client' },
                { value: 'Deal', label: 'Deal' },
                { value: 'Transaction', label: 'Transaction' },
                { value: 'Form', label: 'Form' },
                { value: 'Product', label: 'Product' },
              ]}
              value={selectedTarget}
              onChange={setSelectedTarget}
              placeholder="Filter by target"
            />

            <CustomDropdown
              options={[
                { value: '', label: 'All UTM Sources' },
                { value: 'direct', label: 'Direct' },
                { value: 'google', label: 'Google' },
                { value: 'facebook', label: 'Facebook' },
                { value: 'instagram', label: 'Instagram' },
                { value: 'email', label: 'Email' },
                { value: 'whatsapp', label: 'WhatsApp' },
                { value: 'organic', label: 'Organic' },
                { value: 'referral', label: 'Referral' },
                { value: 'test', label: 'Test' },
              ]}
              value={selectedUtmSource}
              onChange={setSelectedUtmSource}
              placeholder="Filter by UTM source"
            />

            <DateRangePicker
              value={dateRange}
              onChange={setDateRange}
              placeholder="Filter by date range"
            />
          </div>
        )}

        <div className="mt-4 flex items-center justify-between">
          <span className="text-sm text-gray-500">
            {filteredLogs.length} of {totalLogs} logs
          </span>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedLogs.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-700">
                {selectedLogs.length} logs selected
              </span>
              <button
                onClick={clearSelection}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm"
              >
                Delete
              </button>
              <button
                onClick={handleBulkExport}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Logs Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    checked={filteredLogs.length > 0 && selectedLogs.length === filteredLogs.length}
                    onChange={filteredLogs.length > 0 && selectedLogs.length === filteredLogs.length ? clearSelection : selectAllLogs}
                  />
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  View
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  UTM Source
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLogs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      checked={selectedLogs.includes(log.id)}
                      onChange={() => toggleLogSelection(log.id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => handleViewLog(log)}
                      className="text-blue-600 hover:text-blue-800 transition-colors"
                      title="View log details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{log.timestamp.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">
                        {formatDistanceToNow(log.timestamp, { addSuffix: true })}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium ${getActorColor(log.actor)}`}>
                      {log.actor}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getActionIcon(log.action)}
                      <span className="text-sm text-gray-900">{log.action}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {log.target}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                    {log.details}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span className={`px-2 py-1 rounded-md text-xs border ${getUtmSourceColor(log.utmSource)}`}>
                      {log.utmSource}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <button
                      onClick={() => handleDeleteLog(log.id)}
                      className="text-red-600 hover:text-red-800 transition-colors"
                      title="Delete log entry"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
        </>
      )}

      {/* Import History Tab */}
      {activeTab === 'imports' && (
        <>
          {/* Import Stats Cards */}
          {importStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Imports</p>
                    <p className="text-2xl font-bold text-gray-900">{importStats.total_imports || 0}</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <Upload className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Successful Imports</p>
                    <p className="text-2xl font-bold text-gray-900">{importStats.successful_imports || 0}</p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <FileText className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Records Created</p>
                    <p className="text-2xl font-bold text-gray-900">{importStats.total_records_created || 0}</p>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <Activity className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Success Rate</p>
                    <p className={`text-2xl font-bold ${
                      (importStats.average_success_rate || 0) >= 95 ? 'text-green-600' :
                      (importStats.average_success_rate || 0) >= 85 ? 'text-yellow-600' :
                      (importStats.average_success_rate || 0) > 0 ? 'text-red-600' : 'text-gray-900'
                    }`}>
                      {Math.round(importStats.average_success_rate || 0)}%
                    </p>
                  </div>
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <Activity className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Import History Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Import History</h3>
              <p className="text-gray-600">View and manage your data import history</p>
            </div>

            <div className="overflow-x-auto">
              {importLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading import history...</span>
                </div>
              ) : importHistory.length === 0 ? (
                <div className="text-center py-12">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Import History</h3>
                  <p className="text-gray-600">No data imports have been performed yet.</p>
                </div>
              ) : (
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Records</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {importHistory.map((importRecord) => (
                      <tr key={importRecord.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{importRecord.file_name}</div>
                            <div className="text-sm text-gray-500">{importRecord.formatted_file_size}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge variant="info" size="sm">
                            {importRecord.data_type}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <StatusBadge
                            status={importRecord.status}
                            size="sm"
                            statusMap={{
                              completed: 'success',
                              failed: 'error',
                              processing: 'info',
                              pending: 'warning'
                            }}
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>
                            <div>Created: {importRecord.created}</div>
                            <div>Updated: {importRecord.updated}</div>
                            <div>Errors: {importRecord.errors_count}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className={`font-medium ${
                            importRecord.success_rate >= 95 ? 'text-green-600' :
                            importRecord.success_rate >= 85 ? 'text-yellow-600' :
                            importRecord.success_rate > 0 ? 'text-red-600' : 'text-gray-500'
                          }`}>
                            {importRecord.success_rate !== null && importRecord.success_rate !== undefined
                              ? `${Math.round(importRecord.success_rate)}%`
                              : 'N/A'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(importRecord.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <ActionButtons
                            actions={[
                              {
                                icon: Eye,
                                label: 'View Details',
                                onClick: () => handleViewImport(importRecord),
                                variant: 'view'
                              },
                              {
                                icon: Trash2,
                                label: 'Delete',
                                onClick: () => handleDeleteImport(importRecord.id),
                                variant: 'delete'
                              }
                            ]}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </>
      )}

      {/* Log Details Modal */}
      {showLogModal && selectedLog && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity bg-black bg-opacity-50"
              onClick={() => setShowLogModal(false)}
            />

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full max-w-2xl max-h-[90vh] overflow-y-auto relative">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Log Details</h3>
              <button
                onClick={() => setShowLogModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ID</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">{selectedLog.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Timestamp</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                    {selectedLog.timestamp.toLocaleString()}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Actor</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">{selectedLog.actor}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Action</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">{selectedLog.action}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Target</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">{selectedLog.target}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">UTM Source</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">{selectedLog.utmSource}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">{selectedLog.ipAddress}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">User Agent</label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded truncate" title={selectedLog.userAgent}>
                    {selectedLog.userAgent}
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Details</label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded min-h-[60px]">
                  {selectedLog.details}
                </p>
              </div>
            </div>

            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={() => setShowLogModal(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Import Details Modal */}
      {showImportModal && selectedImport && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div className="bg-white px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Import Details</h3>
                  <p className="text-sm text-gray-600 mt-1">Comprehensive import statistics and information</p>
                </div>
                <button
                  onClick={() => setShowImportModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Import Summary Header */}
              <div className="text-center">
                <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">Import Complete!</h3>
                <p className="text-gray-600">Your CSV data was successfully processed.</p>
              </div>

              {/* Enhanced Summary Statistics */}
              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6 border border-blue-200">
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  Import Summary
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedImport.total_rows?.toLocaleString() || 'N/A'}</div>
                    <div className="text-gray-600">Total Rows</div>
                    <div className="text-xs text-gray-500 mt-1">Including header</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{selectedImport.processed?.toLocaleString() || 'N/A'}</div>
                    <div className="text-gray-600">Successfully Processed</div>
                    <div className="text-xs text-gray-500 mt-1">Data rows only</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {selectedImport.total_rows && selectedImport.processed
                        ? Math.max(0, selectedImport.total_rows - selectedImport.processed - 1).toLocaleString()
                        : 'N/A'}
                    </div>
                    <div className="text-gray-600">Failed Records</div>
                    <div className="text-xs text-gray-500 mt-1">Excluding header</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {selectedImport.success_rate !== null && selectedImport.success_rate !== undefined
                        ? `${Math.round(selectedImport.success_rate)}%`
                        : 'N/A'}
                    </div>
                    <div className="text-gray-600">Success Rate</div>
                    <div className="text-xs text-gray-500 mt-1">Data rows only</div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="mt-4 pt-4 border-t border-blue-200">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Processing Speed:</span>
                      <span className="font-medium text-blue-600 ml-2">
                        {selectedImport.processed ? Math.round(selectedImport.processed / 60).toLocaleString() : 'N/A'} records/min
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Data Quality:</span>
                      <span className={`font-medium ml-2 ${
                        selectedImport.success_rate > 95 ? 'text-green-600' :
                        selectedImport.success_rate > 85 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {selectedImport.success_rate > 95 ? 'Excellent' :
                         selectedImport.success_rate > 85 ? 'Good' : 'Needs Review'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <p className="text-2xl font-bold text-green-600">{selectedImport.created || 0}</p>
                  <p className="text-sm text-green-700">Created</p>
                </div>
                <div className="bg-blue-50 rounded-lg p-4 text-center">
                  <p className="text-2xl font-bold text-blue-600">{selectedImport.updated || 0}</p>
                  <p className="text-sm text-blue-700">Updated</p>
                </div>
                <div className="bg-yellow-50 rounded-lg p-4 text-center">
                  <p className="text-2xl font-bold text-yellow-600">{selectedImport.skipped || 0}</p>
                  <p className="text-sm text-yellow-700">Skipped</p>
                </div>
                <div className="bg-red-50 rounded-lg p-4 text-center">
                  <p className="text-2xl font-bold text-red-600">{selectedImport.errors_count || 0}</p>
                  <p className="text-sm text-red-700">Errors</p>
                </div>
              </div>

              {/* File Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">File Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">File Name:</span>
                    <span className="font-medium text-gray-900 ml-2">{selectedImport.file_name}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">File Size:</span>
                    <span className="font-medium text-gray-900 ml-2">{selectedImport.formatted_file_size || 'N/A'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Data Type:</span>
                    <span className="font-medium text-gray-900 ml-2">{selectedImport.data_type}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Status:</span>
                    <span className="ml-2">
                      <StatusBadge
                        status={selectedImport.status}
                        size="sm"
                        statusMap={{
                          completed: 'success',
                          failed: 'danger',
                          processing: 'info',
                          pending: 'warning'
                        }}
                      />
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Processing Time:</span>
                    <span className="font-medium text-gray-900 ml-2">{selectedImport.formatted_processing_time || 'N/A'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Import Date:</span>
                    <span className="font-medium text-gray-900 ml-2">{new Date(selectedImport.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              {/* Skipped Records Section - Show when there are skipped records */}
              {selectedImport.skipped > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-semibold text-yellow-900 mb-3 flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2" />
                    Skipped Records Details
                  </h4>
                  <div className="text-sm text-yellow-900 bg-white p-3 rounded border border-yellow-200">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium">Total Skipped:</span>
                        <span className="font-bold">{selectedImport.skipped}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Reason:</span>
                        <span>Duplicate records (skip_duplicates enabled)</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">Action Taken:</span>
                        <span>Records were skipped to avoid duplicates</span>
                      </div>
                      <div className="mt-3 p-2 bg-yellow-100 rounded text-xs">
                        <strong>Note:</strong> Skipped records are not errors. They were intentionally excluded
                        to maintain data integrity and avoid duplicate entries in your database.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Error Details Section - Only show when there are actual errors */}
              {selectedImport.errors_count > 0 && selectedImport.error_details && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-semibold text-red-900 mb-3 flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2" />
                    Error Details ({selectedImport.errors_count} errors)
                  </h4>
                  <div className="text-sm text-red-900 bg-white p-3 rounded border border-red-200 max-h-40 overflow-y-auto">
                    <pre className="whitespace-pre-wrap">{
                      typeof selectedImport.error_details === 'string'
                        ? selectedImport.error_details
                        : JSON.stringify(selectedImport.error_details, null, 2)
                    }</pre>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => setShowImportModal(false)}
                className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                Close
              </button>
            </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Logs;