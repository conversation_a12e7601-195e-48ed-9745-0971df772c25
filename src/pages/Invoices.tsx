import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus, Search, Filter, Eye, Mail, FileText,
  Edit, Trash2, Download, Copy,
  ChevronDown, X, ChevronLeft, ChevronRight,
  DollarSign, CheckCircle, XCircle, Receipt,
  List, LayoutGrid, CreditCard
} from 'lucide-react';
import { useInvoices, Invoice } from '../contexts/InvoiceContext';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import CustomDropdown from '../components/CustomDropdown';
import ActionDropdown from '../components/ActionDropdown';
import InvoiceSidebar from '../components/InvoiceSidebar';
import { PriorityBadge } from '../components/Badge';
import logger from '../utils/logger';

const Invoices: React.FC = () => {
  const navigate = useNavigate();
  const { 
    invoices, 
    loading, 
    error, 
    deleteInvoice, 
    sendInvoice, 
    markInvoiceAsPaid,
    fetchInvoices 
  } = useInvoices();
  const { showSuccess, showError, showInfo } = useToast();
  const { confirm } = useConfirmation();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    status: '',
    payment_status: '',
    priority: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | undefined>(undefined);

  // Filter and search logic
  const filteredInvoices = useMemo(() => {
    return invoices.filter(invoice => {
      const matchesSearch = !searchTerm || 
        invoice.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.client?.name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = !selectedFilters.status || invoice.status === selectedFilters.status;
      const matchesPaymentStatus = !selectedFilters.payment_status || invoice.payment_status === selectedFilters.payment_status;
      const matchesPriority = !selectedFilters.priority || invoice.priority === selectedFilters.priority;

      return matchesSearch && matchesStatus && matchesPaymentStatus && matchesPriority;
    });
  }, [invoices, searchTerm, selectedFilters]);

  // Pagination
  const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedInvoices = filteredInvoices.slice(startIndex, startIndex + itemsPerPage);

  // Statistics
  const stats = useMemo(() => {
    const total = invoices.length;
    const draft = invoices.filter(i => i.status === 'draft').length;
    const sent = invoices.filter(i => i.status === 'sent').length;
    const paid = invoices.filter(i => i.payment_status === 'paid').length;
    const totalValue = invoices.reduce((sum, i) => sum + (parseFloat(i.total_amount || '0')), 0);

    return { total, draft, sent, paid, totalValue };
  }, [invoices]);

  const handleCreate = () => {
    setSelectedInvoice(undefined);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleView = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleEdit = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDelete = async (invoice: Invoice) => {
    const confirmed = await confirm({
      title: 'Delete Invoice',
      message: `Are you sure you want to delete invoice ${invoice.invoice_number}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        await deleteInvoice(invoice.id);
        logger.log(
          'Deleted',
          'Invoice',
          `Deleted invoice ${invoice.invoice_number}`,
          'Admin',
          'invoices'
        );
        showSuccess('Invoice Deleted', `Invoice ${invoice.invoice_number} has been successfully deleted.`);
      } catch (error) {
        console.error('Error deleting invoice:', error);
        showError('Delete Failed', 'Failed to delete the invoice. Please try again.');
      }
    }
  };

  const handleSend = async (invoice: Invoice) => {
    try {
      await sendInvoice(invoice.id);
      logger.log(
        'Sent',
        'Invoice',
        `Sent invoice ${invoice.invoice_number}`,
        'Admin',
        'invoices'
      );
      showSuccess('Invoice Sent', `Invoice ${invoice.invoice_number} has been sent successfully.`);
    } catch (error) {
      console.error('Error sending invoice:', error);
      showError('Send Failed', 'Failed to send the invoice. Please try again.');
    }
  };

  const handleMarkAsPaid = async (invoice: Invoice) => {
    const confirmed = await confirm({
      title: 'Mark as Paid',
      message: `Are you sure you want to mark invoice ${invoice.invoice_number} as paid?`,
      confirmText: 'Mark as Paid',
      cancelText: 'Cancel',
      type: 'info'
    });

    if (confirmed) {
      try {
        await markInvoiceAsPaid(invoice.id);
        logger.log(
          'Updated',
          'Invoice',
          `Marked invoice ${invoice.invoice_number} as paid`,
          'Admin',
          'invoices'
        );
        showSuccess('Payment Recorded', `Invoice ${invoice.invoice_number} has been marked as paid.`);
      } catch (error) {
        console.error('Error marking invoice as paid:', error);
        showError('Update Failed', 'Failed to update the invoice payment status. Please try again.');
      }
    }
  };

  const toggleInvoiceSelection = (invoiceId: string) => {
    setSelectedInvoices(prev =>
      prev.includes(invoiceId)
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    );
  };

  const selectAllInvoices = () => {
    setSelectedInvoices(paginatedInvoices.map(invoice => invoice.id));
  };

  const clearSelection = () => {
    setSelectedInvoices([]);
  };

  const handleBulkDelete = async () => {
    const confirmed = await confirm({
      title: 'Delete Invoices',
      message: `Are you sure you want to delete ${selectedInvoices.length} invoice(s)? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        for (const invoiceId of selectedInvoices) {
          await deleteInvoice(invoiceId);
        }
        showSuccess(`Successfully deleted ${selectedInvoices.length} invoice(s)`);
        setSelectedInvoices([]);
      } catch (error) {
        console.error('Error deleting invoices:', error);
        showError('Failed to delete invoices');
      }
    }
  };

  const handleBulkExport = () => {
    // TODO: Implement bulk export functionality
    showInfo('Bulk export functionality coming soon');
  };

  const resetFilters = () => {
    setSelectedFilters({
      status: '',
      payment_status: '',
      priority: '',
    });
    setSearchTerm('');
    setCurrentPage(1);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300', icon: FileText },
      sent: { color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300', icon: Mail },
      viewed: { color: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300', icon: Eye },
      paid: { color: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300', icon: CheckCircle },
      overdue: { color: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300', icon: XCircle },
      cancelled: { color: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300', icon: X },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPaymentStatusBadge = (paymentStatus: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300', icon: DollarSign },
      paid: { color: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300', icon: CheckCircle },
      failed: { color: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300', icon: XCircle },
      refunded: { color: 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300', icon: Receipt },
    };

    const config = statusConfig[paymentStatus as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1)}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    return <PriorityBadge priority={priority} />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchInvoices}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Invoices</h1>
          <p className="text-gray-600 dark:text-gray-300">Manage and track your invoices</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button
            onClick={() => {/* TODO: Export functionality */}}
            className="btn-secondary w-full sm:w-auto flex items-center justify-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
          <button
            onClick={handleCreate}
            className="btn-primary w-full sm:w-auto flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>New Invoice</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Receipt className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-content-secondary">Total Invoices</p>
                <p className="text-2xl font-bold text-content-primary">{stats.total}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <FileText className="w-6 h-6 text-gray-600 dark:text-gray-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-content-secondary">Draft</p>
                <p className="text-2xl font-bold text-content-primary">{stats.draft}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Mail className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Sent</p>
              <p className="text-2xl font-bold text-gray-900">{stats.sent}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Paid</p>
              <p className="text-2xl font-bold text-gray-900">{stats.paid}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Value</p>
              <p className="text-2xl font-bold text-gray-900">RM {stats.totalValue.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        <div className="flex items-center space-x-4">
          {/* Clear Filters Button */}
          {(searchTerm || Object.values(selectedFilters).some(filter => filter)) && (
            <button
              onClick={resetFilters}
              className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
            >
              <X className="w-4 h-4" />
              <span>Clear filters</span>
            </button>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search invoices by number, title, or client..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <LayoutGrid className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <CustomDropdown
                options={[
                  { value: '', label: 'All Statuses' },
                  { value: 'draft', label: 'Draft' },
                  { value: 'sent', label: 'Sent' },
                  { value: 'viewed', label: 'Viewed' },
                  { value: 'paid', label: 'Paid' },
                  { value: 'overdue', label: 'Overdue' },
                ]}
                value={selectedFilters.status}
                onChange={(value) => setSelectedFilters(prev => ({ ...prev, status: value }))}
                placeholder="Filter by status"
              />

              <CustomDropdown
                options={[
                  { value: '', label: 'All Payment Status' },
                  { value: 'pending', label: 'Pending' },
                  { value: 'paid', label: 'Paid' },
                  { value: 'failed', label: 'Failed' },
                  { value: 'refunded', label: 'Refunded' },
                ]}
                value={selectedFilters.payment_status}
                onChange={(value) => setSelectedFilters(prev => ({ ...prev, payment_status: value }))}
                placeholder="Filter by payment status"
              />

              <CustomDropdown
                options={[
                  { value: '', label: 'All Priorities' },
                  { value: 'low', label: 'Low' },
                  { value: 'medium', label: 'Medium' },
                  { value: 'high', label: 'High' },
                ]}
                value={selectedFilters.priority}
                onChange={(value) => setSelectedFilters(prev => ({ ...prev, priority: value }))}
                placeholder="Filter by priority"
              />
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedInvoices.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-700">
                {selectedInvoices.length} invoices selected
              </span>
              <button
                onClick={clearSelection}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear selection
              </button>
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:space-x-2">
              <button
                onClick={handleBulkDelete}
                className="w-full sm:w-auto px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm"
              >
                Delete
              </button>
              <button
                onClick={handleBulkExport}
                className="w-full sm:w-auto px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Invoices List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 table-container">
        {filteredInvoices.length === 0 ? (
          <div className="text-center py-12">
            <Receipt className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || Object.values(selectedFilters).some(Boolean)
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first invoice'
              }
            </p>
            {!searchTerm && !Object.values(selectedFilters).some(Boolean) && (
              <div className="mt-6">
                <button
                  onClick={handleCreate}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  New Invoice
                </button>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-gray-900">
                    {filteredInvoices.length} invoice{filteredInvoices.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        checked={paginatedInvoices.length > 0 && selectedInvoices.length === paginatedInvoices.length}
                        onChange={paginatedInvoices.length > 0 && selectedInvoices.length === paginatedInvoices.length ? clearSelection : selectAllInvoices}
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Invoice
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Due Date
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedInvoices.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          checked={selectedInvoices.includes(invoice.id)}
                          onChange={() => toggleInvoiceSelection(invoice.id)}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {invoice.invoice_number}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {invoice.title}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {invoice.client?.name || 'No client'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {invoice.client?.email}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          RM {parseFloat(invoice.total_amount || '0').toFixed(2)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(invoice.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getPaymentStatusBadge(invoice.payment_status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getPriorityBadge(invoice.priority)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <ActionDropdown
                          actions={[
                            {
                              label: 'View',
                              icon: Eye,
                              onClick: () => handleView(invoice),
                            },
                            {
                              label: 'Edit',
                              icon: Edit,
                              onClick: () => handleEdit(invoice),
                            },
                            {
                              label: 'Send',
                              icon: Mail,
                              onClick: () => handleSend(invoice),
                              disabled: invoice.status === 'sent' || invoice.status === 'paid',
                            },
                            {
                              label: 'Mark as Paid',
                              icon: CreditCard,
                              onClick: () => handleMarkAsPaid(invoice),
                              disabled: invoice.payment_status === 'paid',
                            },
                            {
                              label: 'Download',
                              icon: Download,
                              onClick: () => {/* TODO: Download functionality */},
                            },
                            {
                              label: 'Duplicate',
                              icon: Copy,
                              onClick: () => {/* TODO: Duplicate functionality */},
                            },
                            {
                              label: 'Delete',
                              icon: Trash2,
                              onClick: () => handleDelete(invoice),
                              className: 'text-red-600 hover:text-red-700',
                            },
                          ]}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredInvoices.length)} of {filteredInvoices.length} results
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </button>
                    <span className="text-sm text-gray-700">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Invoice Sidebar */}
      {isModalOpen && (
        <InvoiceSidebar
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedInvoice(undefined);
          }}
          invoice={selectedInvoice}
          mode={modalMode}
        />
      )}
    </div>
  );
};

export default Invoices;
