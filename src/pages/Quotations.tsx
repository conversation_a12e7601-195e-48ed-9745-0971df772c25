import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Plus, Search, Filter, Eye, Mail, FileText,
  Edit, Trash2, Download, Copy,
  ChevronDown, X, ChevronLeft, ChevronRight,
  DollarSign, CheckCircle, XCircle, Receipt,
  List, LayoutGrid
} from 'lucide-react';
import { useQuotations, Quotation } from '../contexts/QuotationContext';
import { useInvoices } from '../contexts/InvoiceContext';
import { useToast } from '../contexts/ToastContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import CustomDropdown from '../components/CustomDropdown';
import ActionDropdown from '../components/ActionDropdown';
import QuotationSidebar from '../components/QuotationSidebar';

import logger from '../utils/logger';

const Quotations: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { quotations, loading, error, deleteQuotation, sendQuotation, acceptQuotation, rejectQuotation, duplicateQuotation, fetchQuotations } = useQuotations();
  const { createInvoice, createInvoiceFromQuotation } = useInvoices();

  const { showSuccess, showError, showInfo } = useToast();
  const { confirm } = useConfirmation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    status: '',
    priority: '',
    assignedTo: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedQuotations, setSelectedQuotations] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedQuotation, setSelectedQuotation] = useState<Quotation | undefined>(undefined);

  // Clean up any leftover localStorage data from previous approach
  useEffect(() => {
    localStorage.removeItem('quotationFromDeal');
  }, []);

  // Filter quotations
  const filteredQuotations = useMemo(() => {
    return quotations.filter(quotation => {
      const matchesSearch = !searchTerm || 
        quotation.quotation_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quotation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quotation.client?.name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = !selectedFilters.status || quotation.status === selectedFilters.status;
      const matchesPriority = !selectedFilters.priority || quotation.priority === selectedFilters.priority;
      const matchesAssignedTo = !selectedFilters.assignedTo || quotation.assigned_to === selectedFilters.assignedTo;

      return matchesSearch && matchesStatus && matchesPriority && matchesAssignedTo;
    });
  }, [quotations, searchTerm, selectedFilters]);

  // Pagination
  const totalPages = Math.ceil(filteredQuotations.length / itemsPerPage);
  const paginatedQuotations = filteredQuotations.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const clearFilters = () => {
    setSelectedFilters({
      status: '',
      priority: '',
      assignedTo: '',
    });
    setSearchTerm('');
  };

  const toggleQuotationSelection = (quotationId: string) => {
    setSelectedQuotations(prev => 
      prev.includes(quotationId) 
        ? prev.filter(id => id !== quotationId)
        : [...prev, quotationId]
    );
  };

  const selectAllQuotations = () => {
    setSelectedQuotations(paginatedQuotations.map(quotation => quotation.id));
  };

  const clearSelection = () => {
    setSelectedQuotations([]);
  };

  const handleBulkDelete = async () => {
    const confirmed = await confirm({
      title: 'Delete Quotations',
      message: `Are you sure you want to delete ${selectedQuotations.length} quotation(s)? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        for (const quotationId of selectedQuotations) {
          await deleteQuotation(quotationId);
        }
        showSuccess(`Successfully deleted ${selectedQuotations.length} quotation(s)`);
        setSelectedQuotations([]);
      } catch (error) {
        console.error('Error deleting quotations:', error);
        showError('Failed to delete quotations');
      }
    }
  };

  const handleBulkExport = () => {
    // TODO: Implement bulk export functionality
    showInfo('Bulk export functionality coming soon');
  };

  const handleCreate = () => {
    setSelectedQuotation(undefined);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleConvertToInvoice = async (quotation: Quotation) => {
    const confirmed = await confirm({
      title: 'Convert to Invoice',
      message: `Are you sure you want to convert quotation ${quotation.quotation_number} to an invoice? This will create a new invoice with the same details.`,
      confirmText: 'Convert',
      cancelText: 'Cancel',
      type: 'info'
    });

    if (confirmed) {
      try {
        // Use the proper conversion method from the context
        await createInvoiceFromQuotation(quotation.id);

        logger.log(
          'Created',
          'Invoice',
          `Converted quotation ${quotation.quotation_number} to invoice`,
          'Admin',
          'quotations'
        );
        showSuccess('Invoice Created', `Quotation ${quotation.quotation_number} has been successfully converted to an invoice.`);

        // Refresh quotations to show updated status
        await fetchQuotations();
      } catch (error) {
        console.error('Error converting quotation to invoice:', error);
        showError('Conversion Failed', 'Failed to convert the quotation to an invoice. Please try again.');
      }
    }
  };

  const handleView = (quotation: Quotation) => {
    setSelectedQuotation(quotation);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleEdit = (quotation: Quotation) => {
    setSelectedQuotation(quotation);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDelete = async (quotationId: string) => {
    const quotation = quotations.find(q => q.id === quotationId);
    const quotationNumber = quotation?.quotation_number || quotationId;

    const confirmed = await confirm({
      title: 'Delete Quotation',
      message: `Are you sure you want to delete quotation ${quotationNumber}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        await deleteQuotation(quotationId);
        logger.logQuotationDeleted(quotationNumber);
        showSuccess('Quotation Deleted', `Quotation ${quotationNumber} has been successfully deleted.`);
      } catch (error) {
        console.error('Error deleting quotation:', error);
        showError('Delete Failed', 'Failed to delete the quotation. Please try again.');
      }
    }
  };

  const handleSend = async (quotationId: string) => {
    const quotation = quotations.find(q => q.id === quotationId);
    const quotationNumber = quotation?.quotation_number || quotationId;

    try {
      await sendQuotation(quotationId);
      logger.logQuotationSent(quotationNumber, quotation?.client?.email);
      showSuccess('Quotation Sent', `Quotation ${quotationNumber} has been sent successfully.`);
    } catch (error) {
      console.error('Error sending quotation:', error);
      showError('Send Failed', 'Failed to send the quotation. Please try again.');
    }
  };

  const handleAccept = async (quotationId: string) => {
    const quotation = quotations.find(q => q.id === quotationId);
    const quotationNumber = quotation?.quotation_number || quotationId;

    try {
      await acceptQuotation(quotationId);
      logger.logQuotationAccepted(quotationNumber);
      showSuccess('Quotation Accepted', `Quotation ${quotationNumber} has been accepted.`);
    } catch (error) {
      console.error('Error accepting quotation:', error);
      showError('Accept Failed', 'Failed to accept the quotation. Please try again.');
    }
  };

  const handleReject = async (quotationId: string) => {
    const quotation = quotations.find(q => q.id === quotationId);
    const quotationNumber = quotation?.quotation_number || quotationId;

    try {
      await rejectQuotation(quotationId);
      logger.logQuotationRejected(quotationNumber);
      showInfo('Quotation Rejected', `Quotation ${quotationNumber} has been rejected.`);
    } catch (error) {
      console.error('Error rejecting quotation:', error);
      showError('Reject Failed', 'Failed to reject the quotation. Please try again.');
    }
  };

  const handleDuplicate = async (quotationId: string) => {
    const quotation = quotations.find(q => q.id === quotationId);
    const quotationNumber = quotation?.quotation_number || quotationId;

    try {
      const newQuotation = await duplicateQuotation(quotationId);
      logger.logQuotationDuplicated(quotationNumber, newQuotation.quotation_number);
      showSuccess('Quotation Duplicated', `Quotation duplicated as ${newQuotation.quotation_number}.`);
    } catch (error) {
      console.error('Error duplicating quotation:', error);
      showError('Duplicate Failed', 'Failed to duplicate the quotation. Please try again.');
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedQuotation(undefined);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'sent':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      case 'viewed':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'accepted':
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'expired':
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'MYR') => {
    // Handle NaN, null, undefined values
    const validAmount = isNaN(amount) || amount === null || amount === undefined ? 0 : amount;
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: currency,
    }).format(validAmount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Statistics calculations
  const totalQuotations = quotations.length;
  const draftQuotations = quotations.filter(q => q.status === 'draft').length;
  const sentQuotations = quotations.filter(q => q.status === 'sent').length;
  const acceptedQuotations = quotations.filter(q => q.status === 'accepted').length;
  const totalValue = quotations.reduce((sum, q) => {
    const amount = parseFloat(q.total_amount?.toString() || '0');
    return sum + (isNaN(amount) ? 0 : amount);
  }, 0);
  const acceptedValue = quotations.filter(q => q.status === 'accepted').reduce((sum, q) => {
    const amount = parseFloat(q.total_amount?.toString() || '0');
    return sum + (isNaN(amount) ? 0 : amount);
  }, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-700">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Quotations</h1>
          <p className="text-gray-600 dark:text-gray-300">Manage and track your quotations</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button
            onClick={() => {/* TODO: Export functionality */}}
            className="btn-secondary w-full sm:w-auto flex items-center justify-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>

          <button
            onClick={handleCreate}
            className="btn-primary w-full sm:w-auto flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>New Quotation</span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Quotations</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalQuotations}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Accepted</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{acceptedQuotations}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Value</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(totalValue)}</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <DollarSign className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Accepted Value</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(acceptedValue)}</p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <Receipt className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
          {/* Clear Filters Button */}
          {(searchTerm || Object.values(selectedFilters).some(filter => filter)) && (
            <button
              onClick={clearFilters}
              className="flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 w-full sm:w-auto justify-center sm:justify-start"
            >
              <X className="w-4 h-4" />
              <span>Clear filters</span>
            </button>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              placeholder="Search quotations by number, title, or client..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
              <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>

            <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 relative z-50">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <CustomDropdown
                options={[
                  { value: '', label: 'All Statuses' },
                  { value: 'draft', label: 'Draft' },
                  { value: 'sent', label: 'Sent' },
                  { value: 'viewed', label: 'Viewed' },
                  { value: 'accepted', label: 'Accepted' },
                  { value: 'rejected', label: 'Rejected' },
                  { value: 'expired', label: 'Expired' },
                ]}
                value={selectedFilters.status}
                onChange={(value) => setSelectedFilters({ ...selectedFilters, status: value })}
                placeholder="Filter by status"
              />
              <CustomDropdown
                options={[
                  { value: '', label: 'All Priorities' },
                  { value: 'low', label: 'Low' },
                  { value: 'medium', label: 'Medium' },
                  { value: 'high', label: 'High' },
                  { value: 'urgent', label: 'Urgent' },
                ]}
                value={selectedFilters.priority}
                onChange={(value) => setSelectedFilters({ ...selectedFilters, priority: value })}
                placeholder="Filter by priority"
              />
              <CustomDropdown
                options={[
                  { value: '', label: 'All Assignees' },
                  // TODO: Add actual users from API
                ]}
                value={selectedFilters.assignedTo}
                onChange={(value) => setSelectedFilters({ ...selectedFilters, assignedTo: value })}
                placeholder="Filter by assignee"
              />
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedQuotations.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-700">
                {selectedQuotations.length} quotations selected
              </span>
              <button
                onClick={clearSelection}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm"
              >
                Delete
              </button>
              <button
                onClick={handleBulkExport}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quotations Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                    checked={paginatedQuotations.length > 0 && selectedQuotations.length === paginatedQuotations.length}
                    onChange={paginatedQuotations.length > 0 && selectedQuotations.length === paginatedQuotations.length ? clearSelection : selectAllQuotations}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Quotation
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valid Until
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedQuotations.map((quotation) => (
                <tr key={quotation.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      checked={selectedQuotations.includes(quotation.id)}
                      onChange={() => toggleQuotationSelection(quotation.id)}
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleView(quotation)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View Quotation"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {quotation.quotation_number}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {quotation.title}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {quotation.client?.name || 'No Client'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(parseFloat(quotation.total_amount?.toString() || '0'), quotation.currency)}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(quotation.status)}`}>
                      {quotation.status.charAt(0).toUpperCase() + quotation.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-md ${getPriorityColor(quotation.priority)}`}>
                      {quotation.priority.charAt(0).toUpperCase() + quotation.priority.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {quotation.valid_until ? formatDate(quotation.valid_until) : 'No expiry'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <ActionDropdown
                        actions={[
                          {
                            label: 'Edit',
                            icon: Edit,
                            onClick: () => handleEdit(quotation),
                            color: 'text-green-600'
                          },
                          {
                            label: 'Send',
                            icon: Mail,
                            onClick: () => handleSend(quotation.id),
                            color: 'text-green-600',
                            disabled: quotation.status === 'sent' || quotation.status === 'accepted'
                          },
                          {
                            label: 'Convert to Invoice',
                            icon: Receipt,
                            onClick: () => handleConvertToInvoice(quotation),
                            color: 'text-green-600',
                            disabled: quotation.status !== 'accepted'
                          },
                          {
                            label: 'Duplicate',
                            icon: Copy,
                            onClick: () => handleDuplicate(quotation.id),
                            color: 'text-purple-600'
                          },
                          {
                            label: 'Accept',
                            icon: CheckCircle,
                            onClick: () => handleAccept(quotation.id),
                            color: 'text-green-600',
                            disabled: quotation.status !== 'sent' && quotation.status !== 'viewed'
                          },
                          {
                            label: 'Reject',
                            icon: XCircle,
                            onClick: () => handleReject(quotation.id),
                            color: 'text-red-600',
                            disabled: quotation.status !== 'sent' && quotation.status !== 'viewed'
                          },
                          {
                            label: 'Delete',
                            icon: Trash2,
                            onClick: () => handleDelete(quotation.id),
                            color: 'text-red-600'
                          }
                        ]}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <p className="text-sm text-gray-700">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredQuotations.length)} of {filteredQuotations.length} results
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <span className="px-3 py-1 text-sm font-medium text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quotation Sidebar */}
      <QuotationSidebar
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        quotation={selectedQuotation}
        mode={modalMode}
      />

    </div>
  );
};

export default Quotations;
