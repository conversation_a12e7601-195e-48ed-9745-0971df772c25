import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Mail, ArrowLeft, BookOpen } from 'lucide-react';
import { useToast } from '../contexts/ToastContext';
import { apiService } from '../services/api';

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [errors, setErrors] = useState<{ email?: string }>({});
  const [rateLimited, setRateLimited] = useState(false);
  const [retryAfter, setRetryAfter] = useState(0);
  const { showToast } = useToast();

  const validateEmail = (email: string): string | null => {
    if (!email) {
      return 'Email address is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return 'Please enter a valid email address';
    }
    if (email.length > 255) {
      return 'Email address is too long';
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    const emailError = validateEmail(email);
    if (emailError) {
      setErrors({ email: emailError });
      showToast(emailError, 'error');
      return;
    }

    setLoading(true);
    try {
      await apiService.forgotPassword(email);
      setEmailSent(true);
      showToast('Password reset instructions sent to your email', 'success');
    } catch (error: any) {
      if (error.status === 429) {
        setRateLimited(true);
        setRetryAfter(error.retry_after || 3600);
        showToast('Too many attempts. Please try again later.', 'error');
      } else if (error.status === 422 && error.errors) {
        setErrors(error.errors);
        showToast(Object.values(error.errors).flat().join(', '), 'error');
      } else {
        showToast(error.message || 'Failed to send reset email', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = async () => {
    setLoading(true);
    try {
      await apiService.forgotPassword(email);
      showToast('Password reset instructions sent again', 'success');
    } catch (error: any) {
      showToast(error.message || 'Failed to resend email', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4 py-4">
      {/* Unified Giant Card */}
      <div className="w-full max-w-[95vw] h-[90vh] bg-white dark:bg-gray-800 rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden flex">
        {/* Left Content Area */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-green-600 to-blue-700 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 border-2 border-white rounded-full"></div>
          <div className="absolute top-32 right-20 w-16 h-16 border-2 border-white rounded-lg rotate-45"></div>
          <div className="absolute bottom-20 left-20 w-12 h-12 bg-white rounded-full"></div>
          <div className="absolute bottom-40 right-10 w-8 h-8 bg-white rounded-lg"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="max-w-md">
            {/* Brand Logo */}
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <BookOpen className="w-7 h-7 text-white" />
              </div>
              <span className="text-3xl font-bold text-white">Tarbiah Sentap</span>
            </div>

            <h1 className="text-4xl font-bold mb-6 text-white">Reset your password</h1>
            <p className="text-xl mb-8 text-white">
              Don't worry, it happens to the best of us. We'll help you get back into your account.
            </p>

            {/* Feature highlights */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <Mail className="w-4 h-4 text-white" />
                </div>
                <span className="text-white">Secure Email Verification</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <BookOpen className="w-4 h-4 text-white" />
                </div>
                <span className="text-white">Quick Account Recovery</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                  <ArrowLeft className="w-4 h-4 text-white" />
                </div>
                <span className="text-white">Easy Return to Login</span>
              </div>
            </div>
          </div>
        </div>
      </div>

        {/* Right Form Area */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="max-w-md w-full">
            {/* Header */}
            <div className="text-center mb-8">
              {!emailSent ? (
                <>
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Forgot Password?</h2>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    Enter your email address and we'll send you instructions to reset your password
                  </p>
                </>
              ) : (
                <>
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Check Your Email</h2>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    We've sent password reset instructions to <strong>{email}</strong>
                  </p>
                </>
              )}
            </div>

            {/* Form */}
            <div>
          {!emailSent ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-300"
                    placeholder="Enter your email address"
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center items-center space-x-2 py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
              >
                {loading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Mail className="w-5 h-5" />
                )}
                <span>{loading ? 'Sending...' : 'Send Reset Instructions'}</span>
              </button>
            </form>
          ) : (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-8 h-8 text-green-600 dark:text-green-400" />
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  If an account with that email exists, you should receive the reset instructions within a few minutes.
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Didn't receive the email? Check your spam folder or try again.
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleResendEmail}
                  disabled={loading}
                  className="w-full flex justify-center items-center space-x-2 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Mail className="w-4 h-4" />
                  )}
                  <span>{loading ? 'Sending...' : 'Resend Email'}</span>
                </button>
              </div>
            </div>
          )}

            <div className="mt-6 text-center">
              <Link
                to="/login"
                className="inline-flex items-center space-x-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors duration-300"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Login</span>
              </Link>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
