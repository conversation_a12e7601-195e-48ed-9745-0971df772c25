import React, { useState, useEffect, useMemo } from 'react';
import {
  Users, DollarSign, ShoppingBag, Eye, Filter, Download,
  Shield, TrendingUp, Award, CheckCircle
} from 'lucide-react';
import DashboardCard from '../components/DashboardCard';
import DashboardSection from '../components/DashboardSection';
import TargetCard from '../components/TargetCard';
import EnhancedStatsCard from '../components/EnhancedStatsCard';

import ClientScoreDistributionChart from '../components/charts/ClientScoreDistributionChart';
import LTVSegmentChart from '../components/charts/LTVSegmentChart';
import DataQualityCorrelationChart from '../components/charts/DataQualityCorrelationChart';
import ClientAcquisitionTrendsChart from '../components/charts/ClientAcquisitionTrendsChart';
import DistributionChart from '../components/charts/DistributionChart';
import ScoreDistributionChart from '../components/charts/ScoreDistributionChart';
import TopPerformingClientsTable from '../components/TopPerformingClientsTable';
import ClientsNeedingAttentionTable from '../components/ClientsNeedingAttentionTable';
import ChartErrorBoundary from '../components/ChartErrorBoundary';
import ChartLoadingSkeleton from '../components/ChartLoadingSkeleton';
import { useClients } from '../contexts/ClientContext';
import { calculateDashboardAnalytics } from '../utils/dashboardAnalytics';
import { apiService } from '../services/api';

interface DashboardStats {
  overview: {
    total_clients: number;
    total_revenue: number;
    total_deals: number;
    monthly_growth: number;
  };
  revenue: {
    current_month: number;
    last_month: number;
    growth_percentage: number;
  };
  clients: {
    total: number;
    new_this_month: number;
  };
  leads: {
    total: number;
    hot: number;
  };
  conversion: {
    total_leads: number;
    converted: number;
    rate: number;
  };
  target: {
    target: number;
    achieved: number;
    completion_percentage: number;
  };
}

const Dashboard: React.FC = () => {
  const { allClients, loading: clientsLoading } = useClients();
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [revenueData, setRevenueData] = useState<any[]>([]);
  const [leadSources, setLeadSources] = useState<any[]>([]);
  const [topClients, setTopClients] = useState<any[]>([]);
  const [dealsStatus, setDealsStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Get analytics data from API or calculate from client data
  const analytics = useMemo(() => {
    // Check if we have API analytics data
    const apiAnalytics = (window as any).dashboardAnalytics;
    if (apiAnalytics && apiAnalytics.totalClients !== undefined) {
      return {
        totalClients: apiAnalytics.totalClients || 0,
        totalTransactions: apiAnalytics.totalTransactions || 0,
        totalTransactionValue: apiAnalytics.totalTransactionValue || 0,
        averageOverallScore: apiAnalytics.averageOverallScore || 0,
        phoneScoreDistribution: apiAnalytics.phoneScoreDistribution || [],
        emailScoreDistribution: apiAnalytics.emailScoreDistribution || [],
        scoreDistribution: apiAnalytics.overallScoreDistribution || [],
        // Add mock data for other required fields
        dataQualityDistribution: {
          excellent: Math.floor(apiAnalytics.totalClients * 0.3),
          good: Math.floor(apiAnalytics.totalClients * 0.4),
          fair: Math.floor(apiAnalytics.totalClients * 0.2),
          poor: Math.floor(apiAnalytics.totalClients * 0.1),
        },
        verificationStatus: {
          emailVerified: Math.floor(apiAnalytics.totalClients * 0.7),
          phoneVerified: Math.floor(apiAnalytics.totalClients * 0.6),
          bothVerified: Math.floor(apiAnalytics.totalClients * 0.5),
        },
        highValueClients: Math.floor(apiAnalytics.totalClients * 0.15),
        ltvSegmentData: [
          { name: 'Silver', value: Math.floor(apiAnalytics.totalClients * 0.5), percentage: 50, color: '#8b5cf6' },
          { name: 'Gold', value: Math.floor(apiAnalytics.totalClients * 0.3), percentage: 30, color: '#3b82f6' },
          { name: 'Gold+', value: Math.floor(apiAnalytics.totalClients * 0.15), percentage: 15, color: '#10b981' },
          { name: 'Platinum', value: Math.floor(apiAnalytics.totalClients * 0.05), percentage: 5, color: '#f59e0b' },
        ],
        // Mock data for other distributions
        phoneCarrierDistribution: [
          { carrier: 'Digi', count: Math.floor(apiAnalytics.totalClients * 0.4), percentage: 40, color: '#3b82f6' },
          { carrier: 'Maxis', count: Math.floor(apiAnalytics.totalClients * 0.3), percentage: 30, color: '#10b981' },
          { carrier: 'Celcom', count: Math.floor(apiAnalytics.totalClients * 0.2), percentage: 20, color: '#f59e0b' },
          { carrier: 'U Mobile', count: Math.floor(apiAnalytics.totalClients * 0.1), percentage: 10, color: '#ef4444' },
        ],
        utmSourceDistribution: [
          { source: 'TikTok', count: Math.floor(apiAnalytics.totalClients * 0.4), percentage: 40, color: '#3b82f6' },
          { source: 'Facebook', count: Math.floor(apiAnalytics.totalClients * 0.3), percentage: 30, color: '#10b981' },
          { source: 'Instagram', count: Math.floor(apiAnalytics.totalClients * 0.2), percentage: 20, color: '#f59e0b' },
          { source: 'Direct', count: Math.floor(apiAnalytics.totalClients * 0.1), percentage: 10, color: '#ef4444' },
        ],
        engagementLevelDistribution: [
          { level: 'Cold', count: Math.floor(apiAnalytics.totalClients * 0.6), percentage: 60, color: '#f59e0b' },
          { level: 'Warm', count: Math.floor(apiAnalytics.totalClients * 0.25), percentage: 25, color: '#3b82f6' },
          { level: 'Active', count: Math.floor(apiAnalytics.totalClients * 0.1), percentage: 10, color: '#10b981' },
          { level: 'Frozen', count: Math.floor(apiAnalytics.totalClients * 0.05), percentage: 5, color: '#ef4444' },
        ],
        categoryDistribution: [
          { category: 'First Timer', count: Math.floor(apiAnalytics.totalClients * 0.8), percentage: 80, color: '#3b82f6' },
          { category: 'Returning', count: Math.floor(apiAnalytics.totalClients * 0.15), percentage: 15, color: '#10b981' },
          { category: 'VIP', count: Math.floor(apiAnalytics.totalClients * 0.05), percentage: 5, color: '#f59e0b' },
        ],
      };
    }

    // Fallback to calculating from client data
    if (allClients.length === 0) {
      return null;
    }

    return calculateDashboardAnalytics(allClients);
  }, [allClients]);

  useEffect(() => {
    let isMounted = true;

    const loadDashboardData = async () => {
      if (isMounted) {
        await fetchDashboardData();
      }
    };

    loadDashboardData();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - only run once

  // Listen for statistics refresh events
  useEffect(() => {
    const handleRefreshStatistics = () => {

      fetchDashboardData();
    };

    window.addEventListener('refreshStatistics', handleRefreshStatistics);

    return () => {
      window.removeEventListener('refreshStatistics', handleRefreshStatistics);
    };
  }, []);

  // Handle client navigation
  const handleViewClient = (client: any) => {
    // Navigate to client details page
    window.location.href = `/clients/${client.id}`;
  };

  const handleContactClient = (client: any) => {
    // Open contact modal or navigate to contact page

  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch all dashboard data using API service
      const [stats, revenue, sources, clients, deals] = await Promise.all([
        apiService.getDashboardStats(),
        apiService.getRevenueAnalytics(),
        apiService.getLeadSources(),
        apiService.getTopClients(),
        apiService.getDealsStatus()
      ]);

      setDashboardStats(stats);
      setRevenueData(revenue.weekly || []);
      setLeadSources(sources);
      setTopClients(clients);
      setDealsStatus(deals);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Set mock data as fallback
      setMockData();
    } finally {
      setLoading(false);
    }
  };

  const setMockData = () => {
    setDashboardStats({
      overview: { total_clients: 50, total_revenue: 85316, total_deals: 100, monthly_growth: 30 },
      revenue: { current_month: 85316, last_month: 65000, growth_percentage: 31.3 },
      clients: { total: 50, new_this_month: 12 },
      leads: { total: 25, hot: 8 },
      conversion: { total_leads: 25, converted: 12, rate: 48.0 },
      target: { target: 100000, achieved: 75000, completion_percentage: 75 }
    });

    setRevenueData([
      { period: 'Jan 1', revenue: 12000 },
      { period: 'Jan 8', revenue: 15000 },
      { period: 'Jan 15', revenue: 18000 },
      { period: 'Jan 22', revenue: 22000 },
      { period: 'Jan 29', revenue: 18316 }
    ]);

    setLeadSources([
      { name: 'Facebook', value: 11, color: '#3b82f6' },
      { name: 'Instagram', value: 14, color: '#10b981' },
      { name: 'TikTok', value: 20, color: '#f59e0b' },
      { name: 'Google', value: 12, color: '#ef4444' },
      { name: 'YouTube', value: 15, color: '#8b5cf6' },
      { name: 'WhatsApp', value: 14, color: '#06b6d4' },
      { name: 'Referral', value: 13, color: '#84cc16' }
    ]);

    setTopClients([
      { id: 1, name: 'Fatimah binti Omar', email: '<EMAIL>', total_spent: 2400 },
      { id: 2, name: 'Zainab binti Jalal', email: '<EMAIL>', total_spent: 1600 },
      { id: 3, name: 'Muhammad bin Ali', email: '<EMAIL>', total_spent: 1300 },
      { id: 4, name: 'Aisha binti Raju', email: '<EMAIL>', total_spent: 2000 },
      { id: 5, name: 'Ruqayyah binti Ali', email: '<EMAIL>', total_spent: 1100 }
    ]);

    setDealsStatus({
      status: { successful: 29, pending: 32, failed: 28, hot_leads: 11 },
      success_rate: 42.5,
      total: 100
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!dashboardStats) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400">Failed to load dashboard data</p>
          <button
            onClick={fetchDashboardData}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Show loading state if data is not ready
  if (loading || clientsLoading || !analytics) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-lg text-gray-600 dark:text-gray-400">Loading dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Welcome back, Admin!</h1>
          <p className="text-gray-600 dark:text-gray-300">Track your sales activity, leads and deals here.</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
          <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 min-h-[44px]">
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </button>
          <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2 min-h-[44px]">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Row 1: Client & Transaction Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <EnhancedStatsCard
            title="Total Clients"
            value={analytics?.totalClients?.toLocaleString() || '0'}
            subtitle="Complete client dataset"
            icon={Users}
            iconColor="text-white"
            score={analytics?.averageOverallScore}
            scoreLabel="Avg Score"
            className="cursor-pointer hover:shadow-md transition-shadow bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg"
          />

          <EnhancedStatsCard
            title="Total Transactions"
            value={analytics?.totalTransactions?.toLocaleString() || '0'}
            subtitle="All client transactions"
            icon={ShoppingBag}
            iconColor="text-green-600"
            className="cursor-pointer hover:shadow-md transition-shadow"
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <EnhancedStatsCard
            title="Transaction Value"
            value={`RM ${(analytics?.totalTransactionValue || 0).toLocaleString()}`}
            subtitle="Total revenue generated"
            icon={DollarSign}
            iconColor="text-emerald-600"
            className="cursor-pointer hover:shadow-md transition-shadow"
          />

          <EnhancedStatsCard
            title="Avg Client Score"
            value={Math.round(analytics?.averageOverallScore || 0).toString()}
            subtitle="Overall data quality"
            icon={Award}
            iconColor="text-purple-600"
            className="cursor-pointer hover:shadow-md transition-shadow"
          />
        </div>
      </div>

      {/* Row 2: Score, Carrier & UTM Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <DashboardSection
          title="Overall Score Distribution"
          subtitle="Client performance breakdown"
        >
          <ChartErrorBoundary>
            {loading || clientsLoading || !analytics ? (
              <ChartLoadingSkeleton height={240} type="bar" />
            ) : (
              <ClientScoreDistributionChart
                data={analytics?.scoreDistribution || []}
                height={240}
              />
            )}
          </ChartErrorBoundary>
        </DashboardSection>

        <DashboardSection
          title="Phone Carrier Distribution"
          subtitle="Breakdown by carrier types"
        >
          {loading || clientsLoading || !analytics ? (
            <ChartLoadingSkeleton height={240} type="pie" />
          ) : (
            <DistributionChart
              data={analytics?.phoneCarrierDistribution?.map(item => ({
                name: item.carrier,
                value: item.count,
                percentage: item.percentage,
                color: item.color
              })) || []}
              height={240}
              type="pie"
            />
          )}
        </DashboardSection>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <DashboardSection
          title="UTM Source Distribution"
          subtitle="Traffic source breakdown"
        >
          {loading || clientsLoading || !analytics ? (
            <ChartLoadingSkeleton height={240} type="bar" />
          ) : (
            <DistributionChart
              data={analytics?.utmSourceDistribution?.map(item => ({
                name: item.source,
                value: item.count,
                percentage: item.percentage,
                color: item.color
              })) || []}
              height={240}
              type="bar"
            />
          )}
        </DashboardSection>

        <DashboardSection
          title="Email Score Distribution"
          subtitle="Email verification scores"
        >
          <ChartErrorBoundary>
            {loading || clientsLoading || !analytics ? (
              <ChartLoadingSkeleton height={240} type="bar" />
            ) : (
              <ScoreDistributionChart
                data={analytics?.emailScoreDistribution || []}
                height={240}
                scoreType="email"
              />
            )}
          </ChartErrorBoundary>
        </DashboardSection>
      </div>

      {/* Row 3: Category, LTV & Engagement Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <DashboardSection
          title="Category Distribution"
          subtitle="Client category breakdown"
        >
          {loading || clientsLoading || !analytics ? (
            <ChartLoadingSkeleton height={240} type="bar" />
          ) : (
            <DistributionChart
              data={analytics?.categoryDistribution?.map(item => ({
                name: item.category,
                value: item.count,
                percentage: item.percentage,
                color: item.color
              })) || []}
              height={240}
              type="bar"
            />
          )}
        </DashboardSection>

        <DashboardSection
          title="LTV Segments"
          subtitle="Value tier distribution"
        >
          {loading || clientsLoading || !analytics ? (
            <ChartLoadingSkeleton height={240} type="bar" />
          ) : (
            <LTVSegmentChart
              data={analytics?.ltvSegmentData || []}
              height={240}
            />
          )}
        </DashboardSection>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <DashboardSection
          title="Engagement Levels"
          subtitle="Client engagement status"
        >
          {loading || clientsLoading || !analytics ? (
            <ChartLoadingSkeleton height={240} type="pie" />
          ) : (
            <DistributionChart
              data={analytics?.engagementLevelDistribution?.map(item => ({
                name: item.level,
                value: item.count,
                percentage: item.percentage,
                color: item.color
              })) || []}
              height={240}
              type="pie"
            />
          )}
        </DashboardSection>

        <DashboardSection
          title="Data Quality Distribution"
          subtitle="Quality assessment breakdown"
        >
          {loading || clientsLoading || !analytics ? (
            <ChartLoadingSkeleton height={240} type="pie" />
          ) : (
            <DistributionChart
              data={analytics ? [
                { name: 'Excellent', value: analytics.dataQualityDistribution.excellent, percentage: Math.round((analytics.dataQualityDistribution.excellent / analytics.totalClients) * 100), color: '#10b981' },
                { name: 'Good', value: analytics.dataQualityDistribution.good, percentage: Math.round((analytics.dataQualityDistribution.good / analytics.totalClients) * 100), color: '#3b82f6' },
                { name: 'Fair', value: analytics.dataQualityDistribution.fair, percentage: Math.round((analytics.dataQualityDistribution.fair / analytics.totalClients) * 100), color: '#f59e0b' },
                { name: 'Poor', value: analytics.dataQualityDistribution.poor, percentage: Math.round((analytics.dataQualityDistribution.poor / analytics.totalClients) * 100), color: '#ef4444' }
              ] : []}
              height={240}
              type="pie"
            />
          )}
        </DashboardSection>
      </div>

      {/* Row 4: Top Performing Clients Table */}
      <div className="mb-6">
        <TopPerformingClientsTable
          clients={analytics?.topPerformingClients || []}
          onViewClient={handleViewClient}
        />
      </div>

      {/* Row 5: Clients Needing Attention Table */}
      <div className="mb-6">
        <ClientsNeedingAttentionTable
          clients={analytics?.clientsNeedingAttention || []}
          onViewClient={handleViewClient}
          onContactClient={handleContactClient}
        />
      </div>

    </div>
  );
};

export default Dashboard;