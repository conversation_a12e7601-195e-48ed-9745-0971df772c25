import React, { useState, useMemo, useEffect } from 'react';
import {
  Plus, Search, Filter, Eye, Mail, MessageCircle,
  Edit, Trash2, ChevronDown, X, ChevronLeft, ChevronRight,
  Users, DollarSign, TrendingUp, AlertTriangle,
  List, LayoutGrid
} from 'lucide-react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { useClients, Client } from '../contexts/ClientContext';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { useToast } from '../contexts/ToastContext';
import { useViewState } from '../contexts/ViewStateContext';
import CustomDropdown from '../components/CustomDropdown';
import ActionButtons from '../components/ActionButtons';
import ClientSidebar from '../components/ClientSidebar';
import ClientCardView from '../components/ClientCardView';
import { apiService } from '../services/api';

const Clients: React.FC = () => {
  const {
    clients,
    deleteClient,
    loading,
    pagination,
    filters,
    loadClients,
    setFilters
  } = useClients();
  const { confirm } = useConfirmation();
  const { showSuccess, showError } = useToast();
  const { clientsViewMode, setClientsViewMode } = useViewState();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    category: '',
    ltvSegment: '',
    engagementLevel: '',
    priority: '',
    utmSource: '',
    dataQuality: '',
    phoneCarrier: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedClient, setSelectedClient] = useState<Client | undefined>(undefined);
  const [statistics, setStatistics] = useState({
    total_clients: 0,
    active_clients: 0,
    total_revenue: 0,
    high_priority_clients: 0,
  });

  // Handle view parameter from URL (when returning from client details)
  useEffect(() => {
    const viewParam = searchParams.get('view');
    if (viewParam === 'card' || viewParam === 'table') {
      setClientsViewMode(viewParam);
      // Clear the URL parameter after setting the view
      setSearchParams({});
    }
  }, [searchParams, setClientsViewMode, setSearchParams]);

  // Update items per page based on view mode
  const currentItemsPerPage = clientsViewMode === 'card' ? 50 : 25;

  // Fetch client statistics
  const fetchStatistics = async () => {
    try {
      const stats = await apiService.getClientStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Failed to fetch client statistics:', error);
    }
  };

  // Load statistics when component mounts
  useEffect(() => {
    fetchStatistics();
  }, []);

  // Listen for statistics refresh events
  useEffect(() => {
    const handleRefreshStatistics = () => {

      fetchStatistics();
    };

    window.addEventListener('refreshStatistics', handleRefreshStatistics);

    return () => {
      window.removeEventListener('refreshStatistics', handleRefreshStatistics);
    };
  }, []);

  // Helper function to clean filters by removing empty/undefined values
  const cleanFilters = (filters: Record<string, any>) => {
    const cleaned: Record<string, any> = {};
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        cleaned[key] = value;
      }
    });
    return cleaned;
  };

  // Handle filter changes
  const handleFilterChange = (filterKey: string, value: string) => {
    const newFilters = {
      ...selectedFilters,
      [filterKey]: value
    };
    setSelectedFilters(newFilters);

    // Convert to API format and apply filters
    const apiFilters = cleanFilters({
      search: searchTerm,
      category: newFilters.category,
      ltv_segment: newFilters.ltvSegment,
      engagement_level: newFilters.engagementLevel,
      priority: newFilters.priority,
      utm_source: newFilters.utmSource,
      data_quality: newFilters.dataQuality,
      phone_carrier: newFilters.phoneCarrier,
    });

    setFilters(apiFilters);
    loadClients(1, currentItemsPerPage, apiFilters);
  };

  // Handle search changes
  const handleSearchChange = (search: string) => {
    setSearchTerm(search);

    const apiFilters = cleanFilters({
      search: search,
      category: selectedFilters.category,
      ltv_segment: selectedFilters.ltvSegment,
      engagement_level: selectedFilters.engagementLevel,
      priority: selectedFilters.priority,
      utm_source: selectedFilters.utmSource,
      data_quality: selectedFilters.dataQuality,
      phone_carrier: selectedFilters.phoneCarrier,
    });

    setFilters(apiFilters);
    loadClients(1, currentItemsPerPage, apiFilters);
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    loadClients(page, currentItemsPerPage, filters);
  };

  // Use clients directly from context (already paginated)
  const paginatedClients = clients;
  const totalPages = pagination?.lastPage || 1;
  const currentPage = pagination?.currentPage || 1;

  const clearFilters = () => {
    const clearedFilters = {
      category: '',
      ltvSegment: '',
      engagementLevel: '',
      priority: '',
      utmSource: '',
      dataQuality: '',
      phoneCarrier: '',
    };
    setSelectedFilters(clearedFilters);
    setSearchTerm('');

    // Clear filters and reload clients with no filters
    const emptyFilters = {};
    setFilters(emptyFilters);
    loadClients(1, currentItemsPerPage, emptyFilters);
  };

  const toggleClientSelection = (clientId: string) => {
    setSelectedClients(prev => 
      prev.includes(clientId) 
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    );
  };

  const selectAllClients = () => {
    setSelectedClients(paginatedClients.map(client => client.id));
  };

  const clearSelection = () => {
    setSelectedClients([]);
  };

  const handleBulkDelete = async () => {
    const confirmed = await confirm({
      title: 'Delete Clients',
      message: `Are you sure you want to delete ${selectedClients.length} client(s)? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        for (const clientId of selectedClients) {
          await deleteClient(clientId);
        }
        showSuccess(`Successfully deleted ${selectedClients.length} client(s)`);
        setSelectedClients([]);
      } catch (error) {
        console.error('Error deleting clients:', error);
        showError('Failed to delete clients');
      }
    }
  };

  const handleCreate = () => {
    setSelectedClient(undefined);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleView = (client: Client) => {
    navigate(`/clients/${client.id}`);
  };

  const handleEdit = (client: Client) => {
    setSelectedClient(client);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDelete = async (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    const clientName = client?.name || 'this client';

    const confirmed = await confirm({
      title: 'Delete Client',
      message: `Are you sure you want to delete ${clientName}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        deleteClient(clientId);
        showSuccess('Client Deleted', `${clientName} has been successfully deleted.`);
      } catch (error) {
        showError('Delete Failed', 'Failed to delete the client. Please try again.');
      }
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedClient(undefined);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hot': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Warm': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Cold': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      case 'Frozen': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      case 'High': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Low': return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'Platinum': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300';
      case 'Gold+': return 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300';
      case 'Gold': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'Silver': return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  // Statistics calculations - use API statistics for accurate counts
  const totalClients = statistics.total_clients;
  const activeClients = statistics.active_clients;
  const totalRevenue = statistics.total_revenue;
  const highPriorityClients = statistics.high_priority_clients;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Clients</h1>
          <p className="text-gray-600 dark:text-gray-300">Manage your client relationships and interactions</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button
            onClick={handleCreate}
            className="btn-primary w-full sm:w-auto flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Client</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Clients</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalClients}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Clients</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{activeClients}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">RM {totalRevenue.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <DollarSign className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">High Priority</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{highPriorityClients}</p>
            </div>
            <div className="p-3 bg-red-100 dark:bg-red-900 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-colors duration-300">
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          {/* Clear Filters Button */}
          {(searchTerm || Object.values(selectedFilters).some(filter => filter)) && (
            <button
              onClick={clearFilters}
              className="flex items-center justify-center space-x-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 min-h-[44px] sm:min-h-auto"
            >
              <X className="w-4 h-4" />
              <span>Clear filters</span>
            </button>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              placeholder="Search clients by name, email, phone, or UUID..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 min-h-[44px] bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2 min-h-[44px] text-gray-700 dark:text-gray-300"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
              <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>

            <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setClientsViewMode('table')}
                className={`p-2 rounded-md transition-colors ${
                  clientsViewMode === 'table'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setClientsViewMode('card')}
                className={`p-2 rounded-md transition-colors ${
                  clientsViewMode === 'card'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-7 gap-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Categories' },
                  { value: 'First Timer', label: 'First Timer' },
                  { value: 'Retainer', label: 'Retainer' },
                  { value: 'Loyal', label: 'Loyal' },
                  { value: 'Advocator', label: 'Advocator' },
                ]}
                value={selectedFilters.category}
                onChange={(value) => handleFilterChange('category', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">LTV Segment</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Segments' },
                  { value: 'Silver', label: 'Silver' },
                  { value: 'Gold', label: 'Gold' },
                  { value: 'Gold+', label: 'Gold+' },
                  { value: 'Platinum', label: 'Platinum' },
                ]}
                value={selectedFilters.ltvSegment}
                onChange={(value) => handleFilterChange('ltvSegment', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Engagement</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Levels' },
                  { value: 'Hot', label: 'Hot' },
                  { value: 'Warm', label: 'Warm' },
                  { value: 'Cold', label: 'Cold' },
                  { value: 'Frozen', label: 'Frozen' },
                ]}
                value={selectedFilters.engagementLevel}
                onChange={(value) => handleFilterChange('engagementLevel', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Priorities' },
                  { value: 'High', label: 'High' },
                  { value: 'Medium', label: 'Medium' },
                  { value: 'Low', label: 'Low' },
                ]}
                value={selectedFilters.priority}
                onChange={(value) => handleFilterChange('priority', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">UTM Source</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Sources' },
                  { value: 'facebook', label: 'Facebook' },
                  { value: 'instagram', label: 'Instagram' },
                  { value: 'tiktok', label: 'TikTok' },
                  { value: 'google', label: 'Google' },
                  { value: 'youtube', label: 'YouTube' },
                  { value: 'whatsapp', label: 'WhatsApp' },
                  { value: 'referral', label: 'Referral' },
                ]}
                value={selectedFilters.utmSource}
                onChange={(value) => handleFilterChange('utmSource', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Data Quality</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Quality' },
                  { value: 'Excellent', label: 'Excellent' },
                  { value: 'Good', label: 'Good' },
                  { value: 'Fair', label: 'Fair' },
                  { value: 'Poor', label: 'Poor' },
                ]}
                value={selectedFilters.dataQuality}
                onChange={(value) => handleFilterChange('dataQuality', value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone Carrier</label>
              <CustomDropdown
                options={[
                  { value: '', label: 'All Carriers' },
                  { value: 'Maxis', label: 'Maxis' },
                  { value: 'Celcom', label: 'Celcom' },
                  { value: 'Digi', label: 'Digi' },
                  { value: 'U Mobile', label: 'U Mobile' },
                  { value: 'TuneTalk', label: 'TuneTalk' },
                  { value: 'Yes', label: 'Yes' },
                  { value: 'Altel', label: 'Altel' },
                  { value: 'XOX', label: 'XOX' },
                  { value: 'Other', label: 'Other' },
                ]}
                value={selectedFilters.phoneCarrier}
                onChange={(value) => handleFilterChange('phoneCarrier', value)}
              />
            </div>

            <div className="md:col-span-2 lg:col-span-3 xl:col-span-7 flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {pagination ? `${pagination.from}-${pagination.to} of ${pagination.total}` : '0'} clients
              </span>
            </div>
          </div>
        )}

      {/* Bulk Actions */}
      {selectedClients.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-700">
                {selectedClients.length} clients selected
              </span>
              <button
                onClick={clearSelection}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Clear selection
              </button>
            </div>
            {/* Mobile: 2x2 Grid Layout, Desktop: Horizontal Layout */}
            <div className="grid grid-cols-2 gap-2 sm:flex sm:items-center sm:space-x-2 sm:gap-0">
              <button
                onClick={handleBulkDelete}
                className="px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md hover:bg-red-200 dark:hover:bg-red-800/40 transition-colors text-sm font-medium"
              >
                Delete
              </button>
              <button className="px-3 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800/40 transition-colors text-sm font-medium">
                Send Email
              </button>
              <button className="px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-md hover:bg-green-200 dark:hover:bg-green-800/40 transition-colors text-sm font-medium">
                WhatsApp
              </button>
              <button className="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm font-medium">
                Export
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Clients Content */}
      {clientsViewMode === 'card' ? (
        <div>
          <ClientCardView
            clients={paginatedClients}
            onView={handleView}
            onEdit={handleEdit}
            onDelete={handleDelete}
            selectedClients={selectedClients}
            onToggleSelection={toggleClientSelection}
          />

          {/* Pagination Controls for Card View */}
          {pagination && pagination.total > 0 && (
            <div className="mt-6 flex items-center justify-between bg-white p-4 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-700">
                  {pagination ? `Showing ${pagination.from} to ${pagination.to} of ${pagination.total} clients` : 'Loading...'}
                </span>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-700 dark:text-gray-300">Show:</label>
                  <div className="w-20">
                    <CustomDropdown
                      options={[
                        { value: '10', label: '10' },
                        { value: '25', label: '25' },
                        { value: '50', label: '50' },
                        { value: '100', label: '100' },
                      ]}
                      value={currentItemsPerPage.toString()}
                      onChange={(value) => {
                        const newPerPage = Number(value);
                        loadClients(1, newPerPage, filters);
                      }}
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">per page</span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`px-3 py-1 border rounded-md text-sm ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Mobile View */}
          <div className="block md:hidden">
            {paginatedClients.map((client) => (
              <div key={client.id} className="border-b border-gray-200 dark:border-gray-700 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-start space-x-4">
                  <input
                    type="checkbox"
                    className="mt-1 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                    checked={selectedClients.includes(client.id)}
                    onChange={() => toggleClientSelection(client.id)}
                  />

                  {/* Larger Avatar */}
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-lg font-bold">
                      {client.name?.charAt(0)?.toUpperCase() || 'C'}
                    </span>
                  </div>

                  {/* Name, Email, Badges, and Action Buttons */}
                  <div className="flex-1 min-w-0">
                    {/* Name and Email - Left aligned next to avatar */}
                    <div className="mb-2">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {client.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {client.email || 'No email'}
                      </p>
                    </div>

                    {/* Badges Row - Category, Priority, LTV */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                        {client.category || 'N/A'}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                        {client.priority || 'Medium'}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                        {client.ltvSegment || 'Silver'}
                      </span>
                    </div>

                    {/* Action Buttons Row */}
                    <div className="flex justify-start">
                      <ActionButtons
                        size="sm"
                        actions={[
                          {
                            label: 'View',
                            icon: Eye,
                            onClick: () => handleView(client),
                            color: 'blue'
                          },
                          {
                            label: 'Edit',
                            icon: Edit,
                            onClick: () => handleEdit(client),
                            color: 'green'
                          },
                          {
                            label: 'Send Email',
                            icon: Mail,
                            onClick: () =>,
                            color: 'blue'
                          },
                          {
                            label: 'Send WhatsApp',
                            icon: MessageCircle,
                            onClick: () =>,
                            color: 'green'
                          },
                          {
                            label: 'Delete',
                            icon: Trash2,
                            onClick: () => handleDelete(client.id),
                            color: 'red'
                          },
                        ]}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                      checked={paginatedClients.length > 0 && selectedClients.length === paginatedClients.length}
                      onChange={paginatedClients.length > 0 && selectedClients.length === paginatedClients.length ? clearSelection : selectAllClients}
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    LTV Segment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Engagement
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Spent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedClients.map((client) => (
                  <tr key={client.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        checked={selectedClients.includes(client.id)}
                        onChange={() => toggleClientSelection(client.id)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <Link
                          to={`/clients/${client.id}`}
                          className="p-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors inline-block"
                          title="View Client Details"
                        >
                          <Eye className="w-4 h-4" />
                        </Link>
                        <div className="w-10 h-10 bg-blue-600 rounded-md flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {client.name.split(' ')[0][0]}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{client.name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{client.utmSource}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{client.email}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">{client.phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(client.category)}`}>
                        {client.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(client.ltvSegment)}`}>
                        {client.ltvSegment}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(client.engagementLevel)}`}>
                        {client.engagementLevel}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(client.priority)}`}>
                        {client.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      RM {client.totalSpent.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex justify-start">
                        <ActionButtons
                          actions={[
                            {
                              label: 'Edit',
                              icon: Edit,
                              onClick: () => handleEdit(client),
                              color: 'green'
                            },
                            {
                              label: 'Send Email',
                              icon: Mail,
                              onClick: () =>,
                              color: 'blue'
                            },
                            {
                              label: 'Send WhatsApp',
                              icon: MessageCircle,
                              onClick: () =>,
                              color: 'green'
                            },
                            {
                              label: 'Delete',
                              icon: Trash2,
                              onClick: () => handleDelete(client.id),
                              color: 'red'
                            }
                          ]}
                        />
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination Controls */}
          {pagination && pagination.total > 0 && (
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-700">
                    {pagination ? `Showing ${pagination.from} to ${pagination.to} of ${pagination.total} clients` : 'Loading...'}
                  </span>
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">Show:</label>
                    <div className="w-20">
                      <CustomDropdown
                        options={[
                          { value: '10', label: '10' },
                          { value: '25', label: '25' },
                          { value: '50', label: '50' },
                          { value: '100', label: '100' },
                        ]}
                        value={currentItemsPerPage.toString()}
                        onChange={(value) => {
                          const newPerPage = Number(value);
                          loadClients(1, newPerPage, filters);
                        }}
                      />
                    </div>
                    <span className="text-sm text-gray-700">per page</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`px-3 py-1 border rounded-md text-sm ${
                            currentPage === pageNum
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'border-gray-300 hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Client Sidebar */}
      {isModalOpen && (
        <ClientSidebar
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          client={selectedClient}
          mode={modalMode}
        />
      )}

    </div>
  );
};

export default Clients;