import React, { useState } from 'react';
import {
  Plus, Search, Edit, Trash2, Eye, Copy, X,
  BarChart3, FormInput, Share
} from 'lucide-react';
import CustomDropdown from '../components/CustomDropdown';
import FormSidebar from '../components/FormSidebar';
import ActionButtons from '../components/ActionButtons';
import { useConfirmation } from '../contexts/ConfirmationContext';
import { useToast } from '../contexts/ToastContext';

interface FormField {
  id: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'textarea' | 'checkbox';
  label: string;
  required: boolean;
  options?: string[];
}

interface Form {
  id: string;
  name: string;
  description: string;
  fields: FormField[];
  linkedProduct?: string;
  status: 'Active' | 'Draft' | 'Archived';
  submissions: number;
  conversionRate: number;
  createdAt: Date;
  updatedAt: Date;
}

const Forms: React.FC = () => {
  // Mock form data
  const mockForms: Form[] = [
    {
      id: 'form-1',
      name: 'Islamic Book Interest Form',
      description: 'Capture leads interested in Islamic books for teenagers',
      fields: [
        { id: '1', type: 'text', label: 'Full Name', required: true },
        { id: '2', type: 'email', label: 'Email Address', required: true },
        { id: '3', type: 'phone', label: 'Phone Number', required: false },
        { id: '4', type: 'select', label: 'Age Group', required: true, options: ['13-15', '16-18', '19-21'] },
        { id: '5', type: 'textarea', label: 'What topics interest you most?', required: false },
      ],
      linkedProduct: 'Islamic Book Bundle',
      status: 'Active',
      submissions: 156,
      conversionRate: 23.5,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-15'),
    },
    {
      id: 'form-2',
      name: 'Save Gaza Donation Form',
      description: 'Collect donations for the Save Gaza campaign',
      fields: [
        { id: '1', type: 'text', label: 'Full Name', required: true },
        { id: '2', type: 'email', label: 'Email Address', required: true },
        { id: '3', type: 'phone', label: 'Phone Number', required: true },
        { id: '4', type: 'select', label: 'Donation Amount', required: true, options: ['RM50', 'RM100', 'RM250', 'Custom'] },
        { id: '5', type: 'checkbox', label: 'I want to receive updates about the campaign', required: false },
      ],
      linkedProduct: 'Save Gaza Donation',
      status: 'Active',
      submissions: 234,
      conversionRate: 45.2,
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-18'),
    },
    {
      id: 'form-3',
      name: 'Youth Seminar Registration',
      description: 'Registration form for Islamic youth seminars',
      fields: [
        { id: '1', type: 'text', label: 'Full Name', required: true },
        { id: '2', type: 'email', label: 'Email Address', required: true },
        { id: '3', type: 'phone', label: 'Phone Number', required: true },
        { id: '4', type: 'text', label: 'Institution/School', required: false },
        { id: '5', type: 'select', label: 'Preferred Session', required: true, options: ['Morning', 'Afternoon', 'Evening'] },
      ],
      linkedProduct: 'Youth Islamic Seminar',
      status: 'Draft',
      submissions: 0,
      conversionRate: 0,
      createdAt: new Date('2024-01-20'),
      updatedAt: new Date('2024-01-20'),
    },
  ];

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedForm, setSelectedForm] = useState<Form | undefined>(undefined);
  const [forms, setForms] = useState<Form[]>(mockForms);
  const { showConfirmation } = useConfirmation();
  const { showToast } = useToast();

  const filteredForms = forms.filter(form => {
    const matchesSearch = form.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         form.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !selectedStatus || form.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-700';
      case 'Draft':
        return 'bg-yellow-100 text-yellow-700';
      case 'Archived':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const totalForms = forms.length;
  const activeForms = forms.filter(form => form.status === 'Active').length;
  const totalSubmissions = forms.reduce((sum, form) => sum + form.submissions, 0);
  const avgConversionRate = forms.length > 0 ? forms.reduce((sum, form) => sum + form.conversionRate, 0) / forms.length : 0;

  const handleCreate = () => {
    setSelectedForm(undefined);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleView = (form: Form) => {
    setSelectedForm(form);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleEdit = (form: Form) => {
    setSelectedForm(form);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleDelete = (formId: string) => {
    showConfirmation(
      'Delete Form',
      'Are you sure you want to delete this form? This action cannot be undone.',
      () => {
        setForms(forms.filter(form => form.id !== formId));
        showToast('Form deleted successfully', 'success');
      }
    );
  };

  const handleSave = (formData: Form) => {
    if (modalMode === 'create') {
      const newForm: Form = {
        ...formData,
        id: `form-${Date.now()}`,
        submissions: 0,
        conversionRate: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      setForms([...forms, newForm]);
    } else if (modalMode === 'edit' && selectedForm) {
      setForms(forms.map(form => 
        form.id === selectedForm.id 
          ? { ...formData, id: selectedForm.id, updatedAt: new Date() }
          : form
      ));
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedForm(undefined);
  };

  const handleShare = (form: Form) => {
    const shareUrl = `${window.location.origin}/forms/${form.id}`;
    navigator.clipboard.writeText(shareUrl);
    showToast('Form link copied to clipboard!', 'success');
  };

  const handleAnalytics = (form: Form) => {
    showToast(`Analytics for ${form.name}: ${form.submissions} submissions, ${form.conversionRate}% conversion rate`, 'info');
  };

  const handleDuplicate = (form: Form) => {
    const duplicatedForm = {
      ...form,
      id: `form-${Date.now()}`,
      name: `${form.name} (Copy)`,
      submissions: 0,
      conversionRate: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setForms([...forms, duplicatedForm]);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900">Forms</h1>
          <p className="text-gray-600">Create and manage lead generation forms</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
          <button
            onClick={handleCreate}
            className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Create Form</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Forms</p>
              <p className="text-2xl font-bold text-gray-900">{totalForms}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-md">
              <FormInput className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Forms</p>
              <p className="text-2xl font-bold text-gray-900">{activeForms}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-md">
              <Eye className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Submissions</p>
              <p className="text-2xl font-bold text-gray-900">{totalSubmissions}</p>
            </div>
            <div className="p-3 bg-orange-50 rounded-md">
              <BarChart3 className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Conversion</p>
              <p className="text-2xl font-bold text-gray-900">{avgConversionRate.toFixed(1)}%</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-md">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
        <div className="flex items-center space-x-4">
          {/* Clear Filters Button */}
          {(searchTerm || selectedStatus) && (
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedStatus('');
              }}
              className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
            >
              <X className="w-4 h-4" />
              <span>Clear filters</span>
            </button>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search forms..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <CustomDropdown
            options={[
              { value: '', label: 'All Status' },
              { value: 'Active', label: 'Active' },
              { value: 'Draft', label: 'Draft' },
              { value: 'Archived', label: 'Archived' },
            ]}
            value={selectedStatus}
            onChange={setSelectedStatus}
            className="w-32"
          />
        </div>
      </div>

      {/* Forms Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredForms.map((form) => (
          <div key={form.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <span className={`px-2 py-1 text-xs font-medium rounded-md ${getStatusColor(form.status)}`}>
                {form.status}
              </span>
              <ActionButtons
                actions={[
                  {
                    label: 'View',
                    icon: Eye,
                    onClick: () => handleView(form),
                    color: 'blue'
                  },
                  {
                    label: 'Edit',
                    icon: Edit,
                    onClick: () => handleEdit(form),
                    color: 'green'
                  },
                  {
                    label: 'Duplicate',
                    icon: Copy,
                    onClick: () => handleDuplicate(form),
                    color: 'purple'
                  },
                  {
                    label: 'Delete',
                    icon: Trash2,
                    onClick: () => handleDelete(form.id),
                    color: 'red'
                  }
                ]}
              />
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{form.name}</h3>
            <p className="text-sm text-gray-600 mb-4">{form.description}</p>
            
            {form.linkedProduct && (
              <div className="mb-4">
                <span className="text-xs text-gray-500">Linked Product:</span>
                <p className="text-sm font-medium text-blue-600">{form.linkedProduct}</p>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
              <div>
                <span className="text-gray-500">Submissions:</span>
                <span className="ml-2 font-medium">{form.submissions}</span>
              </div>
              <div>
                <span className="text-gray-500">Conversion:</span>
                <span className="ml-2 font-medium">{form.conversionRate}%</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
              <span className="text-xs text-gray-500">
                Updated {form.updatedAt.toLocaleDateString()}
              </span>
              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => handleShare(form)}
                  className="px-3 py-1 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors text-sm flex items-center space-x-1"
                >
                  <Share className="w-3 h-3" />
                  <span>Share</span>
                </button>
                <button 
                  onClick={() => handleAnalytics(form)}
                  className="px-3 py-1 bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors text-sm flex items-center space-x-1"
                >
                  <BarChart3 className="w-3 h-3" />
                  <span>Analytics</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Form Sidebar */}
      {isModalOpen && (
        <FormSidebar
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          form={selectedForm}
          mode={modalMode}
          onSave={handleSave}
        />
      )}
    </div>
  );
};

export default Forms;