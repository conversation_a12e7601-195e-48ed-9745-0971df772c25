import React, { useState } from 'react';
import { DealProvider, useDeal } from '../contexts/DealContext';
import { Deal } from '../types/deal';
import KanbanBoard from '../components/KanbanBoard';
import DealModal from '../components/DealModal';
import ApiStatus from '../components/ApiStatus';
import CustomDropdown from '../components/CustomDropdown';
import { useToast } from '../contexts/ToastContext';
import {
  BarChart3,
  Download,
  Plus,
  Search,
  X,
  Filter,
  ChevronDown,
  List,
  LayoutGrid
} from 'lucide-react';

// Inner component that uses DealContext
const DealsContent: React.FC = () => {
  const { stats } = useDeal();
  const { showInfo } = useToast();
  const [viewMode, setViewMode] = useState<'table' | 'kanban'>('kanban');
  const [showDealModal, setShowDealModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    pipelineStage: '',
    assignedTo: '',
    priority: ''
  });

  const handleCreateDeal = () => {
    setShowDealModal(true);
  };

  const handleExport = () => {
    // Implement export functionality
    showInfo('Coming Soon', 'Export functionality will be implemented soon');
  };

  return (
    <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Deals</h1>
            <div className="flex items-center gap-4">
              <p className="text-gray-600 dark:text-gray-300">Manage your sales pipeline and opportunities</p>
              <ApiStatus />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:space-x-3">
            {/* Action Buttons */}
            <button
              onClick={handleExport}
              className="w-full sm:w-auto px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
            <button
              onClick={handleCreateDeal}
              className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>New Deal</span>
            </button>
          </div>
        </div>

        {/* Quick Actions Bar */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-gray-900">Pipeline Overview</span>
              </div>

              <div className="h-6 w-px bg-gray-300" />

              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full" />
                  <span className="text-gray-600">Active: {stats?.activeDeals || 0} deals</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <span className="text-gray-600">Won: {stats?.wonDeals || 0} deals</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <span className="text-gray-600">Lost: {stats?.lostDeals || 0} deals</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            {/* Clear Filters Button */}
            {(searchTerm || Object.values(selectedFilters).some(filter => filter)) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedFilters({
                    pipelineStage: '',
                    assignedTo: '',
                    priority: ''
                  });
                }}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
              >
                <X className="w-4 h-4" />
                <span>Clear filters</span>
              </button>
            )}

            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search deals by title, description, or client..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
              <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'table'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('kanban')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'kanban'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Filter Options */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 relative z-50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <CustomDropdown
                  options={[
                    { value: '', label: 'All Stages' },
                    { value: 'prospecting', label: 'Prospecting' },
                    { value: 'qualification', label: 'Qualification' },
                    { value: 'proposal', label: 'Proposal' },
                    { value: 'negotiation', label: 'Negotiation' },
                    { value: 'closing', label: 'Closing' },
                    { value: 'won', label: 'Won' },
                    { value: 'lost', label: 'Lost' },
                  ]}
                  value={selectedFilters.pipelineStage}
                  onChange={(value) => setSelectedFilters({ ...selectedFilters, pipelineStage: value })}
                  placeholder="Filter by stage"
                />
                <CustomDropdown
                  options={[
                    { value: '', label: 'All Priorities' },
                    { value: 'Critical', label: 'Critical' },
                    { value: 'High', label: 'High' },
                    { value: 'Medium', label: 'Medium' },
                    { value: 'Low', label: 'Low' },
                  ]}
                  value={selectedFilters.priority}
                  onChange={(value) => setSelectedFilters({ ...selectedFilters, priority: value })}
                  placeholder="Filter by priority"
                />
                <CustomDropdown
                  options={[
                    { value: '', label: 'All Assignees' },
                    // TODO: Add actual users from API
                  ]}
                  value={selectedFilters.assignedTo}
                  onChange={(value) => setSelectedFilters({ ...selectedFilters, assignedTo: value })}
                  placeholder="Filter by assignee"
                />
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        {viewMode === 'kanban' ? (
          <KanbanBoard
            searchTerm={searchTerm}
            selectedFilters={selectedFilters}
          />
        ) : (
          <DealsListView />
        )}

        {/* Deal Modal */}
        {showDealModal && (
          <DealModal
            isOpen={showDealModal}
            onClose={() => setShowDealModal(false)}
            deal={null}
            mode="create"
          />
        )}
      </div>
  );
};

// Main component with provider
const Deals: React.FC = () => {
  return (
    <DealProvider>
      <DealsContent />
    </DealProvider>
  );
};

// List view component for deals
const DealsListView: React.FC = () => {
  const { deals, loading, error } = useDeal();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  const [showDealModal, setShowDealModal] = useState(false);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [dealModalMode, setDealModalMode] = useState<'view' | 'edit' | 'create'>('view');

  // Use all deals since filtering is handled by parent component
  const filteredDeals = deals;

  // Pagination
  const totalPages = Math.ceil(filteredDeals.length / itemsPerPage);
  const paginatedDeals = filteredDeals.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleDealClick = (deal: Deal) => {
    setSelectedDeal(deal);
    setDealModalMode('view');
    setShowDealModal(true);
  };

  const handleEditDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    setDealModalMode('edit');
    setShowDealModal(true);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ms-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(date));
  };

  const getStageColor = (stage: string) => {
    const colors: Record<string, string> = {
      prospecting: 'bg-blue-100 text-blue-700',
      qualification: 'bg-yellow-100 text-yellow-700',
      proposal: 'bg-purple-100 text-purple-700',
      negotiation: 'bg-orange-100 text-orange-700',
      closing: 'bg-indigo-100 text-indigo-700',
      won: 'bg-green-100 text-green-700',
      lost: 'bg-red-100 text-red-700',
    };
    return colors[stage] || 'bg-gray-100 text-gray-700';
  };

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      Critical: 'bg-red-100 text-red-700',
      High: 'bg-orange-100 text-orange-700',
      Medium: 'bg-yellow-100 text-yellow-700',
      Low: 'bg-green-100 text-green-700',
    };
    return colors[priority] || 'bg-gray-100 text-gray-700';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading deals...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️</div>
          <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Deals</h3>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Deals Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Deal
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client/Lead
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stage
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Probability
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expected Close
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedDeals.map((deal) => (
                <tr key={deal.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => handleDealClick(deal)}>
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{deal.title}</div>
                      <div className="text-sm text-gray-500">{deal.dealNumber}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {deal.client?.name || deal.lead?.name || 'No client assigned'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">{formatCurrency(deal.value)}</div>
                    <div className="text-sm text-gray-500">{deal.dealSize}</div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStageColor(deal.pipelineStage)}`}>
                      {deal.pipelineStage}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(deal.priority)}`}>
                      {deal.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${deal.probability}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-900">{deal.probability}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {deal.expectedCloseDate ? formatDate(deal.expectedCloseDate) : 'Not set'}
                    </div>
                  </td>
                  <td className="px-6 py-4" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditDeal(deal)}
                        className="text-blue-600 hover:text-blue-900 text-sm"
                      >
                        Edit
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <p className="text-sm text-gray-700">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredDeals.length)} of {filteredDeals.length} results
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm font-medium text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Deal Modal */}
      {showDealModal && (
        <DealModal
          deal={selectedDeal}
          isOpen={showDealModal}
          mode={dealModalMode}
          onClose={() => {
            setShowDealModal(false);
            setSelectedDeal(null);
            setDealModalMode('view');
          }}
        />
      )}
    </div>
  );
};

export default Deals;
