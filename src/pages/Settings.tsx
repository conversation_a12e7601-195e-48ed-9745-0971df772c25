import React, { useState, useEffect } from 'react';
import {
  Settings as SettingsIcon, Users, Mail, CreditCard,
  Database, Shield, Bell, Save,
  Eye, EyeOff, Plus, Trash2, Edit, AlertTriangle, MoreVertical,
  Upload, Download, FileText, HardDrive, Check, Cog
} from 'lucide-react';
import ToggleSwitch from '../components/ToggleSwitch';
import CustomDropdown from '../components/CustomDropdown';
import ActionButtons from '../components/ActionButtons';
import Badge, { StatusBadge } from '../components/Badge';
import DataImportWizard from '../components/DataImportWizard';
import DataExportModal from '../components/DataExportModal';
import DataClearModal from '../components/DataClearModal';
import SecuritySettings from '../components/SecuritySettings';

import { useConfirmation } from '../contexts/ConfirmationContext';
import { useToast } from '../contexts/ToastContext';
import { useUsers } from '../hooks/useUsers';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import UserModal from '../components/UserModal';
import { useLocation } from 'react-router-dom';

const Settings: React.FC = () => {
  const { confirm } = useConfirmation();
  const { showSuccess, showError, showInfo } = useToast();
  const { users: allUsers, loading: usersLoading, refreshUsers } = useUsers();
  const { user: currentUser, isAdmin, isStaff } = useAuth();

  // Filter out the current user from the users list to prevent self-management through Settings
  // Users should manage their own accounts through My Profile page only
  const users = allUsers.filter(user => user.id !== currentUser?.id);
  const location = useLocation();

  // Check for tab parameter in URL
  const urlParams = new URLSearchParams(location.search);
  const tabFromUrl = urlParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabFromUrl || 'general');
  const [showApiKey, setShowApiKey] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);

  // Data management states
  const [showImportWizard, setShowImportWizard] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showClearModal, setShowClearModal] = useState(false);
  const [importDataType, setImportDataType] = useState('clients');
  const [twoFactorStatus, setTwoFactorStatus] = useState({
    system_enabled: false,
    user_enabled: false,
    requires_2fa: false
  });
  const [importFileFormat, setImportFileFormat] = useState('csv');
  const [emailSettings, setEmailSettings] = useState({
    password: '',
    smtpHost: 'smtp.zoho.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpEncryption: 'tls',
    fromAddress: '',
    fromName: '',
    replyToAddress: '',
    forceRealEmail: false,
    environment: 'local',
    isProduction: false
  });

  // General settings state
  const [generalSettings, setGeneralSettings] = useState({
    organizationName: 'Islamic Books & Merchandise',
    website: 'https://islamicbooks.com',
    contactEmail: '<EMAIL>',
    phoneNumber: '+60 12-345-6789',
    streetAddress: '123 Jalan Merdeka',
    city: 'Kuala Lumpur',
    state: 'Federal Territory',
    postalCode: '50000',
    country: 'Malaysia'
  });

  const [savingSettings, setSavingSettings] = useState(false);
  const [settingsErrors, setSettingsErrors] = useState<Record<string, string>>({});
  const [notificationSettings, setNotificationSettings] = useState({
    loginNotifications: true,
    newClientNotifications: true,
    paymentNotifications: true,
    campaignNotifications: true,
    dailySummary: false
  });

  // Count pending users for badge (excludes current user since they're filtered out)
  const pendingUsersCount = users.filter(user => !user.is_active).length;

  // Load general settings on component mount
  useEffect(() => {
    loadGeneralSettings();
  }, []);

  const loadGeneralSettings = async () => {
    try {
      const response = await apiService.get('/auth/settings/general');
      if (response.data) {
        setGeneralSettings(response.data);
      }
    } catch (error) {
      console.error('Failed to load general settings:', error);
      // Keep default values if loading fails
    }
  };

  const handleGeneralSettingChange = (field: string, value: string) => {
    setGeneralSettings(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear any existing error for this field
    if (settingsErrors[field]) {
      setSettingsErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateGeneralSettings = () => {
    const errors: Record<string, string> = {};

    if (!generalSettings.organizationName.trim()) {
      errors.organizationName = 'Organization name is required';
    }

    if (generalSettings.website && !generalSettings.website.match(/^https?:\/\/.+/)) {
      errors.website = 'Please enter a valid URL';
    }

    if (generalSettings.contactEmail && !generalSettings.contactEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      errors.contactEmail = 'Please enter a valid email address';
    }

    setSettingsErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const saveGeneralSettings = async () => {
    if (!validateGeneralSettings()) {
      return;
    }

    try {
      setSavingSettings(true);
      await apiService.post('/auth/settings/general', generalSettings);
      showSuccess('Settings Saved', 'General settings have been updated successfully');
    } catch (error: any) {
      showError('Save Failed', error.message || 'Failed to save general settings');
    } finally {
      setSavingSettings(false);
    }
  };

  // Auto-save functionality removed - users must manually save changes

  const tabs = [
    { id: 'general', name: 'General', icon: SettingsIcon },
    {
      id: 'users',
      name: 'Users',
      icon: Users,
      badge: pendingUsersCount > 0 ? pendingUsersCount : undefined,
      badgeLabel: pendingUsersCount > 0 ? 'NEW' : undefined
    },
    { id: 'data', name: 'Data', icon: HardDrive },
    { id: 'email', name: 'Email', icon: Mail },
    { id: 'payments', name: 'Payments', icon: CreditCard },
    { id: 'segments', name: 'Segments', icon: Database },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
  ];

  const handleAddUser = () => {
    setEditingUser(null);
    setShowUserModal(true);
  };

  // Data management handlers
  const handleStartImport = () => {
    setShowImportWizard(true);
  };

  const handleImportComplete = () => {
    setShowImportWizard(false);
    showSuccess('Import Complete', 'Data has been successfully imported');
  };

  const handleOpenExportModal = () => {
    setShowExportModal(true);
  };

  const handleOpenClearModal = () => {
    // Only admin can perform bulk data operations
    if (!isAdmin) {
      showError('You do not have permission to perform bulk data operations');
      return;
    }
    setShowClearModal(true);
  };

  // Fetch 2FA status
  const fetch2FAStatus = async () => {
    try {
      const response = await apiService.get('/auth/2fa/status');
      setTwoFactorStatus(response);
    } catch (error: any) {
      showError('Failed to fetch 2FA status');
    }
  };

  // Toggle user 2FA
  const toggleUser2FA = async () => {
    // Don't allow toggling if system-wide 2FA is enabled
    if (twoFactorStatus.system_enabled) {
      showError('Cannot disable 2FA while system-wide 2FA is enabled');
      return;
    }

    try {
      const response = await apiService.post('/auth/2fa/toggle');

      // Force refresh the entire 2FA status to get updated requires_2fa value
      await fetch2FAStatus();

      showSuccess(response.message);

      // Additional debug logging

    } catch (error: any) {
      console.error('Failed to toggle 2FA:', error);
      showError(error.message || 'Failed to toggle 2FA');
    }
  };

  // Toggle system 2FA (admin only)
  const toggleSystem2FA = async () => {
    if (!isAdmin) {
      showError('You do not have permission to change system settings');
      return;
    }

    try {
      const response = await apiService.post('/auth/2fa/toggle-system');

      // Force refresh the entire 2FA status to get updated requires_2fa value
      await fetch2FAStatus();

      showSuccess(response.message);

      // Additional debug logging

    } catch (error: any) {
      console.error('Failed to toggle system 2FA:', error);
      showError(error.message || 'Failed to toggle system 2FA');
    }
  };

  // Fetch 2FA status and email settings on component mount
  useEffect(() => {
    fetch2FAStatus();
    fetchEmailSettings();
  }, []);

  const fetchEmailSettings = async () => {
    try {

      const response = await apiService.getEmailSettings();

      setEmailSettings(response);
    } catch (error: any) {
      // Use default values if settings don't exist yet

    }
  };

  const saveEmailSettings = async () => {
    try {
      // Validate email configuration
      if (!emailSettings.fromAddress) {
        showError('Validation Error', 'From Address is required');
        return;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailSettings.fromAddress)) {
        showError('Validation Error', 'From Address must be a valid email address');
        return;
      }

      // Validate Zoho configuration only if password is provided
      if (emailSettings.password) {
        if (!emailSettings.smtpUsername) {
          showError('Validation Error', 'SMTP Username is required when using Zoho');
          return;
        }

        if (!emailSettings.smtpUsername.includes('@')) {
          showError('Validation Error', 'SMTP Username should be your Zoho email address');
          return;
        }
      }

      await apiService.saveEmailSettings(emailSettings);
      showSuccess('Email Settings Saved', 'Zoho email configuration has been saved successfully');

      // Refresh settings from server to verify they were saved
      await fetchEmailSettings();

    } catch (error: any) {
      console.error('Failed to save email settings:', error);
      showError('Save Failed', error.message || 'Failed to save email settings');
    }
  };

  const testEmailConfiguration = async () => {
    try {
      // Validate configuration before testing
      if (!emailSettings.password || !emailSettings.smtpUsername || !emailSettings.fromAddress) {
        showError('Configuration Incomplete', 'Please save your email settings before testing');
        return;
      }

      await apiService.testEmailConfiguration(emailSettings.fromAddress);
      showSuccess('Test Email Sent', 'Test email sent successfully! Check your inbox to verify delivery.');
    } catch (error: any) {
      if (error.message && error.message.includes('Authentication failed')) {
        showError('Zoho Authentication Failed',
          'Please verify:\n• Password is correct\n• SMTP Username is your Zoho email address\n• Two-factor authentication is disabled or app-specific password is used');
      } else if (error.message && error.message.includes('Connection')) {
        showError('Connection Failed',
          'Unable to connect to Zoho servers. Please check your internet connection and try again.');
      } else {
        showError('Test Failed', error.message || 'Failed to send test email. Please check your configuration.');
      }
    }
  };

  const toggleRealEmailDelivery = async () => {
    try {
      const newValue = !emailSettings.forceRealEmail;
      const response = await apiService.toggleRealEmailDelivery(newValue);

      setEmailSettings(prev => ({ ...prev, forceRealEmail: newValue }));
      showSuccess('Email Delivery Updated', response.message);
    } catch (error: any) {
      showError('Update Failed', error.message || 'Failed to update email delivery setting');
    }
  };

  const handleEditUser = (user: any) => {
    setEditingUser(user);
    setShowUserModal(true);
  };

  const handleDeleteUser = async (userId: number) => {
    // Only staff and admin can delete individual users
    if (!isStaff && !isAdmin) {
      showError('You do not have permission to delete users');
      return;
    }

    const confirmed = await confirm({
      title: 'Delete User',
      message: 'Are you sure you want to delete this user? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger'
    });

    if (confirmed) {
      try {
        await apiService.delete(`/users/${userId}`);
        showSuccess('User deleted successfully');
        await refreshUsers();
      } catch (error: any) {
        showError(error.message || 'Failed to delete user');
      }
    }
  };

  const toggleUserStatus = async (userId: number, currentStatus: boolean) => {
    // Only admin and manager can activate/deactivate users
    if (!isAdmin) {
      showError('You do not have permission to change user status');
      return;
    }

    try {
      const response = await apiService.patch(`/users/${userId}`, { is_active: !currentStatus });
      showSuccess(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully`);

      // Clear API cache to ensure fresh data
      apiService.clearCache();

      // Force refresh users to ensure UI updates
      await refreshUsers();

      // Additional debug logging

    } catch (error: any) {
      console.error('Failed to update user status:', error);
      showError(error.message || 'Failed to update user status');
    }
  };

  const handleApproveUser = async (userId: number) => {
    // Only admin can approve users
    if (!isAdmin) {
      showError('You do not have permission to approve users');
      return;
    }

    // Find the user to get their details for the confirmation dialog
    // Use allUsers to ensure we can find any user, even if they're filtered out of the display
    const user = allUsers.find(u => u.id === userId);
    const userName = user?.name || 'this user';
    const userEmail = user?.email || '';

    const confirmed = await confirm({
      title: 'Approve User Registration',
      message: `Are you sure you want to approve ${userName} (${userEmail})? This will activate their account and grant them access to the system.`,
      confirmText: 'Approve',
      cancelText: 'Cancel',
      type: 'info'
    });

    if (confirmed) {
      try {
        const response = await apiService.post(`/auth/approve-user/${userId}`);
        showSuccess('User approved successfully');

        // Clear API cache to ensure fresh data
        apiService.clearCache();

        // Force refresh users to ensure UI updates
        await refreshUsers();

        // Additional debug logging

      } catch (error: any) {
        console.error('Failed to approve user:', error);
        showError(error.message || 'Failed to approve user');
      }
    }
  };

  // Mock data for custom segments
  const mockSegments = [
    { id: 1, name: 'VIP Customers', criteria: 'Total Spent > RM 2000', clients: 45, color: 'purple' },
    { id: 2, name: 'New Joiners', criteria: 'Created within 30 days', clients: 23, color: 'blue' },
    { id: 3, name: 'Inactive Users', criteria: 'No activity in 90 days', clients: 12, color: 'gray' },
    { id: 4, name: 'Seminar Attendees', criteria: 'Attended any seminar', clients: 67, color: 'green' },
  ];

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'Admin':
        return 'danger';
      case 'Manager':
        return 'primary';
      case 'Staff':
        return 'info';
      default:
        return 'secondary';
    }
  };

  const getSegmentBadgeVariant = (color: string) => {
    switch (color) {
      case 'purple':
        return 'primary';
      case 'blue':
        return 'info';
      case 'green':
        return 'success';
      case 'gray':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center">
              <Cog className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
              <p className="text-gray-600 dark:text-gray-300">Configure your CRM system and preferences</p>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Content */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors relative ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                  {tab.badge && (
                    <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                      {tab.badgeLabel || tab.badge}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Organization Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Organization Name *
                    </label>
                    <input
                      type="text"
                      value={generalSettings.organizationName}
                      onChange={(e) => handleGeneralSettingChange('organizationName', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${
                        settingsErrors.organizationName
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-200 dark:border-gray-600 focus:ring-blue-500 dark:focus:ring-blue-400'
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                      required
                    />
                    {settingsErrors.organizationName && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{settingsErrors.organizationName}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Website
                    </label>
                    <input
                      type="url"
                      value={generalSettings.website}
                      onChange={(e) => handleGeneralSettingChange('website', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${
                        settingsErrors.website
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-200 dark:border-gray-600 focus:ring-blue-500 dark:focus:ring-blue-400'
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                      placeholder="https://example.com"
                    />
                    {settingsErrors.website && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{settingsErrors.website}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Contact Email
                    </label>
                    <input
                      type="email"
                      value={generalSettings.contactEmail}
                      onChange={(e) => handleGeneralSettingChange('contactEmail', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${
                        settingsErrors.contactEmail
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-200 dark:border-gray-600 focus:ring-blue-500 dark:focus:ring-blue-400'
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                      placeholder="<EMAIL>"
                    />
                    {settingsErrors.contactEmail && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{settingsErrors.contactEmail}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={generalSettings.phoneNumber}
                      onChange={(e) => handleGeneralSettingChange('phoneNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors"
                      placeholder="+60 12-345-6789"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Address</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Street Address
                    </label>
                    <input
                      type="text"
                      value={generalSettings.streetAddress}
                      onChange={(e) => handleGeneralSettingChange('streetAddress', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors"
                      placeholder="123 Main Street"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        City
                      </label>
                      <input
                        type="text"
                        value={generalSettings.city}
                        onChange={(e) => handleGeneralSettingChange('city', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors"
                        placeholder="City"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        State
                      </label>
                      <input
                        type="text"
                        value={generalSettings.state}
                        onChange={(e) => handleGeneralSettingChange('state', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors"
                        placeholder="State"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Postal Code
                      </label>
                      <input
                        type="text"
                        value={generalSettings.postalCode}
                        onChange={(e) => handleGeneralSettingChange('postalCode', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-colors"
                        placeholder="12345"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="w-full">
                  <button
                    onClick={saveGeneralSettings}
                    disabled={savingSettings}
                    className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 font-medium"
                  >
                    <Save className="w-4 h-4" />
                    <span>{savingSettings ? 'Saving...' : 'Save Changes'}</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'data' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Management</h3>
                <p className="text-gray-600 mb-6">Import, export, and manage your CRM data</p>
              </div>

              {showImportWizard ? (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-semibold text-gray-900">Data Import Wizard</h4>
                    <button
                      onClick={() => setShowImportWizard(false)}
                      className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Cancel Import
                    </button>
                  </div>
                  <DataImportWizard
                    dataType={importDataType}
                    fileFormat={importFileFormat}
                    onImportComplete={handleImportComplete}
                  />
                </div>
              ) : (
                <>
                  {/* Data Management Cards - Horizontal Layout */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Import Section */}
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 flex flex-col h-full border border-gray-200 dark:border-gray-700">
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                        <Upload className="w-5 h-5 mr-2" />
                        Import Data
                      </h4>
                      <p className="text-gray-600 dark:text-gray-300 mb-4">Import data from CSV, Excel, or JSON files</p>

                      <div className="space-y-4 flex-grow">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Select Data Type
                          </label>
                          <CustomDropdown
                            options={[
                              { value: 'clients', label: 'Clients' },
                              { value: 'leads', label: 'Leads' },
                              { value: 'deals', label: 'Deals' },
                              { value: 'products', label: 'Products' },
                              { value: 'transactions', label: 'Transactions' }
                            ]}
                            value={importDataType}
                            onChange={setImportDataType}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Select File Format
                          </label>
                          <CustomDropdown
                            options={[
                              { value: 'csv', label: 'CSV (.csv)' },
                              { value: 'excel', label: 'Excel (.xlsx)' },
                              { value: 'json', label: 'JSON (.json)' }
                            ]}
                            value={importFileFormat}
                            onChange={setImportFileFormat}
                            className="w-full"
                          />
                        </div>
                      </div>

                      <button
                        onClick={handleStartImport}
                        className="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 mt-4"
                      >
                        <Upload className="w-4 h-4" />
                        <span>Start Import</span>
                      </button>
                    </div>

                    {/* Export Section */}
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 flex flex-col h-full border border-gray-200 dark:border-gray-700">
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                        <Download className="w-5 h-5 mr-2" />
                        Export Data
                      </h4>
                      <p className="text-gray-600 dark:text-gray-300 mb-4 flex-grow">Export your data in CSV, Excel, or JSON formats</p>

                      <button
                        onClick={handleOpenExportModal}
                        className="w-full px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 mt-4"
                      >
                        <Download className="w-4 h-4" />
                        <span>Export Data</span>
                      </button>
                    </div>

                    {/* Data Reset Section - Admin Only */}
                    {isAdmin && (
                      <div className="bg-red-50 dark:bg-red-900/30 rounded-lg p-6 border border-red-200 dark:border-red-700 flex flex-col h-full">
                        <h4 className="text-md font-semibold text-red-900 dark:text-red-100 mb-4 flex items-center">
                          <AlertTriangle className="w-5 h-5 mr-2" />
                          Reset/Clear Data
                        </h4>
                        <p className="text-red-700 dark:text-red-200 mb-4 flex-grow">Permanently delete data from your CRM. This action cannot be undone.</p>

                        <button
                          onClick={handleOpenClearModal}
                          className="w-full px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2 mt-4"
                        >
                          <Trash2 className="w-4 h-4" />
                          <span>Clear Data</span>
                        </button>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          )}

          {activeTab === 'users' && (
            <div className="space-y-6">
              {/* Note: Current user is excluded from this list to prevent self-management */}
              {/* Users should manage their own accounts through My Profile page */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">User Management</h3>
                <button
                  onClick={handleAddUser}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add User</span>
                </button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Last Login
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {usersLoading ? (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                          Loading users...
                        </td>
                      </tr>
                    ) : users.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                          No users found
                        </td>
                      </tr>
                    ) : (
                      users.map((user) => (
                        <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <span className="text-white text-sm font-medium">
                                  {user.name.split(' ')[0][0]}
                                </span>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">{user.name}</div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant={getRoleBadgeVariant(user.role)} size="sm">
                              {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge variant={user.is_active ? 'success' : 'danger'} size="sm">
                              {user.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : 'Never'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <ActionButtons
                              actions={[
                                // Edit Button - Available to all users with management access
                                ...(isAdmin || currentUser?.role === 'manager' ? [{
                                  label: 'Edit User',
                                  icon: Edit,
                                  onClick: () => handleEditUser(user),
                                  color: 'green' as const
                                }] : []),
                                // Approve Button - Admin only for inactive users
                                ...(isAdmin && !user.is_active ? [{
                                  label: 'Approve User',
                                  icon: Check,
                                  onClick: () => handleApproveUser(user.id),
                                  color: 'blue' as const
                                }] : []),
                                // Activate/Deactivate Button - Admin only for active users
                                ...(isAdmin && user.is_active ? [{
                                  label: user.is_active ? 'Deactivate User' : 'Activate User',
                                  icon: user.is_active ? EyeOff : Eye,
                                  onClick: () => toggleUserStatus(user.id, user.is_active),
                                  color: 'yellow' as const
                                }] : []),
                                // Delete Button - Staff and Admin only
                                ...((isStaff || isAdmin) ? [{
                                  label: 'Delete User',
                                  icon: Trash2,
                                  onClick: () => handleDeleteUser(user.id),
                                  color: 'red' as const
                                }] : [])
                              ]}
                            />
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'email' && (
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Email Configuration</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">Configure your email settings for system notifications and 2FA codes.</p>

              {/* Email Delivery Toggle Section */}
              {!emailSettings.isProduction && (
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Real Email Delivery</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {emailSettings.forceRealEmail ? 'Using Zoho SMTP for real emails' : 'Using Mailpit for testing'}
                      </p>
                    </div>
                    <ToggleSwitch
                      enabled={emailSettings.forceRealEmail}
                      onChange={toggleRealEmailDelivery}
                      size="md"
                    />
                  </div>
                </div>
              )}

              {/* SMTP Configuration Section */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">SMTP Configuration</h3>

                <div className="space-y-4">
                  {/* SMTP Host */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      SMTP Host
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      value={emailSettings.smtpHost}
                      onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpHost: e.target.value }))}
                      placeholder="smtp.zoho.com"
                    />
                  </div>

                  {/* SMTP Port */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      SMTP Port
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      value={emailSettings.smtpPort.toString()}
                      onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPort: parseInt(e.target.value) }))}
                    >
                      <option value="587">587 (TLS)</option>
                      <option value="465">465 (SSL)</option>
                    </select>
                  </div>

                  {/* SMTP Username */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      SMTP Username
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      value={emailSettings.smtpUsername}
                      onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpUsername: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  {/* SMTP Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      SMTP Password
                    </label>
                    <div className="relative">
                      <input
                        type={showApiKey ? 'text' : 'password'}
                        className="w-full px-3 py-2 pr-10 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        value={emailSettings.password}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, password: e.target.value }))}
                        placeholder="Your Zoho password or app password"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? (
                          <EyeOff className="w-4 h-4 text-gray-400" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* SMTP Encryption */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Encryption
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      value={emailSettings.smtpEncryption}
                      onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpEncryption: e.target.value }))}
                    >
                      <option value="tls">TLS (Recommended)</option>
                      <option value="ssl">SSL</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Gateway Configuration</h3>
                
                {/* ToyyibPay */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-medium text-green-900">ToyyibPay</h4>
                      <p className="text-sm text-green-700">Active payment gateway</p>
                    </div>
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                      Active
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Secret Key
                      </label>
                      <div className="relative">
                        <input
                          type={showSecretKey ? 'text' : 'password'}
                          className="w-full px-3 py-2 pr-10 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          defaultValue="olmjxdaz-wx8z-1gl2-v8w8-xej3odua83j3"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowSecretKey(!showSecretKey)}
                        >
                          {showSecretKey ? (
                            <EyeOff className="w-4 h-4 text-gray-400" />
                          ) : (
                            <Eye className="w-4 h-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category Code
                      </label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        defaultValue="os03ei8m"
                      />
                    </div>
                  </div>
                </div>

                {/* Billplz */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">Billplz</h4>
                      <p className="text-sm text-gray-600">Placeholder configuration</p>
                    </div>
                    <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                      Inactive
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        API Key
                      </label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter Billplz API key"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Collection ID
                      </label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter collection ID"
                      />
                    </div>
                  </div>
                </div>

                {/* Chips */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">Chips</h4>
                      <p className="text-sm text-gray-600">Placeholder configuration</p>
                    </div>
                    <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                      Inactive
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        API Key
                      </label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter Chips API key"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Brand ID
                      </label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter brand ID"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <div className="w-full">
                  <button className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 font-medium">
                    <Save className="w-4 h-4" />
                    <span>Save Payment Settings</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'segments' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Custom Segments</h3>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
                  <Plus className="w-4 h-4" />
                  <span>Add Segment</span>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockSegments.map((segment) => (
                  <div key={segment.id} className="border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white">{segment.name}</h4>
                      <ActionButtons
                        size="sm"
                        actions={[
                          {
                            label: 'Edit Segment',
                            icon: Edit,
                            onClick: () => {/* TODO: Implement edit segment */},
                            color: 'green'
                          },
                          {
                            label: 'Delete Segment',
                            icon: Trash2,
                            onClick: () => {/* TODO: Implement delete segment */},
                            color: 'red'
                          }
                        ]}
                      />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{segment.criteria}</p>
                    <div className="flex items-center justify-between">
                      <Badge variant={getSegmentBadgeVariant(segment.color)} size="sm">
                        {segment.clients} clients
                      </Badge>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Updated today
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <SecuritySettings />
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Notification Preferences</h3>
                <div className="space-y-4">
                  {/* Login Notifications - Moved from Security tab */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <ToggleSwitch
                      enabled={notificationSettings.loginNotifications}
                      onChange={() => setNotificationSettings(prev => ({ ...prev, loginNotifications: !prev.loginNotifications }))}
                      label="Login Notifications"
                      description="Get notified when someone logs into your account"
                      size="md"
                      showLabels={true}
                      enabledLabel="Enabled"
                      disabledLabel="Disabled"
                    />
                  </div>

                  {/* New Client Notifications */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <ToggleSwitch
                      enabled={notificationSettings.newClientNotifications}
                      onChange={() => setNotificationSettings(prev => ({ ...prev, newClientNotifications: !prev.newClientNotifications }))}
                      label="New Client Notifications"
                      description="Get notified when a new client signs up"
                      size="md"
                      showLabels={true}
                      enabledLabel="Enabled"
                      disabledLabel="Disabled"
                    />
                  </div>

                  {/* Payment Notifications */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <ToggleSwitch
                      enabled={notificationSettings.paymentNotifications}
                      onChange={() => setNotificationSettings(prev => ({ ...prev, paymentNotifications: !prev.paymentNotifications }))}
                      label="Payment Notifications"
                      description="Get notified about successful payments and donations"
                      size="md"
                      showLabels={true}
                      enabledLabel="Enabled"
                      disabledLabel="Disabled"
                    />
                  </div>

                  {/* Campaign Notifications */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <ToggleSwitch
                      enabled={notificationSettings.campaignNotifications}
                      onChange={() => setNotificationSettings(prev => ({ ...prev, campaignNotifications: !prev.campaignNotifications }))}
                      label="Campaign Notifications"
                      description="Get notified about email campaign results"
                      size="md"
                      showLabels={true}
                      enabledLabel="Enabled"
                      disabledLabel="Disabled"
                    />
                  </div>

                  {/* Daily Summary */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <ToggleSwitch
                      enabled={notificationSettings.dailySummary}
                      onChange={() => setNotificationSettings(prev => ({ ...prev, dailySummary: !prev.dailySummary }))}
                      label="Daily Summary"
                      description="Receive daily summary of activities"
                      size="md"
                      showLabels={true}
                      enabledLabel="Enabled"
                      disabledLabel="Disabled"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User Modal */}
      <UserModal
        isOpen={showUserModal}
        onClose={() => {
          setShowUserModal(false);
          setEditingUser(null);
        }}
        user={editingUser}
        onSuccess={refreshUsers}
      />

      <DataExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
      />

      <DataClearModal
        isOpen={showClearModal}
        onClose={() => setShowClearModal(false)}
      />
    </div>
  );
};

export default Settings;