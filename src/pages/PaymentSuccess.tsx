import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { CheckCircle, Download, Mail, Calendar, CreditCard, Building, Smartphone } from 'lucide-react';
import { apiService } from '../services/api';

interface PaymentDetails {
  transaction_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  paid_at: string;
  client_name: string;
  invoice_number: string;
}

const PaymentSuccess: React.FC = () => {
  const { transactionId } = useParams<{ transactionId: string }>();
  const [payment, setPayment] = useState<PaymentDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (transactionId) {
      fetchPaymentDetails();
    }
  }, [transactionId]);

  const fetchPaymentDetails = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/public/payment/${transactionId}/success`);
      setPayment(response.data.payment);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to load payment details');
    } finally {
      setLoading(false);
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'credit_card':
        return <CreditCard className="w-5 h-5" />;
      case 'bank_transfer':
        return <Building className="w-5 h-5" />;
      case 'ewallet':
        return <Smartphone className="w-5 h-5" />;
      default:
        return <CreditCard className="w-5 h-5" />;
    }
  };

  const getPaymentMethodName = (method: string) => {
    switch (method) {
      case 'credit_card':
        return 'Credit/Debit Card';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'ewallet':
        return 'E-Wallet';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  if (error || !payment) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Not Found</h1>
          <p className="text-gray-600">{error || 'The requested payment could not be found.'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
          <p className="text-gray-600">Your payment has been processed successfully.</p>
        </div>

        {/* Payment Details Card */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Details</h2>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Transaction ID:</span>
              <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{payment.transaction_id}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Invoice Number:</span>
              <span className="font-medium">{payment.invoice_number}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Client:</span>
              <span className="font-medium">{payment.client_name}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Amount Paid:</span>
              <span className="text-2xl font-bold text-green-600">
                {payment.currency} {payment.amount.toFixed(2)}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Payment Method:</span>
              <div className="flex items-center space-x-2">
                {getPaymentMethodIcon(payment.payment_method)}
                <span className="font-medium">{getPaymentMethodName(payment.payment_method)}</span>
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Payment Date:</span>
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="font-medium">
                  {new Date(payment.paid_at).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={() => window.print()}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 flex items-center justify-center space-x-2"
          >
            <Download className="w-5 h-5" />
            <span>Download Receipt</span>
          </button>
          
          <button
            onClick={() => {
              const subject = `Payment Confirmation - Invoice ${payment.invoice_number}`;
              const body = `Dear ${payment.client_name},\n\nYour payment has been successfully processed.\n\nTransaction ID: ${payment.transaction_id}\nAmount: ${payment.currency} ${payment.amount.toFixed(2)}\nPayment Date: ${new Date(payment.paid_at).toLocaleString()}\n\nThank you for your payment.`;
              window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            }}
            className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 flex items-center justify-center space-x-2"
          >
            <Mail className="w-5 h-5" />
            <span>Email Receipt</span>
          </button>
        </div>

        {/* Important Notes */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">Important Notes:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Please save this confirmation for your records</li>
            <li>• A receipt will be sent to your registered email address</li>
            <li>• If you have any questions, please contact our support team</li>
            <li>• Transaction ID: {payment.transaction_id}</li>
          </ul>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>Thank you for your business!</p>
          <p className="mt-1">© 2024 KDT CRM System. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
