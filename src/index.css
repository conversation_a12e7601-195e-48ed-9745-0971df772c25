@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Global base styles */
body {
  @apply bg-white text-gray-900 transition-colors duration-300;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  @apply text-gray-900;
}

/* Paragraphs and text */
p {
  @apply text-gray-600;
}

/* Links */
a {
  @apply text-blue-600 hover:text-blue-700;
}

/* Form elements base styles */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
textarea,
select {
  @apply bg-white border-gray-300 text-gray-900 placeholder-gray-500;
  @apply focus:border-blue-500 focus:ring-blue-500;
}

/* Buttons base styles */
button {
  @apply transition-colors duration-200;
}

/* Tables */
table {
  @apply bg-white;
}

th {
  @apply bg-gray-50 text-gray-900 border-gray-200;
}

td {
  @apply text-gray-900 border-gray-200;
}

/* Code elements */
code, pre {
  @apply bg-gray-100 text-gray-900;
}

/* Horizontal rules */
hr {
  @apply border-gray-200;
}

/* ===== COMPONENT UTILITY CLASSES ===== */
@layer components {
  /* Range slider custom styling for blue handles */
  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  /* Webkit browsers (Chrome, Safari, Edge) */
  input[type="range"]::-webkit-slider-track {
    background: #e5e7eb; /* gray-200 */
    height: 8px;
    border-radius: 4px;
  }

  .dark input[type="range"]::-webkit-slider-track {
    background: #374151; /* gray-700 */
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3b82f6; /* blue-600 */
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  .dark input[type="range"]::-webkit-slider-thumb {
    background: #60a5fa; /* blue-400 */
    border: 2px solid #1f2937; /* gray-800 */
  }

  /* Firefox */
  input[type="range"]::-moz-range-track {
    background: #e5e7eb; /* gray-200 */
    height: 8px;
    border-radius: 4px;
    border: none;
  }

  .dark input[type="range"]::-moz-range-track {
    background: #374151; /* gray-700 */
  }

  input[type="range"]::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #3b82f6; /* blue-600 */
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    border: none;
  }

  .dark input[type="range"]::-moz-range-thumb {
    background: #60a5fa; /* blue-400 */
    border: 2px solid #1f2937; /* gray-800 */
  }

  /* Focus states */
  input[type="range"]:focus::-webkit-slider-thumb {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  input[type="range"]:focus::-moz-range-thumb {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Card components */
  .card {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm;
  }

  .card-header {
    @apply border-b border-gray-200 bg-gray-50 px-6 py-4;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply border-t border-gray-200 bg-gray-50 px-6 py-4;
  }

  /* Modal components */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50;
  }

  .modal-container {
    @apply bg-white rounded-lg shadow-xl border border-gray-200;
  }

  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md;
    @apply transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
  }

  /* Badge components */
  .badge {
    @apply inline-flex items-center px-3 py-1.5 text-sm font-medium rounded;
  }

  .badge-primary {
    @apply badge bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300;
  }

  .badge-secondary {
    @apply badge bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300;
  }

  .badge-success {
    @apply badge bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300;
  }

  .badge-warning {
    @apply badge bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300;
  }

  .badge-danger {
    @apply badge bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300;
  }

  .badge-info {
    @apply badge bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300;
  }

  /* Status badges */
  .status-active {
    @apply badge-success;
  }

  .status-inactive {
    @apply badge-secondary;
  }

  .status-pending {
    @apply badge-warning;
  }

  .status-error {
    @apply badge-danger;
  }

  /* Priority badges */
  .priority-low {
    @apply badge-success;
  }

  .priority-medium {
    @apply badge-warning;
  }

  .priority-high {
    @apply badge-danger;
  }

  .priority-urgent {
    @apply badge-danger;
  }
}

/* Toast animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) scale(0.95);
    opacity: 0;
  }
}

.toast-slide-in {
  animation: slideInRight 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.toast-slide-out {
  animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}
