export interface Deal {
  id: string;
  leadId?: string;
  clientId?: string;
  assignedTo?: User;
  createdBy?: User;
  title: string;
  description?: string;
  dealNumber: string;
  value: number;
  expectedRevenue?: number;
  actualRevenue?: number;
  currency: string;
  pipelineStage: PipelineStage;
  stageOrder: number;
  stageChangedAt?: Date;
  stageChangedBy?: string;
  probability: number;
  dealSize: DealSize;
  expectedCloseDate?: Date;
  actualCloseDate?: Date;
  daysInPipeline: number;
  daysInCurrentStage: number;
  source?: string;
  utmSource?: string;
  utmCampaign?: string;
  utmMedium?: string;
  priority: Priority;
  dealType: DealType;
  tags: string[];
  competitors: string[];
  competitiveAdvantage?: string;
  winProbabilityReason?: WinProbabilityReason;
  notes?: string;
  internalNotes?: string;
  lastActivity?: Date;
  nextFollowUp?: Date;
  nextAction?: string;
  automationTriggers: AutomationTrigger[];
  autoFollowUpEnabled: boolean;
  followUpFrequencyDays: number;
  documents: string[];
  proposalDocuments: string[];
  contractDocuments: string[];
  lossReason?: LossReason;
  lossDetails?: string;
  competitorWon?: string;
  externalId?: string;
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  
  // Relationships
  lead?: any;
  client?: any;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'manager' | 'user';
  department?: string;
  isActive: boolean;
  lastLoginAt?: Date;
}

export type PipelineStage = 
  | 'prospecting'
  | 'qualification' 
  | 'proposal'
  | 'negotiation'
  | 'closing'
  | 'won'
  | 'lost'
  | 'on_hold';

export type DealSize = 'Small' | 'Medium' | 'Large' | 'Enterprise';

export type Priority = 'Low' | 'Medium' | 'High' | 'Critical';

export type DealType = 
  | 'New Business'
  | 'Upsell'
  | 'Cross-sell'
  | 'Renewal'
  | 'Expansion';

export type WinProbabilityReason = 
  | 'Strong Relationship'
  | 'Best Price'
  | 'Superior Product'
  | 'Incumbent Advantage'
  | 'Strategic Partnership'
  | 'Unique Solution'
  | 'Other';

export type LossReason = 
  | 'Price Too High'
  | 'Competitor Won'
  | 'No Budget'
  | 'No Decision'
  | 'Product Mismatch'
  | 'Timing Issues'
  | 'Lost Contact'
  | 'Other';

export interface AutomationTrigger {
  type: string;
  days: number;
  action: string;
}

export interface PipelineStageInfo {
  name: string;
  color: string;
  description: string;
}

export interface DealFilters {
  stage?: PipelineStage;
  assignedTo?: string;
  clientId?: string;
  dealType?: DealType;
  priority?: Priority;
  minValue?: number;
  maxValue?: number;
  closeDateFrom?: Date;
  closeDateTo?: Date;
  search?: string;
  activeOnly?: boolean;
  overdueOnly?: boolean;
}

export interface DealStats {
  totalDeals: number;
  totalValue: number;
  wonDeals: number;
  wonValue: number;
  lostDeals: number;
  activeDeals: number;
  activeValue: number;
  overdueDeals: number;
  avgDealValue: number;
  conversionRate: number;
  stages: Record<PipelineStage, { count: number; totalValue: number }>;
}

export interface CreateDealRequest {
  leadId?: string;
  clientId?: string;
  assignedTo?: string;
  title: string;
  description?: string;
  value: number;
  expectedRevenue?: number;
  currency?: string;
  pipelineStage?: PipelineStage;
  probability?: number;
  dealSize?: DealSize;
  expectedCloseDate?: Date;
  source?: string;
  utmSource?: string;
  utmCampaign?: string;
  utmMedium?: string;
  priority?: Priority;
  dealType?: DealType;
  tags?: string[];
  competitors?: string[];
  competitiveAdvantage?: string;
  winProbabilityReason?: WinProbabilityReason;
  notes?: string;
  internalNotes?: string;
  nextFollowUp?: Date;
  nextAction?: string;
  autoFollowUpEnabled?: boolean;
  followUpFrequencyDays?: number;
}

export interface UpdateDealRequest extends Partial<CreateDealRequest> {
  actualRevenue?: number;
  actualCloseDate?: Date;
  lossReason?: LossReason;
  lossDetails?: string;
  competitorWon?: string;
}

export interface MoveStageRequest {
  pipelineStage: PipelineStage;
  notes?: string;
  lossReason?: LossReason;
  lossDetails?: string;
  competitorWon?: string;
}

export const PIPELINE_STAGES: Record<PipelineStage, PipelineStageInfo> = {
  prospecting: {
    name: 'Prospecting',
    color: '#6B7280',
    description: 'Initial contact and qualification'
  },
  qualification: {
    name: 'Qualification',
    color: '#3B82F6',
    description: 'Needs assessment and budget confirmation'
  },
  proposal: {
    name: 'Proposal',
    color: '#F59E0B',
    description: 'Proposal sent, awaiting response'
  },
  negotiation: {
    name: 'Negotiation',
    color: '#EF4444',
    description: 'Terms and pricing discussion'
  },
  closing: {
    name: 'Closing',
    color: '#8B5CF6',
    description: 'Final approval and contract signing'
  },
  won: {
    name: 'Won',
    color: '#10B981',
    description: 'Deal successfully closed'
  },
  lost: {
    name: 'Lost',
    color: '#6B7280',
    description: 'Deal lost or cancelled'
  },
  on_hold: {
    name: 'On Hold',
    color: '#F97316',
    description: 'Deal temporarily paused'
  }
};

export const DEAL_PRIORITIES: Record<Priority, { color: string; bgColor: string }> = {
  Low: { color: '#6B7280', bgColor: '#F3F4F6' },
  Medium: { color: '#F59E0B', bgColor: '#FEF3C7' },
  High: { color: '#EF4444', bgColor: '#FEE2E2' },
  Critical: { color: '#DC2626', bgColor: '#FECACA' }
};

export const DEAL_SIZES: Record<DealSize, { range: string; color: string }> = {
  Small: { range: 'RM 1K - 10K', color: '#6B7280' },
  Medium: { range: 'RM 10K - 50K', color: '#3B82F6' },
  Large: { range: 'RM 50K - 200K', color: '#F59E0B' },
  Enterprise: { range: 'RM 200K+', color: '#10B981' }
};
