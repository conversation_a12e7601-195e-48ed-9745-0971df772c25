// API Types - These match the Lara<PERSON> backend models exactly

export interface ApiClientPhoneNumber {
  id: number;
  client_id: number;
  phone_number: string;
  is_primary: boolean;
  phone_verified: boolean;
  phone_score: number;
  phone_carrier?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiClientEmail {
  id: number;
  client_id: number;
  email_address: string;
  is_primary: boolean;
  email_verified: boolean;
  email_score: number;
  email_deliverability?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiClient {
  id: number;
  uuid?: string;
  name: string;
  email: string;
  phone: string | null;
  company: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postal_code: string | null;
  status: 'active' | 'inactive' | 'prospect';
  notes: string | null;
  utm_source: string | null;
  tags: string[] | null;
  category: 'First Timer' | 'Retainer' | 'Loyal' | 'Advocator';
  ltv_segment: 'Silver' | 'Gold' | 'Gold+' | 'Platinum';
  engagement_level: 'Hot' | 'Warm' | 'Cold' | 'Frozen';
  priority: 'High' | 'Medium' | 'Low';
  suggested_action: string | null;
  last_activity: string | null;
  total_spent: string;
  transaction_count: number;
  custom_fields: Record<string, any> | null;
  email_verified?: boolean;
  phone_verified?: boolean;
  // New scoring fields
  name_score?: number;
  email_score?: number;
  phone_score?: number;
  overall_score?: number;
  // New validation fields
  email_deliverability?: string;
  phone_validity?: boolean;
  phone_carrier?: string;
  // New categorization fields
  data_quality?: 'Poor' | 'Fair' | 'Good' | 'Excellent';
  customer_category?: string;
  notes_remarks?: string;
  suggested_next_action?: string;
  // Personal information fields
  ic_number?: string;
  birthday?: string;
  gender?: 'Male' | 'Female';
  religion?: 'Muslim' | 'Non-Muslim';
  // Financial information
  income?: string;
  income_category?: 'Low' | 'Medium' | 'High';
  // Enhanced address fields
  address_line_1?: string;
  address_line_2?: string;
  postcode?: string;
  // Behavioral data
  behaviour?: string;
  interest?: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  transactions?: ApiTransaction[];
  leads?: ApiLead[];
  // Multi-contact relationships
  phone_numbers?: ApiClientPhoneNumber[];
  emails?: ApiClientEmail[];
}

export interface ApiLead {
  id: number;
  client_id: number | null;

  // Lead capture fields
  name: string | null;
  email: string | null;
  phone: string | null;
  company: string | null;

  // UTM and campaign tracking
  utm_source: string | null;
  utm_campaign: string | null;
  utm_medium: string | null;
  utm_content: string | null;
  utm_term: string | null;
  channel: string | null;

  // Lead classification
  lead_type: 'capture' | 'opportunity';
  tags: string[] | null;

  // Sales opportunity fields
  title: string | null;
  description: string | null;
  status: 'new' | 'contacted' | 'engaged' | 'qualified' | 'converted' | 'disqualified';
  opportunity_status: 'contacted' | 'info_sent' | 'negotiation' | 'waiting_payment' | 'closed_won' | 'closed_lost' | null;
  priority: 'low' | 'medium' | 'high';
  engagement_level: 'hot' | 'warm' | 'cold' | 'frozen';

  // Sales tracking
  source: string | null;
  assigned_to: string | null;
  estimated_value: string | null;
  probability: number | null;
  expected_close_date: string | null;

  // Activity and lifecycle tracking
  notes: string | null;
  internal_remarks: string | null;
  suggested_action: string | null;
  last_activity: string | null;
  last_contacted: string | null;
  lifecycle_stage: number;

  // Document uploads
  documents: string[] | null;

  // Recycling and conversion tracking
  is_recycled: boolean;
  recycled_at: string | null;
  converted_at: string | null;
  converted_to_client_id: number | null;

  // Timestamps
  created_at: string;
  updated_at: string;
  deleted_at: string | null;

  // Relationships
  client?: ApiClient;
  converted_to_client?: ApiClient;
}

export interface ApiProduct {
  id: number;
  name: string;
  description: string | null;
  price: string;
  cost: string | null;
  sku: string | null;
  category: string | null;
  status: string;
  stock_quantity: number | null;
  min_stock_level: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface ApiTransaction {
  id: number;
  client_id: number;
  amount: string;
  status: string;
  type: string | null;
  description: string | null;
  reference_number: string | null;
  payment_method: string | null;
  payment_date: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  client?: ApiClient;
  products?: ApiProduct[];
}

export interface ApiDashboardStats {
  clients: number;
  products: number;
  transactions: number;
  leads: number;
  revenue: string;
}

// Request types for creating/updating
export interface CreateClientRequest {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  status: 'active' | 'inactive' | 'prospect';
  notes?: string;
  utm_source?: string;
  tags?: string[];
  category?: 'First Timer' | 'Retainer' | 'Loyal' | 'Advocator';
  ltv_segment?: 'Silver' | 'Gold' | 'Gold+' | 'Platinum';
  engagement_level?: 'Hot' | 'Warm' | 'Cold' | 'Frozen';
  priority?: 'High' | 'Medium' | 'Low';
  suggested_action?: string;
  last_activity?: string;
  total_spent?: number;
  transaction_count?: number;
  custom_fields?: Record<string, string | number | boolean | string[]>;
}

export type UpdateClientRequest = Partial<CreateClientRequest>;

export interface CreateLeadRequest {
  client_id?: number;
  title: string;
  description?: string;
  status: 'new' | 'contacted' | 'qualified' | 'proposal' | 'negotiation' | 'closed_won' | 'closed_lost';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  source?: string;
  assigned_to?: string;
  estimated_value?: number;
  probability?: number;
  expected_close_date?: string;
  notes?: string;
}

export type UpdateLeadRequest = Partial<CreateLeadRequest>;

export interface CreateProductRequest {
  name: string;
  description?: string;
  price: number;
  cost?: number;
  sku?: string;
  category?: string;
  status: string;
  stock_quantity?: number;
  min_stock_level?: number;
}

export type UpdateProductRequest = Partial<CreateProductRequest>;

export interface CreateTransactionRequest {
  client_id: number;
  amount: number;
  status: string;
  type?: string;
  description?: string;
  reference_number?: string;
  payment_method?: string;
  payment_date?: string;
  notes?: string;
}

export type UpdateTransactionRequest = Partial<CreateTransactionRequest>;
