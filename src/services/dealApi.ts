import { apiService, PaginatedResponse } from './api';
import { 
  Deal, 
  DealFilters, 
  DealStats, 
  CreateDealRequest, 
  UpdateDealRequest, 
  MoveStageRequest,
  User
} from '../types/deal';

// Transform backend data to frontend Deal type
const transformDeal = (backendDeal: any): Deal => {
  return {
    id: backendDeal.id.toString(),
    leadId: backendDeal.lead_id?.toString(),
    clientId: backendDeal.client_id?.toString(),
    assignedTo: backendDeal.assigned_to ? {
      id: backendDeal.assigned_to.id.toString(),
      name: backendDeal.assigned_to.name,
      email: backendDeal.assigned_to.email,
      phone: backendDeal.assigned_to.phone,
      role: backendDeal.assigned_to.role,
      department: backendDeal.assigned_to.department,
      isActive: backendDeal.assigned_to.is_active,
      lastLoginAt: backendDeal.assigned_to.last_login_at ? new Date(backendDeal.assigned_to.last_login_at) : undefined
    } : undefined,
    createdBy: backendDeal.created_by ? {
      id: backendDeal.created_by.id.toString(),
      name: backendDeal.created_by.name,
      email: backendDeal.created_by.email,
      phone: backendDeal.created_by.phone,
      role: backendDeal.created_by.role,
      department: backendDeal.created_by.department,
      isActive: backendDeal.created_by.is_active,
      lastLoginAt: backendDeal.created_by.last_login_at ? new Date(backendDeal.created_by.last_login_at) : undefined
    } : undefined,
    title: backendDeal.title,
    description: backendDeal.description,
    dealNumber: backendDeal.deal_number,
    value: parseFloat(backendDeal.value),
    expectedRevenue: backendDeal.expected_revenue ? parseFloat(backendDeal.expected_revenue) : undefined,
    actualRevenue: backendDeal.actual_revenue ? parseFloat(backendDeal.actual_revenue) : undefined,
    currency: backendDeal.currency,
    pipelineStage: backendDeal.pipeline_stage,
    stageOrder: backendDeal.stage_order,
    stageChangedAt: backendDeal.stage_changed_at ? new Date(backendDeal.stage_changed_at) : undefined,
    stageChangedBy: backendDeal.stage_changed_by,
    probability: backendDeal.probability,
    dealSize: backendDeal.deal_size,
    expectedCloseDate: backendDeal.expected_close_date ? new Date(backendDeal.expected_close_date) : undefined,
    actualCloseDate: backendDeal.actual_close_date ? new Date(backendDeal.actual_close_date) : undefined,
    daysInPipeline: backendDeal.days_in_pipeline,
    daysInCurrentStage: backendDeal.days_in_current_stage,
    source: backendDeal.source,
    utmSource: backendDeal.utm_source,
    utmCampaign: backendDeal.utm_campaign,
    utmMedium: backendDeal.utm_medium,
    priority: backendDeal.priority,
    dealType: backendDeal.deal_type,
    tags: backendDeal.tags || [],
    competitors: backendDeal.competitors || [],
    competitiveAdvantage: backendDeal.competitive_advantage,
    winProbabilityReason: backendDeal.win_probability_reason,
    notes: backendDeal.notes,
    internalNotes: backendDeal.internal_notes,
    lastActivity: backendDeal.last_activity ? new Date(backendDeal.last_activity) : undefined,
    nextFollowUp: backendDeal.next_follow_up ? new Date(backendDeal.next_follow_up) : undefined,
    nextAction: backendDeal.next_action,
    automationTriggers: backendDeal.automation_triggers || [],
    autoFollowUpEnabled: backendDeal.auto_follow_up_enabled,
    followUpFrequencyDays: backendDeal.follow_up_frequency_days,
    documents: backendDeal.documents || [],
    proposalDocuments: backendDeal.proposal_documents || [],
    contractDocuments: backendDeal.contract_documents || [],
    lossReason: backendDeal.loss_reason,
    lossDetails: backendDeal.loss_details,
    competitorWon: backendDeal.competitor_won,
    externalId: backendDeal.external_id,
    customFields: backendDeal.custom_fields || {},
    createdAt: new Date(backendDeal.created_at),
    updatedAt: new Date(backendDeal.updated_at),
    lead: backendDeal.lead,
    client: backendDeal.client
  };
};

// Transform frontend Deal filters to backend parameters
const transformFilters = (filters: DealFilters): Record<string, any> => {
  const params: Record<string, any> = {};

  if (filters.stage) params.stage = filters.stage;
  if (filters.assignedTo) params.assigned_to = filters.assignedTo;
  if (filters.clientId) params.client_id = filters.clientId;
  if (filters.dealType) params.deal_type = filters.dealType;
  if (filters.priority) params.priority = filters.priority;
  if (filters.minValue) params.min_value = filters.minValue;
  if (filters.maxValue) params.max_value = filters.maxValue;
  if (filters.closeDateFrom) params.close_date_from = filters.closeDateFrom.toISOString().split('T')[0];
  if (filters.closeDateTo) params.close_date_to = filters.closeDateTo.toISOString().split('T')[0];
  if (filters.search) params.search = filters.search;
  if (filters.activeOnly) params.active_only = filters.activeOnly;
  if (filters.overdueOnly) params.overdue_only = filters.overdueOnly;

  return params;
};

// Transform frontend CreateDealRequest to backend format
const transformCreateRequest = (request: CreateDealRequest): Record<string, any> => {
  const data: Record<string, any> = {
    title: request.title,
    value: request.value,
  };

  if (request.leadId) {
    const numericLeadId = typeof request.leadId === 'string' ? parseInt(request.leadId) : request.leadId;
    if (!isNaN(numericLeadId)) {
      data.lead_id = numericLeadId;
    }
  }
  if (request.clientId) data.client_id = request.clientId;
  if (request.assignedTo) data.assigned_to = request.assignedTo;
  if (request.description) data.description = request.description;
  if (request.expectedRevenue) data.expected_revenue = request.expectedRevenue;
  if (request.currency) data.currency = request.currency;
  if (request.pipelineStage) data.pipeline_stage = request.pipelineStage;
  if (request.probability) data.probability = request.probability;
  if (request.dealSize) data.deal_size = request.dealSize;
  if (request.expectedCloseDate) data.expected_close_date = request.expectedCloseDate.toISOString().split('T')[0];
  if (request.source) data.source = request.source;
  if (request.utmSource) data.utm_source = request.utmSource;
  if (request.utmCampaign) data.utm_campaign = request.utmCampaign;
  if (request.utmMedium) data.utm_medium = request.utmMedium;
  if (request.priority) data.priority = request.priority;
  if (request.dealType) data.deal_type = request.dealType;
  if (request.tags) data.tags = request.tags;
  if (request.competitors) data.competitors = request.competitors;
  if (request.competitiveAdvantage) data.competitive_advantage = request.competitiveAdvantage;
  if (request.winProbabilityReason) data.win_probability_reason = request.winProbabilityReason;
  if (request.notes) data.notes = request.notes;
  if (request.internalNotes) data.internal_notes = request.internalNotes;
  if (request.nextFollowUp) data.next_follow_up = request.nextFollowUp.toISOString().split('T')[0];
  if (request.nextAction) data.next_action = request.nextAction;
  if (request.autoFollowUpEnabled !== undefined) data.auto_follow_up_enabled = request.autoFollowUpEnabled;
  if (request.followUpFrequencyDays) data.follow_up_frequency_days = request.followUpFrequencyDays;

  return data;
};

// Transform frontend UpdateDealRequest to backend format
const transformUpdateRequest = (request: UpdateDealRequest, preserveLeadId?: string): Record<string, any> => {
  const data: Record<string, any> = {};

  if (request.title) data.title = request.title;

  // Handle leadId with preservation logic
  if (request.leadId !== undefined) {
    if (request.leadId) {
      const numericLeadId = typeof request.leadId === 'string' ? parseInt(request.leadId) : request.leadId;
      if (!isNaN(numericLeadId)) {
        data.lead_id = numericLeadId;
      }
    } else {
      // If leadId is explicitly set to empty/null, preserve the original if available
      if (preserveLeadId) {
        const numericLeadId = typeof preserveLeadId === 'string' ? parseInt(preserveLeadId) : preserveLeadId;
        if (!isNaN(numericLeadId)) {
          data.lead_id = numericLeadId;
        }
      }
    }
  } else if (preserveLeadId) {
    // If leadId is not provided in the update, preserve the original
    const numericLeadId = typeof preserveLeadId === 'string' ? parseInt(preserveLeadId) : preserveLeadId;
    if (!isNaN(numericLeadId)) {
      data.lead_id = numericLeadId;
    }
  }

  if (request.clientId) data.client_id = request.clientId;
  if (request.assignedTo) data.assigned_to = request.assignedTo;
  if (request.description !== undefined) data.description = request.description;
  if (request.value) data.value = request.value;
  if (request.expectedRevenue !== undefined) data.expected_revenue = request.expectedRevenue;
  if (request.actualRevenue !== undefined) data.actual_revenue = request.actualRevenue;
  if (request.currency) data.currency = request.currency;
  if (request.pipelineStage) data.pipeline_stage = request.pipelineStage;
  if (request.probability !== undefined) data.probability = request.probability;
  if (request.dealSize) data.deal_size = request.dealSize;
  if (request.expectedCloseDate !== undefined) {
    data.expected_close_date = request.expectedCloseDate ? request.expectedCloseDate.toISOString().split('T')[0] : null;
  }
  if (request.actualCloseDate !== undefined) {
    data.actual_close_date = request.actualCloseDate ? request.actualCloseDate.toISOString().split('T')[0] : null;
  }
  if (request.source !== undefined) data.source = request.source;
  if (request.utmSource !== undefined) data.utm_source = request.utmSource;
  if (request.utmCampaign !== undefined) data.utm_campaign = request.utmCampaign;
  if (request.utmMedium !== undefined) data.utm_medium = request.utmMedium;
  if (request.priority) data.priority = request.priority;
  if (request.dealType) data.deal_type = request.dealType;
  if (request.tags !== undefined) data.tags = request.tags;
  if (request.competitors !== undefined) data.competitors = request.competitors;
  if (request.competitiveAdvantage !== undefined) data.competitive_advantage = request.competitiveAdvantage;
  if (request.winProbabilityReason !== undefined) data.win_probability_reason = request.winProbabilityReason;
  if (request.notes !== undefined) data.notes = request.notes;
  if (request.internalNotes !== undefined) data.internal_notes = request.internalNotes;
  if (request.nextFollowUp !== undefined) {
    data.next_follow_up = request.nextFollowUp ? request.nextFollowUp.toISOString().split('T')[0] : null;
  }
  if (request.nextAction !== undefined) data.next_action = request.nextAction;
  if (request.autoFollowUpEnabled !== undefined) data.auto_follow_up_enabled = request.autoFollowUpEnabled;
  if (request.followUpFrequencyDays !== undefined) data.follow_up_frequency_days = request.followUpFrequencyDays;
  if (request.lossReason !== undefined) data.loss_reason = request.lossReason;
  if (request.lossDetails !== undefined) data.loss_details = request.lossDetails;
  if (request.competitorWon !== undefined) data.competitor_won = request.competitorWon;

  return data;
};

export class DealApiService {
  async getDeals(filters: DealFilters = {}, page = 1, perPage = 15): Promise<{ deals: Deal[]; pagination: any }> {
    const params = {
      ...transformFilters(filters),
      page,
      per_page: perPage,
      sort_by: 'created_at',
      sort_order: 'desc'
    };

    const response: PaginatedResponse<any> = await apiService.getDeals(params);
    
    return {
      deals: response.data.map(transformDeal),
      pagination: {
        currentPage: response.current_page,
        lastPage: response.last_page,
        perPage: response.per_page,
        total: response.total,
        from: response.from,
        to: response.to
      }
    };
  }

  async getDeal(id: string): Promise<Deal> {
    const response = await apiService.getDeal(id);
    return transformDeal(response);
  }

  async createDeal(request: CreateDealRequest): Promise<Deal> {
    const data = transformCreateRequest(request);
    const response = await apiService.createDeal(data);
    return transformDeal(response);
  }

  async updateDeal(id: string, request: UpdateDealRequest, originalDeal?: Deal): Promise<Deal> {
    // Get the original deal's leadId for preservation if not provided
    const preserveLeadId = originalDeal?.leadId;
    const data = transformUpdateRequest(request, preserveLeadId);
    const response = await apiService.updateDeal(id, data);
    return transformDeal(response);
  }

  async deleteDeal(id: string): Promise<void> {
    await apiService.deleteDeal(id);
  }

  async moveDealStage(id: string, request: MoveStageRequest, originalDeal?: Deal): Promise<Deal> {
    const data = {
      pipeline_stage: request.pipelineStage,
      notes: request.notes,
      loss_reason: request.lossReason,
      loss_details: request.lossDetails,
      competitor_won: request.competitorWon
    };

    // Preserve leadId if available
    if (originalDeal?.leadId) {
      const numericLeadId = typeof originalDeal.leadId === 'string' ? parseInt(originalDeal.leadId) : originalDeal.leadId;
      if (!isNaN(numericLeadId)) {
        data.lead_id = numericLeadId;
      }
    }

    const response = await apiService.moveDealStage(id, data);
    return transformDeal(response);
  }

  async getDealStats(filters: DealFilters = {}): Promise<DealStats> {
    const params = transformFilters(filters);
    const response = await apiService.getDealPipelineStats(params);
    
    return {
      totalDeals: response.total_deals,
      totalValue: response.total_value,
      wonDeals: response.won_deals,
      wonValue: response.won_value,
      lostDeals: response.lost_deals,
      activeDeals: response.active_deals,
      activeValue: response.active_value,
      overdueDeals: response.overdue_deals,
      avgDealValue: response.avg_deal_value,
      conversionRate: response.conversion_rate,
      stages: response.stages || {}
    };
  }

  async convertLeadToDeal(leadId: string, request: CreateDealRequest): Promise<Deal> {
    const data = transformCreateRequest(request);
    const response = await apiService.convertLeadToDeal(leadId, data);
    return transformDeal(response);
  }

  async getUsers(): Promise<User[]> {
    try {
      const response = await apiService.get('/users?per_page=100');
      const usersData = response.data || [];

      // Transform to match Deal User interface
      return usersData.map((user: any) => ({
        id: user.id.toString(),
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        department: user.department,
        isActive: user.is_active,
        lastLoginAt: user.last_login_at ? new Date(user.last_login_at) : undefined
      }));
    } catch (error) {
      console.error('Failed to fetch users:', error);
      // Return empty array instead of mock data
      return [];
    }
  }
}

export const dealApiService = new DealApiService();
export default dealApiService;
