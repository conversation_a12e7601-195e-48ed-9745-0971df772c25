// API Service Layer for Tarbiah Sentap Application
// Centralized HTTP client for communicating with <PERSON><PERSON> backend

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errors?: Record<string, string[]>
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Simple cache for API requests
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class RequestCache {
  private cache = new Map<string, CacheEntry>();
  private readonly DEFAULT_TTL = 30000; // 30 seconds

  set(key: string, data: any, ttl = this.DEFAULT_TTL) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const isExpired = Date.now() - entry.timestamp > entry.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear() {
    this.cache.clear();
  }

  delete(key: string) {
    this.cache.delete(key);
  }

  // Get all cache keys for pattern matching
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }
}

class ApiService {
  private cache = new RequestCache();
  private pendingRequests = new Map<string, Promise<any>>();
  private authToken: string | null = null;
  private onUnauthorized: (() => void) | null = null;

  setAuthToken(token: string | null): void {
    this.authToken = token;
  }

  setUnauthorizedHandler(handler: () => void): void {
    this.onUnauthorized = handler;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const headers: Record<string, string> = {
      'Accept': 'application/json',
      ...options.headers as Record<string, string>,
    };

    // Only set Content-Type for non-FormData requests
    if (!(options.body instanceof FormData)) {
      headers['Content-Type'] = 'application/json';
    }

    // Add authorization header if token exists
    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    const config: RequestInit = {
      headers,
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Handle 401 Unauthorized errors
        if (response.status === 401 && this.onUnauthorized) {
          this.onUnauthorized();
        }

        throw new ApiError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData.errors
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error occurred',
        0
      );
    }
  }

  // Helper method to clean parameters by removing undefined/null/empty values
  private cleanParams(params: Record<string, any>): Record<string, string> {
    const cleaned: Record<string, string> = {};
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        cleaned[key] = String(value);
      }
    });
    return cleaned;
  }

  // Generic CRUD operations
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const cleanedParams = params ? this.cleanParams(params) : {};
    const searchParams = Object.keys(cleanedParams).length > 0 ? new URLSearchParams(cleanedParams).toString() : '';
    const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
    const cacheKey = `GET:${url}`;

    // Check cache first
    const cachedData = this.cache.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Check if request is already pending to prevent duplicate requests
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey);
    }

    // Make the request
    const requestPromise = this.request<T>(url).then(data => {
      // Cache successful responses
      this.cache.set(cacheKey, data);
      this.pendingRequests.delete(cacheKey);
      return data;
    }).catch(error => {
      // Remove from pending requests on error
      this.pendingRequests.delete(cacheKey);
      throw error;
    });

    // Store pending request
    this.pendingRequests.set(cacheKey, requestPromise);
    return requestPromise;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    // Clear related cache entries on POST requests
    this.clearCacheByPattern(endpoint);

    let body: string | FormData | undefined;

    if (data instanceof FormData) {
      body = data;
    } else if (data) {
      body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body,
    });
  }

  // Helper method to clear cache entries by pattern
  private clearCacheByPattern(pattern: string) {
    const keysToDelete: string[] = [];
    const patternBase = pattern.split('?')[0]; // Remove query parameters

    // Extract the base resource from the pattern (e.g., /users/123 -> /users)
    const resourceBase = patternBase.split('/').slice(0, -1).join('/');

    this.cache.getKeys().forEach(key => {
      // Clear exact matches and related resource endpoints
      if (key.includes(patternBase) || key.includes(resourceBase)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // Public method to clear all cache
  clearCache() {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    // Clear related cache entries on PATCH requests
    this.clearCacheByPattern(endpoint);

    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    // Clear related cache entries on PUT requests
    this.clearCacheByPattern(endpoint);

    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    // Clear related cache entries on DELETE requests
    this.clearCacheByPattern(endpoint);

    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // Client API methods
  async getClients(params?: {
    search?: string;
    status?: string;
    category?: string;
    ltv_segment?: string;
    engagement_level?: string;
    priority?: string;
    utm_source?: string;
    data_quality?: string;
    phone_carrier?: string;
    overall_score_min?: number;
    overall_score_max?: number;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    return this.get('/clients', params);
  }

  async getClient(id: string): Promise<any> {
    return this.get(`/clients/${id}`);
  }

  async createClient(data: any): Promise<any> {

    try {
      const result = await this.post('/clients', data);

      return result;
    } catch (error) {
      console.error('API createClient error:', error);
      throw error;
    }
  }

  async updateClient(id: string, data: any): Promise<any> {
    return this.put(`/clients/${id}`, data);
  }

  async deleteClient(id: string): Promise<{ message: string }> {
    return this.delete(`/clients/${id}`);
  }

  async getClientStatistics(): Promise<{
    total_clients: number;
    active_clients: number;
    total_revenue: number;
    high_priority_clients: number;
  }> {
    return this.get('/clients-statistics');
  }

  async getClientDashboardAnalytics(): Promise<any> {
    return this.get('/clients-dashboard-analytics');
  }

  // Lead API methods
  async getLeads(params?: {
    client_id?: string;
    status?: string;
    priority?: string;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    return this.get('/leads', params);
  }

  async getLead(id: string): Promise<any> {
    return this.get(`/leads/${id}`);
  }

  async createLead(data: any): Promise<any> {
    return this.post('/leads', data);
  }

  async updateLead(id: string, data: any): Promise<any> {
    return this.put(`/leads/${id}`, data);
  }

  async deleteLead(id: string): Promise<{ message: string }> {
    return this.delete(`/leads/${id}`);
  }

  // Deal API methods
  async getDeals(params?: {
    stage?: string;
    assigned_to?: string;
    client_id?: string;
    deal_type?: string;
    priority?: string;
    min_value?: number;
    max_value?: number;
    close_date_from?: string;
    close_date_to?: string;
    search?: string;
    active_only?: boolean;
    overdue_only?: boolean;
    sort_by?: string;
    sort_order?: string;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    return this.get('/deals', params);
  }

  async getDeal(id: string): Promise<any> {
    return this.get(`/deals/${id}`);
  }

  async createDeal(data: any): Promise<any> {
    return this.post('/deals', data);
  }

  async updateDeal(id: string, data: any): Promise<any> {
    return this.put(`/deals/${id}`, data);
  }

  async deleteDeal(id: string): Promise<{ message: string }> {
    return this.delete(`/deals/${id}`);
  }

  async moveDealStage(id: string, data: {
    pipeline_stage: string;
    notes?: string;
    loss_reason?: string;
    loss_details?: string;
    competitor_won?: string;
    lead_id?: number;
  }): Promise<any> {
    return this.patch(`/deals/${id}/move-stage`, data);
  }

  async getDealPipelineStats(params?: {
    assigned_to?: string;
    date_from?: string;
    date_to?: string;
  }): Promise<any> {
    return this.get('/deals-pipeline-stats', params);
  }

  async convertLeadToDeal(leadId: string, data: any): Promise<any> {
    return this.post(`/leads/${leadId}/convert-to-deal`, data);
  }

  async convertLeadToClient(leadId: string, data: any): Promise<any> {
    return this.post(`/leads/${leadId}/convert-to-client`, data);
  }

  // Invoice API methods
  async getInvoices(params?: Record<string, any>): Promise<PaginatedResponse<any>> {
    return this.get('/invoices', params);
  }

  async getInvoice(id: number): Promise<any> {
    return this.get(`/invoices/${id}`);
  }

  async createInvoice(data: any): Promise<any> {
    return this.post('/invoices', data);
  }

  async updateInvoice(id: number, data: any): Promise<any> {
    return this.put(`/invoices/${id}`, data);
  }

  async deleteInvoice(id: number): Promise<void> {
    return this.delete(`/invoices/${id}`);
  }

  async sendInvoice(id: number): Promise<any> {
    return this.post(`/invoices/${id}/send`);
  }

  async markInvoiceAsPaid(id: number, data?: any): Promise<any> {
    return this.post(`/invoices/${id}/mark-paid`, data);
  }

  async createInvoiceFromQuotation(quotationId: number): Promise<any> {
    return this.post(`/quotations/${quotationId}/convert-to-invoice`);
  }

  // Product API methods
  async getProducts(params?: {
    search?: string;
    category?: string;
    status?: string;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    return this.get('/products', params);
  }

  async getProduct(id: string): Promise<any> {
    return this.get(`/products/${id}`);
  }

  async createProduct(data: any): Promise<any> {
    return this.post('/products', data);
  }

  async updateProduct(id: string, data: any): Promise<any> {
    return this.put(`/products/${id}`, data);
  }

  async deleteProduct(id: string): Promise<{ message: string }> {
    return this.delete(`/products/${id}`);
  }

  // Transaction API methods
  async getTransactions(params?: {
    client_id?: string | number;
    product_id?: string;
    status?: string;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    return this.get('/transactions', params);
  }

  async getTransaction(id: string): Promise<any> {
    return this.get(`/transactions/${id}`);
  }

  async createTransaction(data: any): Promise<any> {
    return this.post('/transactions', data);
  }

  async updateTransaction(id: string, data: any): Promise<any> {
    return this.put(`/transactions/${id}`, data);
  }

  async deleteTransaction(id: string): Promise<{ message: string }> {
    return this.delete(`/transactions/${id}`);
  }

  // Quotation methods
  async getQuotations(params?: Record<string, any>): Promise<PaginatedResponse<any>> {
    return this.get('/quotations', params);
  }

  async getQuotation(id: number): Promise<any> {
    return this.get(`/quotations/${id}`);
  }

  async createQuotation(data: any): Promise<any> {
    return this.post('/quotations', data);
  }

  async updateQuotation(id: number, data: any): Promise<any> {
    return this.put(`/quotations/${id}`, data);
  }

  async deleteQuotation(id: number): Promise<void> {
    return this.delete(`/quotations/${id}`);
  }

  async sendQuotation(id: number): Promise<any> {
    return this.post(`/quotations/${id}/send`);
  }

  async acceptQuotation(id: number): Promise<any> {
    return this.post(`/quotations/${id}/accept`);
  }

  async rejectQuotation(id: number): Promise<any> {
    return this.post(`/quotations/${id}/reject`);
  }

  async duplicateQuotation(id: number): Promise<any> {
    return this.post(`/quotations/${id}/duplicate`);
  }

  async getQuotationStatistics(): Promise<any> {
    return this.get('/quotations-statistics');
  }

  // Dashboard API methods
  async getDashboardStats(): Promise<any> {
    return this.get('/dashboard/stats');
  }

  async getRevenueAnalytics(): Promise<any> {
    return this.get('/dashboard/revenue-analytics');
  }

  async getLeadSources(): Promise<any> {
    return this.get('/dashboard/lead-sources');
  }

  async getTopClients(): Promise<any> {
    return this.get('/dashboard/top-clients');
  }

  async getDealsStatus(): Promise<any> {
    return this.get('/dashboard/deals-status');
  }

  // Activity Logs API methods
  async getActivityLogs(params?: {
    search?: string;
    action?: string;
    type?: string;
    user_id?: number;
    performed_by?: string;
    start_date?: string;
    end_date?: string;
    sort_by?: string;
    sort_order?: string;
    per_page?: number;
    page?: number;
  }): Promise<PaginatedResponse<any>> {
    return this.get('/activity-logs', params);
  }

  async clearAllActivityLogs(): Promise<any> {
    return this.delete('/activity-logs/clear');
  }

  // Password Reset API methods
  async forgotPassword(email: string): Promise<{ message: string }> {
    return this.post('/auth/forgot-password', { email });
  }

  async verifyResetToken(token: string, email: string): Promise<{ valid: boolean; message: string }> {
    return this.get('/auth/verify-reset-token', { token, email });
  }

  async resetPassword(token: string, email: string, password: string, password_confirmation: string): Promise<{ message: string }> {
    return this.post('/auth/reset-password', {
      token,
      email,
      password,
      password_confirmation
    });
  }

  // Email Settings API methods
  async getEmailSettings(): Promise<{
    password: string;
    smtpHost: string;
    smtpPort: number;
    smtpUsername: string;
    smtpEncryption: string;
    fromAddress: string;
    fromName: string;
    replyToAddress: string;
  }> {
    return this.get('/auth/settings/email');
  }

  async saveEmailSettings(settings: {
    password: string;
    smtpHost: string;
    smtpPort: number;
    smtpUsername: string;
    smtpEncryption: string;
    fromAddress: string;
    fromName: string;
    replyToAddress: string;
  }): Promise<{ message: string }> {
    return this.post('/auth/settings/email', settings);
  }

  async testEmailConfiguration(testEmail: string): Promise<{ message: string }> {
    return this.post('/auth/settings/email/test', { testEmail });
  }

  async toggleRealEmailDelivery(enabled: boolean): Promise<{ message: string; forceRealEmail: boolean }> {
    return this.post('/auth/settings/email/toggle-real-delivery', { enabled });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request('/health');
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
