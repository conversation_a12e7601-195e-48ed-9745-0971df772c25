version: '3.8'

services:
  kdt-postgres:
    image: postgres:15-alpine
    container_name: kdt-postgres
    environment:
      POSTGRES_DB: kdt
      POSTGRES_USER: kdt
      POSTGRES_PASSWORD: kdt_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kdt -d kdt"]
      interval: 10s
      timeout: 5s
      retries: 5

  kdt-redis:
    image: redis:7-alpine
    container_name: kdt-redis
    ports:
      - "6379:6379"
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  kdt-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kdt-backend
    ports:
      - "8000:8000"
    volumes:
      # Mount source code for live development
      - ./backend:/var/www/html
      - /var/www/html/vendor  # Exclude vendor directory
      - /var/www/html/node_modules  # Exclude node_modules if any
      - backend_storage:/var/www/html/storage
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=kdt-postgres
      - DB_DATABASE=kdt
      - DB_USERNAME=kdt
      - DB_PASSWORD=kdt_password
      - REDIS_HOST=kdt-redis
    depends_on:
      kdt-postgres:
        condition: service_healthy
      kdt-redis:
        condition: service_healthy
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      sh -c "
        # Wait for database
        until php -r \"try { new PDO('pgsql:host=kdt-postgres;port=5432;dbname=kdt', 'kdt', 'kdt_password'); exit(0); } catch(Exception \$$e) { exit(1); }\"; do
          echo 'Waiting for database...'
          sleep 2
        done
        
        # Install dependencies if needed
        if [ ! -d 'vendor' ]; then
          composer install
        fi
        
        # Run migrations
        php artisan migrate --force
        
        # Start PHP-FPM and Nginx
        php-fpm -D && nginx -g 'daemon off;'
      "

  kdt-frontend:
    image: node:18-alpine
    container_name: kdt-frontend
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      # Mount entire project for live development
      - .:/app
      - /app/node_modules  # Exclude node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8000/api/v1
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      sh -c "
        # Install dependencies
        echo 'Installing dependencies...'
        npm ci

        # Start development server with hot reload
        echo 'Starting development server...'
        npm run dev -- --host 0.0.0.0 --port 3000
      "

  kdt-mailpit:
    image: axllent/mailpit:latest
    container_name: kdt-mailpit
    ports:
      - "8025:8025"  # Web interface
      - "1025:1025"  # SMTP server
    networks:
      - kdt-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3

  kdt-adminer:
    image: adminer:latest
    container_name: kdt-adminer
    ports:
      - "8001:8080"
    environment:
      ADMINER_DEFAULT_SERVER: kdt-postgres
    networks:
      - kdt-network

volumes:
  postgres_data:
  backend_storage:

networks:
  kdt-network:
    driver: bridge
