# Authentication & CORS Issues Fix

## Problem Summary

The Pi deployment at https://ts.crtvmkmn.space was experiencing recurring authentication failures with CORS errors after code deployments. The login functionality would fail with errors like:

```
Cross-Origin Request Blocked: The Same Origin Policy disallows reading the remote resource at http://localhost:4001/api/v1/auth/login
```

## Root Cause Analysis

The issue was caused by incorrect API URL configuration in the frontend build process:

1. **Hardcoded Fallback URLs**: Multiple files contained hardcoded `localhost` URLs as fallbacks
2. **Incorrect Vite Configuration**: The build process wasn't properly replacing environment variables
3. **Missing Pi-Specific Config**: The deployment script wasn't using the correct Vite configuration

## Files Fixed

### 1. Environment Configuration
- **`.env.production`**: Updated `VITE_API_URL` from `http://localhost:4001/api/v1` to `/api/v1`
- **`vite.config.pi.js`**: Added explicit define configuration to force `/api/v1`

### 2. Source Code Updates
- **`src/services/api.ts`**: Changed fallback from `http://localhost:8000/api/v1` to `/api/v1`
- **`src/components/DataExportModal.tsx`**: Fixed hardcoded localhost URL
- **`vite.config.ts`**: Updated development fallback to use port 8000 instead of 4001

### 3. Build Process
- **`package.json`**: Updated `build:pi` script to explicitly use `vite.config.pi.js`
- **`deploy-csv-import.sh`**: Added build verification step

### 4. Verification Tools
- **`scripts/verify-build.sh`**: New script to check for problematic localhost URLs in built files

## Technical Solution

### Why Relative URLs Work

The Pi deployment uses nginx proxy configuration that forwards `/api/` requests to the backend:

```nginx
# Frontend nginx config (port 4000)
location /api/ {
    proxy_pass http://127.0.0.1:4001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

This means:
- Frontend: `https://ts.crtvmkmn.space` (port 4000 via Cloudflare tunnel)
- API calls: `https://ts.crtvmkmn.space/api/v1/...` → proxied to `http://127.0.0.1:4001`

### Build Configuration

The `vite.config.pi.js` now explicitly sets the API URL:

```javascript
define: {
  // Force the API URL to use relative path for Pi deployment
  'import.meta.env.VITE_API_URL': JSON.stringify('/api/v1'),
},
```

## Prevention Measures

### 1. Build Verification
The deployment script now includes automatic verification:

```bash
./scripts/verify-build.sh
```

This script:
- Checks for problematic localhost URLs in built files
- Verifies correct API URL patterns are present
- Fails the deployment if issues are found

### 2. Deployment Process
Updated deployment script (`deploy-csv-import.sh`):
- Uses explicit Vite config: `--config vite.config.pi.js`
- Runs build verification before uploading
- Aborts deployment if verification fails

### 3. Environment Variables
- **Development**: Uses `http://localhost:8000/api/v1` (Docker backend)
- **Production**: Uses `/api/v1` (relative URL for nginx proxy)

## Testing the Fix

1. **Build Verification**:
   ```bash
   npm run build:pi
   ./scripts/verify-build.sh
   ```

2. **Login Test**:
   - Visit https://ts.crtvmkmn.space
   - Login with: <EMAIL> / password123
   - Should work without CORS errors

3. **API Calls**:
   - All API calls should use relative URLs
   - No localhost URLs in browser network tab

## Future Deployments

To prevent this issue from recurring:

1. **Always run build verification** before deploying
2. **Use the Pi-specific build command**: `npm run build:pi`
3. **Check browser console** for CORS errors after deployment
4. **Never hardcode localhost URLs** in source code - always use environment variables

## Emergency Fix

If the issue occurs again:

1. Check built files for localhost URLs:
   ```bash
   grep -r "localhost" frontend/dist/assets/
   ```

2. Verify environment variables are being applied:
   ```bash
   grep -o "/api/v1\|localhost" frontend/dist/assets/index-*.js
   ```

3. Rebuild with explicit config:
   ```bash
   rm -rf frontend/dist
   NODE_ENV=production npm run build:pi
   ./scripts/verify-build.sh
   ```

## Related Files

- `vite.config.pi.js` - Pi-specific Vite configuration
- `.env.production` - Production environment variables
- `scripts/verify-build.sh` - Build verification script
- `deploy-csv-import.sh` - Deployment script with verification
- `deploy/pi-native/nginx/kdt-frontend.conf` - Nginx proxy configuration
