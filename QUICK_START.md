# KDT Pi Native Deployment - Quick Start

## 🚀 One-Command Installation

```bash
# Clone and install in one go
cd /home/<USER>/Apps && \
sudo rm -rf ts-crm && \
git clone -b pi-native-deployment https://github.com/zulhelminasir/ts-crm.git && \
cd ts-crm && \
chmod +x deploy/pi-native/scripts/*.sh && \
./deploy/pi-native/scripts/install.sh && \
./deploy/pi-native/scripts/configure-app.sh && \
./deploy/pi-native/scripts/build-frontend.sh && \
./deploy/pi-native/scripts/verify-deployment.sh
```

## ✅ Success Indicators

After installation, you should see:
- ✅ All services running
- ✅ All ports listening (4000-4003)
- ✅ Frontend responding on http://localhost:4000
- ✅ Backend API responding on http://localhost:4001

## 🔧 If Something Goes Wrong

```bash
# Check what failed
./deploy/pi-native/scripts/verify-deployment.sh

# Restart all services
sudo systemctl restart nginx php8.2-fpm postgresql redis-kdt kdt-queue

# Check logs
sudo journalctl -u nginx -f
sudo journalctl -u kdt-queue -f
```

## 📱 Access Your Application

- **Frontend**: http://localhost:4000
- **API**: http://localhost:4001/api/v1
- **Health**: http://localhost:4001/api/v1/health

## 🔄 Quick Commands

### Service Management
```bash
# Check all services
sudo systemctl status nginx php8.2-fpm postgresql redis-kdt kdt-queue

# Restart all services
sudo systemctl restart nginx php8.2-fpm kdt-queue

# View service logs
sudo journalctl -u kdt-queue -f
sudo journalctl -u nginx -f
```

### Application Management
```bash
# Laravel commands
cd /home/<USER>/Apps/ts-crm/backend
php artisan migrate --force
php artisan cache:clear
php artisan config:cache

# Frontend rebuild
cd /home/<USER>/Apps/ts-crm
npm run build:pi
```

### Troubleshooting
```bash
# Check ports
sudo netstat -tuln | grep -E ':(4000|4001|4002|4003)'

# Check processes
ps aux | grep -E '(nginx|php-fpm|postgres|redis)'

# Fix permissions
sudo chown -R zulhelminasir:www-data /home/<USER>/Apps/ts-crm/backend/storage
sudo chmod -R 775 /home/<USER>/Apps/ts-crm/backend/storage
```

## 🎯 Performance Tips

### System Optimization
```bash
# Check system resources
htop
free -h
df -h

# Monitor specific services
sudo systemctl status kdt-queue
sudo journalctl -u kdt-queue --since "1 hour ago"
```

### Application Optimization
```bash
# Clear all caches
cd /home/<USER>/Apps/ts-crm/backend
php artisan cache:clear
php artisan config:cache
php artisan route:cache
php artisan view:clear

# Optimize Composer autoloader
composer dump-autoload --optimize
```

## 🔄 Updates

### Quick Update
```bash
cd /home/<USER>/Apps/ts-crm
git pull origin pi-native-deployment
composer install --no-dev --optimize-autoloader
npm install && npm run build:pi
sudo systemctl restart nginx php8.2-fpm kdt-queue
```

### Database Updates
```bash
cd /home/<USER>/Apps/ts-crm/backend
php artisan migrate --force
php artisan db:seed --force
```

## 🆘 Emergency Recovery

### If Services Won't Start
```bash
# Kill all related processes
sudo pkill -f nginx
sudo pkill -f php-fpm
sudo pkill -f postgres
sudo pkill -f redis

# Restart services
sudo systemctl start postgresql
sudo systemctl start redis-kdt
sudo systemctl start php8.2-fpm
sudo systemctl start nginx
sudo systemctl start kdt-queue
```

### If Database Issues
```bash
# Reset database
sudo -u postgres dropdb kdt_production
sudo -u postgres createdb kdt_production
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE kdt_production TO kdt_user;"

# Re-run migrations
cd /home/<USER>/Apps/ts-crm/backend
php artisan migrate --force
php artisan db:seed --force
```

### If Frontend Issues
```bash
# Clean rebuild
cd /home/<USER>/Apps/ts-crm
rm -rf node_modules package-lock.json frontend/dist
npm install
npm run build:pi
sudo systemctl restart nginx
```

## 📊 Monitoring

### Health Checks
```bash
# Quick health check
curl -s http://localhost:4000 | head -n 5
curl -s http://localhost:4001/api/v1/health

# Detailed verification
./deploy/pi-native/scripts/verify-deployment.sh
```

### Log Monitoring
```bash
# Real-time logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /home/<USER>/Apps/ts-crm/backend/storage/logs/laravel.log
sudo journalctl -u kdt-queue -f
```

---

## 📚 More Information

For detailed instructions and troubleshooting, see **NATIVE_DEPLOYMENT.md**

**🎉 Your KDT application should now be running smoothly on Raspberry Pi!**
