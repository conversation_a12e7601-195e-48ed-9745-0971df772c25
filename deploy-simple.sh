#!/bin/bash

# Simple Non-Docker Deployment for KDT CRM
# This deploys directly to Raspberry Pi without Docker

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Configuration
WEB_ROOT="/var/www/ts-crm"
NGINX_CONF="/etc/nginx/sites-available/ts-crm"
PHP_VERSION="8.2"

# Check if running as root for system setup
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        error "Don't run this script as root. Use sudo when needed."
    fi
}

# Install system dependencies
install_dependencies() {
    log "Installing system dependencies..."
    
    sudo apt update
    sudo apt install -y \
        nginx \
        php${PHP_VERSION}-fpm \
        php${PHP_VERSION}-pgsql \
        php${PHP_VERSION}-mbstring \
        php${PHP_VERSION}-xml \
        php${PHP_VERSION}-curl \
        php${PHP_VERSION}-zip \
        php${PHP_VERSION}-gd \
        php${PHP_VERSION}-bcmath \
        php${PHP_VERSION}-redis \
        postgresql \
        postgresql-contrib \
        redis-server \
        nodejs \
        npm \
        composer \
        git \
        curl
    
    log "✓ System dependencies installed"
}

# Setup database
setup_database() {
    log "Setting up PostgreSQL database..."
    
    # Start PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Create database and user
    sudo -u postgres psql -c "CREATE DATABASE kdt;" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE USER kdt WITH PASSWORD 'kdt123';" 2>/dev/null || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE kdt TO kdt;" 2>/dev/null || true
    
    log "✓ Database setup complete"
}

# Setup Redis
setup_redis() {
    log "Setting up Redis..."
    
    sudo systemctl start redis-server
    sudo systemctl enable redis-server
    
    log "✓ Redis setup complete"
}

# Deploy backend
deploy_backend() {
    log "Deploying Laravel backend..."
    
    # Create web directory
    sudo mkdir -p $WEB_ROOT
    sudo chown -R $USER:www-data $WEB_ROOT
    
    # Copy backend files
    cp -r backend/* $WEB_ROOT/
    
    # Set permissions
    sudo chown -R www-data:www-data $WEB_ROOT/storage $WEB_ROOT/bootstrap/cache
    sudo chmod -R 775 $WEB_ROOT/storage $WEB_ROOT/bootstrap/cache
    
    # Install PHP dependencies
    cd $WEB_ROOT
    composer install --no-dev --optimize-autoloader
    
    # Setup environment
    cp .env.example .env
    php artisan key:generate
    
    # Configure database in .env
    sed -i 's/DB_CONNECTION=.*/DB_CONNECTION=pgsql/' .env
    sed -i 's/DB_HOST=.*/DB_HOST=127.0.0.1/' .env
    sed -i 's/DB_PORT=.*/DB_PORT=5432/' .env
    sed -i 's/DB_DATABASE=.*/DB_DATABASE=kdt/' .env
    sed -i 's/DB_USERNAME=.*/DB_USERNAME=kdt/' .env
    sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=kdt123/' .env
    
    # Run migrations
    php artisan migrate --force
    
    # Cache configuration
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    cd - > /dev/null
    
    log "✓ Backend deployed"
}

# Deploy frontend
deploy_frontend() {
    log "Deploying React frontend..."
    
    # Install Node dependencies
    npm install
    
    # Build for production
    npm run build
    
    # Copy built files to web directory
    sudo mkdir -p $WEB_ROOT/public/app
    sudo cp -r dist/* $WEB_ROOT/public/app/
    
    log "✓ Frontend deployed"
}

# Configure Nginx
configure_nginx() {
    log "Configuring Nginx..."
    
    # Create Nginx configuration
    sudo tee $NGINX_CONF > /dev/null << EOF
server {
    listen 3000;
    server_name localhost;
    root $WEB_ROOT/public;
    index index.php index.html;

    # Frontend (React app)
    location / {
        try_files \$uri \$uri/ /app/index.html;
    }

    # API routes
    location /api {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php${PHP_VERSION}-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
EOF

    # Enable site
    sudo ln -sf $NGINX_CONF /etc/nginx/sites-enabled/ts-crm
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test and reload Nginx
    sudo nginx -t
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    log "✓ Nginx configured"
}

# Start services
start_services() {
    log "Starting services..."
    
    sudo systemctl start php${PHP_VERSION}-fpm
    sudo systemctl enable php${PHP_VERSION}-fpm
    sudo systemctl restart nginx
    
    log "✓ Services started"
}

# Main deployment function
main() {
    cat << EOF
╔══════════════════════════════════════════════════════════════╗
║                 KDT Simple Deployment                       ║
║              Direct Installation (No Docker)                ║
╚══════════════════════════════════════════════════════════════╝

This will install KDT directly on your Raspberry Pi:
- Install PHP, Nginx, PostgreSQL, Redis
- Deploy Laravel backend
- Deploy React frontend
- Configure services

EOF
    
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 0
    fi
    
    check_permissions
    install_dependencies
    setup_database
    setup_redis
    deploy_backend
    deploy_frontend
    configure_nginx
    start_services
    
    log "✓ Deployment completed successfully!"
    
    cat << EOF

╔══════════════════════════════════════════════════════════════╗
║                    Deployment Complete!                     ║
╚══════════════════════════════════════════════════════════════╝

Your KDT CRM is now running at:
- Application: http://$(hostname -I | awk '{print $1}'):3000
- API: http://$(hostname -I | awk '{print $1}'):3000/api

Services:
- Nginx: Active
- PHP-FPM: Active  
- PostgreSQL: Active
- Redis: Active

Logs:
- Nginx: sudo tail -f /var/log/nginx/error.log
- PHP: sudo tail -f /var/log/php${PHP_VERSION}-fpm.log

EOF
}

# Show help
show_help() {
    cat << EOF
KDT Simple Deployment Script

Usage: $0 [OPTIONS]

Options:
    --help          Show this help message
    --install-only  Only install dependencies
    --deploy-only   Only deploy application (skip system setup)

This script deploys KDT without Docker for simpler management.

EOF
}

# Parse command line arguments
case "${1:-deploy}" in
    --help)
        show_help
        ;;
    --install-only)
        install_dependencies
        setup_database
        setup_redis
        ;;
    --deploy-only)
        deploy_backend
        deploy_frontend
        configure_nginx
        start_services
        ;;
    deploy|*)
        main
        ;;
esac
