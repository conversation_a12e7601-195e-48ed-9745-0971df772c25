#!/bin/bash

# Deploy CSV Import Feature to Raspberry Pi
echo "🚀 Deploying CSV Import Feature to Raspberry Pi..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PI_USER="zulhelminasir"
PI_HOST="*************"  # Tailscale IP
PI_PATH="/home/<USER>/Apps/ts-crm"

echo -e "${BLUE}📦 Installing frontend dependencies...${NC}"
npm install

echo -e "${BLUE}🔨 Building frontend with CSV import feature for production...${NC}"
# Set production environment and build
NODE_ENV=production npm run build:pi

echo -e "${BLUE}🔍 Verifying build configuration...${NC}"
./scripts/verify-build.sh
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build verification failed! Aborting deployment.${NC}"
    exit 1
fi

echo -e "${BLUE}📤 Uploading files to Raspberry Pi...${NC}"

# Upload frontend build
echo "Uploading frontend build..."
rsync -avz --progress frontend/dist/ ${PI_USER}@${PI_HOST}:${PI_PATH}/frontend/dist/

# Upload backend controller
echo "Uploading CSV import controller..."
scp backend/app/Http/Controllers/CsvImportController.php ${PI_USER}@${PI_HOST}:${PI_PATH}/backend/app/Http/Controllers/

# Upload updated routes
echo "Uploading API routes..."
scp backend/routes/api.php ${PI_USER}@${PI_HOST}:${PI_PATH}/backend/routes/

echo -e "${BLUE}🔄 Restarting services on Pi...${NC}"
ssh ${PI_USER}@${PI_HOST} << 'EOF'
cd /home/<USER>/Apps/ts-crm

# Restart PHP-FPM and Nginx
sudo systemctl restart php8.3-fpm
sudo systemctl restart nginx

# Clear Laravel cache
cd backend
php artisan config:clear
php artisan route:clear
php artisan cache:clear

echo "✅ Services restarted successfully"
EOF

echo -e "${GREEN}🎉 CSV Import Feature deployed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Access the application: https://ts.crtvmkmn.space"
echo "2. Navigate to 'CSV Import' in the sidebar"
echo "3. Upload your CSV file: master_contacts_crm_ready_deduplicated.csv"
echo "4. Map columns and import your 30k client records"
echo ""
echo -e "${BLUE}🔗 Direct link: https://ts.crtvmkmn.space/csv-import${NC}"
