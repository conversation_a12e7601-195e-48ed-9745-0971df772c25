mac-terminal.log

zulhelminasir@Zulhelmis-MacBook-Pro KDT % # Check what's running on common development ports
echo "🔍 Checking ports 3000, 8000, 5173, 4000..."
lsof -i :3000 || echo "Port 3000: Nothing running"
lsof -i :8000 || echo "Port 8000: Nothing running"
lsof -i :5173 || echo "Port 5173: Nothing running"
lsof -i :4000 || echo "Port 4000: Nothing running"

echo ""
echo "🐳 Checking Docker containers..."
docker ps || echo "Docker not running or not installed"

echo ""
echo "📁 Checking current directory structure..."
pwd
ls -la

echo ""
echo "🔧 Checking if KDT services are running..."
ps aux | grep -E "(node|php|nginx|postgres)" | grep -v grep || echo "No KDT-related processes found"

echo ""
echo "📦 Checking package.json scripts..."
cat package.json | grep -A 10 '"scripts"'
🔍 Checking ports 3000, 8000, 5173, 4000...
COMMAND     PID          USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
firefox   45659 zulhelminasir  101u  IPv4 0xd3d6c97cfabdea6f      0t0  TCP localhost:61386->localhost:hbci (ESTABLISHED)
com.docke 97402 zulhelminasir  240u  IPv6 0x5ae97adb3030624d      0t0  TCP localhost:hbci->localhost:61386 (ESTABLISHED)
com.docke 97402 zulhelminasir  300u  IPv6 0x3cc39d650d6b037f      0t0  TCP *:hbci (LISTEN)
COMMAND     PID          USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
com.docke 97402 zulhelminasir  264u  IPv6 0xcf31ea9a5c67e3b6      0t0  TCP *:irdmi (LISTEN)
Port 5173: Nothing running
Port 4000: Nothing running

🐳 Checking Docker containers...
CONTAINER ID   IMAGE                    COMMAND                  CREATED        STATUS                    PORTS                                                                                          NAMES
bb912bc6c337   kdt-kdt-backend          "docker-php-entrypoi…"   11 hours ago   Up 11 hours (healthy)     0.0.0.0:8000->8000/tcp, [::]:8000->8000/tcp                                                    kdt-backend
2e778130f012   adminer:latest           "entrypoint.sh docke…"   11 hours ago   Up 11 hours               0.0.0.0:8001->8080/tcp, [::]:8001->8080/tcp                                                    kdt-adminer
f06b4f8318bf   postgres:15-alpine       "docker-entrypoint.s…"   11 hours ago   Up 11 hours (healthy)     0.0.0.0:5432->5432/tcp, [::]:5432->5432/tcp                                                    kdt-postgres
98bd23a6947f   redis:7-alpine           "docker-entrypoint.s…"   11 hours ago   Up 11 hours (healthy)     0.0.0.0:6379->6379/tcp, [::]:6379->6379/tcp                                                    kdt-redis
1ae5fe93e6f7   node:18-alpine           "docker-entrypoint.s…"   11 hours ago   Up 11 hours (unhealthy)   0.0.0.0:3000->3000/tcp, [::]:3000->3000/tcp                                                    kdt-frontend
4f94e16d973c   sail-8.4/app             "start-container"        7 days ago     Up 7 days                 0.0.0.0:5175->5175/tcp, [::]:5175->5175/tcp, 0.0.0.0:7001->80/tcp, [::]:7001->80/tcp           adla-laravel-1
a3d3a65a5c87   redis:alpine             "docker-entrypoint.s…"   7 days ago     Up 7 days (healthy)       0.0.0.0:16380->6379/tcp, [::]:16380->6379/tcp                                                  adla-redis-1
d7a813b28131   axllent/mailpit:latest   "/mailpit"               7 days ago     Up 7 days (healthy)       0.0.0.0:11026->1025/tcp, [::]:11026->1025/tcp, 0.0.0.0:18026->8025/tcp, [::]:18026->8025/tcp   adla-mailpit-1
e927a4223e11   mysql/mysql-server:8.0   "/entrypoint.sh mysq…"   7 days ago     Up 7 days (healthy)       0.0.0.0:33061->3306/tcp, [::]:33061->3306/tcp                                                  adla-mysql-1

📁 Checking current directory structure...
/Users/<USER>/Projects/Bolt/KDT
total 25112
drwxr-xr-x   72 <USER>  <GROUP>     2304 Jul 24 13:45 .
drwxr-xr-x    4 <USER>  <GROUP>      128 Jul  8 20:14 ..
-rw-r--r--@   1 <USER>  <GROUP>      282 Jul 24 08:03 .deployignore
-rw-r--r--@   1 <USER>  <GROUP>      406 Jul  8 17:08 .dockerignore
-rw-r--r--@   1 <USER>  <GROUP>    10244 Jul 24 13:46 .DS_Store
-rw-r--r--@   1 <USER>  <GROUP>      499 Jul  8 20:32 .env
-rw-r--r--@   1 <USER>  <GROUP>      503 Jul  8 17:48 .env.example
-rw-r--r--@   1 <USER>  <GROUP>      127 Jul  9 07:00 .env.local
-rw-r--r--@   1 <USER>  <GROUP>      164 Jul 24 08:11 .env.production
-rw-r--r--@   1 <USER>  <GROUP>     1900 Jul 24 03:46 .env.production.example
drwxr-xr-x@  12 <USER>  <GROUP>      384 Jul 24 08:23 .git
-rw-r--r--@   1 <USER>  <GROUP>     2230 Jul 23 07:52 .gitignore
drwxr-xr-x@  31 <USER>  <GROUP>      992 Jul 24 08:03 backend
-rw-r--r--@   1 <USER>  <GROUP>     5792 Jul 22 00:36 client_data_import_and_optimization_summary.md
-rw-r--r--@   1 <USER>  <GROUP>    13866 Jul 19 15:39 client-detail-enhancement-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     8562 Jul 19 17:56 client-details-optimization-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     5345 Jul 24 03:49 CLOUDFLARE_TUNNEL_SETUP.md
-rw-r--r--@   1 <USER>  <GROUP>    26797 Jul 22 01:52 console-logs.log
-rw-r--r--@   1 <USER>  <GROUP>     8565 Jul 19 18:34 critical-fixes-and-layout-redesign-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     6421 Jul 22 02:10 dashboard-revamp-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     5288 Jul 22 00:52 DATA_RECOVERY_PLAN.md
-rw-r--r--@   1 <USER>  <GROUP>     5828 Jul 19 15:55 data-persistence-fix-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     5818 Jul 22 00:13 database_schema_enhancement_plan.md
-rw-r--r--@   1 <USER>  <GROUP>     5022 Jul 12 23:24 DEBUGGING_ANALYSIS.md
drwxr-xr-x@   7 <USER>  <GROUP>      224 Jul 24 08:06 deploy
-rwxr-xr-x@   1 <USER>  <GROUP>     1877 Jul 24 13:43 deploy-csv-import.sh
-rw-r--r--@   1 <USER>  <GROUP>     7696 Jul 23 09:57 deploy-simple.sh
-rwxr-xr-x@   1 <USER>  <GROUP>     7872 Jul 23 07:47 deploy.sh
-rw-r--r--@   1 <USER>  <GROUP>     8359 Jul 24 03:48 DEPLOYMENT_GUIDE.md
drwxr-xr-x@   4 <USER>  <GROUP>      128 Jul 13 10:19 dist
drwxr-xr-x@   3 <USER>  <GROUP>       96 Jul  8 20:31 docker
-rw-r--r--@   1 <USER>  <GROUP>     3312 Jul 24 03:30 docker-compose.dev.yml
-rw-r--r--@   1 <USER>  <GROUP>     2441 Jul 11 01:36 docker-compose.yml
-rw-r--r--@   1 <USER>  <GROUP>      788 Jul 24 03:50 Dockerfile
-rw-r--r--@   1 <USER>  <GROUP>     1643 Jul 24 03:51 Dockerfile.prod
-rw-r--r--@   1 <USER>  <GROUP>      739 Jul  8 12:17 eslint.config.js
drwxr-xr-x@   4 <USER>  <GROUP>      128 Jul 24 13:45 frontend
-rw-r--r--@   1 <USER>  <GROUP>     5458 Jul 22 01:46 IMPORT_STATISTICS_ANALYSIS.md
-rw-r--r--@   1 <USER>  <GROUP>      390 Jul  8 12:17 index.html
-rw-r--r--@   1 <USER>  <GROUP>     5306 Jul 19 16:44 lead-conversion-fix-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     4246 Jul 12 12:47 Makefile
-rw-r--r--@   1 <USER>  <GROUP>  5972111 Jul 22 00:04 master_contacts_crm_dashboard.csv
-rw-r--r--@   1 <USER>  <GROUP>  6283081 Jul 22 18:48 master_contacts_crm_ready_deduplicated.csv
-rw-r--r--@   1 <USER>  <GROUP>     7349 Jul 24 08:21 NATIVE_DEPLOYMENT.md
drwxr-xr-x@ 239 <USER>  <GROUP>     7648 Jul 24 13:43 node_modules
-rw-r--r--@   1 <USER>  <GROUP>   162947 Jul 24 13:45 package-lock.json
-rw-r--r--@   1 <USER>  <GROUP>     1060 Jul 24 13:38 package.json
-rw-r--r--@   1 <USER>  <GROUP>     5248 Jul 12 17:20 PAYMENT_SIMULATION_GUIDE.md
drwxr-xr-x    4 <USER>  <GROUP>      128 Jul 19 11:29 persona card
-rw-r--r--@   1 <USER>  <GROUP>    37044 Jul 24 10:48 pi-output.log
-rw-r--r--@   1 <USER>  <GROUP>       81 Jul  8 12:17 postcss.config.js
drwxr-xr-x    2 <USER>  <GROUP>       64 Jul 12 12:48 public
-rw-r--r--@   1 <USER>  <GROUP>     4458 Jul 24 08:21 QUICK_START.md
-rwxr-xr-x@   1 <USER>  <GROUP>    10444 Jul 23 07:46 quick-setup.sh
-rw-r--r--@   1 <USER>  <GROUP>     6987 Jul 24 03:49 README_PRODUCTION.md
-rw-r--r--@   1 <USER>  <GROUP>     4660 Jul 24 03:48 README.md
drwxr-xr-x@  11 <USER>  <GROUP>      352 Jul 23 02:30 scripts
drwxrwxr-x@  14 <USER>  <GROUP>      448 Jul 12 11:45 src
-rw-r--r--@   1 <USER>  <GROUP>      311 Jul 19 13:52 tailwind.config.js
-rw-r--r--@   1 <USER>  <GROUP>      546 Jul 22 01:35 test_multiple_phones.csv
-rw-r--r--@   1 <USER>  <GROUP>     2246 Jul 22 01:47 test_small_import.csv
-rw-r--r--@   1 <USER>  <GROUP>     2046 Jul 13 15:35 test_sync_fix.md
-rw-r--r--@   1 <USER>  <GROUP>     5432 Jul 12 22:58 test-invoice-creation.js
-rw-r--r--@   1 <USER>  <GROUP>     2319 Jul 19 14:17 test-pagination-dropdown.md
-rw-r--r--@   1 <USER>  <GROUP>     4860 Jul 19 14:21 test-smart-dropdown-positioning.md
-rw-r--r--@   1 <USER>  <GROUP>      552 Jul  8 12:17 tsconfig.app.json
-rw-r--r--@   1 <USER>  <GROUP>      119 Jul  8 12:17 tsconfig.json
-rw-r--r--@   1 <USER>  <GROUP>      479 Jul  8 12:17 tsconfig.node.json
-rw-r--r--@   1 <USER>  <GROUP>     9109 Jul 19 18:49 ui-improvements-and-fixes-summary.md
-rw-r--r--@   1 <USER>  <GROUP>     7651 Jul 19 17:02 validation-fix-comprehensive-summary.md
-rw-r--r--@   1 <USER>  <GROUP>      625 Jul 24 08:11 vite.config.pi.js
-rw-r--r--@   1 <USER>  <GROUP>      465 Jul 24 03:50 vite.config.ts

🔧 Checking if KDT services are running...
zulhelminasir    85008   1.6  0.6 1866881392 113984   ??  S    Tue06PM  22:06.98 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --dns-result-order=ipv4first --experimental-network-inspection --inspect-port=0 --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,17175247402062358909,11304191083047741991,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
zulhelminasir    10023   1.5  0.3 1866853312  54352   ??  S     2:59AM   2:10.44 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,17175247402062358909,11304191083047741991,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
zulhelminasir    68864   0.0  0.0 410203376   1072 s004  S+    1:54PM   0:00.00 scp backend/app/Http/Controllers/CsvImportController.php zulhelminasir@*************:/home/<USER>/Apps/ts-crm/backend/app/Http/Controllers/
zulhelminasir    21887   0.0  0.1 1865082736  10960   ??  S    Wed02AM   0:06.77 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /Users/<USER>/.vscode/extensions/ms-azuretools.vscode-containers-2.1.0/dist/compose-language-service/lib/server.js --node-ipc --node-ipc --clientProcessId=85008
zulhelminasir    21886   0.0  0.1 1865100144  10928   ??  S    Wed02AM   0:06.28 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /Users/<USER>/.vscode/extensions/ms-azuretools.vscode-containers-2.1.0/dist/dockerfile-language-server-nodejs/lib/server.js --node-ipc --node-ipc --clientProcessId=85008
zulhelminasir     2802   0.0  0.1 1865080688  10960   ??  S    Tue10PM   0:07.54 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /Applications/Visual Studio Code.app/Contents/Resources/app/extensions/markdown-language-features/dist/serverWorkerMain --node-ipc --clientProcessId=85008
zulhelminasir    85279   0.0  0.0 1865129840   9104   ??  S    Tue06PM   0:03.46 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /Applications/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/typingsInstaller.js --globalTypingsCacheLocation /Users/<USER>/Library/Caches/typescript/5.8 --enableTelemetry --typesMapLocation /Applications/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/typesMap.json --validateDefaultNpmLocation
zulhelminasir    85269   0.0  0.0 **********   8208   ??  S    Tue06PM   0:53.65 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --max-old-space-size=3072 /Applications/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/tsserver.js --useInferredProjectPerProjectRoot --enableTelemetry --cancellationPipeName /var/folders/yy/0k21rw8d4fv8flfx10q6wfkw0000gn/T/vscode-typescript501/975413fed585504e3e64/tscancellation-4290211290f64c837c6f.tmp* --locale en --noGetErrOnBackgroundUpdate --canUseWatchEvents --validateDefaultNpmLocation --useNodeIpc
zulhelminasir    85268   0.0  0.0 **********   7984   ??  S    Tue06PM   0:06.33 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --max-old-space-size=3072 /Applications/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/tsserver.js --serverMode partialSemantic --useInferredProjectPerProjectRoot --disableAutomaticTypingAcquisition --cancellationPipeName /var/folders/yy/0k21rw8d4fv8flfx10q6wfkw0000gn/T/vscode-typescript501/975413fed585504e3e64/tscancellation-18f8027b31deecbc1520.tmp* --locale en --noGetErrOnBackgroundUpdate --canUseWatchEvents --validateDefaultNpmLocation --useNodeIpc
zulhelminasir    85258   0.0  0.1 **********  11120   ??  S    Tue06PM   0:08.85 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /Applications/Visual Studio Code.app/Contents/Resources/app/extensions/json-language-features/server/dist/node/jsonServerMain --node-ipc --clientProcessId=85008
zulhelminasir    85149   0.0  0.1 **********  10928   ??  S    Tue06PM   0:13.85 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /Users/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.6.2/dist/server.bundle.js --cancellationReceive=file:033f2d46d2e1710e305b4a5a3e4136ca8482044bef --node-ipc --clientProcessId=85008
zulhelminasir    85007   0.0  0.1 1866873152  16896   ??  S    Tue06PM   1:15.24 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,17175247402062358909,11304191083047741991,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
zulhelminasir    46318   0.0  0.2 1866841824  32960   ??  S    17Jul25   1:52.49 /Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-US --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,17175247402062358909,11304191083047741991,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version

📦 Checking package.json scripts...
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint .",
    "preview": "vite preview",
    "build:pi": "vite build --mode production --outDir frontend/dist",
    "preview:pi": "vite preview --port 4000 --host 0.0.0.0"
  },
  "dependencies": {
    "date-fns": "^4.1.0",
    "lucide-react": "^0.344.0",
zulhelminasir@Zulhelmis-MacBook-Pro KDT % # Look for Docker configuration files
echo "🐳 Looking for Docker files..."
find . -name "docker-compose*.yml" -o -name "Dockerfile*" -o -name ".dockerignore" | head -10

echo ""
echo "📋 Checking if there's a Makefile with Docker commands..."
ls -la Makefile 2>/dev/null && echo "Makefile found:" && head -20 Makefile || echo "No Makefile found"

echo ""
echo "🔧 Checking backend setup..."
ls -la backend/ 2>/dev/null || echo "No backend directory found"

echo ""
echo "📄 Checking for Laravel environment files..."
ls -la backend/.env* 2>/dev/null || echo "No .env files found in backend"
🐳 Looking for Docker files...
./Dockerfile.prod
./Dockerfile
./backend/Dockerfile
./docker-compose.dev.yml
./.dockerignore
./docker-compose.yml

📋 Checking if there's a Makefile with Docker commands...
-rw-r--r--@ 1 <USER>  <GROUP>  4246 Jul 12 12:47 Makefile
Makefile found:
# KDT Development Environment Makefile

.PHONY: help start stop restart rebuild watch status logs clean install

# Default target
help: ## Show this help message
        @echo "KDT Development Environment"
        @echo ""
        @echo "Available commands:"
        @awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install all dependencies
        @echo "Installing development dependencies..."
        @chmod +x scripts/dev.sh
        @chmod +x scripts/watch-and-rebuild.sh
        @chmod +x scripts/watch-and-rebuild.js
        @cd scripts && npm install
        @echo "Dependencies installed successfully"

start: ## Start development environment

🔧 Checking backend setup...
total 24728
drwxr-xr-x@ 31 <USER>  <GROUP>      992 Jul 24 08:03 .
drwxr-xr-x  72 <USER>  <GROUP>     2304 Jul 24 13:45 ..
-rw-r--r--@  1 <USER>  <GROUP>     8196 Jul 24 13:36 .DS_Store
-rw-r--r--@  1 <USER>  <GROUP>     1162 Jul 12 13:50 .env
-rw-r--r--@  1 <USER>  <GROUP>     1115 Jul  8 20:30 .env.example
-rw-r--r--@  1 <USER>  <GROUP>     1233 Jul 24 08:11 .env.production.example
drwxr-xr-x@  9 <USER>  <GROUP>      288 Jul  9 12:26 app
-rwxr-xr-x@  1 <USER>  <GROUP>      427 Jul  8 20:27 artisan
drwxr-xr-x@  5 <USER>  <GROUP>      160 Jul 22 00:18 bootstrap
-rw-r--r--@  1 <USER>  <GROUP>     1888 Jul 22 00:18 composer.json
-rw-r--r--   1 <USER>  <GROUP>   302751 Jul 22 00:18 composer.lock
drwxr-xr-x@ 11 <USER>  <GROUP>      352 Jul  8 21:13 config
drwxr-xr-x@  6 <USER>  <GROUP>      192 Jul  8 20:26 database
drwxr-xr-x@  8 <USER>  <GROUP>      256 Jul 24 13:36 deploy
drwxr-xr-x@  5 <USER>  <GROUP>      160 Jul  8 20:38 docker
-rw-r--r--@  1 <USER>  <GROUP>     1495 Jul  8 21:16 Dockerfile
-rw-r--r--   1 <USER>  <GROUP>     1612 Jul 13 14:01 Lead::withTrashed()->count()
-rw-r--r--   1 <USER>  <GROUP>    13067 Jul 22 19:47 lim';
-rw-r--r--@  1 <USER>  <GROUP>  5972111 Jul 22 00:04 master_contacts_crm_dashboard.csv
-rw-r--r--@  1 <USER>  <GROUP>  6283081 Jul 22 18:48 master_contacts_crm_ready_deduplicated.csv
-rw-r--r--   1 <USER>  <GROUP>      607 Jul 22 00:54 missing_records_v2.csv
-rw-r--r--   1 <USER>  <GROUP>      116 Jul 22 00:52 missing_records.csv
drwxr-xr-x@  2 <USER>  <GROUP>       64 Jul 12 12:48 node_modules
drwxr-xr-x@  3 <USER>  <GROUP>       96 Jul  8 20:27 public
drwxr-xr-x@  5 <USER>  <GROUP>      160 Jul  8 20:26 resources
drwxr-xr-x@  5 <USER>  <GROUP>      160 Jul  8 20:27 routes
drwxr-xr-x@  6 <USER>  <GROUP>      192 Jul  8 20:26 storage
-rw-r--r--@  1 <USER>  <GROUP>      546 Jul 22 01:35 test_multiple_phones.csv
-rw-r--r--@  1 <USER>  <GROUP>     2246 Jul 22 01:47 test_small_import.csv
drwxr-xr-x@  5 <USER>  <GROUP>      160 Jul  8 20:26 tests
drwxr-xr-x@  2 <USER>  <GROUP>       64 Jul 12 12:48 vendor

📄 Checking for Laravel environment files...
-rw-r--r--@ 1 <USER>  <GROUP>  1162 Jul 12 13:50 backend/.env
-rw-r--r--@ 1 <USER>  <GROUP>  1115 Jul  8 20:30 backend/.env.example
-rw-r--r--@ 1 <USER>  <GROUP>  1233 Jul 24 08:11 backend/.env.production.example
zulhelminasir@Zulhelmis-MacBook-Pro KDT % 