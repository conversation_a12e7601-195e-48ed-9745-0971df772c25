# KDT Native Deployment Guide for Raspberry Pi

This guide provides step-by-step instructions for deploying the KDT application natively on Raspberry Pi, bypassing Docker for better performance and resource efficiency.

## 🎯 Overview

**Native Deployment Benefits:**
- ✅ Better resource utilization on Raspberry Pi
- ✅ Faster startup times (no container overhead)
- ✅ Direct access to system services and logs
- ✅ Easier debugging and maintenance
- ✅ Resolves Docker build issues (view:cache failures)

**Port Configuration:**
- **4000**: Frontend (React/Vite)
- **4001**: Backend API (Laravel/PHP-FPM)
- **4002**: PostgreSQL Database
- **4003**: Redis Cache
- **4004**: Reserved for future services

## 🔧 Prerequisites

### Raspberry Pi Requirements
- **Hardware**: Raspberry Pi 4 (4GB+ RAM recommended)
- **OS**: Raspberry Pi OS (64-bit) or Ubuntu 22.04 LTS
- **Storage**: 32GB+ microSD card (Class 10 or better)
- **Network**: Stable internet connection

### System Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential tools
sudo apt install -y curl wget git unzip software-properties-common
```

## 🚀 Installation Process

### Phase 1: Clone Deployment Branch

```bash
# Navigate to Apps directory
cd /home/<USER>/Apps

# Remove any existing Docker-based installation
sudo rm -rf ts-crm

# Clone the native deployment branch
git clone -b pi-native-deployment https://github.com/zulhelminasir/ts-crm.git
cd ts-crm

# Verify you're on the correct branch
git branch
# Should show: * pi-native-deployment
```

### Phase 2: Run Installation Scripts

```bash
# Make all scripts executable
chmod +x deploy/pi-native/scripts/*.sh

# Step 1: Install system dependencies and services
./deploy/pi-native/scripts/install.sh

# Step 2: Configure application and database
./deploy/pi-native/scripts/configure-app.sh

# Step 3: Build frontend for production
./deploy/pi-native/scripts/build-frontend.sh

# Step 4: Verify deployment
./deploy/pi-native/scripts/verify-deployment.sh
```

## 🔍 Service Configuration Details

### Nginx Configuration
- **Frontend**: Serves React app on port 4000
- **Backend**: Proxies PHP-FPM on port 4001
- **Config Files**: 
  - `/etc/nginx/sites-available/kdt-frontend.conf`
  - `/etc/nginx/sites-available/kdt-backend.conf`

### PHP-FPM Configuration
- **Pool**: Custom KDT pool optimized for Pi
- **Socket**: `/var/run/php/php8.2-fpm-kdt.sock`
- **Config**: `/etc/php/8.2/fpm/pool.d/kdt-pool.conf`

### PostgreSQL Configuration
- **Database**: `kdt_production`
- **User**: `kdt_user`
- **Port**: 4002 (custom port to avoid conflicts)

### Redis Configuration
- **Port**: 4003
- **Config**: `/etc/redis/redis-kdt.conf`
- **Service**: `redis-kdt.service`

### Systemd Services
- **Queue Worker**: `kdt-queue.service`
- **Scheduler**: `kdt-scheduler.service` + `kdt-scheduler.timer`

## 🛠️ Manual Configuration (If Needed)

### Database Setup
```bash
# Connect to PostgreSQL
sudo -u postgres psql

# Create database and user manually
CREATE DATABASE kdt_production;
CREATE USER kdt_user WITH ENCRYPTED PASSWORD 'kdt_secure_password_2024';
GRANT ALL PRIVILEGES ON DATABASE kdt_production TO kdt_user;
\q
```

### Service Management
```bash
# Check service status
sudo systemctl status nginx
sudo systemctl status php8.2-fpm
sudo systemctl status postgresql
sudo systemctl status redis-kdt
sudo systemctl status kdt-queue

# Restart services if needed
sudo systemctl restart nginx
sudo systemctl restart php8.2-fpm
sudo systemctl restart kdt-queue

# View logs
sudo journalctl -u nginx -f
sudo journalctl -u kdt-queue -f
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Port Conflicts
```bash
# Check what's using ports
sudo netstat -tuln | grep -E ':(4000|4001|4002|4003)'

# Kill conflicting processes
sudo fuser -k 4000/tcp
sudo fuser -k 4001/tcp
```

#### 2. Permission Issues
```bash
# Fix Laravel storage permissions
sudo chown -R zulhelminasir:www-data /home/<USER>/Apps/ts-crm/backend/storage
sudo chmod -R 775 /home/<USER>/Apps/ts-crm/backend/storage
sudo chmod -R 775 /home/<USER>/Apps/ts-crm/backend/bootstrap/cache
```

#### 3. Database Connection Issues
```bash
# Test database connection
sudo -u postgres psql -d kdt_production -c "SELECT version();"

# Check PostgreSQL is listening on correct port
sudo netstat -tuln | grep 4002
```

#### 4. Frontend Build Issues
```bash
# Clear npm cache and rebuild
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
npm run build:pi
```

### Log Locations
- **Nginx**: `/var/log/nginx/error.log`
- **PHP-FPM**: `/var/log/php8.2-fpm.log`
- **PostgreSQL**: `/var/log/postgresql/postgresql-15-main.log`
- **Redis**: `/var/log/redis/redis-kdt.log`
- **Laravel**: `/home/<USER>/Apps/ts-crm/backend/storage/logs/laravel.log`

## 🎯 Post-Installation

### Access URLs
- **Frontend**: http://localhost:4000
- **Backend API**: http://localhost:4001/api/v1
- **Health Check**: http://localhost:4001/api/v1/health

### Cloudflare Tunnel Setup (Optional)
```bash
# Install cloudflared
wget https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-arm64.deb
sudo dpkg -i cloudflared-linux-arm64.deb

# Configure tunnel for port 4000
cloudflared tunnel --url http://localhost:4000
```

### Backup Strategy
```bash
# Database backup
sudo -u postgres pg_dump kdt_production > kdt_backup_$(date +%Y%m%d).sql

# Application backup
tar -czf kdt_app_backup_$(date +%Y%m%d).tar.gz /home/<USER>/Apps/ts-crm
```

## 🔄 Updates and Maintenance

### Updating Application
```bash
cd /home/<USER>/Apps/ts-crm

# Pull latest changes
git pull origin pi-native-deployment

# Update backend dependencies
cd backend
composer install --no-dev --optimize-autoloader
php artisan migrate --force
php artisan config:cache
php artisan route:cache
cd ..

# Rebuild frontend
npm install
npm run build:pi

# Restart services
sudo systemctl restart nginx php8.2-fpm kdt-queue
```

### Performance Monitoring
```bash
# Check system resources
htop
df -h
free -h

# Monitor service performance
sudo systemctl status kdt-queue
sudo journalctl -u kdt-queue --since "1 hour ago"
```

## 🆘 Support

If you encounter issues:

1. **Check the verification script**: `./deploy/pi-native/scripts/verify-deployment.sh`
2. **Review logs**: Check service logs in `/var/log/`
3. **Restart services**: Use `systemctl restart` for problematic services
4. **Check permissions**: Ensure proper file ownership and permissions

## 📋 Quick Reference

### Essential Commands
```bash
# Service management
sudo systemctl {start|stop|restart|status} {nginx|php8.2-fpm|postgresql|redis-kdt|kdt-queue}

# Application commands
cd /home/<USER>/Apps/ts-crm/backend
php artisan {migrate|cache:clear|config:cache|queue:work}

# Frontend commands
cd /home/<USER>/Apps/ts-crm
npm run {build:pi|preview:pi}
```

### File Locations
- **App Directory**: `/home/<USER>/Apps/ts-crm`
- **Nginx Configs**: `/etc/nginx/sites-available/kdt-*`
- **PHP-FPM Pool**: `/etc/php/8.2/fpm/pool.d/kdt-pool.conf`
- **Redis Config**: `/etc/redis/redis-kdt.conf`
- **Systemd Services**: `/etc/systemd/system/kdt-*`

---

**🎉 Congratulations!** Your KDT application is now running natively on Raspberry Pi with optimal performance and resource utilization.
